"""
Conceptual Value Reinforcement Framework (CVRF)
Implements cross-cycle performance comparison, concept generation, and belief updating
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import json
import os
from datetime import datetime

class ConceptLearner:
    def __init__(self,
                 concept_categories: List[str] = ["data_source_priority", "risk_response", "indicator_combination"],
                 learning_rate_base: float = 0.2,
                 min_overlap_threshold: float = 0.3,
                 run_name: str = None):
        """
        Initialize the Concept Learner

        Args:
            concept_categories: Categories of concepts to learn
            learning_rate_base: Base learning rate for textual gradient descent
            min_overlap_threshold: Minimum overlap percentage to apply small updates
            run_name: Name of the run, used for saving logs and concepts
        """
        self.concept_categories = concept_categories
        self.learning_rate_base = learning_rate_base
        self.min_overlap_threshold = min_overlap_threshold
        self.run_name = run_name
        self.cycle_data = {}
        self.concepts = {category: [] for category in concept_categories}

        # 创建概念应用记录
        self.concept_applications = []

        # 如果提供了run_name，创建日志目录
        if self.run_name:
            self.concept_logs_dir = os.path.join(self.run_name, 'concept_generation_logs')
            self.concept_application_logs_dir = os.path.join(self.run_name, 'concept_application_logs')
            os.makedirs(self.concept_logs_dir, exist_ok=True)
            os.makedirs(self.concept_application_logs_dir, exist_ok=True)
            print(f"概念学习器初始化完成，日志将保存到: {self.run_name}")

    def record_cycle_data(self, cycle_id: int,
                          actions: List[float],
                          daily_returns: List[float],
                          risk_metrics: List[Dict[str, Any]],
                          agent_reasoning: List[str]) -> None:
        """
        Record data for a trading cycle

        Args:
            cycle_id: Identifier for the cycle (e.g., week number)
            actions: List of trading actions taken during the cycle
            daily_returns: List of daily returns during the cycle
            risk_metrics: List of risk metrics during the cycle
            agent_reasoning: List of agent reasoning texts during the cycle
        """
        self.cycle_data[cycle_id] = {
            "actions": actions,
            "daily_returns": daily_returns,
            "risk_metrics": risk_metrics,
            "agent_reasoning": agent_reasoning,
            "total_return": sum(daily_returns),
            "risk_warnings": sum(1 for m in risk_metrics if m.get("warning") is not None),
            "avg_position_size": np.mean([abs(a) for a in actions if a != 0])
        }

    def compare_cycles(self, cycle_id1: int, cycle_id2: int) -> Dict[str, Any]:
        """
        Compare two trading cycles

        Args:
            cycle_id1: First cycle to compare
            cycle_id2: Second cycle to compare

        Returns:
            Dictionary of comparison metrics
        """
        if cycle_id1 not in self.cycle_data or cycle_id2 not in self.cycle_data:
            raise ValueError(f"Cycle {cycle_id1} or {cycle_id2} not found in recorded data")

        cycle1 = self.cycle_data[cycle_id1]
        cycle2 = self.cycle_data[cycle_id2]

        # Calculate action overlap percentage
        actions1 = [1 if a > 0 else (-1 if a < 0 else 0) for a in cycle1["actions"]]
        actions2 = [1 if a > 0 else (-1 if a < 0 else 0) for a in cycle2["actions"]]

        # Ensure actions are of the same length for comparison
        min_len = min(len(actions1), len(actions2))
        actions1 = actions1[:min_len]
        actions2 = actions2[:min_len]

        # Calculate overlap percentage
        overlap_count = sum(1 for a1, a2 in zip(actions1, actions2) if a1 == a2)
        overlap_percentage = overlap_count / min_len if min_len > 0 else 0

        # Determine which cycle performed better
        better_cycle = cycle_id1 if cycle1["total_return"] > cycle2["total_return"] else cycle_id2

        return {
            "overlap_percentage": overlap_percentage,
            "return_difference": cycle2["total_return"] - cycle1["total_return"],
            "risk_warning_difference": cycle2["risk_warnings"] - cycle1["risk_warnings"],
            "position_size_difference": cycle2["avg_position_size"] - cycle1["avg_position_size"],
            "better_cycle": better_cycle
        }

    def generate_concepts(self, cycle_id1: int, cycle_id2: int, llm_function) -> List[Dict[str, Any]]:
        """
        Generate concepts by comparing two cycles using an LLM

        Args:
            cycle_id1: First cycle to compare
            cycle_id2: Second cycle to compare
            llm_function: Function to call the language model

        Returns:
            List of generated concepts
        """
        comparison = self.compare_cycles(cycle_id1, cycle_id2)
        cycle1 = self.cycle_data[cycle_id1]
        cycle2 = self.cycle_data[cycle_id2]

        return self._generate_concepts_from_comparison(comparison, cycle1, cycle2, llm_function)

    def generate_concepts_from_data(self, cycle_data1: Dict[str, Any], cycle_data2: Dict[str, Any], llm_function) -> List[Dict[str, Any]]:
        """
        Generate concepts by comparing two cycle data dictionaries directly

        Args:
            cycle_data1: First cycle data
            cycle_data2: Second cycle data
            llm_function: Function to call the language model

        Returns:
            List of generated concepts
        """
        # 计算周期1的指标
        cycle1 = {}
        cycle1["actions"] = cycle_data1.get("actions", [])
        cycle1["daily_returns"] = cycle_data1.get("daily_returns", [])
        cycle1["risk_metrics"] = cycle_data1.get("risk_metrics", [])
        cycle1["agent_reasoning"] = cycle_data1.get("agent_reasoning", [])

        # 计算总收益率
        cycle1["total_return"] = sum(cycle1["daily_returns"]) if cycle1["daily_returns"] else 0

        # 计算风险警告次数
        cycle1["risk_warnings"] = sum(1 for r in cycle1["risk_metrics"] if r and r.get("warning", False))

        # 计算平均仓位大小
        cycle1["avg_position_size"] = np.mean([abs(a) for a in cycle1["actions"]]) if cycle1["actions"] else 0

        # 计算周期2的指标
        cycle2 = {}
        cycle2["actions"] = cycle_data2.get("actions", [])
        cycle2["daily_returns"] = cycle_data2.get("daily_returns", [])
        cycle2["risk_metrics"] = cycle_data2.get("risk_metrics", [])
        cycle2["agent_reasoning"] = cycle_data2.get("agent_reasoning", [])

        # 计算总收益率
        cycle2["total_return"] = sum(cycle2["daily_returns"]) if cycle2["daily_returns"] else 0

        # 计算风险警告次数
        cycle2["risk_warnings"] = sum(1 for r in cycle2["risk_metrics"] if r and r.get("warning", False))

        # 计算平均仓位大小
        cycle2["avg_position_size"] = np.mean([abs(a) for a in cycle2["actions"]]) if cycle2["actions"] else 0

        # 计算比较指标
        comparison = {}

        # 确定哪个周期表现更好
        if cycle1["total_return"] > cycle2["total_return"]:
            comparison["better_cycle"] = 0  # 使用0表示第一个周期
            comparison["return_difference"] = cycle1["total_return"] - cycle2["total_return"]
        else:
            comparison["better_cycle"] = 1  # 使用1表示第二个周期
            comparison["return_difference"] = cycle2["total_return"] - cycle1["total_return"]

        # 计算动作重叠百分比
        min_len = min(len(cycle1["actions"]), len(cycle2["actions"]))
        if min_len > 0:
            overlap_count = sum(1 for i in range(min_len) if np.sign(cycle1["actions"][i]) == np.sign(cycle2["actions"][i]))
            comparison["overlap_percentage"] = overlap_count / min_len
        else:
            comparison["overlap_percentage"] = 0

        # 计算风险警告差异
        comparison["risk_warning_difference"] = abs(cycle1["risk_warnings"] - cycle2["risk_warnings"])

        # 生成概念
        better_cycle = cycle1 if comparison["better_cycle"] == 0 else cycle2
        worse_cycle = cycle2 if comparison["better_cycle"] == 0 else cycle1

        return self._generate_concepts_from_comparison(comparison, better_cycle, worse_cycle, llm_function)

    def _generate_concepts_from_comparison(self, comparison: Dict[str, Any],
                                          better_cycle: Dict[str, Any],
                                          worse_cycle: Dict[str, Any],
                                          llm_function) -> List[Dict[str, Any]]:
        """
        Generate concepts from a comparison of two cycles

        Args:
            comparison: Comparison metrics
            better_cycle: Data for the better performing cycle
            worse_cycle: Data for the worse performing cycle
            llm_function: Function to call the language model

        Returns:
            List of generated concepts
        """
        # 记录生成概念的时间
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Prepare the prompt for the LLM
        # 注意：在这个方法中，better_cycle和worse_cycle已经作为参数传入，不需要再次获取

        prompt = f"""
        I will provide you with data from two trading cycles. Analyze the differences and extract key concepts that explain why one cycle performed better than the other.

        BETTER PERFORMING CYCLE:
        - Total Return: {better_cycle["total_return"]*100:.2f}%
        - Risk Warnings: {better_cycle["risk_warnings"]}
        - Average Position Size: {better_cycle["avg_position_size"]:.2f}
        - Sample Reasoning: {better_cycle["agent_reasoning"][0][:200] if better_cycle["agent_reasoning"] else "No reasoning available"}...

        WORSE PERFORMING CYCLE:
        - Total Return: {worse_cycle["total_return"]*100:.2f}%
        - Risk Warnings: {worse_cycle["risk_warnings"]}
        - Average Position Size: {worse_cycle["avg_position_size"]:.2f}
        - Sample Reasoning: {worse_cycle["agent_reasoning"][0][:200] if worse_cycle["agent_reasoning"] else "No reasoning available"}...

        COMPARISON:
        - Action Overlap: {comparison["overlap_percentage"]*100:.2f}%
        - Return Difference: {abs(comparison["return_difference"])*100:.2f}%
        - Risk Warning Difference: {abs(comparison["risk_warning_difference"])}

        Extract 3-5 key concepts that explain the performance difference, categorized as:
        1. Data Source Priority: How different data sources should be weighted
        2. Risk Response: How to respond to risk signals
        3. Indicator Combination: How to combine different market indicators

        Format each concept as a JSON object with "category" and "concept" fields.
        """

        # 记录生成概念的详细信息
        concept_generation_info = {
            "timestamp": timestamp,
            "comparison_metrics": comparison,
            "better_cycle_metrics": {
                "total_return": better_cycle["total_return"]*100,
                "risk_warnings": better_cycle["risk_warnings"],
                "avg_position_size": better_cycle["avg_position_size"]
            },
            "worse_cycle_metrics": {
                "total_return": worse_cycle["total_return"]*100,
                "risk_warnings": worse_cycle["risk_warnings"],
                "avg_position_size": worse_cycle["avg_position_size"]
            },
            "prompt": prompt
        }

        # Call the LLM to generate concepts
        print(f"正在使用LLM生成概念... 时间: {timestamp}")
        response = llm_function(prompt)

        # 记录LLM响应
        concept_generation_info["llm_response"] = response

        # Parse the response to extract concepts
        # This is a simplified parsing - in practice, you'd want more robust parsing
        concepts = []
        try:
            # Try to extract JSON objects from the response
            import re
            json_matches = re.findall(r'\{[^{}]*\}', response)
            for json_str in json_matches:
                try:
                    concept = json.loads(json_str)
                    if "category" in concept and "concept" in concept:
                        concepts.append(concept)
                except:
                    continue

            # If no valid JSON objects were found, try to parse the text manually
            if not concepts:
                lines = response.split('\n')
                current_category = None
                for line in lines:
                    line = line.strip()
                    if any(cat in line.lower() for cat in [c.lower() for c in self.concept_categories]):
                        current_category = next((c for c in self.concept_categories if c.lower() in line.lower()), None)
                    elif current_category and line and not line.startswith('#') and not line.startswith('-'):
                        concepts.append({"category": current_category, "concept": line})
        except Exception as e:
            # 记录解析错误
            concept_generation_info["parsing_error"] = str(e)
            # Fallback to a simple extraction if all else fails
            concepts = [{"category": "general", "concept": response}]

        # 记录解析出的概念
        concept_generation_info["extracted_concepts"] = concepts

        # 记录概念生成信息到文件
        if hasattr(self, 'run_name') and self.run_name:
            # 确保目录存在
            concept_logs_dir = os.path.join(self.run_name, 'concept_generation_logs')
            os.makedirs(concept_logs_dir, exist_ok=True)

            # 保存概念生成信息
            log_file = os.path.join(concept_logs_dir, f"concept_generation_{timestamp}.json")
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(concept_generation_info, f, ensure_ascii=False, indent=2)

            print(f"概念生成详细信息已保存到: {log_file}")

        # Store the concepts (替换策略：用新概念替换同类别的旧概念)
        for concept in concepts:
            category = concept.get("category")
            if category in self.concepts:
                # 替换策略：清空该类别的旧概念，只保留最新的
                self.concepts[category] = [concept.get("concept")]
                # 打印应用的概念
                print(f"替换概念 - 类别: {category}, 新概念: {concept.get('concept')}")
            else:
                # 如果类别不存在，直接添加
                self.concepts[category] = [concept.get("concept")]
                print(f"新增概念 - 类别: {category}, 概念: {concept.get('concept')}")

        return concepts

    def update_agent_prompts(self, agent_prompts: Dict[str, str],
                             comparison: Dict[str, Any]) -> Dict[str, str]:
        """
        Update agent prompts based on learned concepts using concept replacement strategy

        概念替换策略：每次都从原始提示词开始，只应用最新概念，避免概念累积

        Args:
            agent_prompts: Dictionary of ORIGINAL base agent prompts (without any historical concepts)
            comparison: Comparison metrics between cycles

        Returns:
            Updated agent prompts with only the latest concepts applied
        """
        # 记录更新时间
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        print(f"开始更新代理提示词... 时间: {timestamp}")

        # Calculate learning rate based on overlap percentage
        overlap = comparison["overlap_percentage"]
        learning_rate = self.learning_rate_base

        # If overlap is high, use a smaller learning rate
        if overlap > self.min_overlap_threshold:
            learning_rate *= (1 - overlap)

        print(f"计算得到的学习率: {learning_rate:.4f} (基础学习率: {self.learning_rate_base}, 重叠度: {overlap:.4f})")

        updated_prompts = {}
        prompt_update_records = []

        # Update each agent prompt
        for agent_name, prompt in agent_prompts.items():
            original_prompt = prompt
            updated_prompt = prompt
            applied_concepts = []

            # Apply relevant concepts to each agent
            for category, concept_list in self.concepts.items():
                if not concept_list:
                    continue

                # Select the most recent concept in each category
                concept = concept_list[-1]

                # 确定是否应该应用这个概念
                should_apply = False
                # 标准化类别名称进行比较
                category_normalized = category.lower().replace(" ", "_")

                if (category_normalized == "data_source_priority" and
                    any(term in agent_name.lower() for term in ["analyst", "onchain", "news"])):
                    should_apply = True
                elif (category_normalized == "risk_response" and
                      any(term in agent_name.lower() for term in ["manager", "final", "trader"])):
                    should_apply = True
                elif (category_normalized == "indicator_combination" and
                      any(term in agent_name.lower() for term in ["analyst", "technical"])):
                    should_apply = True

                # 如果应该应用，则应用概念
                if should_apply:
                    print(f"为代理 {agent_name} 应用概念 - 类别: {category}, 概念: {concept}")
                    before_prompt = updated_prompt
                    updated_prompt = self._apply_concept_to_prompt(updated_prompt, concept, learning_rate)

                    # 检查提示词是否实际被修改
                    if updated_prompt != before_prompt:
                        applied_concepts.append({
                            "category": category,
                            "concept": concept,
                            "applied": True
                        })
                    else:
                        applied_concepts.append({
                            "category": category,
                            "concept": concept,
                            "applied": False,
                            "reason": "No change in prompt"
                        })

            # 记录提示词更新信息
            update_record = {
                "agent_name": agent_name,
                "timestamp": timestamp,
                "original_prompt": original_prompt,
                "updated_prompt": updated_prompt,
                "applied_concepts": applied_concepts,
                "learning_rate": learning_rate,
                "comparison_metrics": comparison
            }

            prompt_update_records.append(update_record)
            updated_prompts[agent_name] = updated_prompt

            # 打印更新摘要
            if original_prompt != updated_prompt:
                print(f"代理 {agent_name} 的提示词已更新，应用了 {len([c for c in applied_concepts if c['applied']])} 个概念")
            else:
                print(f"代理 {agent_name} 的提示词未发生变化")

        # 保存更新记录
        if hasattr(self, 'run_name') and self.run_name:
            update_log_file = os.path.join(self.concept_application_logs_dir, f"prompt_updates_{timestamp}.json")
            with open(update_log_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": timestamp,
                    "comparison_metrics": comparison,
                    "learning_rate": learning_rate,
                    "updates": prompt_update_records
                }, f, ensure_ascii=False, indent=2)

            print(f"提示词更新详细信息已保存到: {update_log_file}")

        # 添加到概念应用记录
        self.concept_applications.extend(prompt_update_records)

        return updated_prompts

    def _apply_concept_to_prompt(self, prompt: str, concept: str, learning_rate: float) -> str:
        """
        Apply a concept to a prompt using textual gradient descent

        Note: Modified to always apply concepts (100% application rate) instead of probabilistic application

        Args:
            prompt: The original prompt
            concept: The concept to apply
            learning_rate: The learning rate (still used for other calculations, but not for determining whether to apply)

        Returns:
            Updated prompt
        """
        # This is a simplified implementation of textual gradient descent
        # Modified to ensure 100% application of concepts

        # Convert the concept into a directive
        directive = f"Important: {concept}"

        # Always insert the directive (removed probabilistic check)
        # Find a good insertion point - after the role description but before examples
        lines = prompt.split('\n')

        # Look for a good insertion point - after role description
        insertion_point = 1  # Default to after the first line
        for i, line in enumerate(lines):
            if "You are" in line or "role" in line.lower() or "analyst" in line.lower():
                insertion_point = i + 1
                break

        # Insert the directive
        lines.insert(insertion_point, directive)
        updated_prompt = '\n'.join(lines)
        return updated_prompt

    def save_concepts(self, save_path: str) -> None:
        """
        Save learned concepts to a file

        Args:
            save_path: Path to save the concepts
        """
        with open(save_path, 'w') as f:
            json.dump(self.concepts, f, indent=2)

    def load_concepts(self, load_path: str) -> None:
        """
        Load learned concepts from a file

        Args:
            load_path: Path to load the concepts from
        """
        if os.path.exists(load_path):
            with open(load_path, 'r') as f:
                self.concepts = json.load(f)
