[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "identifier", "type": "string"}], "name": "Register", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "identifier", "type": "string"}, {"indexed": false, "internalType": "string", "name": "report_hash", "type": "string"}], "name": "Report", "type": "event"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "string", "name": "identifier", "type": "string"}], "name": "register_agent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "register_agent_generic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "string", "name": "identifier", "type": "string"}, {"internalType": "string", "name": "report_hash", "type": "string"}], "name": "report_activity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "string", "name": "report_hash", "type": "string"}], "name": "report_activity_generic", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]