#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置环境变量以使用智谱AI API
export USE_ZHIPUAI=true
export USE_DEEPSEEK_API=false

# 禁用代理设置
export http_proxy=""
export https_proxy=""
export HTTP_PROXY=""
export HTTPS_PROXY=""

# 默认参数
START_DATE="2023-01-01"
END_DATE="2023-03-31"
MODEL="zhipu-glm4"
RUN_NAME="benchmark_results"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --start-date)
      START_DATE="$2"
      shift 2
      ;;
    --end-date)
      END_DATE="$2"
      shift 2
      ;;
    --model)
      MODEL="$2"
      shift 2
      ;;
    --run-name)
      RUN_NAME="$2"
      shift 2
      ;;
    *)
      echo -e "${YELLOW}警告: 未知参数 $1${NC}"
      shift
      ;;
  esac
done

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}开始执行基准测试和消融实验${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}使用参数:${NC}"
echo "开始日期: $START_DATE"
echo "结束日期: $END_DATE"
echo "模型: $MODEL"
echo "结果目录: $RUN_NAME"

# 安装zhipuai库（如果尚未安装）
pip install zhipuai -q

# 测试智谱AI API连接
echo -e "\n${YELLOW}正在测试智谱AI API连接...${NC}"
python -c "from utils import get_chat; print(get_chat('Hello', 'zhipu-glm4', debug=True))"

if [ $? -ne 0 ]; then
    echo -e "${RED}智谱AI API连接测试失败，请检查API密钥和网络连接${NC}"
    exit 1
fi

echo -e "\n${GREEN}智谱AI API连接测试成功！${NC}"

# 运行基准测试和消融实验
echo -e "\n${YELLOW}开始运行基准测试和消融实验...${NC}"

# 首先只运行传统策略
echo -e "\n${YELLOW}第1步：运行传统交易策略...${NC}"
python run_benchmark.py \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --model "$MODEL" \
    --run_name "$RUN_NAME" \
    --run_traditional

# 然后运行完整LLM策略
echo -e "\n${YELLOW}第2步：运行完整LLM策略...${NC}"
python run_benchmark.py \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --model "$MODEL" \
    --run_name "$RUN_NAME" \
    --run_full

# 最后运行消融实验
echo -e "\n${YELLOW}第3步：运行消融实验...${NC}"
python run_benchmark.py \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --model "$MODEL" \
    --run_name "$RUN_NAME" \
    --run_ablation \
    --merged_news \
    --no_reflection \
    --no_risk

# 生成最终比较报告
echo -e "\n${YELLOW}第4步：生成比较报告...${NC}"
python run_benchmark.py \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --model "$MODEL" \
    --run_name "$RUN_NAME"

# 检查运行结果
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}实验成功完成！${NC}"
    echo -e "${GREEN}结果已保存在'$RUN_NAME'目录中。${NC}"

    # 检查是否生成了比较报告
    if [ -f "$RUN_NAME/comparison/comparison_report.html" ]; then
        echo -e "${GREEN}比较报告已生成: $RUN_NAME/comparison/comparison_report.html${NC}"
        # 尝试打开报告
        if command -v xdg-open &> /dev/null; then
            xdg-open "$RUN_NAME/comparison/comparison_report.html"
        elif command -v open &> /dev/null; then
            open "$RUN_NAME/comparison/comparison_report.html"
        fi
    else
        echo -e "${YELLOW}警告: 未找到比较报告${NC}"
    fi
else
    echo -e "\n${RED}实验过程中出现错误！${NC}"
    echo -e "${YELLOW}请检查上面的错误信息${NC}"
fi
