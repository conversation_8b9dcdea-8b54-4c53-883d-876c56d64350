#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置环境变量以使用DeepSeek API
export USE_DEEPSEEK_API=true
export USE_ZHIPUAI=false

# 禁用代理设置
export http_proxy=""
export https_proxy=""
export HTTP_PROXY=""
export HTTPS_PROXY=""

# 默认参数
START_DATE="2023-04-01"
END_DATE="2023-06-30"
MODEL="deepseek-chat"  # 使用DeepSeek模型
RUN_NAME="vs_buyandhold_results"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --start-date)
      START_DATE="$2"
      shift 2
      ;;
    --end-date)
      END_DATE="$2"
      shift 2
      ;;
    --model)
      MODEL="$2"
      shift 2
      ;;
    --run-name)
      RUN_NAME="$2"
      shift 2
      ;;
    *)
      echo -e "${YELLOW}警告: 未知参数 $1${NC}"
      shift
      ;;
  esac
done

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}开始执行框架与Buy and Hold策略对比实验${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}使用参数:${NC}"
echo "开始日期: $START_DATE"
echo "结束日期: $END_DATE"
echo "模型: $MODEL"
echo "结果目录: $RUN_NAME"

# 安装zhipuai库（如果尚未安装）
pip install zhipuai -q

# 测试DeepSeek API连接
echo -e "\n${YELLOW}正在测试DeepSeek API连接...${NC}"
python -c "from utils import get_chat; print(get_chat('Hello', 'deepseek-chat', debug=True))"

if [ $? -ne 0 ]; then
    echo -e "${RED}DeepSeek API连接测试失败，请检查API密钥和网络连接${NC}"
    exit 1
fi

echo -e "\n${GREEN}DeepSeek API连接测试成功！${NC}"

# 运行对比实验
echo -e "\n${YELLOW}开始运行框架与Buy and Hold策略对比实验...${NC}"

# 首先确保目录存在
mkdir -p "$RUN_NAME"

# 检查是否已经存在框架策略结果
if [ -f "$RUN_NAME/framework/env_results_trial_0.json" ]; then
    echo -e "\n${GREEN}发现已有框架策略结果，将直接使用...${NC}"
fi

# 检查是否已经存在Buy and Hold策略报告
if [ -f "$RUN_NAME/buyandhold/reports/performance_report.html" ]; then
    echo -e "\n${GREEN}发现已有Buy and Hold策略报告，将直接使用...${NC}"
fi

# 检查是否已经存在对比报告
if [ -f "$RUN_NAME/comparison/comparison_report.html" ]; then
    echo -e "\n${GREEN}发现已有对比报告，将直接使用...${NC}"
    echo -e "\n${GREEN}对比报告已生成: $RUN_NAME/comparison/comparison_report.html${NC}"

    # 尝试打开报告
    if command -v xdg-open &> /dev/null; then
        xdg-open "$RUN_NAME/comparison/comparison_report.html"
    elif command -v open &> /dev/null; then
        open "$RUN_NAME/comparison/comparison_report.html"
    fi

    # 询问是否重新生成
    read -p "是否要重新生成报告？(y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "\n${GREEN}使用已有报告，退出程序${NC}"
        exit 0
    fi
fi

# 运行对比实验
echo -e "\n${YELLOW}运行对比实验...${NC}"

# 设置Python不缓冲输出
export PYTHONUNBUFFERED=1

python -u run_comparison.py \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --model "$MODEL" \
    --run_name "$RUN_NAME"

# 如果出错，尝试再次运行，但使用不同的目录
if [ $? -ne 0 ]; then
    echo -e "\n${RED}运行失败，尝试使用新目录重新运行...${NC}"

    NEW_RUN_NAME="${RUN_NAME}_$(date +%Y%m%d%H%M%S)"
    echo -e "\n${YELLOW}使用新目录: $NEW_RUN_NAME${NC}"

    python -u run_comparison.py \
        --starting_date "$START_DATE" \
        --ending_date "$END_DATE" \
        --model "$MODEL" \
        --run_name "$NEW_RUN_NAME"

    # 更新运行名称
    if [ $? -eq 0 ]; then
        RUN_NAME="$NEW_RUN_NAME"
    fi
fi

# 检查运行结果
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}对比实验成功完成！${NC}"
    echo -e "${GREEN}结果已保存在'$RUN_NAME'目录中。${NC}"

    # 检查是否生成了比较报告
    if [ -f "$RUN_NAME/comparison/comparison_report.html" ]; then
        echo -e "${GREEN}比较报告已生成: $RUN_NAME/comparison/comparison_report.html${NC}"
        # 尝试打开报告
        if command -v xdg-open &> /dev/null; then
            xdg-open "$RUN_NAME/comparison/comparison_report.html"
        elif command -v open &> /dev/null; then
            open "$RUN_NAME/comparison/comparison_report.html"
        fi
    else
        echo -e "${YELLOW}警告: 未找到比较报告${NC}"
    fi
else
    echo -e "\n${RED}对比实验过程中出现错误！${NC}"
    echo -e "${YELLOW}请检查上面的错误信息${NC}"
fi
