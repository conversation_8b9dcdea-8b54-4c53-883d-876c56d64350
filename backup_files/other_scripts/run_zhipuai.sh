#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}正在使用智谱AI API运行交易系统...${NC}"

# 设置环境变量以使用智谱AI API
export USE_ZHIPUAI=true
export USE_DEEPSEEK_API=false

# 设置默认参数
START_DATE="2023-08-01"
END_DATE="2023-08-15"
NUM_TRIALS=3
RUN_NAME="zhipuai_run"
MAX_TOKENS=512  # 限制生成的token数量

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --start-date)
      START_DATE="$2"
      shift 2
      ;;
    --end-date)
      END_DATE="$2"
      shift 2
      ;;
    --trials)
      NUM_TRIALS="$2"
      shift 2
      ;;
    --run-name)
      RUN_NAME="$2"
      shift 2
      ;;
    --max-tokens)
      MAX_TOKENS="$2"
      shift 2
      ;;
    *)
      echo -e "${YELLOW}警告: 未知参数 $1${NC}"
      shift
      ;;
  esac
done

echo -e "${YELLOW}使用参数:${NC}"
echo "开始日期: $START_DATE"
echo "结束日期: $END_DATE"
echo "试验次数: $NUM_TRIALS"
echo "运行名称: $RUN_NAME"
echo "最大Token数: $MAX_TOKENS"

# 设置环境变量以限制生成的token数量
export MAX_TOKENS_LIMIT=$MAX_TOKENS

# 禁用代理设置
export http_proxy=""
export https_proxy=""
export HTTP_PROXY=""
export HTTPS_PROXY=""

# 安装zhipuai库（如果尚未安装）
pip install zhipuai -q

# 测试智谱AI API连接
echo -e "\n${YELLOW}正在测试智谱AI API连接...${NC}"
python -c "from utils import get_chat; print(get_chat('Hello', 'zhipu-glm4', debug=True))"

if [ $? -ne 0 ]; then
    echo -e "${RED}智谱AI API连接测试失败，请检查API密钥和网络连接${NC}"
    exit 1
fi

echo -e "\n${GREEN}智谱AI API连接测试成功！${NC}"

# 使用智谱AI模型运行交易代理
echo -e "\n${YELLOW}正在启动交易代理...${NC}"
python run_agent.py \
    --model zhipu-glm4 \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --use_risk 1 \
    --risk_window_size 5 \
    --risk_confidence 0.95 \
    --risk_threshold 0.1 \
    --use_cvrf 1 \
    --cycle_length 7 \
    --learning_rate 0.2 \
    --num_trials $NUM_TRIALS \
    --generate_report 1 \
    --open_report 1 \
    --run_name "$RUN_NAME"

# 检查运行结果
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}运行成功完成！${NC}"
    echo -e "${GREEN}结果已保存在'$RUN_NAME'目录中。${NC}"
    
    # 检查是否生成了性能报告
    if [ -f "$RUN_NAME/reports/performance_report.html" ]; then
        echo -e "${GREEN}性能报告已生成: $RUN_NAME/reports/performance_report.html${NC}"
    else
        echo -e "${YELLOW}警告: 未找到性能报告${NC}"
    fi
else
    echo -e "\n${RED}运行过程中出现错误！${NC}"
    echo -e "${YELLOW}请检查上面的错误信息，并尝试以下操作:${NC}"
    echo "1. 确保智谱AI API密钥正确"
    echo "2. 检查网络连接"
    echo "3. 尝试减小max_tokens参数: ./run_zhipuai.sh --max-tokens 256"
fi
