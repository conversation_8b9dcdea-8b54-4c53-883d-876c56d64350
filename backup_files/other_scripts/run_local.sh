#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 检查LM Studio是否正在运行
echo -e "${YELLOW}正在检查LM Studio是否运行中...${NC}"

# 尝试连接LM Studio API（禁用代理）
response=$(curl -s -w "%{http_code}" --noproxy "*" http://127.0.0.1:1234/v1/models -o /tmp/lm_studio_response.txt)
status_code=$response

if [ $? -ne 0 ] || [ "$status_code" != "200" ]; then
    echo -e "${RED}错误：LM Studio未运行或无法在http://127.0.0.1:1234访问${NC}"
    echo -e "${RED}状态码: $status_code${NC}"
    echo -e "${YELLOW}请在运行此脚本前启动LM Studio并加载Gemma模型。${NC}"

    # 提供更多诊断信息
    echo -e "\n${YELLOW}诊断步骤:${NC}"
    echo "1. 打开LM Studio应用程序"
    echo "2. 确认Gemma-3-1b-it模型已加载"
    echo "3. 确认本地服务器已启动（应显示\"Local Server: Running\"）"
    echo "4. 检查服务器地址是否为http://127.0.0.1:1234"
    echo -e "\n${YELLOW}如需更详细的诊断，请运行:${NC}"
    echo "python test_lm_studio_connection.py"

    exit 1
fi

# 检查是否有可用模型
models=$(cat /tmp/lm_studio_response.txt)
if ! echo "$models" | grep -q "data"; then
    echo -e "${RED}错误：LM Studio API返回了意外的响应${NC}"
    echo -e "${YELLOW}请确保LM Studio正确配置并加载了模型。${NC}"
    exit 1
fi

# 检查是否有Gemma模型
if ! echo "$models" | grep -q -i "gemma"; then
    echo -e "${YELLOW}警告：未检测到Gemma模型${NC}"
    echo -e "${YELLOW}请确保在LM Studio中加载了Gemma-3-1b-it模型。${NC}"
    echo -e "${YELLOW}可用模型:${NC}"
    echo "$models" | grep -o '"id":"[^"]*"' | cut -d'"' -f4

    # 询问用户是否继续
    read -p "是否仍要继续? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo -e "${GREEN}LM Studio检查通过！${NC}"

# 设置默认模型为gemma-local
export DEFAULT_MODEL="gemma-local"

# 设置默认参数
START_DATE="2023-08-01"
END_DATE="2023-08-15"
NUM_TRIALS=3
RUN_NAME="gemma_run"
MAX_TOKENS=512  # 限制生成的token数量，减少内存使用

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --start-date)
      START_DATE="$2"
      shift 2
      ;;
    --end-date)
      END_DATE="$2"
      shift 2
      ;;
    --trials)
      NUM_TRIALS="$2"
      shift 2
      ;;
    --run-name)
      RUN_NAME="$2"
      shift 2
      ;;
    --max-tokens)
      MAX_TOKENS="$2"
      shift 2
      ;;
    *)
      echo -e "${YELLOW}警告: 未知参数 $1${NC}"
      shift
      ;;
  esac
done

echo -e "${YELLOW}使用参数:${NC}"
echo "开始日期: $START_DATE"
echo "结束日期: $END_DATE"
echo "试验次数: $NUM_TRIALS"
echo "运行名称: $RUN_NAME"
echo "最大Token数: $MAX_TOKENS"

# 设置环境变量以限制生成的token数量
export MAX_TOKENS_LIMIT=$MAX_TOKENS

# 禁用代理设置
export http_proxy=""
export https_proxy=""
export HTTP_PROXY=""
export HTTPS_PROXY=""

# 使用本地Gemma模型运行交易代理
echo -e "\n${YELLOW}正在启动交易代理...${NC}"
python run_agent.py \
    --model $DEFAULT_MODEL \
    --starting_date "$START_DATE" \
    --ending_date "$END_DATE" \
    --use_risk 1 \
    --risk_window_size 5 \
    --risk_confidence 0.95 \
    --risk_threshold 0.1 \
    --use_cvrf 1 \
    --cycle_length 7 \
    --learning_rate 0.2 \
    --num_trials $NUM_TRIALS \
    --generate_report 1 \
    --open_report 1 \
    --run_name "$RUN_NAME"

# 检查运行结果
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}运行成功完成！${NC}"
    echo -e "${GREEN}结果已保存在'$RUN_NAME'目录中。${NC}"

    # 检查是否生成了性能报告
    if [ -f "$RUN_NAME/reports/performance_report.html" ]; then
        echo -e "${GREEN}性能报告已生成: $RUN_NAME/reports/performance_report.html${NC}"
    else
        echo -e "${YELLOW}警告: 未找到性能报告${NC}"
    fi
else
    echo -e "\n${RED}运行过程中出现错误！${NC}"
    echo -e "${YELLOW}请检查上面的错误信息，并尝试以下操作:${NC}"
    echo "1. 确保LM Studio正在运行且模型已加载"
    echo "2. 尝试减小max_tokens参数: ./run_local.sh --max-tokens 256"
    echo "3. 运行诊断工具: python test_lm_studio_connection.py"
    echo "4. 检查系统资源使用情况"
fi
