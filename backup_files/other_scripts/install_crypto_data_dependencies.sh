#!/bin/bash

# 安装加密货币数据获取工具所需的依赖

echo "安装加密货币数据获取工具所需的依赖..."

# 安装基本依赖
pip install pandas tqdm requests

# 安装 yfinance (Yahoo Finance API)
pip install yfinance

# 安装 ccxt (加密货币交易所API)
pip install ccxt

# 安装 pycoingecko (CoinGecko API)
pip install pycoingecko

echo "依赖安装完成！"
echo "现在可以使用以下脚本获取加密货币数据："
echo "1. python crypto_data_fetcher.py         # 使用 CoinGecko API"
echo "2. python crypto_data_fetcher_yfinance.py # 使用 Yahoo Finance API"
echo "3. python crypto_data_fetcher_ccxt.py     # 使用 CCXT (多交易所)"
