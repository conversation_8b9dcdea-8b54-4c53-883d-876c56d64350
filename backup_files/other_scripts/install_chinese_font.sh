#!/bin/bash

# 安装中文字体脚本
echo "开始安装中文字体..."

# 检查是否为Ubuntu/Debian系统
if [ -f /etc/debian_version ]; then
    echo "检测到Debian/Ubuntu系统"
    
    # 安装文泉驿微米黑字体
    sudo apt-get update
    sudo apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei
    
    # 安装思源黑体
    sudo apt-get install -y fonts-noto-cjk
    
    echo "字体安装完成！"
    
# 检查是否为CentOS/RHEL系统
elif [ -f /etc/redhat-release ]; then
    echo "检测到CentOS/RHEL系统"
    
    # 安装文泉驿微米黑字体
    sudo yum install -y wqy-microhei-fonts wqy-zenhei-fonts
    
    # 安装思源黑体
    sudo yum install -y google-noto-sans-cjk-fonts
    
    echo "字体安装完成！"
    
# 检查是否为Arch Linux系统
elif [ -f /etc/arch-release ]; then
    echo "检测到Arch Linux系统"
    
    # 安装文泉驿微米黑字体
    sudo pacman -S --noconfirm wqy-microhei wqy-zenhei
    
    # 安装思源黑体
    sudo pacman -S --noconfirm noto-fonts-cjk
    
    echo "字体安装完成！"
    
# 检查是否为macOS系统
elif [ "$(uname)" == "Darwin" ]; then
    echo "检测到macOS系统"
    
    # 使用Homebrew安装字体
    if command -v brew &>/dev/null; then
        brew tap homebrew/cask-fonts
        brew install --cask font-wqy-microhei font-wqy-zenhei font-noto-sans-cjk
    else
        echo "请先安装Homebrew: https://brew.sh/"
        exit 1
    fi
    
    echo "字体安装完成！"
    
else
    echo "无法识别的操作系统，请手动安装中文字体。"
    echo "推荐安装以下字体之一："
    echo "- 文泉驿微米黑 (WenQuanYi Micro Hei)"
    echo "- 文泉驿正黑 (WenQuanYi Zen Hei)"
    echo "- 思源黑体 (Noto Sans CJK)"
    exit 1
fi

# 清除字体缓存
if [ "$(uname)" == "Linux" ]; then
    fc-cache -fv
elif [ "$(uname)" == "Darwin" ]; then
    sudo atsutil databases -remove
fi

echo "字体缓存已更新，请重启应用程序以应用新字体。"
echo "如果matplotlib仍然无法显示中文，请尝试重启系统。"
