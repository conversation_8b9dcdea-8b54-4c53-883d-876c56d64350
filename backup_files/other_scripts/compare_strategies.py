#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import os
import sys
import traceback
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 读取ETH数据
def load_data(file_path):
    df = pd.read_csv(file_path)
    df['snapped_at'] = pd.to_datetime(df['snapped_at'])
    df.set_index('snapped_at', inplace=True)
    return df

# 从HTML报告中提取交易数据
def extract_data_from_html(html_path):
    with open(html_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    # 使用pandas读取HTML表格
    tables = pd.read_html(html_path)

    # 假设交易数据表格是最后一个表格
    trade_data = tables[-1]

    # 处理日期列
    trade_data['日期'] = pd.to_datetime(trade_data['日期'])
    trade_data.set_index('日期', inplace=True)

    return trade_data

# 生成比较图表
def generate_comparison_charts(framework_data, buyandhold_data, output_dir):
    """
    生成两种策略的比较图表

    参数:
    framework_data: 框架策略的交易数据DataFrame
    buyandhold_data: 买入并持有策略的交易数据DataFrame
    output_dir: 图表输出目录
    """
    os.makedirs(output_dir, exist_ok=True)

    # 设置图表样式
    plt.style.use('seaborn-v0_8-darkgrid')

    # 1. 净资产价值曲线比较
    plt.figure(figsize=(12, 6))
    plt.plot(framework_data.index, framework_data['净资产价值'], label='框架策略', linewidth=2)
    plt.plot(buyandhold_data.index, buyandhold_data['净资产价值'], label='买入并持有策略', linewidth=2)
    plt.title('净资产价值曲线比较')
    plt.xlabel('日期')
    plt.ylabel('净资产价值')
    plt.grid(True)
    plt.legend()
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator())
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'net_worth_comparison.png'), dpi=300)
    plt.close()

    # 2. 累计收益率曲线比较
    plt.figure(figsize=(12, 6))
    # 将百分比字符串转换为浮点数
    framework_returns = framework_data['累计收益率'].str.rstrip('%').astype('float') / 100
    buyandhold_returns = buyandhold_data['累计收益率'].str.rstrip('%').astype('float') / 100

    plt.plot(framework_data.index, framework_returns * 100, label='框架策略', linewidth=2)
    plt.plot(buyandhold_data.index, buyandhold_returns * 100, label='买入并持有策略', linewidth=2)
    plt.title('累计收益率曲线比较')
    plt.xlabel('日期')
    plt.ylabel('累计收益率 (%)')
    plt.grid(True)
    plt.legend()
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator())
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'cumulative_return_comparison.png'), dpi=300)
    plt.close()

    # 3. 日收益率比较
    plt.figure(figsize=(12, 6))
    # 将百分比字符串转换为浮点数
    framework_daily_returns = framework_data['日收益率'].str.rstrip('%').astype('float') / 100
    buyandhold_daily_returns = buyandhold_data['日收益率'].str.rstrip('%').astype('float') / 100

    plt.plot(framework_data.index, framework_daily_returns * 100, label='框架策略', alpha=0.7)
    plt.plot(buyandhold_data.index, buyandhold_daily_returns * 100, label='买入并持有策略', alpha=0.7)
    plt.title('日收益率比较')
    plt.xlabel('日期')
    plt.ylabel('日收益率 (%)')
    plt.grid(True)
    plt.legend()
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator())
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'daily_return_comparison.png'), dpi=300)
    plt.close()

    # 4. 收益率差异图
    plt.figure(figsize=(12, 6))
    returns_diff = framework_daily_returns - buyandhold_daily_returns
    plt.bar(framework_data.index, returns_diff * 100,
            color=np.where(returns_diff >= 0, 'green', 'red'),
            alpha=0.7)
    plt.title('框架策略与买入并持有策略的日收益率差异')
    plt.xlabel('日期')
    plt.ylabel('收益率差异 (%)')
    plt.grid(True)
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator())
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'return_difference.png'), dpi=300)
    plt.close()

# 生成HTML比较报告
def generate_comparison_report(framework_data, buyandhold_data, framework_metrics, buyandhold_metrics, output_dir, charts_dir, output_file):
    """
    生成HTML比较报告

    参数:
    framework_data: 框架策略的交易数据DataFrame
    buyandhold_data: 买入并持有策略的交易数据DataFrame
    framework_metrics: 框架策略的性能指标字典
    buyandhold_metrics: 买入并持有策略的性能指标字典
    output_dir: 输出目录
    charts_dir: 图表目录
    output_file: 输出文件名
    """
    os.makedirs(output_dir, exist_ok=True)

    # 提取性能指标
    framework_total_return = framework_metrics.get('total_return', '-')
    framework_max_drawdown = framework_metrics.get('max_drawdown', '-')
    framework_sharpe = framework_metrics.get('sharpe_ratio', '-')
    framework_win_rate = framework_metrics.get('win_rate', '-')

    buyandhold_total_return = buyandhold_metrics.get('total_return', '-')
    buyandhold_max_drawdown = buyandhold_metrics.get('max_drawdown', '-')
    buyandhold_sharpe = buyandhold_metrics.get('sharpe_ratio', '-')
    buyandhold_win_rate = buyandhold_metrics.get('win_rate', '-')

    # 从HTML报告中提取性能指标
    # 这里简化处理，直接从HTML文件中提取文本
    with open('/home/<USER>/project_python/DAO_MultiAgent-main/vs_buyandhold_results/framework/reports/performance_report.html', 'r', encoding='utf-8') as f:
        framework_html = f.read()

    with open('/home/<USER>/project_python/DAO_MultiAgent-main/vs_buyandhold_results/buyandhold/reports/performance_report.html', 'r', encoding='utf-8') as f:
        buyandhold_html = f.read()

    # 从HTML中提取总收益率
    import re

    # 框架策略指标
    framework_total_return_match = re.search(r'<h3>总收益率</h3>\s*<div class="metric-value .*?">(.*?)</div>', framework_html)
    framework_total_return = framework_total_return_match.group(1) if framework_total_return_match else '-'

    framework_sharpe_match = re.search(r'<h3>Sharpe比率</h3>\s*<div class="metric-value .*?">(.*?)</div>', framework_html)
    framework_sharpe = framework_sharpe_match.group(1) if framework_sharpe_match else '-'

    framework_max_drawdown_match = re.search(r'<h3>最大回撤</h3>\s*<div class="metric-value .*?">(.*?)</div>', framework_html)
    framework_max_drawdown = framework_max_drawdown_match.group(1) if framework_max_drawdown_match else '-'

    framework_win_rate_match = re.search(r'<h3>胜率</h3>\s*<div class="metric-value">(.*?)</div>', framework_html)
    framework_win_rate = framework_win_rate_match.group(1) if framework_win_rate_match else '-'

    # 买入并持有策略指标
    buyandhold_total_return_match = re.search(r'<h3>总收益率</h3>\s*<div class="metric-value .*?">(.*?)</div>', buyandhold_html)
    buyandhold_total_return = buyandhold_total_return_match.group(1) if buyandhold_total_return_match else '-'

    buyandhold_sharpe_match = re.search(r'<h3>Sharpe比率</h3>\s*<div class="metric-value .*?">(.*?)</div>', buyandhold_html)
    buyandhold_sharpe = buyandhold_sharpe_match.group(1) if buyandhold_sharpe_match else '-'

    buyandhold_max_drawdown_match = re.search(r'<h3>最大回撤</h3>\s*<div class="metric-value .*?">(.*?)</div>', buyandhold_html)
    buyandhold_max_drawdown = buyandhold_max_drawdown_match.group(1) if buyandhold_max_drawdown_match else '-'

    buyandhold_win_rate_match = re.search(r'<h3>胜率</h3>\s*<div class="metric-value">(.*?)</div>', buyandhold_html)
    buyandhold_win_rate = buyandhold_win_rate_match.group(1) if buyandhold_win_rate_match else '-'

    # HTML模板
    html_template = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>策略比较报告 - 框架策略 vs 买入并持有策略</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1, h2, h3 {{ color: #333; }}
                    table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    .metrics {{ display: flex; flex-wrap: wrap; }}
                    .metric-card {{
                        background-color: #f8f9fa;
                        border-radius: 5px;
                        padding: 15px;
                        margin: 10px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        flex: 1;
                        min-width: 200px;
                    }}
                    .metric-value {{
                        font-size: 24px;
                        font-weight: bold;
                        margin: 10px 0;
                        color: #0066cc;
                    }}
                    .positive {{ color: green; }}
                    .negative {{ color: red; }}
                    .chart-container {{ margin: 20px 0; }}
                    .chart {{ width: 100%; max-width: 800px; margin: 0 auto; }}
                    .comparison-table {{ width: 100%; margin-bottom: 30px; }}
                    .comparison-table th {{ text-align: center; }}
                    .comparison-table td {{ text-align: center; }}
                    .winner {{ font-weight: bold; background-color: #e6ffe6; }}
                </style>
            </head>
            <body>
                <h1>策略比较报告 - 框架策略 vs 买入并持有策略</h1>
                <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

                <h2>1. 性能指标比较</h2>
                <table class="comparison-table">
                    <tr>
                        <th>指标</th>
                        <th>框架策略</th>
                        <th>买入并持有策略</th>
                        <th>差异</th>
                    </tr>
                    <tr>
                        <td>总收益率</td>
                        <td class="{('positive' if '-' not in framework_total_return else 'negative')}">{framework_total_return}</td>
                        <td class="{('positive' if '-' not in buyandhold_total_return else 'negative')}">{buyandhold_total_return}</td>
                        <td class="{('positive' if float(framework_total_return.strip('%')) > float(buyandhold_total_return.strip('%')) else 'negative')}">{float(framework_total_return.strip('%')) - float(buyandhold_total_return.strip('%')):.2f}%</td>
                    </tr>
                    <tr>
                        <td>Sharpe比率</td>
                        <td>{framework_sharpe}</td>
                        <td>{buyandhold_sharpe}</td>
                        <td class="{('positive' if float(framework_sharpe) > float(buyandhold_sharpe) else 'negative')}">{float(framework_sharpe) - float(buyandhold_sharpe):.2f}</td>
                    </tr>
                    <tr>
                        <td>最大回撤</td>
                        <td class="negative">{framework_max_drawdown}</td>
                        <td class="negative">{buyandhold_max_drawdown}</td>
                        <td class="{('positive' if float(framework_max_drawdown.strip('%')) > float(buyandhold_max_drawdown.strip('%')) else 'negative')}">{float(framework_max_drawdown.strip('%')) - float(buyandhold_max_drawdown.strip('%')):.2f}%</td>
                    </tr>
                    <tr>
                        <td>胜率</td>
                        <td>{framework_win_rate}</td>
                        <td>{buyandhold_win_rate}</td>
                        <td class="{('positive' if float(framework_win_rate.strip('%')) > float(buyandhold_win_rate.strip('%')) else 'negative')}">{float(framework_win_rate.strip('%')) - float(buyandhold_win_rate.strip('%')):.2f}%</td>
                    </tr>
                </table>

                <h2>2. 性能图表比较</h2>

                <div class="chart-container">
                    <h3>净资产价值曲线比较</h3>
                    <img class="chart" src="charts/net_worth_comparison.png" alt="净资产价值曲线比较">
                </div>
                <div class="chart-container">
                    <h3>累计收益率曲线比较</h3>
                    <img class="chart" src="charts/cumulative_return_comparison.png" alt="累计收益率曲线比较">
                </div>
                <div class="chart-container">
                    <h3>日收益率比较</h3>
                    <img class="chart" src="charts/daily_return_comparison.png" alt="日收益率比较">
                </div>
                <div class="chart-container">
                    <h3>收益率差异图</h3>
                    <img class="chart" src="charts/return_difference.png" alt="收益率差异图">
                </div>

                <h2>3. 结论与分析</h2>
                <div>
                    <p>在2023年4月1日至2023年6月30日期间的回测中，我们对比了框架策略和买入并持有策略的表现：</p>
                    <ul>
                        <li><strong>总收益率</strong>: 框架策略的总收益率为{framework_total_return}，而买入并持有策略为{buyandhold_total_return}。
                        {
                            "框架策略表现优于买入并持有策略。"
                            if float(framework_total_return.strip('%')) > float(buyandhold_total_return.strip('%'))
                            else "买入并持有策略表现优于框架策略。"
                        }</li>
                        <li><strong>最大回撤</strong>: 框架策略的最大回撤为{framework_max_drawdown}，买入并持有策略为{buyandhold_max_drawdown}。
                        {
                            "框架策略的风险控制表现更好。"
                            if abs(float(framework_max_drawdown.strip('%'))) < abs(float(buyandhold_max_drawdown.strip('%')))
                            else "买入并持有策略的风险控制表现更好。"
                        }</li>
                        <li><strong>Sharpe比率</strong>: 框架策略的Sharpe比率为{framework_sharpe}，买入并持有策略为{buyandhold_sharpe}。
                        {
                            "框架策略的风险调整收益更高。"
                            if float(framework_sharpe) > float(buyandhold_sharpe)
                            else "买入并持有策略的风险调整收益更高。"
                        }</li>
                        <li><strong>胜率</strong>: 框架策略的胜率为{framework_win_rate}，买入并持有策略为{buyandhold_win_rate}。
                        {
                            "框架策略的交易成功率更高。"
                            if float(framework_win_rate.strip('%')) > float(buyandhold_win_rate.strip('%'))
                            else "买入并持有策略的交易成功率更高。"
                        }</li>
                    </ul>
                    <p><strong>总体结论</strong>:
                    {
                        "框架策略在此测试期间内整体表现优于买入并持有策略，不仅收益更高，风险控制也更好。"
                        if float(framework_total_return.strip('%')) > float(buyandhold_total_return.strip('%')) and abs(float(framework_max_drawdown.strip('%'))) < abs(float(buyandhold_max_drawdown.strip('%')))
                        else "买入并持有策略在此测试期间内整体表现优于框架策略，收益更高且风险控制更好。"
                        if float(framework_total_return.strip('%')) < float(buyandhold_total_return.strip('%')) and abs(float(framework_max_drawdown.strip('%'))) > abs(float(buyandhold_max_drawdown.strip('%')))
                        else "两种策略各有优劣，框架策略在收益/风险控制方面表现更好，而买入并持有策略在另一方面表现更好。"
                    }
                    </p>
                </div>
            </body>
            </html>
    """

    # 写入HTML文件
    with open(os.path.join(output_dir, output_file), 'w', encoding='utf-8') as f:
        f.write(html_template)

def main():
    try:
        # 设置路径
        framework_report_path = '/home/<USER>/project_python/DAO_MultiAgent-main/vs_buyandhold_results/framework/reports/performance_report.html'
        buyandhold_report_path = '/home/<USER>/project_python/DAO_MultiAgent-main/vs_buyandhold_results/buyandhold/reports/performance_report.html'
        output_base_dir = '/home/<USER>/project_python/DAO_MultiAgent-main/vs_buyandhold_results/comparison'
        reports_dir = os.path.join(output_base_dir, 'reports')
        charts_dir = os.path.join(reports_dir, 'charts')

        # 确保目录存在
        os.makedirs(charts_dir, exist_ok=True)

        # 检查文件是否存在
        if not os.path.exists(framework_report_path):
            print(f"错误: 框架策略报告文件不存在: {framework_report_path}")
            return

        if not os.path.exists(buyandhold_report_path):
            print(f"错误: 买入并持有策略报告文件不存在: {buyandhold_report_path}")
            return

        # 提取交易数据
        print(f"从报告中提取框架策略数据: {framework_report_path}")
        framework_data = extract_data_from_html(framework_report_path)
        print(f"框架策略数据提取成功，共 {len(framework_data)} 条记录")

        print(f"从报告中提取买入并持有策略数据: {buyandhold_report_path}")
        buyandhold_data = extract_data_from_html(buyandhold_report_path)
        print(f"买入并持有策略数据提取成功，共 {len(buyandhold_data)} 条记录")

        # 生成比较图表
        print(f"生成比较图表到: {charts_dir}")
        generate_comparison_charts(framework_data, buyandhold_data, charts_dir)

        # 生成HTML比较报告
        print(f"生成HTML比较报告到: {reports_dir}")
        generate_comparison_report(
            framework_data,
            buyandhold_data,
            {}, # 框架策略指标将从HTML中提取
            {}, # 买入并持有策略指标将从HTML中提取
            reports_dir,
            'charts',
            'comparison_report.html'
        )

        print("比较分析完成！")
        print(f"比较报告已保存到: {os.path.join(reports_dir, 'comparison_report.html')}")

    except Exception as e:
        print(f"错误: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
