#!/bin/bash

# 下载中文字体脚本
echo "开始下载中文字体..."

# 创建字体目录
mkdir -p fonts

# 下载文泉驿微米黑字体
echo "正在下载文泉驿微米黑字体..."
wget -O fonts/wqy-microhei.ttc https://github.com/anthonyfok/fonts-wqy-microhei/raw/master/wqy-microhei.ttc

if [ $? -eq 0 ]; then
    echo "字体下载成功！"
    echo "字体已保存到 fonts/wqy-microhei.ttc"
else
    echo "字体下载失败，请手动下载文泉驿微米黑字体并放置到 fonts 目录中。"
    echo "下载地址: https://github.com/anthonyfok/fonts-wqy-microhei/raw/master/wqy-microhei.ttc"
fi

echo "完成！"
