[{"id": 41, "url": "https://news.google.com/rss/articles/CBMingFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tc2VsbC1vZmYtZGVjb2RlZC1iaXRjb2luLWRvd24tMTktZnJvbS1pdHMtMjAyMy1oaWdocy1uZWFycy10aGUtdXNkLTIwMDAwLW1hcmsvdmlkZW9zaG93Lzk4NTQwNDA3LmNtc9IBogFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tc2VsbC1vZmYtZGVjb2RlZC1iaXRjb2luLWRvd24tMTktZnJvbS1pdHMtMjAyMy1oaWdocy1uZWFycy10aGUtdXNkLTIwMDAwLW1hcmsvYW1wX3ZpZGVvc2hvdy85ODU0MDQwNy5jbXM?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Mar 2023 08:00:00 GMT", "title": "Crypto sell-off decoded: Bitcoin down 19% from its 2023 highs, nears the USD 20,000 mark - The Economic Times", "content": "Find this comment offensive?\n\nChoose your reason below and click on the Report button. This will alert our moderators to take action\n\nName\n\nReason for reporting:\n\nFoul language\n\nSlanderous\n\nInciting hatred against a certain community"}, {"id": 22, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTAzLTEwL2JpdGNvaW4tZHJvcHMtYmVsb3ctMjAtMDAwLWxldmVsLWZvci1maXJzdC10aW1lLXNpbmNlLWphbnVhcnnSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Mar 2023 08:00:00 GMT", "title": "Bitcoin Drops Below $20000 Level for First Time Since January - Bloomberg", "content": "Bitcoin is having its worst week since November as an equity selloff, jitters in the banking sector and an escalating US regulatory crackdown on crypto combine to hurt investor sentiment.\n\nThe largest token fell as much 3.2% on Friday, breaking below $20,000 for the first time since January, after falling more than 8% on Thursday, data compiled by Bloomberg show. Smaller coins like Ether , Solana and Cardano also fell, though Bitcoin recouped some of its losses on Friday afternoon and other tokens turned positive after earlier declines."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiaWh0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjMvMDMvMTAvYml0Y29pbi1mYWxscy1iZWxvdy0yMDAwMC02My1iaWxsaW9uLXdpcGVkLW9mZi1jcnlwdG9jdXJyZW5jeS1tYXJrZXQuaHRtbNIBbWh0dHBzOi8vd3d3LmNuYmMuY29tL2FtcC8yMDIzLzAzLzEwL2JpdGNvaW4tZmFsbHMtYmVsb3ctMjAwMDAtNjMtYmlsbGlvbi13aXBlZC1vZmYtY3J5cHRvY3VycmVuY3ktbWFya2V0Lmh0bWw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Mar 2023 08:00:00 GMT", "title": "More than $70 billion wiped off crypto market in 24 hours as bitcoin drops below $20,000 - CNBC", "content": "In this article BTC.CM=\n\nETH.CM= Follow your favorite stocks CREATE FREE ACCOUNT\n\nJonathan <PERSON> | Nurphoto | Getty Images\n\nBitcoin briefly fell 8% to below $20,000 on Friday, hitting a near-two-month low, after a stock market sell-off in the U.S. and the collapse of a crypto-focused lender. The cryptocurrency market saw more than $70 billion wiped off its value over the course of the 24 hours. Bitcoin was last trading lower by just 2.7% at $19,944.66, according to Coin Metrics. Ether was last down 2.6% at $1,414.21. The crypto sell-off has been prompted by a number of factors. The movement of cryptocurrency prices is quite closely correlated to U.S. stock markets, in particular the tech-heavy Nasdaq .\n\nOn Tuesday, U.S. Federal Reserve Chairman <PERSON> indicated that interest rates may go higher —and stay higher — than expected. The raising of interest rates over the past year has weighed on risk assets such as stocks, and in particular cryptocurrencies. \"There is just little reason to buy bitcoin now as the market is saturated with negative developments, not just specifically for the crypto industry, but also for the wider financial market as well,\" <PERSON><PERSON>, an analyst at Japanese crypto firm Bitbank, told CNBC via email.\n\nBanking worries\n\nAnother major factor weighing on crypto prices is the collapse of Silvergate Capital, a major lender to the crytpo industry. Silvergate said Wednesday it is winding down operations and liquidating its bank. Silvergate's fall is another example how the collapse of major cryptocurrency exchange FTX continues to have an impact on the industry. FTX was a big customer of Silvergate. Separately, on Friday morning the Federal Deposit Insurance Corporation closed Silicon Valley Bank and took control of its deposits, making it the largest U.S. bank failure since the global financial crisis. The bank's parent company, SVB Financial, said late Wednesday that it sold off $21 billion worth of its holdings at a $1.8 billion loss. SVB was a major bank in the technology start-up space.\n\nwatch now"}, {"id": 42, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9iaXRjb2luLWV0aGVyLWZvcmthc3QtNTAwLW5mdC0wOTUxNDY0MzAuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Mar 2023 08:00:00 GMT", "title": "Bitcoin, Ether, Forkast 500 NFT index close lower on the day - Yahoo Finance", "content": "Bitcoin and Ether tumbled during Asia trading hours on Friday afternoon, with Dogecoin leading losses among the top 10 largest non-stablecoin cryptocurrencies by market capitalization, pressured by crypto bank Silvergate’s liquidation announcement, and a new U.S. tax proposal on crypto mining. Equities also weakened, pressured by the prospect of higher-than-expected interest rates and a tighter U.S. labor market. The Forkast 500 NFT index also closed lower on the day.\n\nSee related article: Bank bust\n\nFast facts\n\nBitcoin fell by 7.96% in the past 24 hours, to trade at US$19,929 by 4:30 p.m. in Hong Kong. Ether lost 8.2% to change hands at US$1,409, according to CoinMarketCap data.\n\nBitcoin and Ether, the two largest cryptocurrencies, were pressured by crypto bank Silvergate Capital’s announcement to cease operations and liquidate its banking unit. Crypto investors also reacted to the administration of U.S. President <PERSON> proposing an excise tax on crypto mining equal to 30% of the electricity cost used.\n\nThe crypto fear and greed index, which measures crypto market sentiment, declined to 34, its lowest since Jan. 13 and reflected market concern among investors.\n\nDogecoin was the worst performer of the day, losing 8.58% to trade at US$0.06546, followed closely by the Shiba Inu token, which fell 8.09% to US$$0.********.\n\nThe global crypto market cap decreased 6.62% over the last 24 hours to US$928.9 billion, with the total crypto market volumes rising by 60.53% to US$69.37 billion.\n\nThe Forkast NFT 500 index fell 1.7% to 4,164.86 over the last 24 hours, with Moonbirds down 12.77% as the biggest decliner, followed by the Bored Ape Kennel Club that lost 17.91%. The Forkast NFT 500 Index is a proxy measure of the performance of the global NFT market and includes 500 eligible smart contracts on any given day.\n\nAsian equity markets weakened, mirroring Wall Street’s overnight downtrend, amid mounting concern that the U.S. Fed’s key interest rate will end up higher than expected, combined with a tighter U.S. labor market.\n\nJapan’s Nikkei 225 fell 1.67%, the Shanghai Composite Index lost 1.4%, the Shenzen Component declined 1.19% and Hong Kong’s Hang Seng Index ended the day 3.04% lower, its lowest close in 11 weeks.\n\nEuropean stocks also tumbled on Friday. The STOXX 600 fell 1.7% and Germany’s DAX 40 lost 1.81%, as investors worldwide remained cautious after shares of Silicon Valley Bank (SVB), a major lender to technology startups, plunged more than 60% on Thursday. The SVB chaos led to four of the biggest U.S. banks losing more than US$50 billion in market value.\n\nThe U.S. Bureau of Labor Statistics is scheduled to release its February job report on Friday, which will include two key metrics that may provide hints on the Fed’s upcoming decision on interest rates, unemployment and nonfarm payroll. Analysts are forecasting 225,000 new jobs added, according to CNBC.\n\nSee related article: CFTC chair calls Ethereum a commodity, in contrast to SEC chair Gensler’s position\n\n(Updates to the ninth bullet to add information on Silicon Valley Bank jitters and adds corrections to unemployment data in the last bullet.)"}, {"id": 26, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9yZXZpZXdpbmctY29kZS1taW5kLW51bWJpbmctcS0xNTM1MzQ3NjQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Mar 2023 08:00:00 GMT", "title": "Reviewing Code Is Mind-Numbing: Q&A With Bitcoin Maintainer <PERSON> - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nFew people understand the key technical issues currently facing the world’s dominant cryptocurrency. <PERSON> is one of them.\n\n<PERSON> is one of four “maintainers” for Bitcoin Core (or just Core), the most popular software for connecting to the Bitcoin network.\n\nMaintainers review changes to Bitcoin Core known as “commits,” which are submitted – by fellow Bitcoin developers – as “pull requests” or “PRs.<PERSON> <PERSON> and other maintainers then approve or “merge” those changes into Core’s source code. The “code review” is critical to ensuring no buggy code gets merged.\n\nThe process is transparent, and as <PERSON> combs through commits he livestreams the process on Twitch.\n\n<PERSON> is a gamer at heart, and only got into Bitcoin in high school to pay for video games he couldn’t otherwise afford. His parents wouldn’t give him a credit card, open a bank account for him or even give him an allowance. He resorted to freelancing on the BitcoinTalk forum and began writing code in exchange for bitcoin (BTC).\n\n<PERSON>, who says he's now in his mid-20s, gets paid as an engineer at the Bitcoin infrastructure firm Blockstream, where aside from a few corporate tasks, his main priority is working on Bitcoin Core.\n\nHe says code review is one of the biggest challenges Bitcoin faces today. Most Core developers are keen on writing code for new features, but few enjoy the more mundane task of reviewing code submitted by their peers. <PERSON> says more contributors need to focus on code review to tackle the 300-plus PRs in Core’s GitHub repository. The community has a Bitcoin Core PR Review Club that meets weekly to help newer contributors learn about the review process.\n\n<PERSON> agreed to an interview with CoinDesk at the Advancing Bitcoin conference in London. He elaborated on why code review is so critical, explained what Bitcoin Core contributors do every day, and weighed in on the current debate over op_vault and Speedy Trial. Here’s a partial transcript of that interview.\n\nStory continues\n\nCoinDesk: How did you discover Bitcoin?\n\nAndrew <PERSON>: When I was younger, in high school, I didn't have a bank account because I was under 18. My parents didn't open one for me. I didn't have a credit card – even a supervised credit card – and I didn't have an allowance. But Steam was selling games for bitcoin. If you do PC gaming, you can download Steam and it has basically all the PC games.\n\nAlso, on purse.io, you could sell bitcoin for stuff. Well, I wanted to play games. I wanted to buy them. I mean, I'm okay with pirating but, you know, pirating things is kind of sketchy. You don't know what you're downloading. It could be complete malware.\n\nSo I was, like, this bitcoin thing is fully electronic. Maybe I can use that to buy games – but how do I get bitcoin? Maybe I can do some work and get paid in bitcoin.\n\nI know a few people who did that. So that's how I learned programming. I’d go on BitcoinTalk and people would say, “I will pay you however much to write me a script that does this.”\n\nWell, that seemed simple enough. I also had a friend in high school. He was, like, “Hey, have you heard about this bitcoin thing? I think you might like it.” He was definitely buying drugs with bitcoin.\n\nSo that's how I got into Bitcoin. And eventually I was like, “Well, I'm using this wallet and I'm running into these issues. I clearly know how to write a program. Maybe I can fix this wallet.” That's how I got into doing development.\n\nI was running this thing called Armory. Which was basically not maintained. I mean, it's still kind of maintained by one guy, so barely.\n\nBy the time I was using it, it was kind of a mess and it didn't always work. I was finding that some of the problems that were happening in Armory were caused by things that Bitcoin Core was doing. So I started going into Bitcoin Core and asking what’s Bitcoin Core doing? Oh, Bitcoin Core has this bug that's causing us to have a bug.\n\nArmory was doing something not recommended, which was to read the block files directly from Bitcoin Core – you're not supposed to do that. When they changed the format, it broke everything.\n\nI was trying to reconcile the two, and then Armory just kind of fell off my map. That's how I transitioned to Bitcoin Core. I eventually stopped working on Armory because I got more done on Core.\n\nYesterday we talked about the ratio of Bitcoin contributors who review code versus contributors who write code. Can you share your thoughts on that?\n\nOur main bottleneck in Bitcoin Core has been review. We have 300-plus PRs open and they need to be reviewed. Whether it’s just to make sure the code is good or just conceptually like, “Do we even want this change?”\n\nThe problem with every PR is that one person usually writes it, but we need multiple people to review it, give an approval or leave comments. Therefore, we must have more reviewers than people writing, but that's just not how it works.\n\nPersonally, I find code review to be a little bit boring. It's a little annoying and it can be kind of mind-numbing. But I still do it. I guess it's like a necessary evil and it's because I don't find it fun. If I do it enoug I start feeling like I’ll burn out because it's no longer enjoyable.\n\nSo you have to find some balance between writing code and reviewing code. It’s a bit of a catch-22. We have to have more reviewers than coders, but how do you become competent enough to review code if you're not writing code? It's a conundrum.\n\nWe're in a bear market and organizations like Brink that fund Bitcoin development are saying funding is down by about 50%. Why do we need to pay Bitcoin contributors and developers?\n\nFundamentally, every piece of software has bugs. There will always be bugs to find and bugs to fix. That's just general software maintenance that must happen.\n\nAnd even then, the software that exists now cannot last forever. Operating systems will evolve and libraries will evolve and change. Eventually the software will just stop compiling on a computer; it might just stop running. And so, there needs to be constant work just to keep it up to date.\n\nSo there are always things to update, even without new features. But there are new features and we do want to improve Bitcoin. Not just the consensus rules, but also how we relay transactions, what kind of transactions we accept into the mempool and the peer-to-peer protocol.\n\nThere can be DoS vectors we want to fix or change that maybe haven't been discovered yet. There's always something.\n\nIf I'm a new Core contributor, what are some of the big issues I would need to know about?\n\nThere are currently a number of issues that exist, like pinning attacks, that are pretty well documented. It seems to be that no one exploits them, but that's not a good reason to not fix them.\n\nThere's been a lot of work on the mempool – how and what transactions are accepted into the mempool, what methods there are for fee bumping, and things like that. It's relevant to Lightning and other [layer 2] networks.\n\nWhat’s a “pinning attack?”\n\nIf both of us open a Lightning channel together, I can make it so that you can never bump the fee on that transaction. So I can make it perpetually low-fee and it never gets mined, then try to double-spend it later.\n\nThere's a bunch of attacks you can do with the existing mempool policy rules. These are documented on the mailing list and they're definitely problems. If someone tried to exploit them it would be annoying, but I don't think we've seen anyone try to exploit them.\n\nWe still want to fix them and there's been a lot of work on making improvements so that we don't have these pinning attacks, or at least, if you want to pin a transaction, it'll be really expensive.\n\nWe also discussed op_vault and Speed Trial yesterday. There have been some tensions around James O’Beirne’s recommendation to deploy op_vault using Speedy Trial. Any comments?\n\nWith a new proposal like that, deployment should be the last thing to think about.\n\nSome ideas on how to deploy things are, for some reason, contentious. If you want to have a discussion about the proposal, having deployment in there kind of causes it to be derailed.\n\nSo I do think James putting that in there was probably a mistake. The Taproot deployment section wasn't defined until after Taproot. The code changes themselves were merged into Bitcoin Core but not active. It's not unusual to just say we'll deal with deployment after we figure out what we want the code changes to be.\n\nSpeedy Trial was an experiment for Taproot. We've tried different deployment methods over the years with varying degrees of success."}]