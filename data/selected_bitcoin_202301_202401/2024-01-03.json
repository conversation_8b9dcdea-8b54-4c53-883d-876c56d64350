[{"id": 23, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDI0LTAxLTAzL2pwbW9yZ2FuLWdvbGRtYW4tdGFsa2luZy10by1ncmF5c2NhbGUtYWJvdXQtYml0Y29pbi1ldGYtcm9sZdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "<PERSON><PERSON><PERSON><PERSON>, Goldman in Talks With Grayscale About Bitcoin ETF Role - Bloomberg", "content": "Crypto asset manager Grayscale Investments is in talks with firms, including JPMorgan and Goldman Sachs, to potentially play a key role in its proposed Bitcoin exchange-traded fund, according to a person familiar with the matter.\n\nBoth are being considered to be authorized participants — firms that have the power to create and redeem shares of the fund, said the person, who asked to remain anonymous to discuss the private talks."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDI0LTAxLTAzL2JpdGNvaW4tYnRjLXJldmVyc2VzLWNvdXJzZS1haGVhZC1vZi1lYWdlcmx5LWF3YWl0ZWQtZXRmLWRlY2lzaW9u0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Bitcoin Reverses Course Ahead of Eagerly Awaited ETF Decision - Bloomberg", "content": "A slump in Bitcoin on Wednesday saw the cryptocurrency erase almost all gains it had made in the first days of this year, bucking a long-running upswing that outperformed a global malaise in traditional assets.\n\nThe world’s largest token fell as much as 9.2% to dip briefly below $41,000 shortly after 7 a.m. in New York, a day after the digital asset had topped the $45,000 mark in a 21-month high. Wednesday’s price swings are the largest in more than two months. The volatility also spilled over into crypto-linked stocks, with shares in the US crypto exchange Coinbase Global Inc. falling as much as 8.1% before paring the decline."}, {"id": 35, "url": "https://news.google.com/rss/articles/CBMifmh0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjQvMDEvMDMvYml0Y29pbi1kcm9wcy01cGVyY2VudC1naXZpbmctYmFjay1hbGwtb2YtaXRzLW5ldy15ZWFyLWdhaW5zLWFzLXRyYWRlcnMtc3RheS1vbi1ldGYtd2F0Y2guaHRtbNIBggFodHRwczovL3d3dy5jbmJjLmNvbS9hbXAvMjAyNC8wMS8wMy9iaXRjb2luLWRyb3BzLTVwZXJjZW50LWdpdmluZy1iYWNrLWFsbC1vZi1pdHMtbmV3LXllYXItZ2FpbnMtYXMtdHJhZGVycy1zdGF5LW9uLWV0Zi13YXRjaC5odG1s?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Bitcoin gives back Tuesday's new year rally as traders weigh ETF decision, Fed policy - CNBC", "content": "Bitcoin on Wednesday reversed its new year rally as investors weighed the Federal Reserve's policy outlook and stayed on alert for updates on the Securities and Exchange Commission's looming bitcoin exchange-traded fund decision.\n\nThe price of bitcoin was last lower by more than 4% at $42,685.85, according to Coin Metrics. Earlier in the day, it fell as much as 6%, giving back nearly all of its gains from Tuesday, when it rose as high as $45,913.30, its highest level since April 2022.\n\nEther was down more than 6% at $2,221.27, while other coins suffered steeper losses. Solana declined by 7% and Ripple's XRP fell 6%, while and litecoin and dogecoin slid 10% and 9%, respectively.\n\nInvestors cited some concern that the SEC wouldn't approve an ETF this year, as expected by bitcoin bulls.\n\nThat uncertainty \"triggered some jitters in short-term traders who then decided to unwind long positions, especially since leverage had been increasing fast,\" said <PERSON>, economist and author of the \"Crypto is Macro Now\" newsletter."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vd3d3Lm55dGltZXMuY29tLzIwMjMvMDQvMDkvYnVzaW5lc3MvYml0Y29pbi1taW5pbmctZWxlY3RyaWNpdHktcG9sbHV0aW9uLmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "The Real-World Costs of the Digital Race for Bitcoin - The New York Times", "content": "Texas was gasping for electricity. Winter Storm Uri had knocked out power plants across the state, leaving tens of thousands of homes in icy darkness. By the end of Feb. 14, 2021, nearly 40 people had died, some from the freezing cold.\n\nMeanwhile, in the husk of a onetime aluminum smelting plant an hour outside of Austin, row upon row of computers were using enough electricity to power about 6,500 homes as they raced to earn Bitcoin, the world’s largest cryptocurrency.\n\nThe computers were performing trillions of calculations per second, hunting for an elusive combination of numbers that Bitcoin’s algorithm would accept. About every 10 minutes, a computer somewhere guesses correctly and wins a small number of Bitcoins worth, in recent weeks, about $170,000. Anyone can try, but to make a business of it can require as much electricity as a small city.\n\nIn Texas, the computers kept running until just after midnight. Then the state’s power grid operator ordered them shut off, under an agreement that allowed it to do so if the system was about to fail. In return, it began paying the Bitcoin company, Bitdeer, an average of $175,000 an hour to keep the computers offline. Over the next four days, Bitdeer would make more than $18 million for not operating, from fees ultimately paid by Texans who had endured the storm.\n\nThe New York Times has identified 34 such large-scale operations, known as Bitcoin mines, in the United States, all putting immense pressure on the power grid and most finding novel ways to profit from doing so. Their operations can create costs — including higher electricity bills and enormous carbon pollution — for everyone around them, most of whom have nothing to do with Bitcoin.\n\nUntil June 2021, most Bitcoin mining was in China. Then it drove out Bitcoin operations, at least for a time, citing their power use among other reasons. The United States quickly became the industry’s global leader."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vd3d3LmJ1c2luZXNzaW5zaWRlci5jb20vcGVyc29uYWwtZmluYW5jZS93aGF0LWlzLWNyeXB0b2N1cnJlbmN50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Cryptocurrency Explained: Definition & Examples of Crypto - Business Insider", "content": "Paid non-client promotion: Affiliate links for the products on this page are from partners that compensate us (see our advertiser disclosure with our list of partners for more details). However, our opinions are our own. See how we rate investing products to write unbiased product reviews.\n\nCryptocurrencies are digital assets that are created and run on a blockchain.\n\nBitcoin and ether are two popular cryptocurrencies, but there are many others.\n\nInvesting in cryptocurrency can be extremely risky, and the underlying technology is very new.\n\nNEW LOOK Sign up to get the inside scoop on today’s biggest stories in markets, tech, and business — delivered daily. Read preview Thanks for signing up! Access your favorite topics in a personalized feed while you're on the go. download the app Email address Sign up By clicking “Sign Up”, you accept our Terms of Service and Privacy Policy . You can opt-out at any time.\n\nAdvertisement\n\nIt's important for investors to understand how cryptocurrencies work, who creates and controls them, and why you might want to buy cryptocurrencies.\n\nWhile there may be opportunities to build wealth, there are a lot of risks involved with crypto investing, and you need to be mindful of scams.\n\nWhat is cryptocurrency?\n\nCryptocurrency is a type of decentralized digital currency that investors can buy and sell along the blockchain. Unlike banknotes or minted coins that have a tangible physical form, cryptocurrencies can only be accessed using computers and other electronic devices.\n\nAdvertisement\n\nA decentralized currency is a currency not issued by a government or financial institution. In fact, no single person, company, or government controls a crypto's blockchain. Instead, it's run by a decentralized network of computers worldwide. Anyone with advanced technology skills and coding experience can create a cryptocurrency.\n\nThe lack of a central authority can also make cryptocurrencies more secure. \"It's hack-proof because there's no one central point of failure,\" explains David Donovan, executive vice president at Publicis Sapient.\n\nHow do cryptocurrencies work?\n\nWhile there are thousands of cryptocurrencies, many with unique traits, they all tend to work in similar ways. It's hard to avoid some jargon when discussing cryptos, but the concepts can be relatively easy to understand.\n\nBlockchain technology\n\nA cryptocurrency's blockchain is a digital record of all the transactions involving that crypto. Copies of the blockchain are stored and maintained by computers around the world. They're often compared to general ledgers, part of traditional double-entry bookkeeping systems where each transaction leads to debit and credit in different sections of the books.\n\nAdvertisement\n\n\"It works like a general ledger — it's that simple,\" says Donovan. Perhaps you start with two coins and send one to someone. \"On the blockchain, it would say I'm sending you one coin, and I now have one coin, and you have one coin.\"\n\nEach grouping of transactions is turned into a block and chained to the existing ledger. Once a block is added it can't be reversed or altered — which is why people describe blockchains as \"immutable.\"\n\nSome cryptos have their own blockchain. For example, there are Bitcoin and Ethereum blockchains. But there are also cryptos that are built on top of an existing blockchain rather than starting from zero.\n\nEther is the cryptocurrency native to the Ethereum blockchain, but is also available for trading on other exchanges like Coinbase, Binance.US, and Robinhood.\n\nPublic transactions under pseudonymous\n\nCryptocurrencies have another defining feature. The blockchains are public ledgers, which means anyone can see and review the transactions that occurred. However, they can also provide a degree of anonymity.\n\nAdvertisement\n\n\"You have a private key, which is how you initiate transactions, and a public key, which is how someone identifies you in the market,\" says Donovan.\n\nA blockchain's transactions are tied to a crypto wallet's public key, but nobody necessarily knows who controls that wallet. This is why cryptos are often described as pseudonymous — the public key is a person's pseudonym.\n\nComing to consensus Cryptocurrencies commonly use one of two mechanisms to create a system of trust and determine which transactions are valid and added to their blockchain:\n\n\n\nProof of work . This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power.\n\n. This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power. Proof of stake. This is a newer and less energy-intensive mechanism. \"Proof of stake is they validate transactions on the blockchain by people putting value on the line,\" explains Parisi. \"They stake some of the currency they own to make sure they only validate true transactions.\"\n\nTypes of cryptocurrencies\n\nAccording to CoinMarketCap, there were more than 25,149 different cryptocurrencies with a global market value of about $1.16 trillion as of May 30, 2023. Some of the most popular cryptocurrencies include:\n\nBitcoin\n\nDogecoin\n\nEther\n\nLitecoin\n\nTether\n\nBinance coin\n\nDai\n\nTRON\n\nCronos\n\nUSD coin\n\nBitcoin cash\n\nAdvertisement\n\nBitcoin, the first cryptocurrency, was launched in 2009 as an alternative type of decentralized and digital money. Since then, people have also created cryptocurrencies that serve other functions or are designed for specific types of transactions.\n\n\"Cryptocurrencies can have many different uses,\" says Parisi. \"Some are used in gaming environments to earn rewards in a game, while others facilitate payments. Some are designed for cross-border remittances … some are designed for micro payments.\"\n\nFor example, stablecoins are a type of cryptocurrency that try to maintain a steady and fixed exchange rate with another asset, such as the US dollar. Governance tokens are another example of a specialized cryptocurrency. They give token holders voting power in a corresponding crypto project.\n\nInsider's Featured Crypto Apps Public Investing\n\nWealthfront Investing Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Editor's Rating 4.14/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Editor's Rating 4.34/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Start investing On Public's website Start investing On Wealthfront's website\n\nWhat is digital currency?\n\nDigital currency is a type of currency that can only be accessed in an electronic form, such as through a computer or mobile phone. This money has no physical equivalent, unlike tangible forms of currency like banknotes or minted coins. But just like physical money, digital currencies can be used to purchase goods and services.\n\nAdvertisement\n\nHowever, you'll be limited to online platforms and communities, such as investing platforms, gaming sites, and gambling portals. Some of the most popular forms of digital currency include cryptocurrencies, central bank digital currencies (CBDC), and stablecoins.\n\n\"There's a strive toward decentralization,\" says Nisa Amoils, a managing partner at A100xx Ventures. \"Digital currencies like cryptocurrencies continue to be a worthwhile investment for many investors.\"\n\nDigital currencies come in two forms:\n\nCentralized currency: Currencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public.\n\nCurrencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public. Decentralized currency: Currencies not issued by governments or financial institutions. Instead, decentralized currencies operate through peer-to-peer financial networks to eliminate the middleman (aka banks) and allow lending, trading, and borrowing directly with merchants.\n\nAdvertisement\n\nDigital currencies like crypto are often appealing to investors who are wary of government-issued funds and are that are seeking alternatives.\n\n\"Some people who had been excluded from the traditional financial system, or have had their currencies devalued, are seeking an opportunity to participate in the markets, and this is a retail-driven phenomenon first,\" says Amoils. \"There's this crisis of trust, and people want wealth creation for themselves. And so that spurred this whole kind of trading speculative movement.\"\n\nHow to invest in cryptocurrency\n\nYou can start investing in cryptocurrencies through existing crypto exchanges and investing platforms. Some of the best cryptocurrency exchanges (such as Kraken and Coinbase) offer assets like staking rewards, goal-planning features, low fees, and more.\n\nSome of the best investment apps that offer cryptocurrencies (such as Robinhood Investing) include a range of investment types, low fees, market access, and more.\n\nAdvertisement\n\nYou can create your own crypto\n\nAnyone with coding skills and/or advanced technical knowledge can create their own cryptocurrencies — although this is not always an easy feat and isn't recommended for beginners. The three ways to create crypto are:\n\nBuilding a new blockchain: The most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more.\n\nThe most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more. Modifying a blockchain: If you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge.\n\nIf you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge. Building upon a blockchain: The simplest way to make your own coins or tokens is by expanding upon an already existing blockchain. But keep in mind that the success of your cryptocurrency will be reliant on the success of the original blockchain. Some blockchains that allow this are Binance and Ethereum.\n\nAre cryptocurrencies secure?\n\nThe blockchain technology behind cryptocurrencies can help ensure that the coins and systems remain secure. \"What's never been refuted is the value of blockchain,\" says Donovan. \"The way the ledger system is set up and every transaction is recorded. And the fact that it's immutable.\"\n\nHowever, that doesn't mean you don't need to worry about security. The crypto world is rife with scams. Of course, that's also true of traditional financial systems and currencies. Someone asking you to pay with a gift card or wire transfer is a red flag that you're dealing with a scammer. But several factors could make crypto scams especially worrisome.\n\nAdvertisement\n\nFor example, cryptocurrency transactions can't be reversed. There's also less regulation of cryptocurrencies and platforms than of traditional financial services in the US. Plus, some people may feel pressure to act quickly and send or invest their money because they're worried about missing out on an opportunity.\n\n\"One way to avoid a scam is to invest in more well-established cryptocurrencies,\" says Parisi. \"You still may be subject to scams or fraud in terms of how you hold it, send it, or receive it.\" But you can have some certainty that the cryptocurrency itself isn't a scam.\n\nAre cryptocurrencies a good investment?\n\nCryptocurrencies may present a good investment opportunity, and there are many ways to invest in the crypto world.\n\nYou could buy a coin (or coins) and hold onto them, hoping they'll increase in value. Or you could use your coins in a decentralized finance (DeFi) platform to earn interest through staking or lending. You also might take a more traditional route, such as an exchange-traded fund (ETF) that is tied to cryptocurrencies. There could even be opportunities to invest in projects or supporting industries rather than in the cryptocurrencies themselves.\n\nAdvertisement\n\n\"From an investment perspective, crypto is rapidly evolving,\" says Parisi. \"You shouldn't put an amount of assets you're not willing to lose. It should be, relatively speaking, a small portion of your portfolio.\"\n\nBefore making any investment, consider the potential pros and cons:\n\nPros Cons Easy to invest\n\nDiversify your portfolio\n\nThere is a lot of opportunity\n\nFaster and cost-effective transactions\n\nDecentralized currency\n\nSecurity and transparency through the blockchain Cryptocurrencies can be very volatile\n\nSome crypto projects may fail\n\nThe investment may be a scam\n\nEnvironmental impact due to excessive power consumption through ASIC computers\n\nLacking refund and cancellation policies\n\nShould you invest in crypto?\n\nWhile cryptocurrency investing is a hotly debated topic, it's worth understanding what's going on so you can make an informed decision. If you decide to get started, you could fully jump in or just dip your toe.\n\n\"Learn about crypto by opening up wallets, accounts, trading currencies, and learning more about the use cases,\" says Parisi. \"But do it in a reasonable way. We're still in the early days, and regulation of crypto is still evolving.\"\n\nAdvertisement\n\nDonovan suggests opening an account with a regulated and publicly traded company like Coinbase. But, he says, \"It's really about being smart and using the system to take baby steps.\"\n\nCrypto FAQs\n\nWhat is Bitcoin? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Bitcoin is a cryptocurrency, an electronic version of money that verifies transactions using cryptography (the science of encoding and decoding information). As Bitcoin educator, developer, and entrepreneur Jimmy Song says, Bitcoin is \"decentralized, digital, and scarce money.\" Bitcoin is decentralized because this code is run by thousands of computers (i.e., 'nodes') spread across the globe, digital because it exists as a set of code that determines how it operates, and scarce because its code caps its overall volume to 21 million bitcoins. When you use bitcoin to buy something, it records the transaction on a blockchain, which is essentially a ledger or database whose entries can't be modified or erased.\n\nWhat is Ethereum? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Ethereum is an open-source, decentralized computing platform network. The Ethereum network works like the Bitcoin network in that it's built on blockchain technology, essentially a digital public ledger where financial agreements can be verified and stored entirely by software — without the intervention of a third party.\n\nWhat are privacy coins? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Privacy coins are cryptocurrencies that obscure transactions on their blockchain to maintain the anonymity of users and their activity. Participants in a transaction will know the amount transacted and the parties involved. However, the same information will be unobtainable to any outside observer. The anonymity that privacy coins provide offers a potentially appealing outlet for money laundering or other criminal transactions. As such, privacy coins are a point of contention in the ongoing debate around cryptocurrency privacy and regulation.\n\nWhat is a crypto wallet? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. A crypto wallet is a software program or physical device that allows you to store your crypto and allow for the sending and receiving of crypto transactions. A crypto wallet consists of two key pairs: private keys and public keys. A public key is derived from the private key and serves as the address used to send crypto to the wallet. The important part of a wallet — and the part where new users often find themselves getting into trouble — is the private key. A private key is like the key to a safe deposit box. Anyone who has access to the private key of a wallet can take control of the balance held there. But unlike a safe deposit box, crypto users who hold their own private keys and make transactions using non-custodial wallets (i.e., a wallet not hosted by an exchange or other third-party) become their own bank.\n\nWhat is hash rate? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Hash rate is a measure of the total computational power being used by a proof-of-work cryptocurrency network to process transactions in a blockchain. It can also be a measure of how fast a cryptocurrency miner's machines complete these computations. Miners use computers to run computations on complex mathematical puzzles based on transaction data. These systems generate millions or trillions of guesses per second as to what the solutions to these puzzles could be. These are hashes, alphanumeric codes randomized to identify a single, unique piece of data.\n\nWhat is yield farming? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Yield farming is a means of earning interest on your cryptocurrency, similar to how you'd earn interest on any money in your savings account. And similarly to depositing money in a bank, yield farming involves locking up your cryptocurrency, called \"staking,\" for a period of time in exchange for interest or other rewards, such as more cryptocurrency.\n\nWhat is crypto staking? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Crypto staking is similar to depositing money in a bank, in that an investor locks up their assets, and in exchange, earns rewards, or \"interest.\" \"Staking is a term used to refer to the delegating of a certain number of tokens to the governance model of the blockchain and thus locking them out of circulation for a specified length of time,\" says Nicole DeCicco, the owner and founder of CryptoConsultz, a cryptocurrency consultancy in the Portland, Oregon area. A particular network's protocol locks up an investor's holdings — similar to depositing money in a bank, and agreeing not to withdraw it for a set time period, which benefits the network in a couple of ways, according to DeCicco. First, this can increase the value of a token by limiting the supply. Second, the tokens can be used to govern the blockchain if the network uses a proof-of-stake (PoS) system. A PoS system — as opposed to a proof-of-work (PoW) one, which incorporates \"mining\" — can be fairly complicated, especially for crypto newcomers."}]