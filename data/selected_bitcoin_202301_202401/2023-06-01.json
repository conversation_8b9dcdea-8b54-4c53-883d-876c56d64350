[{"id": 23, "url": "https://news.google.com/rss/articles/CBMid2h0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA2LTAxL2NyeXB0by1ydWVzLWJpdGNvaW4tcy1idGMtZGVjb3VwbGluZy1mcm9tLWFpLWZ1ZWxlZC10ZWNoLXN0b2NrLWdhaW5z0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 01 Jun 2023 07:00:00 GMT", "title": "Crypto Rues Bitcoin's Decoupling From AI-Fueled Tech Stock Gains - Bloomberg", "content": "Crypto fans are now paying a price for Bitcoin’s weakened correlation with technology stocks.\n\nThe top digital asset posted a monthly slump in May for the first time in 2023, while the Nasdaq 100 added almost 8% amid hype over artificial intelligence."}, {"id": 26, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA2LTAxL2JpdGNvaW4tbWluZXJzLWFyZS1jaHVybmluZy1vdXQtbW9yZS1jb21wdXRpbmctcG93ZXItdGhhbi1ldmVy0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 01 Jun 2023 07:00:00 GMT", "title": "Bitcoin Mining Hash Rate at Record High Helps Send Shares Surging - Bloomberg", "content": "Bitcoin miners are producing more computing power than ever with powerful specialized computers.\n\nMining difficulty, a measure of computing power to earn Bitcoin, reached an all-time high on Wednesday, according to data from btc.com. The bi-weekly update posted a 3.4% increase after another 3.22% jump during the last period. It has been on the rise with only two slight declines this year."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiVmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9iaXRjb2luLW1pbGxpb25haXJlLWNyeXB0by1mb3VuZGVyLWZvdW5kLTAyNTM1MjA5Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 01 Jun 2023 07:00:00 GMT", "title": "Bitcoin Millionaire and Crypto Founder Found Dead with Gunshot Wound - Yahoo Finance", "content": "Bitcoin Millionaire and Crypto Founder Found Dead with Gunshot Wound\n\nBitcoin Millionaire and Crypto Founder Found Dead with Gunshot Wound\n\nDr. <PERSON>, a prominent figure in the crypto space and an emergency room doctor, was found dead on May 30th with a gunshot wound. He had been missing for a week after failing to show up for work at the Mercy Hospital in Cassville, Missouri. His unlocked car was discovered near the hospital with his belongings, including his wallet, passport, laptop, and work briefcase, inside. The police did not suspect any foul play in his death.\n\n<PERSON><PERSON><PERSON> was the co-founder of ONFO coin, a referral-based cryptocurrency project based on “social mining”. He was also an early adopter of Bitcoin and a vocal critic of the U.S. dollar. In 2020, Forbes called him a \"Bitcoin millionaire\" who had a passion for mathematics and blockchain technology.\n\n<PERSON><PERSON><PERSON>'s death is the latest in a series of mysterious fatalities among crypto enthusiasts and entrepreneurs. In April, <PERSON>, the creator of Cash App and former technology chief at Square, was stabbed to death in San Francisco. Last June, <PERSON><PERSON>, a controversial Bitcoin billionaire, drowned in Costa Rica. Some speculate that their crypto fortunes may be lost forever without access to their private keys."}, {"id": 41, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb21lb25lLWJvdWdodC1sb2FmLWJyZWFkLWJpdGNvaW4tMjI0NzI1Mjc4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 01 Jun 2023 07:00:00 GMT", "title": "Someone Bought a Loaf of Bread With a Bitcoin Wallet Baked Inside - Yahoo Finance", "content": "Yacht parties, tank tops, McLarens: For the last three years, the eponymous annual Bitcoin conference has taken root in Miami, celebrating the excess of crypto’s nouveau riche while showcasing a unique and particularly masculine strain of financialized tribalism.\n\nThough the conference’s latest iteration last month featured notably smaller crowds and less exuberance than in past years, it also appears to have broken new barriers for a new and very different kind of eye-popping excess. On May 18, pseudonymous conceptual artist <PERSON><PERSON><PERSON> sold a loaf of bread to a collector for 0.5 BTC, or just over $13,400 worth.\n\nThe sale was not a trick or a gag. The loaf was real, perishable country bread. It was really sold to a man, <PERSON>, who did in fact pay thousands of dollars for it.\n\nIn fairness, the loaf—made in partnership with Bitcoin NFT marketplace DIBA—was unlike other grocery store varieties, as it contained a hardware wallet that stored an undisclosed amount of BTC. But neither the artist nor <PERSON><PERSON><PERSON> plan to disclose the amount.\n\nThat may be because the actual value of \"Bit.Bread,\" as the piece is titled, has nothing to do with money itself. As with OONA’s previous works, it instead investigates the perception of value, particularly as that perception pertains to gender—an often fraught subject in the male-dominated crypto industry.\n\n“It fundamentally investigates the ways in which this technology is more progressive than the people that use it,” <PERSON><PERSON><PERSON> told Decrypt of \"Bit.Bread.\"\n\nSince 2021, <PERSON><PERSON><PERSON> has created works of performance art at crypto conferences, always sporting glasses and a draped mask that conceals her identity. Her work has focused on the treatment and perception of women in crypto, and the ways in which crypto—a technology the artist believes can financially and politically liberate and empower people of all backgrounds—has emerged within a culture that she believes does not adequately respect or include women.\n\nStory continues\n\nAt Art Basel Miami last December, OONA, with the help of performance artist <PERSON> <PERSON>, auctioned off a cup of her own breast milk—milked live in front of a large audience—before being escorted away by conference security. The piece, \"Milking the Artist,\" received a high bid of $200,000.\n\nThey're both part of a progression of performance art activations, as detailed in a recent blog post by OONA, including tokenizing and auctioning off butter that she had churned. It's a juxtaposition between the \"permanent\" and \"perishable,\" as she describes it.\n\nThat piece and \"Bit.Bread\" both riff on edible symbols of the kitchen, in an effort to interrogate the value placed on women in crypto-related environments. That value is typically minimal, and often reduced to sexualization and objectification, according to the artist.\n\nOONA (left) alongside an unidentified actor hired for the performance. Image: Alejandra Bernal\n\nWhat’s more, the degree of crypto-specific sexism that OONA has repeatedly, personally encountered has generally risen with the tide of crypto prices and speculative frenzy.\n\n“At the 2022 Avalanche Summit, I was asked what my hourly rate was at least five times, because people assumed that I was a prostitute,” OONA recounted. “Going back this year, most conversations were about my performance art.”\n\nSame thing at Bitcoin Miami: Last year, during the waning days of the bull market, OONA said that she had to repeatedly fight off men intent on trying to rip off her mask to reveal her face. This year, in the doldrums of the bear market, such an invasion of her artistic and physical privacy never once occurred.\n\nMinted by Dance: Inside Art Blocks' Latest Immersive Digital Art Collection\n\nIn that sense, \"Bit.Bread\" can be interpreted as a reflection on the relationship between speculation and gender. So far, crypto’s speculative excesses have typically encouraged a hyper-masculine subculture, one personified by the “crypto bro” stereotype.\n\nBut that doesn’t mean speculation is itself harmful, according to OONA. On the contrary, speculation is at the heart of Bitcoin’s promise as a tool and value system that she believes will hopefully, eventually, lift up people of all backgrounds.\n\n“I wanted to connect speculation around the value of women with speculation around Bitcoin,” she said, adding that both \"involve a new set of knowledge, a new way of interacting, and a new way of perceiving self-sovereignty.”\n\nEditor's note: This article was updated after publication to correct the date that the bread was sold."}, {"id": 45, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvd2hhdC1pcy1hLWJpdGNvaW4tcmFpbmJvdy1jaGFydNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 01 Jun 2023 07:00:00 GMT", "title": "What is a Bitcoin Rainbow Chart? - Nasdaq", "content": "It’s no secret that the cryptocurrency market is volatile. Since the price of Bitcoin tends to rise and fall dramatically, investors are always looking for a tool to streamline making efficient investment choices.\n\nThe Bitcoin Rainbow Chart is one of the many tools invented to create an easy visual guide to investing in this cryptocurrency. As the price of Bitcoin rises and falls, the rainbow chart is designed to help investors determine the appropriate times to buy and sell their tokens. It also allows you to compare Bitcoin’s current price to historical trends.\n\nLet’s take a closer look at the Bitcoin Rainbow Chart and how you can efficiently build your investment portfolio.\n\nKey Takeaways\n\nThe Bitcoin Rainbow Chart reflects a logarithmic growth curve.\n\nThe chart uses various colors to highlight good times to buy or sell Bitcoin.\n\nAlthough a helpful visual, investors should look beyond the Bitcoin Rainbow Chart when making investment decisions.\n\nWhat is a Bitcoin Rainbow Chart?\n\nThe Bitcoin Rainbow Chart is a tool that investors use to assess the current value of the cryptocurrency in relation to the big picture. Within the chart, you’ll see a rainbow of colors that reflects a logarithmic growth curve, which investors use as a mathematical tool to forecast the possible future price of Bitcoin.\n\nWhen looking at the rainbow chart, you’ll also see the price of Bitcoin over time. Notably, the chart’s designers didn’t want daily fluctuations in price to throw off its data. Instead, the chart ignores the noise of market movements throughout the day to focus on the overarching Bitcoin price trends.\n\nWhy is Bitcoin so Volatile?\n\nBitcoins are produced through a process called mining. Miners are nodes in Bitcoin’s network that solve complex computational math problems to earn the next “block” of Bitcoins. Mining has a dual purpose: minting new Bitcoins that enter circulation and auditing the blockchain the make sure all transactions are valid and no double-spending is occurring.\n\nBecause Bitcoin mining requires robust hardware and significant energy, it’s costly, slow, and only sometimes financially rewarding. Still, it has attracted considerable interest in recent years because of the decentralized nature of the Bitcoin blockchain and its status as a kind of modern precious metal.\n\nUnlike the gold standard or fiat currency, Bitcoin is a new and mostly untested currency. It’s not used in most people’s everyday lives and has yet to be accepted by most retailers and vendors as a legitimate form of payment.\n\nBitcoin is volatile because its price is dependent on supply and demand. The price will swing depending on how many Bitcoins are in circulation, how much people are willing to pay for the tokens, and how close the currency is to its 21 million Bitcoin limit.\n\nThis limit was purposefully put in place by Bitcoin’s inventor so miners couldn’t keep minting coins to the point of causing wild price fluctuations. Hype for Bitcoin in the media or negative press can also lead to volatile Bitcoin prices. For example, some experts argued the recent collapse of Silicon Valley Bank led to increased interest in cryptocurrency products, pushing up the price of Bitcoin.\n\nWhat happened to Bitcoin’s price in 2022?\n\n2022 was a particularly volatile year for economies worldwide. Inflation in the United States led to the Federal Reserve instituting a series of aggressive rate hikes, which made borrowing more expensive to reduce discretionary spending. As rates increased, they damaged corporate profits, and many tech companies laid off thousands of employees.\n\nThe Russian invasion of Ukraine also led to widespread economic uncertainty last year. Energy prices skyrocketed, particularly in Europe, as access to Russian energy sources became limited.\n\nBitcoin saw its price fall from over $60,000 at the end of 2021 to under $20,000 at the end of 2022. The collapse of FTX in November 2022 marked a particularly low point for Bitcoin, as investor confidence in the speculative asset seemed low.\n\nHowever, the price of Bitcoin has since rebounded and is seeing positive growth in 2023. As inflation and rate hikes ease up, consumers are starting to spend more and feel more comfortable putting money toward something as risky as Bitcoin.\n\nWho Created the Bitcoin Rainbow Chart?\n\nThe Bitcoin Rainbow Chart hit the scene when a Reddit user, Azop, introduced the chart to track the price of Bitcoin. The chart is meant to predict future market trends without the distractions of daily market volatility.\n\nHow Does the Chart Work?\n\nThe chart superimposes the price of Bitcoin onto a rainbow. Since the logarithmic growth curve intends to forecast the future price of Bitcoin, different colors are intended to highlight the prime opportunities for buying or selling it.\n\nIn general, investors use this chart as a long-term valuation tool. But the predictions made within the rainbow chart aren’t necessarily accurate. It’s simply one way to assess Bitcoin’s current price levels quickly.\n\nAt the end of each 24 hours, the chart is updated with the daily close price. With that, you’ll filter through the fluctuations within a single day to better assess the bigger picture.\n\nA Guide To The Bitcoin Rainbow Chart\n\nThe appeal of the rainbow chart is that it seems to provide clear advice on what actions you should take in your portfolio, at least according to the chart’s measurements. Here’s a breakdown of what the colors mean:\n\nRed: When the price of Bitcoin is in the red, the Bitcoin Rainbow Chart indicates a “maximum bubble territory.” With that, it’s a good time to sell Bitcoin but not to buy it.\n\nOrange: When the price of Bitcoin is in the orange, it’s in the “FOMO intensifies” stage, which means that the price is relatively high. With that, it’s a wise time to sell Bitcoin but not to buy it.\n\nYellow: When the price of Bitcoin is in the yellow, the Bitcoin Rainbow Chart indicates that the price is neither too high nor too low. With that, there’s little advantage to either buying or selling the coin.\n\nLight green: When the price of Bitcoin is in the light green, the Bitcoin Rainbow Chart indicates that the price is on the low side. With that, there is potential for growth, making it a reasonably good time to buy but not sell.\n\nGreen: When the price of Bitcoin is in the green, the Bitcoin Rainbow Chart indicates that the price is on the low side. Since it is potentially undervalued, there is some potential room for growth, making it a reasonably good time to buy but not sell.\n\nBlue-green: When the price of Bitcoin is in the blue-green, the Bitcoin Rainbow Chart indicates that the price is on the low side. With that, there is a lot of growth potential, making it a good time to stock up.\n\nIndigo: A price at the indigo level is about as good as it gets, otherwise known as a fire sale, making it a perfect time to buy Bitcoin.\n\nBy following the rainbow chart, it’s easy to see where the price of Bitcoin stands based on the color.\n\nAdvantages and Disadvantages of the Bitcoin Rainbow Chart\n\nAs with all investment tools, using the rainbow chart has pros and cons.\n\nLet’s start with the pros:\n\nReasonably good track record: Since the chart launched in 2014, it has remained relatively accurate.\n\nEasy to use: It’s easy to check in with the rainbow chart before buying or selling Bitcoin.\n\nNow for the cons:\n\nNot a foolproof result: Although the chart has been right so far, that doesn’t mean it will always be a good judge of Bitcoin’s price direction.\n\nDoesn’t look at the daily fluctuations: If you want to buy and sell Bitcoin throughout the day, this chart won’t be much help to you.\n\nShould You Rely on the Rainbow Chart?\n\nThe Bitcoin Rainbow Chart is a helpful visual to assess where the price of Bitcoin stands quickly. But long-term research has yet to back-up the chart. When dealing with highly volatile cryptocurrencies, things can change rapidly.\n\nSince past performance doesn’t necessarily indicate future results, relying on the rainbow chart won’t always translate into blockbuster investment returns. Solely relying on this chart could mislead your investment decisions if the price of Bitcoin behaves unexpectedly.\n\nThe rainbow chart might be a good place to start investment decision-making. But it’s important to follow up on your initial discovery with other information about the state of the Bitcoin market before moving forward with an investment decision.\n\nHow to Invest in Cryptocurrency\n\nMost successful cryptocurrency investors regularly follow changes in the market to guide their decisions. Since the cryptocurrency market is highly volatile, constant monitoring seems to be one path to success.\n\nHowever, only some investors interested in the cryptocurrency have the time or inclination to monitor the markets consistently. The good news is that with the spread of artificial intelligence, it’s possible to find investing software that protects your money from volatility and price swings.\n\nAt the same time, nothing in the crypto space is guaranteed, and you shouldn’t invest any money you need to survive. Multiple established crypto exchanges collapsed in 2022. Investors should be wary of any cryptocurrency that makes investors promises that seem too good to be true.\n\nThe Bottom Line\n\nThe Bitcoin Rainbow Chart is one tool available to cryptocurrency investors. If you are trying to determine the right time to buy or sell Bitcoin, this tool can give you a quick take on the situation. However, it shouldn’t be the only tool you use to determine the appropriate time to buy or sell Bitcoin.\n\nThe post What is a Bitcoin Rainbow Chart? appeared first on Due.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}]