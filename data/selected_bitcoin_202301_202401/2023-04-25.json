[{"id": 31, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9iaXRjb2luLXRvcC0xMC1jcnlwdG9zLWZhbGwtMTEzNzU4OTA0Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Apr 2023 07:00:00 GMT", "title": "Bitcoin, all top 10 cryptos fall; Asian equity markets drop on rising Covid fears - Yahoo Finance", "content": "Bitcoin fell in Tuesday afternoon trade in Asia, along with all other top 10 non-stablecoin cryptocurrencies. Polygon was the biggest loser followed by Solana. Most Asian markets dropped amid concerns of rising Covid cases in China ahead of its Labour Day holiday, the first long public holiday since the Lunar New Year. Most U.S. stock futures traded in the red while European bourses extended losses.\n\nSee related article: Cryptocurrencies trade lower as weekly losses pile up\n\nFast facts"}, {"id": 35, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9iaXRjb2luLXRvcC0xMC1jcnlwdG9zLWZhbGwtMTEzNzU4OTA0Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Apr 2023 07:00:00 GMT", "title": "Bitcoin, all top 10 cryptos fall; Asian equity markets drop on rising Covid fears - Yahoo Finance", "content": "Bitcoin fell in Tuesday afternoon trade in Asia, along with all other top 10 non-stablecoin cryptocurrencies. Polygon was the biggest loser followed by Solana. Most Asian markets dropped amid concerns of rising Covid cases in China ahead of its Labour Day holiday, the first long public holiday since the Lunar New Year. Most U.S. stock futures traded in the red while European bourses extended losses.\n\nSee related article: Cryptocurrencies trade lower as weekly losses pile up\n\nFast facts"}, {"id": 28, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vb3Bpbmlvbi9hcnRpY2xlcy8yMDIzLTA0LTI2L2JpdGNvaW4tY3J5cHRvLWJyb3MtaGFpbGluZy1ldS1yZWQtdGFwZS1hcmUtaW4tZm9yLWEtbmFzdHktc3VycHJpc2XSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Apr 2023 07:00:00 GMT", "title": "Crypto Bros Hailing EU Red Tape Are In for a Nasty Surprise - Bloomberg", "content": "On April 20, the day that the European Parliament approved a sweeping package of cryptocurrency rules known as MiCA, a new euro-denominated stablecoin was unveiled on the continent. “CoinVertible” seeks to bridge the gap between traditional and digital finance, is available only to institutional investors, and is designed not by hoodie-wearing techies but French banking giant Societe Generale SA.\n\nThe sight of regulated banks dabbling in blockchain quickly generated online eye-rolling from crypto bros, who mocked the token’s heavy-handed compliance procedures. CoinVertible’s pitch is to offer legal certainty, collateral transparency and interoperability with TradFi — all in the spirit of MiCA — but only for clients who are approved using SocGen’s existing know-your-customer procedures."}, {"id": 34, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vb3Bpbmlvbi9hcnRpY2xlcy8yMDIzLTA0LTI2L2JpdGNvaW4tY3J5cHRvLWJyb3MtaGFpbGluZy1ldS1yZWQtdGFwZS1hcmUtaW4tZm9yLWEtbmFzdHktc3VycHJpc2XSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Apr 2023 07:00:00 GMT", "title": "Crypto Bros Hailing EU Red Tape Are In for a Nasty Surprise - Bloomberg", "content": "On April 20, the day that the European Parliament approved a sweeping package of cryptocurrency rules known as MiCA, a new euro-denominated stablecoin was unveiled on the continent. “CoinVertible” seeks to bridge the gap between traditional and digital finance, is available only to institutional investors, and is designed not by hoodie-wearing techies but French banking giant Societe Generale SA.\n\nThe sight of regulated banks dabbling in blockchain quickly generated online eye-rolling from crypto bros, who mocked the token’s heavy-handed compliance procedures. CoinVertible’s pitch is to offer legal certainty, collateral transparency and interoperability with TradFi — all in the spirit of MiCA — but only for clients who are approved using SocGen’s existing know-your-customer procedures."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiXmh0dHBzOi8vd3d3LnRoZXZlcmdlLmNvbS8yMzY5NjQ2Ni9iaXRjb2luLWNyeXB0by1saWdodG5pbmctbmV0d29yay1saWdodHNwYXJrLWNlby1kYXZpZC1tYXJjdXPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Apr 2023 07:00:00 GMT", "title": "Bitcoin is still the future of payments, says Lightspark CEO <PERSON> - The Verge", "content": "We’ve got a special episode with <PERSON>, deputy editor at The Verge and a familiar host for Decoder listeners, and <PERSON>, the CEO of Lightspark. That’s a company that just launched a service to make fast transactions using Bitcoin on something called the Lightning Network. <PERSON> was previously at PayPal, and then he led Meta’s big payments effort that went nowhere, but he’s got a lot to say about where crypto and payments are right now.\n\nThis transcript has been lightly edited for clarity.\n\n<PERSON><PERSON>: <PERSON>, welcome.\n\n<PERSON>: Hey.\n\nNP: It’s good to have you back. Why did Lightspark catch your eye? What did you find interesting about this little startup?\n\nAH: Yeah, I don’t cover crypto all the time, but I do pay attention to what <PERSON> is doing. He is a Bitcoin OG and has been in the space for a long time. And he had quite a journey trying to launch Facebook’s cryptocurrency.\n\nNP: <PERSON> was the president of PayPal, and then we should talk about what happened at Facebook just for a second. Facebook wanted to launch a new cryptocurrency called Libra, was going to do it with governments around the world and different payment services, and it collapsed under the weight of its political ambitions. And now, <PERSON> is back with a much leaner, simpler startup. Did you talk about the difference between those two ideas?\n\nAH: We did. Libra, for people who don’t remember, it feels like a blur now, this was in 2019, Facebook was going to do this stablecoin that was <PERSON>’ brainchild, really, and he led it inside the company. And it met a lot of resistance for several reasons, but mainly, I think when you talk to people who worked on it, it was because Facebook was doing it, but there were also concerns just about a private company of that size creating a currency, and it was based on a basket of fiat currencies rather than just one pure cryptocurrency.\n\nThis gets kind of in the weeds, but I think the distinction is important because <PERSON> has essentially decided that, for his next company — and interestingly, he took a lot of the founding team that made Libra with him at Facebook to Lightspark with him — that it’s going to be simpler for him to just build on top of Bitcoin. He chose that instead of all the other cryptocurrencies, layer one networks out there, ethereum, etc.\n\nAnd I think David, out of pretty much anyone in the industry, knows the most about the regulatory environment around crypto because, when he was doing Libra, he testified in front of Congress. I remember there was a photo of [Mark] Zuckerberg’s face on the dollar bill, with Zuck Bucks displayed behind him at one point. He had to meet regulators all around the world. They tried incorporating Libra in several different countries, including Switzerland. This is a guy who knows the global regulatory situation around crypto better than anyone. So the fact that he decided to make a company that he told me he hopes to be his life’s work around Bitcoin, specifically, I thought was interesting.\n\nNP: Yeah, that’s actually a really interesting part to me. We lived through what I would call crypto summer last year, where the hype was off the charts. There was a lot of conversation about ethereum, these other coins. I think that has all fallen by the wayside. It’s interesting to me that, out of all that wreckage, Dave Marcus is back, and he’s saying Bitcoin is it, and it’s still viable, and he’s managed to raise a bunch of money with that pitch.\n\nAH: Yeah, he’s raised a lot of money and the company’s pretty lean, but I think his goal is to scale Bitcoin as a payments network. He’s a payments guy through and through. That’s what he was doing at PayPal. He led the acquisition of Braintree and Venmo when he was there — and what he was trying to do at Facebook when he was working on Messenger before Libra even had to do with payments. So this has really been his life’s work, and he sees the opportunity thanks to this thing called a layer two network, which is this network that sits on top of Bitcoin, called Lightning, to let people actually transact using Bitcoin.\n\nAnd the most interesting part of all this is that he thinks people don’t even need to know that Bitcoin is the network they’re using to transact with because Lightning is that fast, and he actually sees companies using it to settle back into fiat on both sides of the transaction, so legal tender in real time. And I think that’s really interesting because he’s basically saying, with this network, we can obfuscate crypto out of the equation for the end consumer, and they don’t even need to know that they’re using it necessarily.\n\nNP: That’s fascinating. Did you ask him the only question I ever want to ask anybody about Bitcoin, which is, if I have a Bitcoin, why would I ever spend it?\n\nAH: Well, that gets to the whole reason he’s doing the company, which is he agrees that Bitcoin is, in its current state, a bad thing to transact with. It fluctuates. The idea with Lightning and the company he’s building, which is basically Bitcoin payments in a box for companies, is that it’s happening so fast, and it’s using microtransactions, so micro fractions of Bitcoins, called Satoshis, with this Lightning network, of course, that you don’t even know that Bitcoin is being transacted, and it happens so fast that the price fluctuations don’t matter.\n\nHe’s a really smart guy. He thinks very thoughtfully about this stuff. He’s a long-term thinker. He doesn’t really buy into the hype waves of the different crypto summers we’ve had. He’s been through many crypto winters. He thinks this one, interestingly, will be a very long and painful one, but he’s choosing to start a company in the space anyway, really in the middle of it. And he thinks he has the best bet at doing something in a space that’s not going to get gunned down by regulators like Libra was. And also just hearing him reflect on going from a big company, Facebook, trying to lead a project, basically a startup within that company, which is what Libra was, to actually just running his own startup now and what he enjoys about that and what the tradeoffs are, I thought was really interesting.\n\nNP: That’s pure Decoder bait. All right. I’m excited for this one. It’s a good conversation. To the moon, Alex Heath with Lightspark CEO David Marcus. Here we go.\n\nDavid Marcus, you’re the CEO and co-founder of Lightspark. Welcome to Decoder.\n\nThank you for having me.\n\nI first wanted to just table set and talk about the crypto industry more broadly. I think it’s safe to say we’re in a crypto winter, thanks in large part to the collapse of FTX. You’ve got rising interest rates — a lot of factors in there. I was reading your end-of-2022 predictions post, and you predicted that we won’t exit this crypto winter this year and probably not in 2024, either. And you wrote that “A lot of value, trust and stability was destroyed in 2022.” You’ve been doing this for a really long time, and you’ve lived through multiple crypto winters. I’m not sure how many. Do you know how many?\n\nAt least three, if not four.\n\nWhat’s different about this one in your mind versus the others?\n\nI think that this one is different because the industry got a lot bigger. So the downturn affected many more people, touched more consumers, more companies, more investors. There was a ton more capital deployed in this cycle versus the previous cycles. So it’s just bigger.\n\nAnd when you think about the opportunity still for crypto, what is it? Especially when you have this much disillusionment, so many retail investors were touched by the collapse of FTX. What is the long-term potential for the market?\n\nWell, look, I think that everything that’s speculative ends up in booms and busts cycles. It’s only if you build something valuable that solves real-world problems that you can actually create long-standing value. And so I think we’re starting on that journey now. There are a bunch of companies that built things that solve real-world problems, exchanges, wallets, and many more. But there was so much speculation and teams that were just building something just for the sake of listing a token and making a quick buck. And I feel like this era is coming to an end if it hasn’t ended already. And now teams are actually focusing on building valuable things.\n\nYou left Facebook in December of 2021, which we can get more into later. When did you decide that your new company was going to be Bitcoin focused? Because what you were doing at Facebook was a stablecoin. Why Bitcoin?\n\nListen to Decoder, a show hosted by The Verge’s Nilay Patel about big ideas — and other problems. Subscribe here!\n\nSo I had this idea in the back of my mind, but I wasn’t sure yet that I was going to just take the plunge and go with it. Many friends told me, “You should take at least nine months off.” And after month three, I was really going crazy, so it was time for me to get back on the horse. But why Bitcoin? Because I’ve come to the conclusion that if you want a true open, interoperable protocol for money on the internet, which is what we’re trying to help build here, it has to be built on something that literally no one controls.\n\nBitcoin is the only network and asset that fits the bill when you think about it that way. There’s no consortium of companies. There’s no people at the helm. There’s no leader. There’s Satoshi Nakamoto, but who knows who they or he or she is? And that’s kind of all of the value of the network.\n\nIt’s also the most robust network out there and battle-tested network out there that has been around for over 13 years and has resisted all possible kinds of attacks and issues. So it is, in our mind, the only network that can support a truly open interoperable protocol for money on the internet.\n\nWhat you just said — open, interoperable protocol — sounds similar to what you were trying to do with Libra, but I know what you’re doing with Lightspark is different. Why not Bitcoin as the basis for what Libra was? Why go from the synthetic stablecoin approach to Bitcoin?\n\nSo we actually explored Lightning in the early days of Libra, and we spent time looking at it, and we had a lot of back and forth, and Lightning wasn’t mature enough at the time. We’re talking early 2018. And the problem with Bitcoin as a pure play transactional asset, if you don’t have a real-time settlement network to move money in real time is the volatility of Bitcoin itself. And so it makes it a poor medium of exchange. Not everywhere, because you have a number of countries where the volatility of Bitcoin is actually better than the alternative of hyperinflation of their own home currency. At that time, we felt like we needed to try to bring something to the market that had really stable value so that it could become a great medium of exchange. And if we wanted that at the time, we had to go build something proprietary to actually support it because there was no blockchain that would do at-scale, real-time settlement for potentially hundreds of million if not billions of people.\n\nWhat is Lightning for people who are listening and aren’t quite sure? Maybe they’ve heard of it in passing but aren’t sure.\n\nSo Lightning is a Layer 2 payments protocol built on top of Bitcoin, and the concept is basically one of a channel-based payment network. And so you open payment channels between people or entities that want to transact with one another, and you basically move beads, for the lack of a better term, from one side to another. And you can do that in real time because those channels are very fast. And then there’s a network. So let’s say that you and I have a payment channel open and we want to pay someone else and you have a channel open with that third person, but I don’t. I can go through you to actually pay that third person.\n\nSo it’s a pretty unusual setup, but it’s not unfamiliar. Networking on the internet looks fairly similar in the sense that you have routers on the internet that route packets. You don’t have to be connected directly with anyone and everyone. You can move those packets through third parties, et cetera. So a small unit of Bitcoin on top of Lightning for us is a little bit like a TCP/IP packet for money.\n\nAnd Lightning, importantly, like Bitcoin, is not controlled by a centralized entity?\n\nCorrect. It runs on top of Bitcoin, so it’s completely decentralized. It brings a lot of capabilities to Bitcoin when it comes to payments because it’s near real time. So it’s really, really fast. It’s very cheap, and it’s almost infinitely scalable. And it’s also private, which Bitcoin Layer 1 isn’t. It basically has all of the attributes of a great open payment network.\n\nIs there any concern with the fact that the protocol is private in that it’s not totally visible on chain like Bitcoin in terms of the decentralization of it over time?\n\nNo, because you can see the topography of the network. The only thing you can’t see is actually who’s paying whom, which is a feature when it comes to payments.\n\nIf you’re a merchant, you don’t want your competitors to know exactly how much you’re doing in sales any given day or month. If you’re a consumer, you don’t want people to know exactly who you’re transacting with at all times. And so privacy is a feature of all existing payment networks and the same here with Lightning.\n\nSo you are thinking about Lightning even while you’re at Facebook in the early iterations of coming up with Libra. You leave Facebook at the end of 2021, and you have friends telling you you should take some time, you should not just jump right in. You did anyway. You jumped in. What was the impetus to jump in and go, even after everything you went through at Facebook and having to speak in front of Congress, all the scrutiny — what made you go, “I’m ready to do another company”?\n\nYeah, that’s a good question. I feel like I had a deep sense of unfinished business, and I think all of us, all of my co-founders here, feel exactly the same way because the fact that we’re in 2023 and that money moves the same way it moved in the late ’60s and ’70s on top of completely archaic, outdated systems is borderline infuriating to me. It’s just not right. It’s an anomaly in the world. Money should move the same way anything else on the internet moves. You should be able to send money the way you send an email or a text message. We failed trying to do that when we tried the Libra / DM journey at Facebook. And in that time period, everyone else that tried also failed. And so we still felt that massive amount of fire and passion to actually go fix that for the world because we feel it’s just not right.\n\nAnd what I think is also unique about Lightspark and the founding team that you have with you is it’s pretty much everyone who worked on Libra with you, almost, at least at the senior level, it seems. What was it like convincing everyone who just went through this with you?\n\nWell, it’s not everyone, but —\n\nA lot of people.\n\nBut look, I feel like we shared that same passion for seeing this through. And “this” is actually really trying to help the world with an open, interoperable, dirt-cheap, real-time settlement payment protocol. And so when we started hanging out again, we all felt the same feeling, the same fire to actually go and do this. So it wasn’t actually a complicated path to actually form that co-founding team that we have. And everyone felt like we still needed to try this. And as far as I’m concerned personally, I’ve now absolutely decided, and I have the purest possible version of clarity on this, that this is going to be my life’s thing. I’m just not going to stop until I get it done. This is it.\n\nLet’s get into Lightspark, then. So you recognize Bitcoin and Lightning are what you want to build on. Where is the opportunity to make a company in that space, and what is Lightspark?\n\nThe one downside currently with Lightning is actually that it’s very complicated to fully understand. And the barrier of entry as far as just the learning curve is so incredibly high that it dissuades a lot of people to actually use that network. Additionally, operationally, it’s actually very complex to operationalize Lightning because spinning up a node is complicated, but then you need to open channels with other peers on the network to have a well-connected node. And when you do that, when payments actually get routed through your nodes, you need to rebalance the liquidity that is actually locked up in payment channels. So this concept of payment channels is a little bit foreign for everyone who wants to actually send and receive money across the world 24/7 in real time.\n\nThe first step is really simplifying all of that, really removing all of the complexity and building an enterprise-grade entry point to Lightning so that developers and platforms around the world can actually tap into all the benefits of a real-time global payment network.\n\nSo that’s what we’re doing, and that’s what we’re launching this week. It’s really the easiest, fastest way for anyone to actually onboard the Lightning network and send and receive payments in no time. No operational overhead, no high liquidity requirements, none of that. We’ve taken something that’s fairly complex but great when you know how to use it into something that’s actually really simple, reliable, and enterprise-grade. And the business model for us is actually quite simple, which is we’re providing all these services, and we’re taking a small cut of the transactions that actually go through our stack. And it’s not dissimilar from AWS or any SaaS cloud-based business which brings together software that makes something that’s complicated, simple and accessible and charges a small fee for it.\n\nWhat is that fee relative to — I know there’s not really competition when it comes to Lightning, but in terms of similar types of companies that exist on other payment rails?\n\nWell, so we have a tiered payment plan, but at scale, we’ll basically collect about 15 basis points with no minimum fixed fee. So at a $100 transaction level, that’s about half of the cheapest payment systems that typically settle not in real time. Take ACH, for instance, which settles in three days. It’s considered to be really cheap. And that’s about half of that for real-time secure payments.\n\nWho do you see the customer being for this, maybe now, but also, I think probably more importantly, as you think about company building in the next several years? Who do you see being the companies wanting to plug into this network?\n\nWe’re excited and optimistic about this concept of streaming money.\n\nSo right now, we’re really targeting this toward developers, wallets, exchanges, OTC players. But the developer’s piece is really the most intriguing to us because we feel that if you empower developers with great developer tools and you give them the ability to send and receive money and build products and services that can move value around the world on the internet in real time at a really low cost, that you can build crazy new experiences. So a good example of that, of something that we’re actually pretty excited about and optimistic about, is this concept of streaming money.\n\nAnd let me just take a minute to explain what streaming money is. Because for the last two decades, we’ve been talking about micropayments as a thing, and it never became a thing. And the reason it never became a thing is that the current rails and the way that the pricing is set for payments cannot actually support that use case. The attempts that have typically been made require you as a consumer to preload a wallet that is actually not interoperable with other properties or other publishers and then start spending gradually as you go. So you don’t know as a consumer that you want to put $20 in one publisher’s wallet and you can’t use that balance anywhere else. It’s not really a micropayment — it’s a commitment.\n\nAnd then the way that the pricing is structured has a minimum fee. So if you wanted to do a 2 cents or 5 cents transaction, the actual cost of the transaction would far exceed the actual amount you’re trying to send. And so streaming money is this concept that you can actually send a cent or a fraction of a cent every second if you want on the network, between say a consumer wallet and a creator wallet. So let’s say that now this podcast would become a premium podcast where the first 10 minutes are free, but then you have dynamic pricing and the most interesting parts are maybe more expensive, and consumers could actually spend cents every second for each segment or every five seconds or every 10 seconds, and that would work globally.\n\nIt’s a concept that we think is very exciting because it could create new monetization opportunities for creators; it could create just new payment models as you go. And we’re very excited about what developers are going to build once you remove all of the constraints of all of these legacy payment rails that are around.\n\nThis is naturally leading us into company process questions. How do you go about selling this product? Do you build a sales team in-house to go out and knock on people’s doors, so to speak? Do you just wait and hope that developers see this and flock to it? You’re starting the company from scratch. How do you think about that?\n\nWell, eventually, we’ll build a sales team, but right now, the sales team is a sales team of internal people, me included, and we’re reaching out to companies we know well. And I think we have a pretty good sense of the real problems that those companies are facing and how we can help them address them and how we can help them build new products that would make them more competitive in pretty competitive landscapes. So I think, at first, it’s going to be all internal. The company is small — it’s a startup, which feels amazing.\n\nHow many employees?\n\nTwenty-six. So it’s just perfect. And so we’re going to keep it as small for as long as we possibly can. So we’re very principled in how we hire, and we just don’t want to ever get to this place where we’re bloated. So we just want to take it easy with hiring. But that being said, we’re going to launch this product, we’re going to get a number of developers and clients on the platform, and then we have a pretty busy road map ahead where we’re going to continue iterating on the product and build more capabilities and features in whatever’s left of this year.\n\nHow much money have you raised?\n\nSo we don’t really talk about that. It was a fairly substantial round, but we feel like the-\n\nThe reporting I saw was $175 million.\n\nYeah…\n\nSo ballpark, correct? The reason I ask is because I think it speaks to the longevity of what you’re hoping to create and also just the resilience of the company itself.\n\nI think for me, the important thing was how can I structure this in such a way that we can focus on the work and only the work for as long as possible with no or minimal overhead? And part of the fundraising strategy was that — I actually don’t want to be in a world where we’re fundraising every other year because that takes three months out of the year for the senior leadership team. And so I feel like this is a competitive advantage that we can actually focus on the core product and building rather than being distracted.\n\nThe downside of it, though, is if you’re not very cognizant of the fact that you have a lot of resources and you should try to box that, then you become bloated and fat prematurely, and you can’t ship anything. It’s like this notion that nothing great has ever been shipped by a large team, and I really believe in that. We’re trying to act and behave as if we needed to raise at the end of this year, despite the fact that we don’t.\n\nMaybe if we could dive into that a little more — how do you think about using capital now as a startup founder versus when you worked at a large company with a ton of resources before? You have a lot of resources relative to most companies your size and just personal connections. You mentioned, “We want to operate as though we may need to raise at some point. We don’t want to just think that we have unlimited resources.” How do you, as a CEO, manage that day-to-day with the team and manage expectations?\n\nIt’s culture — it’s 100 percent culture. And at this scale, culture setting is really not hard because it’s 26 people. We’re all thinking about things the same way. And a good measure of a good culture is when you add someone to the team and they feel they’ve always been here two weeks in. And so far, 100 percent of people who joined are exactly that. So we feel pretty good about our culture. And whenever we want to add a software engineer to the team or we want to add anyone on the team, there’s a lot of healthy debate. Do we really need that additional role? Do we really need that additional role for more than a certain period of time? Is it a permanent function that we need someone to actually look after or not?\n\nAnd I feel like we’ve got the right culture. At the time we talk now, we’re launching a product in a few days, and everyone’s been working around the clock over the weekend because we’re pushing something to production for the first time. And it’s just such a great feeling to see people being so hardcore and driven without you having to just ask. It’s just natural. So culture is everything at this scale.\n\nAnd for you as a manager, what’s been the biggest surprise from going from Big Tech — you were president at PayPal before — to a small company? What’s been the most surprising and also maybe the most rewarding in that shift?\n\nSo first of all, I don’t see myself as a manager. It feels very “large company” to talk about it that way. But also, remember that I did startups for all of my life before this decade in large companies. So it feels right at home, it feels very natural for me, and it feels like something that’s really in my DNA. It feels just right. So I have no surprises whatsoever, except that I didn’t know whether I was going to enjoy the hustle of building something from scratch again with the same level of fulfillment and happiness. And I’m actually surprised on the other side of it. I’ve just never felt this great. It’s amazing to be with such talented people on our team doing something really meaningful and no overhead.\n\nTalk about then, I know you’re only 26 people, but how have you structured the company and the people you brought on, and are you a functional organization? How do you think about scaling the company organizationally?\n\nWell, right now, we’re pretty flat. So there’s not a lot of structure by design, and we have very, very senior leaders. So it’s not like we need to spend a ton of time talking about how to structure the team. An interesting thing that I’ve done and tried in the past that works really well is that when I look at the engineering team, the responsibilities are actually split in two. So we have James Everingham, who’s done so many things in our industry for so many years, and he’s really focused on the management piece of the engineering team, the process or lack of process, so to speak. So he’s really acting in a way that enables every single engineer to do their best work and not have overhead.\n\nAnd then Kevin Hurley, our CTO, is really deep into tech in the engine room with all of the engineers. So it’s a split structure — on one side, more day-to-day management process stuff and, on the other, really deep into the technical weeds of how do we actually make this protocol become the winning protocol for money on the internet.\n\nAnd are you spending most of your time on product, or is it —\n\nI’m doing everything. So it’s product, it’s sales, BD partnerships, everything. It’s great.\n\nThis idea of transacting with Bitcoin is a fascinating one to me, and it’s one that the industry has talked about for years, and obviously, we’re talking about it with Lightning. I think the inherent tension in this is that many Bitcoin buyers really still see Bitcoin as an investment, even though you’ve got people like Gensler out there saying, “It’s the only crypto I would consider a commodity. Doesn’t really pass the Howey Test,” which maybe that’s debatable —\n\nIt’s not, it’s really not.\n\nIt’s not, okay. But when you have people out there making still $1 million coin price predictions, it seems like that’s a very hard mindset to change for people to think of Bitcoin as more than just a thing you hold on to — the digital gold analogy. So can you unpack that from your perspective and how you think that will shift because you’re making a huge bet that it will?\n\n“Dirt-cheap transactions, real-time settlement, infinite scalability almost.”\n\nSo let’s step back for a minute. First of all, it can’t be a really great way to transact if every transaction takes 10-, 20-plus minutes to clear. And every transaction is actually more expensive than the existing alternatives. So that doesn’t work. So in comes Lightning, really dirt-cheap transactions, real-time settlement, infinite scalability almost. So that solves that problem.\n\nAnd now, when you think about the behavior of people and the way that they think about Bitcoin and transacting with Bitcoin, you talked about the fact that people think of it as gold. But if you go back in history, the way that this happened is basically people were transacting with precious metals and the likes, and then they realized it was actually pretty inconvenient to actually move around with heavy gold to pay for stuff. And so they came up with bank notes.\n\nSo bank notes were basically just a paper that said you had the gold stashed somewhere. And that was true until 1972 when we stopped the gold-pegged dollar. I see Bitcoin to be exactly that, which is today you’re actually not paying with Bitcoin because, to your point, it’s volatile. It moves in price, et cetera, just like gold was and still is, right?\n\nBut if you can actually use fractions of Bitcoin on top of Lightning, in real time, and you have liquidity with all of the other fiat currencies that exist in the world, you can build products that are actually denominated in whatever currency you want. And so the underlying transaction and settlement asset is Bitcoin, but what consumers or merchants are exposed to can be whatever currency you want because you just get in and out of the network the same way that when you send an image, it gets broken into zeros and ones and sent over a TCP/IP network on the internet. You could break down value with small amounts of Bitcoin transacting and moving on the Lightning network in real time.\n\nSo we see it as a core infrastructure to move value around. And then our hope is that developers and other players will build services on top of this stack that are denominated in whatever currency they choose.\n\nSo the idea is that the end consumer doesn’t even necessarily know that this was settled through the Lightning network or Bitcoin.\n\nCorrect.\n\nIt’s all in fiat for the consumer.\n\nIt could be.\n\nDo you see the world trending in denominations of Satoshis, or Sats, which are the smallest units of Bitcoins or staying in fiat, which is legal tender like dollars? Or do you think that’s more of a sector-specific thing?\n\nI think there might be really good use cases for native Sats-denominated payments, and so if you spend time on Nostr, which is this decentralized social network running on relays.\n\nJack Dorsey is a huge proponent of it. It integrates with Lightning.\n\nYeah. It integrates with Lightning, and it’s fascinating to see the velocity of payments for Zaps or small payments for tipping essentially for posts. And you see the growth of the volume of payments on the network with very, very tiny amounts. And I think when it comes to publishing or when it comes to content monetization or creator monetization, I think you could see models exist on top of this network that are going to be Sats-denominated because it’s kind of the native purest form of payment on the settlement network. And so again, we’ll see what developers build now that the bearer of entry in terms of complexity and operational overhead has been completely eliminated.\n\nWhat is the number one concern you hear from a potential customer you’re talking to about building on Lightning, building with you?\n\nYeah, that’s a great question. We spent a lot of time engaging with all kinds of potential future customers, and they fall into two categories. One is they have looked into Lightning because, on paper, it looks amazing. And then they’ve spent cycles to actually try to get a well-connected node and try to get transactions moving on the network. And then they threw in the towel because it was too operationally intensive and complicated, and they had to have full-time people actually managing their node. So that’s one category.\n\nThe other category is actually, “Oh, I already am on the Lightning Network, but I have all of these same problems. Wouldn’t it be nice to actually not have to deal with all of that stuff so we can focus on our core business, which is definitely not managing a node on the Lightning network?”\n\nSo it’s a little bit like if, in 2023, you wanted to launch a website where you would have to actually go buy a server hardware and a router and configure the whole thing and put it in a rack and do all of these things, no one would do it. And so the analogy is basically that now it’s going to be as simple as just going to a hosting website and launching your website with “what-you-see-is-what-you-get” kind of tools. So that’s basically the analogy. We’re moving from that rack, a server, connected to a router era of the Lightning network to, “I go to lightspark.com, I open an account, and I’m up and running in minutes.”\n\nI thought you were going to say maybe a regulatory concern about using or settling with Bitcoin at all. Does that not really come up?\n\n“Out of all of the networks and assets out there, Bitcoin has the greatest regulatory clarity.”\n\nNo. That’s one of the advantages also of building on top of Bitcoin, which is that out of all of the networks and assets out there, it has the greatest regulatory clarity. And you see it because you see mainstream financial institutions offering Bitcoin products. Fidelity is now offering a 401(k) denominated in Bitcoin and the ability for any Fidelity customer to buy and sell Bitcoin. You can see mainstream financial services companies, well-established brands, carry Bitcoin products for their customers. And you don’t see that with other assets and other networks. And that’s because there is more regulatory clarity than with any other assets or network.\n\nDo you think there’s enough, though? Regulatory clarity?\n\nLook, if you ask me about the state of the US regulatory clarity for crypto in general, of course we don’t have enough regulatory clarity. I’ve been very vocal about that as well, which is if you want to reap the benefits of this revolution — because it is a revolution that will actually benefit consumers and create the next generation of leading companies — and if you want all of that to happen in the US, you need to have clear laws and regulations that, on one hand, protect consumers but, on the other, really incentivizes innovation to stay onshore in the US rather than just escape and go to The Bahamas or wherever you want to go.\n\nSo I think it’s essential. I’m optimistic that we’re moving in the right direction. I think right now, you can see the House Financial Services Committee actually moving in a direction of trying to bring more clarity for digital assets in general. So I think we’ll get to a good place, but it’s been, I know, very frustrating for many, many players out there to not have a clear set of rules that they can operate around.\n\nDo you have any sense of optimism, though, just in seeing what the tenor of the conversation was like when you were at Facebook and you were speaking before Congress in DC a lot, versus now in the progression? It seems like progression has been made, but maybe only on the edges in terms of —\n\nNo, I don’t think we’ve made progress.\n\nYou don’t think there’s any progress?\n\nI don’t think we made a lot of progress. I think people got more educated about the risks and opportunities I think. I feel like the education is slanted toward the risks more than the opportunities, which is kind of unfortunate. But no, we haven’t made that much progress. And it’s a problem. It’s a real problem.\n\nWhat do you think is holding that back? Why are politicians so hawkish on this?\n\nI think it’s a combination of different political undercurrents. But at the end of the day, I think that the surprising part is that if you’re an elected official, you need to look after the interest of your constituents. So on one hand, it’s normal when you see FTX blow up and all these things to try to say, “Okay, this is actually really not okay. We need to protect investors, and we need to protect consumers from bad actors, the same way that we’ve protected consumers from Wall Street bad actors back in the day. And now we have a clear set of rules, and we’re in a good place.”\n\nI think that the problem is actually when you don’t look at the opportunities to lower the costs for all kinds of financial services and payments on behalf of your constituents and, therefore, basically just stay very aligned with the status quo when it comes to incumbents controlling everything and basically pricing things way too high. And so with payments, notably, we’re in a world that looks exactly like pre-internet era with telecoms, where you used to spend a dollar a minute for an international call and you used to pay 25 cents for text messages. We’re exactly in that world and we’re in 2023. And there’s no open competition. So it’s a problem.\n\nHow does progress get made?\n\nWell, first I think that we could have done without all of the bad actors giving a terrible reputation to the entire industry by their reckless behavior. And I think that the problem is also as an industry, we have to start building things that solve real-world problems for a lot of people, and not build speculative products that are made to get rich really quickly, which obviously is just not the way to build an industry.\n\nAnd I think that the fact that we’re in a bear market in a crypto winter is actually good in that way. And you see a lot more energy coming back to Bitcoin when it comes to developer energy with Ordinals and all kinds of other developments. And that wouldn’t have happened last year because people would’ve wanted to issue a token and make money out of this thing. And now actually building on top of Bitcoin, you’re not making money with a new token, you’re going to actually make money because you build something of value, and the underlying asset is Bitcoin, and you can’t actually jumpstart the whole value creation. You have to go with it. So I think all of these things are going to help change the temperature and get us to a good place, but I still think it’s going to take at least a couple of years.\n\nWhat is the biggest risk to Bitcoin as a currency, as a network in your mind right now?\n\nWell, I think public opinion is just generally a thing. And when you look at the narrative around energy consumption and all of these things around Bitcoin, I feel like there’s a lot of posturing and not a lot of understanding of how energy generation, storage, and usage actually works. So I think that’s one angle where I feel like the narrative doesn’t match the reality of energy companies and utility companies and the fact that Bitcoin actually accelerates the adoption of renewables, because you can actually build a plant without waiting to have a 10-year lead time to connect it to the grid and have Bitcoin miners use the energy early on, and then amortize basically your investment as a renewable energy company until you can connect to the grid. That’s one example. No one talks about that.\n\nAnd I think, of course, the network, you could have said a decade ago that this was actually at risk of being hacked or risk of vulnerabilities, et cetera. But this network has proven to be incredibly resilient over 13 years. So I feel really good about that. It’s more of a general trend of the underlying crypto industry that is problematic for the whole industry, rather than Bitcoin itself.\n\nMaybe we could end it here just on a big-picture thing about Bitcoin itself. We’ve talked a lot about why it could work as a settlement layer, some of the concerns around regulation. For the average, everyday person who has most of their money in their bank in USD or whatever, and they think of Bitcoin as this thing that you can use on gambling websites or whatever, it’s this thing still on the peripheral. What do you say to them? Why is Bitcoin something that they should even be thinking about?\n\nI find that it’s really hard to explain the reality of the majority of people living on Earth to people here in America because we have it all, right? We have a stable currency. We have a banking system that, for the most part, we can actually feel pretty confident about. We have decently efficient payment systems. It’s not like we go buy a coffee and we tell ourselves, “Oh man, I wish that there was an easier way to pay for this thing.” But that’s a myopic view of what the rest of the world experiences. In a lot of other countries, people have hyperinflation at insane rates, which forces them to actually spend everything they have the minute they get their paycheck because actually, a bottle of water would retain more value than the bank notes in their pocket over the course of a couple of days even.\n\n“The fact that you have a form of value that is not controlled by any group of people is lifesaving, literally.”\n\nAnd they can’t trust their banking system. So in those places, actually, the fact that you have a form of value that is not controlled by any group of people is lifesaving, literally. And so I think we need to put that in perspective.\n\nBut I think that for us here in most Western countries and other parts of the world that have stable currencies, I think the reality is when we still want to move money around, it’s still complicated. When I was running PayPal, we acquired Braintree and Venmo as a package deal. That was maybe 10 years ago, and you still can’t move money around between Venmo and PayPal, and they’re part of the same company. It’s a little bit like you were trapped in Gmail and you could never send an email between Gmail and whatever other domain or Yahoo Mail or whatever it is. And so that’s the state of payments today.\n\nSo when I think about countries like ours and other parts of the world, I think about all of the innovation that can come from a truly interoperable, really cheap payment protocol on the internet. We talked about streaming money. We talked about moving money around the world in a more seamless way. We talked about people potentially getting their money way sooner and, as such, really having more opportunities to spend the money they earn.\n\nSo there’s a lot of innovation that will be unlocked. When you think about my analogy with telecoms, it’s not like the internet changed the way that people talk on the phone. You still talk on the phone, but instead of paying a dollar a minute, it’s now free. And now if you want to do a group video chat with everyone in high definition, you can. And it’s amazing. And that is net new. And so I think that when you think about it, a brand-new fully interoperable open protocol for payments on the internet, I think that we’ll see a lot of innovation around how value moves around the world that will benefit everyone, not only people who are as lucky as we are here in the US.\n\nAll right, David, I think we’ll leave it there. Thanks for being on Decoder.\n\nThank you for having me."}]