[{"id": 36, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9jcnlwdG8tc2VudGltZW50LXNwbGl0LWJldHdlZW4tYnVsbC0xOTQ2MTc4NzQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 22 Jan 2023 08:00:00 GMT", "title": "Crypto Sentiment Split Between Bull Trap or Bottom as Bitcoin Nears 23K - Yahoo Finance", "content": "A recent upswing in the price of cryptocurrencies like Bitcoin and Ethereum has provoked a distantly familiar debate: whether a market rebound could be imminent or if recent trends are just a prelude to more pain.\n\nSince Bitcoin reached an all-time high of around $69,000 in November of 2021, digital assets have been walloped by higher interest rates and a series of high-profile collapses–firms like Three Arrows Capital (3AC) and FTX, to name a few.\n\nWhile Bitcoin is down nearly 67% from its peak, digital assets–and other investments like stocks–have had a positive start to the year. The price of Bitcoin has risen 38% so far this month to $22,858, its highest price since last August. Ethereum, meanwhile, has seen the value of ETH rise around 38% as well to $1,645, according to CoinGecko.\n\nCryptocurrency prices began rising earlier this month in anticipation of an economic report that showed inflation cooled in December. The reading also lifted hopes about the Federal Reserve raising interest rates less aggressively than they have in the past year to tame soaring prices.\n\n2022 Wasn't All Bad for Crypto (Just Mostly)\n\nBut be weary, say the cautious. Many crypto commentators believe the recent uptick in prices is too good to be true and are labeling the rally a bull trap, predicting the breakout will come crashing down and burn traders who mistook it as the beginning of a new uptrend.\n\nOthers following the rally are also skeptical. A Twitter poll conducted by a popular Bitcoin page reached a consensus among over 18,000 participants that the rally was indeed a bull trap on Jan. 15.\n\nMore recently, “il Capo Of Crypto,” a prominent influencer and self-proclaimed crypto analyst, agreed with the bearish take, saying the rally “clearly looks manipulated” and is the “biggest bull trap” they’ve ever seen.\n\nI've been checking charts all this time, avoiding noise from Twitter. The way the upward movement is happening, the way htf resistances are being tested... it clearly looks manipulated, no real demand. Once again, the biggest bull trap I've ever seen. But they won't trap me. — il Capo Of Crypto (@CryptoCapo_) January 21, 2023\n\nThe sentiment among cautious crypto enthusiasts was echoed on Reddit, where one user pushed back against observations supporting a market bottom made in a news article.\n\nStory continues\n\n“Hard to believe that it was only a week or so ago that everyone and their analyst was solemnly and confidently proclaiming that [Bitcoin at] 12k was inevitable and unavoidable,” the user stated.\n\nAnd, of course, there’s Jim Cramer. The host of MSNBC’s Mad Money tweeted Wednesday that the recent bounce is “manipulation” and further evidence that digital assets are a “sham market.”\n\nthe manipulation higher of crypto shows you this is truly a sham market.... — Jim Cramer (@jimcramer) January 18, 2023\n\nThe mixed accuracy of Cramer’s commentary over the long haul has become the subject of ridicule, prompting many memes and the popularity of accounts like the “Inverse Cramer ETF,” a fictional Exchange-Traded Fund that recommends the opposite of Cramer’s advice.\n\nJim Cramer Turns Bearish on Crypto. Is That Bullish?\n\nIn response to Cramer’s assertion on Wednesday, multiple accounts took the host’s pessimism as a positive sign, such as Dan Held, head of growth marketing at crypto exchange Kraken, who replied, “Bottom is in!”\n\nBottom is in! — Dan Held (@danheld) January 19, 2023\n\nOther influential accounts on Twitter were earnestly bullish, such as “PlanB,” who declared a new bull market in digital assets had begun as Bitcoin’s recent pump took hold. Some community members took it as an opportunity to harp on those who believe digital assets will face more losses.\n\n“It’s just a bull trap trust me bro, I’m still waiting for 12k to load up.” pic.twitter.com/tPUbw3vSMm — Crypto Michael (@MichaelXBT) January 12, 2023\n\nThe upswing in prices has also left a few on Wall Street scratching their heads.\n\nA research report published Friday by JP Morgan analysts couldn’t explain the surge in crypto prices with confidence, but did acknowledge market conditions for riskier assets have grown more favorable, citing the recent inflation report’s release.\n\n“We don’t have a great answer on the January-to-date rally of crypto, but we do think it is emblematic of the underlying conviction many still have in cryptocurrencies,” it stated. “The crypto-bulls and whales seem to have been reinvigorated.”"}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9jcnlwdG8tc2VudGltZW50LXNwbGl0LWJldHdlZW4tYnVsbC0xOTQ2MTc4NzQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 22 Jan 2023 08:00:00 GMT", "title": "Crypto Sentiment Split Between Bull Trap or Bottom as Bitcoin Nears 23K - Yahoo Finance", "content": "A recent upswing in the price of cryptocurrencies like Bitcoin and Ethereum has provoked a distantly familiar debate: whether a market rebound could be imminent or if recent trends are just a prelude to more pain.\n\nSince Bitcoin reached an all-time high of around $69,000 in November of 2021, digital assets have been walloped by higher interest rates and a series of high-profile collapses–firms like Three Arrows Capital (3AC) and FTX, to name a few.\n\nWhile Bitcoin is down nearly 67% from its peak, digital assets–and other investments like stocks–have had a positive start to the year. The price of Bitcoin has risen 38% so far this month to $22,858, its highest price since last August. Ethereum, meanwhile, has seen the value of ETH rise around 38% as well to $1,645, according to CoinGecko.\n\nCryptocurrency prices began rising earlier this month in anticipation of an economic report that showed inflation cooled in December. The reading also lifted hopes about the Federal Reserve raising interest rates less aggressively than they have in the past year to tame soaring prices.\n\n2022 Wasn't All Bad for Crypto (Just Mostly)\n\nBut be weary, say the cautious. Many crypto commentators believe the recent uptick in prices is too good to be true and are labeling the rally a bull trap, predicting the breakout will come crashing down and burn traders who mistook it as the beginning of a new uptrend.\n\nOthers following the rally are also skeptical. A Twitter poll conducted by a popular Bitcoin page reached a consensus among over 18,000 participants that the rally was indeed a bull trap on Jan. 15.\n\nMore recently, “il Capo Of Crypto,” a prominent influencer and self-proclaimed crypto analyst, agreed with the bearish take, saying the rally “clearly looks manipulated” and is the “biggest bull trap” they’ve ever seen.\n\nI've been checking charts all this time, avoiding noise from Twitter. The way the upward movement is happening, the way htf resistances are being tested... it clearly looks manipulated, no real demand. Once again, the biggest bull trap I've ever seen. But they won't trap me. — il Capo Of Crypto (@CryptoCapo_) January 21, 2023\n\nThe sentiment among cautious crypto enthusiasts was echoed on Reddit, where one user pushed back against observations supporting a market bottom made in a news article.\n\nStory continues\n\n“Hard to believe that it was only a week or so ago that everyone and their analyst was solemnly and confidently proclaiming that [Bitcoin at] 12k was inevitable and unavoidable,” the user stated.\n\nAnd, of course, there’s Jim Cramer. The host of MSNBC’s Mad Money tweeted Wednesday that the recent bounce is “manipulation” and further evidence that digital assets are a “sham market.”\n\nthe manipulation higher of crypto shows you this is truly a sham market.... — Jim Cramer (@jimcramer) January 18, 2023\n\nThe mixed accuracy of Cramer’s commentary over the long haul has become the subject of ridicule, prompting many memes and the popularity of accounts like the “Inverse Cramer ETF,” a fictional Exchange-Traded Fund that recommends the opposite of Cramer’s advice.\n\nJim Cramer Turns Bearish on Crypto. Is That Bullish?\n\nIn response to Cramer’s assertion on Wednesday, multiple accounts took the host’s pessimism as a positive sign, such as Dan Held, head of growth marketing at crypto exchange Kraken, who replied, “Bottom is in!”\n\nBottom is in! — Dan Held (@danheld) January 19, 2023\n\nOther influential accounts on Twitter were earnestly bullish, such as “PlanB,” who declared a new bull market in digital assets had begun as Bitcoin’s recent pump took hold. Some community members took it as an opportunity to harp on those who believe digital assets will face more losses.\n\n“It’s just a bull trap trust me bro, I’m still waiting for 12k to load up.” pic.twitter.com/tPUbw3vSMm — Crypto Michael (@MichaelXBT) January 12, 2023\n\nThe upswing in prices has also left a few on Wall Street scratching their heads.\n\nA research report published Friday by JP Morgan analysts couldn’t explain the surge in crypto prices with confidence, but did acknowledge market conditions for riskier assets have grown more favorable, citing the recent inflation report’s release.\n\n“We don’t have a great answer on the January-to-date rally of crypto, but we do think it is emblematic of the underlying conviction many still have in cryptocurrencies,” it stated. “The crypto-bulls and whales seem to have been reinvigorated.”"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xvLWJpdGNvaW4tbWluZXItc29sdmVzLWJsb2NrLTE3NDIzNTk4OS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 22 Jan 2023 08:00:00 GMT", "title": "Solo Bitcoin Miner Solves Block With Hash Rate of Just 10 TH/s, Beating Extremely Unlikely Odds - Yahoo Finance", "content": "A solo Bitcoin miner with an average hashing power of just 10 TH/s (terahashes per second) won the race to add block 772,793 to the Bitcoin blockchain on Friday.\n\nAt the time the block was added, Bitcoin's total hash rate was just over 269 exahash per second, meaning the solo miner's 10 TH/s hash rate represented just 0.000000037% of the blockchain's entire computational power.\n\nPut simply: It was an extremely unlikely win for an individual miner.\n\nCongratulations to miner ********************************<PERSON> with only 10TH who solved a solo block at https://t.co/UWgBvLkDqc ! https://t.co/0O7A7KR4eu — <PERSON><PERSON> (@ckpooldev) January 20, 2023\n\nDespite the odds stacked against them, the solo miner was the first to produce a valid hash for the block to be mined. In return, the miner received 98% of the total 6.35939231 BTC allotted for the block reward and fees. The remaining 2% went to Solo CK Pool, an online mining service that facilitates individual mining.\n\nBitcoin's randomness and probabilities coded for luck and work\n\nTo add a block to a proof-of-work blockchain like Bitcoin, the miner must be the first to calculate a valid hash for the block, which can only be discovered using brute computational force.\n\nMining machines run an encryption algorithm to produce a hash that falls below a threshold specified by the network. If the algorithm produces a value that is above the hash target, the miner tries the algorithm again with a slightly altered input to produce a completely new value for the hash. Miners built specifically to perform this function are able to compute trillions of unique hashes each second.\n\nHowever, even if a miner's machine were able to only produce one hash per second, it is theoretically possible that the algorithm's first output could be a valid hash to solve the block.\n\nStory continues\n\nWhat were the odds?\n\nThe chances of adding a block as a solo miner are determined by the number of hashes the miner's rig is computing per second in relation to the total number of hashes that all of the machines on the network are computing each second.\n\nAccording to a post from user Willi9974 on the BitcoinTalk forum less than an hour after block 772,793 was solved, the lucky solo miner had an average hash rate over the previous hour of 10.6 TH/s.\n\nThe information posted on BitcoinTalk also revealed that the ~10 TH/s was the combined power of four machines (called \"workers\"). This suggests that this solo miner's rig was likely made up of four USB stick Bitcoin miners, which can individually achieve a hash rate of around 3 TH/s and cost roughly $200 each.\n\nUsing the difficulty level included in block 772,793 and assuming the solo miner's rig was computing 10 TH/s, it is possible to calculate the total estimated hash rate as 269,082,950 TH/s at the time the block was solved.\n\nBased on this, the odds of this solo miner being the first to solve the block with a valid hash are one in 26.9 million. Statistically, that means that if the same circumstances were repeated infinite times, the solo miner would add the block 0.000000037% of the time, on average.\n\nUnlikely, but not impossible—and this has happened before\n\nWhile this scenario was extremely unlikely, similar \"once-in-a-lifetime\" events in Bitcoin mining have happened before.\n\nOne year ago, in less than two weeks, there were three different solo miners that solved blocks with improbable hash rates—the third's hash rate was apparently just 8.3 TH/s in comparison to the estimated 190,719,350 TH/s total hash rate, which comes out to a one in 23 million chance (or 0.000000044%).\n\nA hash is either valid and thus solves the block, or it isn't. There is no strategy involved, as the entire system is based on the random generation of hash values and the response mechanisms of the network to maintain core probabilities. Bitcoin runs on code and formulas, so a solo miner somehow solving the next four blocks is perfectly possible within Bitcoin’s mathematical system.\n\nMining pools remain the usual winners\n\nAnecdotes about solo miners like these could end up introducing a new hobby to the ever-hopeful. However, the vast majority of blocks added to the Bitcoin blockchain today have been produced by large pools of mining rigs that combine their hashing power and share earnings.\n\nIn doing so, each miner's contribution is rewarded proportionally each time the pool mines a block.\n\nAccording to blockchain explorer and mining pool BTC.com, the largest Bitcoin mining pool is currently Foundry USA, with its collective 90.19 EH/s computing power making up 31.3% of the network's total hash rate—which means they earn a share of the block rewards and fees for one in every three blocks, on average.\n\nMining pools date back to 2010 and have steadily captured greater shares of hash rate distribution year over year as mining difficulty increased and mining technology improved. Today, at least 98% of Bitcoin miners online belong to a mining pool."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xvLWJpdGNvaW4tbWluZXItc29sdmVzLWJsb2NrLTE3NDIzNTk4OS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 22 Jan 2023 08:00:00 GMT", "title": "Solo Bitcoin Miner Solves Block With Hash Rate of Just 10 TH/s, Beating Extremely Unlikely Odds - Yahoo Finance", "content": "A solo Bitcoin miner with an average hashing power of just 10 TH/s (terahashes per second) won the race to add block 772,793 to the Bitcoin blockchain on Friday.\n\nAt the time the block was added, Bitcoin's total hash rate was just over 269 exahash per second, meaning the solo miner's 10 TH/s hash rate represented just 0.000000037% of the blockchain's entire computational power.\n\nPut simply: It was an extremely unlikely win for an individual miner.\n\nCongratulations to miner ********************************<PERSON> with only 10TH who solved a solo block at https://t.co/UWgBvLkDqc ! https://t.co/0O7A7KR4eu — <PERSON><PERSON> (@ckpooldev) January 20, 2023\n\nDespite the odds stacked against them, the solo miner was the first to produce a valid hash for the block to be mined. In return, the miner received 98% of the total 6.35939231 BTC allotted for the block reward and fees. The remaining 2% went to Solo CK Pool, an online mining service that facilitates individual mining.\n\nBitcoin's randomness and probabilities coded for luck and work\n\nTo add a block to a proof-of-work blockchain like Bitcoin, the miner must be the first to calculate a valid hash for the block, which can only be discovered using brute computational force.\n\nMining machines run an encryption algorithm to produce a hash that falls below a threshold specified by the network. If the algorithm produces a value that is above the hash target, the miner tries the algorithm again with a slightly altered input to produce a completely new value for the hash. Miners built specifically to perform this function are able to compute trillions of unique hashes each second.\n\nHowever, even if a miner's machine were able to only produce one hash per second, it is theoretically possible that the algorithm's first output could be a valid hash to solve the block.\n\nStory continues\n\nWhat were the odds?\n\nThe chances of adding a block as a solo miner are determined by the number of hashes the miner's rig is computing per second in relation to the total number of hashes that all of the machines on the network are computing each second.\n\nAccording to a post from user Willi9974 on the BitcoinTalk forum less than an hour after block 772,793 was solved, the lucky solo miner had an average hash rate over the previous hour of 10.6 TH/s.\n\nThe information posted on BitcoinTalk also revealed that the ~10 TH/s was the combined power of four machines (called \"workers\"). This suggests that this solo miner's rig was likely made up of four USB stick Bitcoin miners, which can individually achieve a hash rate of around 3 TH/s and cost roughly $200 each.\n\nUsing the difficulty level included in block 772,793 and assuming the solo miner's rig was computing 10 TH/s, it is possible to calculate the total estimated hash rate as 269,082,950 TH/s at the time the block was solved.\n\nBased on this, the odds of this solo miner being the first to solve the block with a valid hash are one in 26.9 million. Statistically, that means that if the same circumstances were repeated infinite times, the solo miner would add the block 0.000000037% of the time, on average.\n\nUnlikely, but not impossible—and this has happened before\n\nWhile this scenario was extremely unlikely, similar \"once-in-a-lifetime\" events in Bitcoin mining have happened before.\n\nOne year ago, in less than two weeks, there were three different solo miners that solved blocks with improbable hash rates—the third's hash rate was apparently just 8.3 TH/s in comparison to the estimated 190,719,350 TH/s total hash rate, which comes out to a one in 23 million chance (or 0.000000044%).\n\nA hash is either valid and thus solves the block, or it isn't. There is no strategy involved, as the entire system is based on the random generation of hash values and the response mechanisms of the network to maintain core probabilities. Bitcoin runs on code and formulas, so a solo miner somehow solving the next four blocks is perfectly possible within Bitcoin’s mathematical system.\n\nMining pools remain the usual winners\n\nAnecdotes about solo miners like these could end up introducing a new hobby to the ever-hopeful. However, the vast majority of blocks added to the Bitcoin blockchain today have been produced by large pools of mining rigs that combine their hashing power and share earnings.\n\nIn doing so, each miner's contribution is rewarded proportionally each time the pool mines a block.\n\nAccording to blockchain explorer and mining pool BTC.com, the largest Bitcoin mining pool is currently Foundry USA, with its collective 90.19 EH/s computing power making up 31.3% of the network's total hash rate—which means they earn a share of the block rewards and fees for one in every three blocks, on average.\n\nMining pools date back to 2010 and have steadily captured greater shares of hash rate distribution year over year as mining difficulty increased and mining technology improved. Today, at least 98% of Bitcoin miners online belong to a mining pool."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xvLWJpdGNvaW4tbWluZXItc29sdmVzLWJsb2NrLTE3NDIzNTk4OS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 22 Jan 2023 08:00:00 GMT", "title": "Solo Bitcoin Miner Solves Block With Hash Rate of Just 10 TH/s, Beating Extremely Unlikely Odds - Yahoo Finance", "content": "A solo Bitcoin miner with an average hashing power of just 10 TH/s (terahashes per second) won the race to add block 772,793 to the Bitcoin blockchain on Friday.\n\nAt the time the block was added, Bitcoin's total hash rate was just over 269 exahash per second, meaning the solo miner's 10 TH/s hash rate represented just 0.000000037% of the blockchain's entire computational power.\n\nPut simply: It was an extremely unlikely win for an individual miner.\n\nCongratulations to miner ********************************<PERSON> with only 10TH who solved a solo block at https://t.co/UWgBvLkDqc ! https://t.co/0O7A7KR4eu — <PERSON><PERSON> (@ckpooldev) January 20, 2023\n\nDespite the odds stacked against them, the solo miner was the first to produce a valid hash for the block to be mined. In return, the miner received 98% of the total 6.35939231 BTC allotted for the block reward and fees. The remaining 2% went to Solo CK Pool, an online mining service that facilitates individual mining.\n\nBitcoin's randomness and probabilities coded for luck and work\n\nTo add a block to a proof-of-work blockchain like Bitcoin, the miner must be the first to calculate a valid hash for the block, which can only be discovered using brute computational force.\n\nMining machines run an encryption algorithm to produce a hash that falls below a threshold specified by the network. If the algorithm produces a value that is above the hash target, the miner tries the algorithm again with a slightly altered input to produce a completely new value for the hash. Miners built specifically to perform this function are able to compute trillions of unique hashes each second.\n\nHowever, even if a miner's machine were able to only produce one hash per second, it is theoretically possible that the algorithm's first output could be a valid hash to solve the block.\n\nStory continues\n\nWhat were the odds?\n\nThe chances of adding a block as a solo miner are determined by the number of hashes the miner's rig is computing per second in relation to the total number of hashes that all of the machines on the network are computing each second.\n\nAccording to a post from user Willi9974 on the BitcoinTalk forum less than an hour after block 772,793 was solved, the lucky solo miner had an average hash rate over the previous hour of 10.6 TH/s.\n\nThe information posted on BitcoinTalk also revealed that the ~10 TH/s was the combined power of four machines (called \"workers\"). This suggests that this solo miner's rig was likely made up of four USB stick Bitcoin miners, which can individually achieve a hash rate of around 3 TH/s and cost roughly $200 each.\n\nUsing the difficulty level included in block 772,793 and assuming the solo miner's rig was computing 10 TH/s, it is possible to calculate the total estimated hash rate as 269,082,950 TH/s at the time the block was solved.\n\nBased on this, the odds of this solo miner being the first to solve the block with a valid hash are one in 26.9 million. Statistically, that means that if the same circumstances were repeated infinite times, the solo miner would add the block 0.000000037% of the time, on average.\n\nUnlikely, but not impossible—and this has happened before\n\nWhile this scenario was extremely unlikely, similar \"once-in-a-lifetime\" events in Bitcoin mining have happened before.\n\nOne year ago, in less than two weeks, there were three different solo miners that solved blocks with improbable hash rates—the third's hash rate was apparently just 8.3 TH/s in comparison to the estimated 190,719,350 TH/s total hash rate, which comes out to a one in 23 million chance (or 0.000000044%).\n\nA hash is either valid and thus solves the block, or it isn't. There is no strategy involved, as the entire system is based on the random generation of hash values and the response mechanisms of the network to maintain core probabilities. Bitcoin runs on code and formulas, so a solo miner somehow solving the next four blocks is perfectly possible within Bitcoin’s mathematical system.\n\nMining pools remain the usual winners\n\nAnecdotes about solo miners like these could end up introducing a new hobby to the ever-hopeful. However, the vast majority of blocks added to the Bitcoin blockchain today have been produced by large pools of mining rigs that combine their hashing power and share earnings.\n\nIn doing so, each miner's contribution is rewarded proportionally each time the pool mines a block.\n\nAccording to blockchain explorer and mining pool BTC.com, the largest Bitcoin mining pool is currently Foundry USA, with its collective 90.19 EH/s computing power making up 31.3% of the network's total hash rate—which means they earn a share of the block rewards and fees for one in every three blocks, on average.\n\nMining pools date back to 2010 and have steadily captured greater shares of hash rate distribution year over year as mining difficulty increased and mining technology improved. Today, at least 98% of Bitcoin miners online belong to a mining pool."}]