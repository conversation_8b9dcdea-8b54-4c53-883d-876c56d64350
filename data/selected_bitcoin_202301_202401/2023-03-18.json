[{"id": 22, "url": "https://news.google.com/rss/articles/CBMijAFodHRwczovL2RlY3J5cHQuY28vbmV3cy1leHBsb3Jlcj9waW5uZWQ9MTM0NjIxJnRpdGxlPWJhbGFqaS1zcmluaXZhc2FuLWJldHMtMW0tb24tYml0Y29pbi1oaXR0aW5nLTFtLXdpdGhpbi05MC1kYXlzLWR1ZS10by11cy1oeXBlcmluZmxhdGlvbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 18 Mar 2023 22:46:10 GMT", "title": "News Explorer — <PERSON><PERSON><PERSON> Bets $1M on Bitcoin Hitting $1M Within 90 Days Due to US Hyperinflation - Decrypt", "content": "Mar 18, 10:04 pm\n\nCointelegraph\n\n<PERSON><PERSON><PERSON> Bets $1M on Bitcoin Hitting $1M Within 90 Days Due to US Hyperinflation\n\n<PERSON><PERSON><PERSON>, former Coinbase CTO, has made a bet of 1 BTC and $1 million in USD Coin (USDC) with <PERSON> on whether the US will experience hyperinflation. The terms of the bet are that if Bitcoin's price fails to reach $1 million by June 17, <PERSON><PERSON>ock will win the bet, and if it does, <PERSON><PERSON><PERSON><PERSON> will keep the 1 BTC and the $1 million in USDC. <PERSON><PERSON><PERSON><PERSON> has also moved $2 million in USDC for another wager on the same topic."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9mb3JtZXItY29pbmJhc2UtY3RvLW1ha2VzLTJtLWJldC1vbi1iaXRjb2luLXMtcGVyZm9ybWFuY2XSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 18 Mar 2023 07:00:00 GMT", "title": "Former Coinbase CTO makes $2M bet on Bitcoin's performance - Cointelegraph", "content": "Former Coinbase chief technology officer <PERSON><PERSON><PERSON> has made a millionaire bet on Bitcoin’s (BTC) price over the next 90 days, predicting the cryptocurrency will reach $1 million by June 17.\n\nThe wager was initiated on March 17, when pseudonymous Twitter user <PERSON> offered to bet anyone $1 million that the United States would not experience hyperinflation. A few hours later, the former Coinbase executive accepted the bet.\n\nSir, I believe we have ourselves a deal https://t.co/9JYaLNo9Eq — <PERSON> (@jdcmedlock) March 18, 2023\n\nUnder the proposed terms, if Bitcoin’s price fails to reach $1 million by June 17, Medlock will win $1 million worth of the dollar-pegged stablecoin USD Coin (USDC) and 1 BTC. In the same way, if Bitcoin is worth at least $1 million by the date, then <PERSON><PERSON><PERSON> can keep the 1 BTC and the $1 million in USDC. <PERSON><PERSON><PERSON><PERSON> explained in the thread:\n\n“You buy 1 BTC. I will send $1M USD. This is ~40:1 odds as 1 BTC is worth ~$26k. The term is 90 days.”\n\nRelated: Banking crisis: What does it mean for crypto?\n\nPer the thread, other Twitter users helped set up a smart contract with the betting terms. <PERSON><PERSON><PERSON><PERSON> also disclosed that he would move another $1 million in USDC for another wager on the same topic:\n\n“I am moving $2M into USDC for the bet. I will do it with <PERSON><PERSON><PERSON> and one other person, sufficient to prove the point. See my next tweet. Everyone else should just go buy Bitcoin, as it’ll be much cheaper for you than locking one up for 90 days.“\n\nMedlock and Srinivasan made the wager based on their different views of the U.S. economy’s future amid ongoing uncertainty regarding its banking system.\n\nSrinivasan argues that there’s an impending crisis that will lead to the deflation of the U.S. dollar and, thus, to a hyperinflation scenario that would take the BTC price to $1 million. Medlock, on the other hand, is bearish about upcoming hyperinflation in the country.\n\nMeanwhile, Bitcoin’s price has reached $27,387 at the time of writing, with its market capitalization adding over $194 billion year-to-date to a 66% growth in 2023, outperforming Wall Street bank stocks amid fears of a global banking crisis.\n\nAlso, for the first time in a year, BTC’s price has shifted away from United States stocks, rising about 65% compared to the S&P 500’s 2.5% gains and the Nasdaq’s 15% decline, Cointelegraph reported."}, {"id": 14, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9iaXRjb2luLW1hcmtldC1jYXAtZ3Jvd3MtNjAtaW4tMjAyMy1hcy10b3Atd2FsbC1zdHJlZXQtYmFua3MtbG9zZS0xMDBi0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 18 Mar 2023 07:00:00 GMT", "title": "Bitcoin market cap grows 60% in 2023 as top Wall Street banks lose $100B - Cointelegraph", "content": "The market capitalization of Bitcoin (BTC) has added $194 billion in 2023. Its 66% year-to-date (YTD) growth vastly outperforms top Wall Street bank stocks, particularly as fears of a global banking crisis are rising.\n\nBTC market cap daily performance chart. Source: TradingView\n\nMoreover, Bitcoin has decoupled from United States stocks for the first time in a year, with its price rising about 65% versus S&P 500’s 2.5% gains and Nasdaq’s 15% decline in 2023.\n\nSPX and NDAQ YTD performance vs. BTC/USD. Source: TradingView\n\nWall Street banks lose $100B in 2023\n\nThe six largest U.S. banks — JPMorgan Chase, Bank of America, Citigroup, Wells Fargo, Morgan Stanley and Goldman Sachs — have lost nearly $100 billion in market valuation since the year’s start, according to data gathered by CompaniesMarketCap.com.\n\nBank of America’s stock is the worst performer among the Wall Street banking players, with a nearly 17% YTD drop in valuation. Goldman Sachs trails with an almost 12% YTD decrease, followed by Wells Fargo (9.74%), JPMorgan Chase (6.59%), Citi (3.62%) and Morgan Stanley (0.84%).\n\nWall Street banks YTD performance. Source: TradingView\n\nU.S. bank valuations have slid amid the ongoing U.S. regional banking collapse. That includes the announcement last week that Silvergate, a crypto-friendly bank, was closing its doors, followed by regulators' subsequent takeover of Signature Bank and Silicon Valley Bank.\n\nRelated: Breaking: SVB Financial Group files for Chapter 11 bankruptcy\n\nThe crisis further expanded with the near-collapse of First Republic Bank, which was saved at the last moment through a $30 billion combined injection by Wells Fargo, JPMorgan Chase, Bank of America and Citigroup — among others.\n\nCyprus and Greece deja vu?\n\nThe rise of Bitcoin in the face of a growing U.S. banking crisis is similar to how it reacted during banking collapses in Cyprus and Greece.\n\nBTC’s price grew by up to 5,000% amid the Cyprus financial crisis in 2013, prompted by the exposure of Cypriot banks to overleveraged regional real-estate companies.\n\nBTC/USD performance during Cyprus banking crisis. Source: TradingView\n\nThe situation was so dire in March 2013 that Cyprus authorities closed all banks to avoid a bank run.\n\nWhen Greece faced a similar crisis in 2015 and imposed capital controls on citizens to avoid a bank run, Bitcoin’s price gained 150%.\n\nBTC/USD performance during the Greece banking crisis. Source: TradingView\n\n“Fears over the stability of the banking system, along with declining real interest rates, creates a good environment for Bitcoin to rebound,” commented Ilan Solot, co-head of digital assets at London broker Marex, adding that the crypto “is seen by some investors as a hedge against systemic risks.“\n\nThis article does not contain investment advice or recommendations. Every investment and trading move involves risk, and readers should conduct their own research when making a decision."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vd3d3LmNiYy5jYS9uZXdzL3BvbGl0aWNzL2NyeXB0by1taW5pbmctZWxlY3RyaWNpdHktcHJvdmluY2VzLTEuNjc4MDkwN9IBIGh0dHBzOi8vd3d3LmNiYy5jYS9hbXAvMS42NzgwOTA3?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 18 Mar 2023 07:00:00 GMT", "title": "Crypto at a crossroads: Some provinces are wary of the technology's vast appetite for electricity - CBC.ca", "content": "Proponents of cryptocurrency mining say the industry's future in Canada is hanging in the balance after several provinces moved to restrict new projects earlier this year in response to concerns about their electricity usage.\n\nCrypto entrepreneurs — most of them focused on Bitcoin — have been drawn to Canada because of the abundant supply of clean, inexpensive electricity in provinces like British Columbia and Quebec. Most crypto operations need unfettered access to cheap power to operate the rows of high-powered computers required for cryptomining.\n\n\"Why Canada? So, first of all, we said, 'What are the key ingredients you need to run this computing service?'\" said <PERSON>, an Australian cryptocurrency entrepreneur whose company, Iris Energy, operates three facilities in B.C.\n\n\"Cool temperatures — really important. Stability of law, good regulatory jurisdiction. But most importantly, renewable energy.\"\n\nCBC News: The House 20:20 The power of cryptocurrency mining and its uncertain future Several Canadian provinces have moved to put limits on new cryptocurrency mining operations, putting into question Canada’s place in the emerging sector. In a special report, freelance journalist <PERSON> speaks with entrepreneurs who are pushing for more mining operations in Canada and B.C. Energy Minister <PERSON> speaks with host <PERSON> about why her province has hit the brakes on new operations.\n\n<PERSON> said he sees a new wave of economic prosperity growing out of cryptocurrency mining in provinces like B.C., which currently enjoys an electricity surplus.\n\n\"We can build a whole industry around this. We can go into those regional towns where they've been decimated by the end of the pulp-and-paper mill … rehire local workers, retrain them, and deliver all these benefits back into the community,\" he said.\n\nBut some provinces have slammed the brakes on new projects, saying the mining sites — where computers churn through complex equations to verify cryptocurrency transactions on the blockchain ledger (earning digital assets as a reward) — consume a staggering amount of electrical power.\n\nB.C. currently has seven mining sites in operation, with six more in advanced states of development. But it also has imposed an 18-month moratorium on connecting any new crypto mining projects to its electrical grid — halting 21 other projects which the province says would have used the same amount of power as 570,000 homes.\n\nManitoba also has paused new crypto hookups, while Hydro-Québec has set up higher rates and an electricity usage cap for mining projects. Ontario has proposed excluding crypto miners from an incentive program that could allow them to save money on electricity.\n\nUncertainty clouds future investments\n\nRight now, Canadian crypto miners account for the fourth highest amount of computing power being contributed to the blockchain network, after crypto operations in the United States, China and Kazakhstan. Moves by some provinces to ration the sector's access to electricity have some crypto enthusiasts questioning whether Canada will continue to be a major player.\n\n\"As a public company, I have shareholders and I need to pause or not make decisions until I know what the rules are. And once I know what the rules are, I look at whether to invest in Canada or somewhere more lucrative,\" said Sheldon Bennett, CEO of DMG Blockchain Solutions and part of the Canadian Digital Asset Mining Coalition, an advocacy organization.\n\nB.C. Energy Minister Josie Osborne told The House B.C.'s decision to impose the moratorium was meant to give the province time to consult with the industry to make sure energy is being put to good use.\n\nThe Site C dam in B.C., under construction in 2021. (B.C. Hydro/submitted)\n\nWhile B.C. has an energy surplus right now, Osborne said that might not always be the case.\n\n\"We don't want to put that electricity at risk. It's why we have to take this pause right now and instead use the electricity for the best opportunities in the future,\" she told host Catherine Cullen.\n\nOsborne argued that in order for B.C. to achieve its climate and economic goals, it has to look at other areas where its electricity might be more useful.\n\n\"Cryptocurrency definitely does not create the number of jobs that other industry does,\" she said.\n\nIt also does nothing to help B.C. achieve its climate goals, she added.\n\n\"Cryptocurrency mining doesn't lower pollution in other industries,\" she said. \"We want to use that electricity for our mines and for forestry operations, for marine port operations, for hydrogen operations [so] we could use the hydrogen to blend natural gas and decarbonize there. We want to use these electrons for their highest and best use.\"\n\n\n\nOsborne did signal her government is somewhat open to hooking up new crypto operations in the future.\n\nDan Roberts, co-founder of the cryptocurrency company Iris Energy, says Canada's supply of clean energy is a huge draw for his industry. (Bob Keating/CBC)\n\nCryptocurrency was once a trendy topic in Canadian politics. It was championed by Pierre Poilievre during his successful run for the Conservative leadership (he famously bought a shawarma sandwich with Bitcoin just under a year ago).\n\nPoilievre suggested at the time that cryptocurrencies could allow ordinary Canadians to \"opt out\" of inflation because they are not influenced by central banks. That was before many cryptocurrencies crashed last year; Bitcoin's value in late 2022 had dropped to about one-fourth of what it had been a year prior.\n\nBut policy development on crypto is moving forward. The Canadian Securities Administrators (CSA), the umbrella organization representing Canada's provincial and territorial securities regulators, has pushed for restrictions on crypto trading, while the Bank of Canada is in the midst of a digital asset review .\n\nThe shift of some cryptocurrencies like Ethereum — the second largest cryptocurrency — to what's known as a \"proof of stake\" system has eliminated the need for mining, and thus for most of the currency's energy consumption. That's provided hope to some advocates that the energy argument against cryptocurrencies can one day be eliminated.\n\nBut Bitcoin remains on a \"proof of work\" model, where mining is key. Bennett said he wonders about Canada's willingness to engage with the new sector.\n\n\"What does Canada decide it wants to do with this industry? Does it want to foster it and grow it? Does it appreciate the technology, the jobs and the investment that's coming into it and want to grow that?\" he said.\n\n\"Or does it want to sit back and see how other countries manage it?\""}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZGFpbHljb2luLmNvbS9iaXRjb2luLWJ0Yy1iaXJ0aC1jcnlwdG9jdXJyZW5jeS1ibG9ja2NoYWluLXJldm9sdXRpb24v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 18 Mar 2023 07:00:00 GMT", "title": "Bitcoin (BTC): The Birth of Cryptocurrency and the Blockchain Revolution - DailyCoin", "content": "Bitcoin (BTC) has been given plenty of names in its lifetime. Digital Gold, The Future of Money, even ‘The Biggest Ponzi Scheme in Human History.’ Over a decade has passed since <PERSON><PERSON> published the Bitcoin Whitepaper. Despite bitcoin price volatility and what its naysayers would have you believe, Bitcoin is here to stay.\n\nNot only has Bitcoin stood the test of time, the world’s largest digital asset also inspired a technological revolution. Cryptocurrency and blockchain technology are re-inventing how we store our funds and manage our finances online in a way that traditional systems, like central banks, cannot replicate.\n\nDecentralized and fully open-source, the bitcoin network has given people greater control over their finances than ever before. It blazed the trail for other digital currencies, like Ethereum (ETH) and Ripple (XRP), to innovate and improve. While it’s more often considered a store of value than a currency these days, Bitcoin is still the centerpiece of the crypto market.\n\nWhat is Bitcoin? What is bitcoin mining and Proof-of-Work? Will Bitcoin always be king, or is it possible that altcoins like Ether or BNB will siphon BTC’s market cap and take its place at the top?\n\nWhat Is Bitcoin?\n\nBitcoin is a cryptocurrency, a kind of digital asset that’s used in the same way as fiat currencies like USD. With BTC, users can pay for goods and services and send funds to friends and family using a decentralized, peer-to-peer network.\n\nSounds like normal money, right?\n\nNot really.\n\nFiat currencies, such as the USD or EUR, are controlled and governed by centralized bodies. They’re often stored in central banks that can deny users access to their funds. If I want to transfer fiat currency to someone, I must trust an intermediary to settle the payment. This third party could block the transfer or charge me extortionate fees for their services.\n\nOn the other hand, bitcoin transactions are trustless and permissionless. Nothing stops me from using the bitcoin blockchain and sending BTC directly to a friend or business over the network. Moreover, I can watch the transaction in real-time by tracking it transparently on-chain.\n\nHow does it all work, then? If there’s no intermediary to settle the payment, what ensures that bitcoin transactions are processed?\n\nHow Does Bitcoin Work?\n\nThe bitcoin network is powered by miners and a unique algorithm called the Proof-of-Work consensus mechanism. Bitcoin miners are responsible for processing transactions on the blockchain and producing, or ‘mining’ new blocks. Simply put, a block is a unit of data on the blockchain that records previous transactions.\n\nIn the Proof-of-Work mechanism, miners compete to produce new blocks. Using intensive computational power, Miners solve complex puzzles using cryptography. Miners earn BTC as a reward for solving these puzzles, which process bitcoin transactions and create new blocks for the network.\n\nAnother thing separating Bitcoin from traditional fiat currencies is that BTC has a fixed max supply of 21 million coins. This makes BTC immune to inflation and is one of the main reasons many investors consider BTC a store of value and a hedge against inflation.\n\nBitcoin miner rewards slowly diminish over time through a process called Bitcoin halving. After every 210,000 blocks, the amount of BTC distributed to miners is cut in half. When BTC was first launched in 2009, every new block released 50 BTC into the circulating supply. However, following the third halving in May 2020, these rewards have been reduced to just 6.25 BTC per block.\n\nWhat Can Bitcoin Be Used for?\n\nThe bitcoin network was originally designed to be a decentralized peer-to-peer payment network. As a result, it’s most commonly used to send and receive funds anonymously over the internet. However, BTC has grown beyond its initial use cases and is now considered a store of value, like gold. This makes it a popular investment vehicle for speculators.\n\nAs blockchain technology evolves, BTC is witnessing greater adoption and utility. El Salvador recognizes BTC as a legal tender, and businesses worldwide accept bitcoin payments in exchange for goods and services. Even publicly traded, NASDAQ-listed companies like Tesla hold BTC, and other altcoins like DOGE, on their balance sheets.\n\nWhat’s more, innovation across the cryptocurrency industry continues to bring further use cases to Bitcoin. For example, BTC can be wrapped and deployed onto other blockchain ecosystems. This means that bitcoin’s value and liquidity can be used in DeFi apps on Ethereum.\n\nWith the rise of Ordinals, the Bitcoin blockchain hosts its own NFTs. These ordinal NFTs exist on the Bitcoin network, denominating their value in BTC price. At the beginning of 2023, Ordinal trading volume brought renewed attention and fresh eyes to the world’s first cryptocurrency.\n\nBitcoin Pros & Cons\n\nObjectively, Bitcoin is far from perfect. While it provides a secure, transparent, and decentralized payment network available to anyone with an internet connection, the bitcoin blockchain still has drawbacks.\n\nBitcoin Pros\n\nDecentralized – The bitcoin network has no centralized owner or governance. Moreover, the blockchain is distributed, meaning the network will remain fully functional even if large mining operations unexpectedly collapse.\n\n– The bitcoin network has no centralized owner or governance. Moreover, the blockchain is distributed, meaning the network will remain fully functional even if large mining operations unexpectedly collapse. Fast – Bitcoin transactions are faster than traditional banking transfers and are not restricted by international borders. With bitcoin, you can send funds anywhere in the world in seconds.\n\n– Bitcoin transactions are faster than traditional banking transfers and are not restricted by international borders. With bitcoin, you can send funds anywhere in the world in seconds. Accessible – Anyone with access to the internet can own bitcoin and use it for payments. There are no invasive KYC procedures that might stop users from opening a bank account or accessing financial services.\n\n– Anyone with access to the internet can own bitcoin and use it for payments. There are no invasive KYC procedures that might stop users from opening a bank account or accessing financial services. Affordable – Bitcoin transactions cost anywhere between a few cents and a couple of dollars, dependent on network congestion. In a world where centralized banks can charge commissions on large transfers and international transactions, the bitcoin network is an affordable option.\n\n– Bitcoin transactions cost anywhere between a few cents and a couple of dollars, dependent on network congestion. In a world where centralized banks can charge commissions on large transfers and international transactions, the bitcoin network is an affordable option. Transparent – The Bitcoin blockchain is a transparent and publically verifiable ledger of transactions. The entire protocol is open-source, meaning anyone can verify its source code to ensure the network is safe.\n\nBitcoin Cons\n\nEnergy Consumption – One of bitcoin’s biggest criticisms is that it requires enormous computational power. Analysts like the Bitcoin Energy Consumption Index estimate that over a year, the bitcoin network uses as much energy as Kazakhstan and expels a carbon footprint comparable to Portugal.\n\n– One of bitcoin’s biggest criticisms is that it requires enormous computational power. Analysts like the Bitcoin Energy Consumption Index estimate that over a year, the bitcoin network uses as much energy as Kazakhstan and expels a carbon footprint comparable to Portugal. Scalability Issues – While the Bitcoin blockchain is faster and more affordable than traditional banking, competing blockchain payment networks like Ripple have developed far more scalable systems. While Bitcoin processes around 4-5 transactions per second, Ripple handles over 1,000, with reduced fees.\n\n– While the Bitcoin blockchain is faster and more affordable than traditional banking, competing blockchain payment networks like Ripple have developed far more scalable systems. While Bitcoin processes around 4-5 transactions per second, Ripple handles over 1,000, with reduced fees. Price Volatility – If you’ve spent time in the crypto market, you’ll understand that bitcoin prices are prone to unexpected drops and surges. These prices affect Bitcoin’s market capitalization, and this volatility makes it difficult to use BTC as a currency of exchange.\n\nBitcoin’s Mysterious Beginnings\n\nThe Global Financial Crisis of 2008 wreaked havoc on economies worldwide. Central banks and financial institutions were in critical debt after a sudden housing market crash destroyed the value of their assets. Governments were forced to bail out overleveraged banks and institutions, and the resulting credit crunch broke the global economy.\n\nIn 2008, Bitcoin’s anonymous founder, or group of founders, published the Bitcoin whitepaper. Satoshi Nakamoto’s iconic document has become a manifesto for crypto enthusiasts. In just a few months, the Bitcoin network was launched. Nakamoto themself mined the first block, immortalizing a significant line of text into the blockchain forever: “The Times 03/Jan/2009 Chancellor on brink of second bailout for banks.”\n\nSatoshi Nakamoto continued to contribute to the bitcoin project. However, in 2011 the enigmatic founder confirmed in an email to a friend that he’d moved on from Bitcoin. He remains anonymous, though it is widely accepted he is the world’s largest BTC holder.\n\nThe Future of Bitcoin\n\nDespite Nakamoto’s disappearance, the bitcoin community has continued to develop the network to keep up with the growth of the wider blockchain industry. Their primary adjective is to increase the network’s scalability without sacrificing its decentralization.\n\nWith the uprising of smart contracts and decentralized finance, parts of the bitcoin community are worried that the network will get left behind. Some developers are working towards deploying native bitcoin smart contracts in the ecosystem, which will provide greater utility to BTC moving forward.\n\nLightning Network\n\nInitially proposed in 2015, the Lightning network is a kind of Layer 2 scaling solution for Bitcoin transactions. It takes certain payments off-chain into specific channels between two trusted parties. The Lightning network is intended for smaller, everyday transactions like buying coffee. As the name would suggest, the Lightning network is faster and more affordable than Bitcoin’s main network.\n\nBitcoin Smart Contracts – TapRoot Upgrades & Stacks Network\n\nRecent Taproot upgrades have introduced basic payment smart contracts to the network to make Bitcoin more composable. However, given Bitcoin’s underlying immutable code, these contracts are rather limited and don’t come close to the flexibility provided by other Layer 1 networks like Ethereum.\n\nMeanwhile, the Stacks (STX) network is an open-source Layer-1 blockchain that connects to Bitcoin’s main chain. Stacks provide developers and users with a suitable environment to deploy more complex smart contracts. The project aims to bring smart contracts to the Bitcoin ecosystem while utilizing Bitcoin’s security.\n\nIn the same way that BTC can be wrapped and deployed on Ethereum as wBTC tokens, Stacks will host sBTC tokens that can be redeemed for BTC at a 1:1 ratio.\n\nOn the Flipside\n\nDespite providing the foundation for the entire cryptocurrency and blockchain industry, Bitcoin’s scalability issues make it difficult to use as a payment network. Bitcoin also devastates the environment and consumes as much energy per year as a small country.\n\nWhy You Should Care\n\nWithout Bitcoin, your favorite blockchain, NFT, or meme coin wouldn’t exist. It’s important to know why Bitcoin and blockchain technologies were created and how they work. This also extends to the value of true decentralization and self-custody, which are cornerstones of the cryptocurrency world.\n\nFAQs"}]