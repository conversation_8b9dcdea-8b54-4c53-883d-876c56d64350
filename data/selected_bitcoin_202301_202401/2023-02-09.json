[{"id": 21, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTAyLTA5L2NyeXB0b2N1cnJlbmNpZXMtZmFsbC1hbWlkLWNvbmNlcm4tb3Zlci1yZWd1bGF0b3J5LWNyYWNrZG93btIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "Bitcoin’s 2023 Bounce Is Fizzling as SEC Turns Up Heat on Crypto - Bloomberg", "content": "Why did this happen?\n\nPlease make sure your browser supports JavaScript and cookies and that you are not blocking them from loading. For more information you can review our Terms of Service and Cookie Policy."}, {"id": 24, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy92aWRlb3MvMjAyMy0wMi0wOS9iaXRjb2luLWhhcy1oaXQtdGhlLWJvdHRvbS1vc3ByZXktZnVuZHMtY2VvLXZpZGVv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "Bitcoin Has Hit the Bottom: Osprey Funds CEO - Bloomberg", "content": "Why did this happen?\n\nPlease make sure your browser supports JavaScript and cookies and that you are not blocking them from loading. For more information you can review our Terms of Service and Cookie Policy."}, {"id": 38, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvYml0Y29pbi1leGNoYW5nZS1sb2NhbGJpdGNvaW5zLXRvLXNodXQtZG93bi1jaXRpbmctbWFya2V0LWNvbmRpdGlvbnPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "Bitcoin Exchange LocalBitcoins To Shut Down, Citing Market Conditions - Nasdaq", "content": "According to an announcement on its website, the peer-to-peer Bitcoin platform will be shutting down before the end of February 2023.\n\nLocalBitcoins has announced that the exchange will be closed for service by the end of February. The long-time Bitcoin trading service was the first peer-to-peer (P2P) transaction platform for many Bitcoiners. Launched in June 2012, the exchange is one of the oldest entities in Bitcoin, establishing itself as a primary P2P player in its hey-day with over $100 million in weekly trading volume of peer-to-peer transactions.\n\n“Originally LocalBitcoins was established to Bring Bitcoin Everywhere and drive global financial inclusion,” the announcement reads. “We have honored that mission for over 10 years and we are proud of what we have achieved together with all of you, our loyal community.”\n\nThe announcement cites “challenges during the ongoing very cold crypto-winter” as reasoning for the shutdown. The exchange has informed customers that they should withdraw their funds from LocalBitcoins, as well as withdrawing their bitcoin from the LocalBitcoins wallet. It notes that customers will have 12 months to complete this process, although they encourage users to do so sooner.\n\nNotably, the shutdown has arrived just as P2P Bitcoin merchants and individual sellers have come under heavy scrutiny from U.S. regulatory authorities. While there is no mention of this in the announcement, it could be assumed that these recent actions have led to decreased volume on platforms like LocalBitcoins.\n\nDespite these pressures, alternatives such as RoboSats and Bisq still function for Bitcoiners seeking to acquire bitcoin via P2P transactions.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 26, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9vbmNoYWluLW1vbmtleS1uZnRzLWRvdWJsZS1wcmljZS0wMDAyMDU0MjMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "OnChain Monkey NFTs Double in Price After Creators Put Them on Bitcoin - Yahoo Finance", "content": "Buzz around Bitcoin-based NFTs—via the recently-launched Ordinals protocol—keeps growing by the day, with record-setting mints on Thursday and high-value sales over the past day. And one established Ethereum NFT collection is reaping the benefits of going multi-chain, seeing its prices surge after revealing Bitcoin-based counterparts.\n\nOnChain Monkey, a collection of 10,000 Ethereum NFT profile pictures (PFPs) minted in 2021, used Ordinals to “inscribe” all of its existing artwork on Bitcoin over the past day. Now the NFT holders on Ethereum can also say that their respective collectibles live on Bitcoin, as well.\n\nPrices for the Ethereum NFTs have almost tripled since the announcement, with the floor price—that is, the cheapest listed NFT on a marketplace—for the project jumping from 0.79 ETH at the start of the day (per NFT Price Floor) to a peak of 1.75 ETH before settling to about 1.5 ETH (nearly $2,500) as of this writing.\n\nOnChainMonkey is the first 10K collection to be inscribed on Bitcoin via Ordinals! Own an ETH OCM Genesis = Owning a BTC OCM Genesis All on-chain on Bitcoin on Feb 8, 2023, and all on-chain on Ethereum on Sept 11, 2021! 🔥 🧵/8 pic.twitter.com/AMuUlERJZM — OnChainMonkey (@OnChainMonkey) February 9, 2023\n\nAccording to data from CryptoSlam, the move has fueled a 12,200% increase in NFT trading volume for the Ethereum project over the past 24 hours compared to the previous span. The analytics platform reports about $1.1 million in sales over the past day for a project with lifetime secondary sales totaling almost $39 million.\n\nMetagood, the startup behind OnChain Monkey, said it put all 10,000 NFTs onto Bitcoin via the Ordinals protocol using a single transaction, much as it did for the original Ethereum collection back in 2021.\n\nStory continues\n\nThe Hottest Bitcoin NFTs Right Now Are CryptoPunks Clones\n\nIn a Twitter Spaces today, Metagood co-founder Danny Yang said that enabling trading was the next step for the team but suggested that other tooling needs to be created around Ordinals to facilitate that feature. He also noted that Metagood plans to build a bridge between Ethereum and Bitcoin to let NFT holders switch between the two versions.\n\n“They are the same on both chains,” an OnChain Monkey Discord moderator wrote earlier today. “Buy on ETH and you will have access to the BTC version when the tools catch up.”\n\nThe project’s Discord server is filling up with users who claim to have purchased one of the Ethereum NFTs following the Bitcoin announcement and are asking for details on how it will work.\n\nOnChain Monkey is part of Metagood’s push to use Web3 initiatives to fund programs that benefit communities. These include efforts to fund coral restoration and provide aid to Ukraine amid the Russian invasion. Metagood, co-founded by venture capitalist Bill Tai alongside Yang and Amanda Terry, raised $5 million in December.\n\nMetagood says that it minted the first 10,000 NFT project on Ordinals—a claim that the creators of Bitcoin Punks, a clone of popular Ethereum project CryptoPunks, have also made.\n\nBitcoin Punks began inscribing its NFTs via Ordinals before OnChain Monkey, but opted to put each Punk as a separate inscription—a process that stretched from Wednesday into Thursday. OnChain Monkey, on the other hand, committed its entire collection to Ordinals via a single transaction late Wednesday.\n\nEditor's note: This article was updated after publication to clarify when OnChain Monkey and Bitcoin Punks were inscribed via Ordinals."}, {"id": 31, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvaG93LWluc2NyaXB0aW9ucy1pbXBhY3QtYml0Y29pbi1ibG9jay1zcGFjZdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "How Inscriptions Impact Bitcoin Block Space - Nasdaq", "content": "A new use case for bitcoin is causing a stir because of its ability to include data directly on-chain. An analysis of inscriptions’ impact on block space.\n\nThe below is an excerpt from a recent edition of Bitcoin Magazine PRO, Bitcoin Magazine's premium markets newsletter. To be among the first to receive these insights and other on-chain bitcoin market analysis straight to your inbox, subscribe now.\n\nBitcoin Ordinals And Inscriptions\n\nA recent and somewhat contentious use of Bitcoin is an innovative application of the Taproot soft fork that was merged into the protocol in 2021. Ordinal Theory is a way of serializing each individual unit of bitcoin and labeling these specific satoshis “ordinals.” The creator of this numbering scheme, <PERSON>, described it in his blog saying, “Satoshis are numbered in the order in which they’re mined, and transferred from transaction inputs to transaction outputs in first-in-first-out order.”\n\nBy serializing these individual satoshis and utilizing the Taproot upgrade, Bitcoin users can also include arbitrary data directly on the blockchain. While this was already possible with text using the OP_RETURN function, these new “inscriptions” can be anything from jpegs, short sound clips and even simple games.\n\nThere is growing debate in the development community about the implications of storing all this data directly on Bitcoin and what that means for users who want to run a full archival node. While this discussion is important, we want to dig into how inscriptions are currently impacting Bitcoin’s fee market and how it might look in the future.\n\nEfficient Use Of Block Space\n\nBy their nature, inscriptions are larger files and therefore take up more of the finite space in each Bitcoin block. The users that are creating inscriptions are required to pay the necessary fees in order to send their transactions, however, inscriptions are included in witness data which is given a slight fee discount thanks to the SegWit soft fork in 2017.\n\nOrdinals officially launched on January 21, 2023. Less than three weeks later, inscriptions are already taking up 50% of Bitcoin’s block space according to Pierre Rochard, vice president of research at Riot Platforms.\n\nBitcoin’s fee market is a constantly changing landscape. Fees rise when demand to transact on-chain is high and users want to get their transaction included in the next block. Inversely, the fee rate drops when demand is low and users don’t need their transactions confirmed in a timely manner.\n\nWhether or not these inscriptions should be considered an “acceptable” use of Bitcoin, the market will decide the appropriate fee pricing for those who wish to include this arbitrary data into each block. Should transaction fees rise enough, it’s likely that less important or smaller bitcoin transactions will be priced out of the market and move to Layer 2 protocols, such as Lightning. These additional layers were always the game-theoretical hypothesis of Bitcoin’s fee structure, even predicted by Hal Finney in 2010.\n\nHistorical Block Weight\n\nThis is not the first time that a significant number of transactions have filled the mempool. As noted, Bitcoin’s fee market is dynamic and the cycle of high fees create efficient uses of block space, create low fees, create inefficient use of block space, create high fees will repeat ad infinitum.\n\nShown below is mempool data and fee prices going back to the beginning of 2017. Blockspace tends to be at a premium during bull runs as many people are sending bitcoin back and forth from exchanges or cold storage or spending it at the relatively high exchange rate.\n\nZooming in on the past three months, it’s clear that there was a significant number of transactions happening in the second half of November as bitcoin flew off exchanges with users protecting themselves from any other potential contagion events.\n\nBeyond extreme cases, transaction fees have been low for long stretches of time and have led to questions about Bitcoin’s long-term security budget as the block subsidy dwindles and fees must become a larger percentage of bitcoin miners’ revenue. Again, the hypothesis from Bitcoin proponents is that demand for block space will increase over time as bitcoin gains adoption and scales, causing more usage to migrate to other layers built on top of the protocol.\n\nIn the last few weeks, the average block size has seen a massive spike.\n\nEven with this major increase in block size, fee market competition has yet to heat up. It’s likely that those who wish to send monetary transactions will increase their fees to get their transaction included more quickly or those who want to mint an inscription without having to wait will do the same. Either way, should fees increase, so will profitability for miners who would collect additional revenue in the block reward in the form of higher transaction fees.\n\nTransaction fees are still an insignificant percentage of the mining block reward, falling somewhere between 1% and 3%. Will fees begin to rise as more and more people attempt to use bitcoin for sending money and minting inscriptions?\n\nLike this content? Subscribe now to receive PRO articles directly in your inbox.\n\nRelevant Past Articles:\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}]