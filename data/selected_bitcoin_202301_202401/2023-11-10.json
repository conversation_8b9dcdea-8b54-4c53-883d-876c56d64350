[{"id": 3, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTExLTEwL2JpdGNvaW4tZXRmLWV4dWJlcmFuY2UtZHJpdmVzLWZvdXItd2Vlay1ub3RoaW5nLWZvci1zYWxlLXJhbGx50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Nov 2023 08:00:00 GMT", "title": "Bitcoin ETF Exuberance Drives Four-Week 'Nothing for Sale' Rally - Bloomberg", "content": "Bitcoin is climbing for a fourth consecutive week, with the digital token’s price lingering just below an 18-month high of $38,000, as more investors bet that US exchange-traded funds that hold the largest cryptocurrency are on the verge of winning regulatory approval.\n\n“We are referring to the current Bitcoin rallies internally as, ‘nothing for sale’ rallies, said <PERSON><PERSON> , co-founder and chief executive of FRNT Financial. “On even the slightest positive news, whether ETF related or otherwise, Bitcoin is seeing very little resistance to the upside and is having the propensity to ‘gap.’ The dynamic speaks to the impressively strong price base Bitcoin has developed post-FTX.”"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTExLTEwL2JpdGNvaW4tZXRmLWV4dWJlcmFuY2UtZHJpdmVzLWZvdXItd2Vlay1ub3RoaW5nLWZvci1zYWxlLXJhbGx50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Nov 2023 08:00:00 GMT", "title": "Bitcoin ETF Exuberance Drives Four-Week 'Nothing for Sale' Rally - Bloomberg", "content": "Bitcoin is climbing for a fourth consecutive week, with the digital token’s price lingering just below an 18-month high of $38,000, as more investors bet that US exchange-traded funds that hold the largest cryptocurrency are on the verge of winning regulatory approval.\n\n“We are referring to the current Bitcoin rallies internally as, ‘nothing for sale’ rallies, said <PERSON><PERSON> , co-founder and chief executive of FRNT Financial. “On even the slightest positive news, whether ETF related or otherwise, Bitcoin is seeing very little resistance to the upside and is having the propensity to ‘gap.’ The dynamic speaks to the impressively strong price base Bitcoin has developed post-FTX.”"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTExLTEwL2JpdGNvaW4tZXRmLWV4dWJlcmFuY2UtZHJpdmVzLWZvdXItd2Vlay1ub3RoaW5nLWZvci1zYWxlLXJhbGx50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Nov 2023 08:00:00 GMT", "title": "Bitcoin ETF Exuberance Drives Four-Week 'Nothing for Sale' Rally - Bloomberg", "content": "Bitcoin is climbing for a fourth consecutive week, with the digital token’s price lingering just below an 18-month high of $38,000, as more investors bet that US exchange-traded funds that hold the largest cryptocurrency are on the verge of winning regulatory approval.\n\n“We are referring to the current Bitcoin rallies internally as, ‘nothing for sale’ rallies, said <PERSON><PERSON> , co-founder and chief executive of FRNT Financial. “On even the slightest positive news, whether ETF related or otherwise, Bitcoin is seeing very little resistance to the upside and is having the propensity to ‘gap.’ The dynamic speaks to the impressively strong price base Bitcoin has developed post-FTX.”"}, {"id": 40, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9wYXN0LXRocmVlLXllYXJzLWJpdGNvaW4tZ3JvdXAtMDc1NDIwMDc3Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Nov 2023 08:00:00 GMT", "title": "The past three years for Bitcoin Group (ETR:ADE) investors has not been profitable - Yahoo Finance", "content": "Bitcoin Group SE (ETR:ADE) shareholders should be happy to see the share price up 11% in the last month. But that doesn't change the fact that the returns over the last three years have been less than pleasing. In fact, the share price is down 21% in the last three years, falling well short of the market return.\n\nNow let's have a look at the company's fundamentals, and see if the long term shareholder return has matched the performance of the underlying business.\n\nCheck out our latest analysis for Bitcoin Group\n\nThere is no denying that markets are sometimes efficient, but prices do not always reflect underlying business performance. By comparing earnings per share (EPS) and share price changes over time, we can get a feel for how investor attitudes to a company have morphed over time.\n\nBitcoin Group saw its EPS decline at a compound rate of 16% per year, over the last three years. In comparison the 8% compound annual share price decline isn't as bad as the EPS drop-off. So the market may not be too worried about the EPS figure, at the moment -- or it may have previously priced some of the drop in. With a P/E ratio of 65.60, it's fair to say the market sees a brighter future for the business.\n\nThe image below shows how EPS has tracked over time (if you click on the image you can see greater detail).\n\nWe know that Bitcoin Group has improved its bottom line lately, but is it going to grow revenue? You could check out this free report showing analyst revenue forecasts.\n\nA Different Perspective\n\nIt's nice to see that Bitcoin Group shareholders have received a total shareholder return of 9.1% over the last year. And that does include the dividend. That certainly beats the loss of about 3% per year over the last half decade. We generally put more weight on the long term performance over the short term, but the recent improvement could hint at a (positive) inflection point within the business. I find it very interesting to look at share price over the long term as a proxy for business performance. But to truly gain insight, we need to consider other information, too. Take risks, for example - Bitcoin Group has 2 warning signs we think you should be aware of.\n\nStory continues\n\nOf course, you might find a fantastic investment by looking elsewhere. So take a peek at this free list of companies we expect will grow earnings.\n\nPlease note, the market returns quoted in this article reflect the market weighted average returns of stocks that currently trade on German exchanges.\n\nHave feedback on this article? Concerned about the content? Get in touch with us directly. Alternatively, email editorial-team (at) simplywallst.com.\n\n\n\nThis article by Simply Wall St is general in nature. We provide commentary based on historical data and analyst forecasts only using an unbiased methodology and our articles are not intended to be financial advice. It does not constitute a recommendation to buy or sell any stock, and does not take account of your objectives, or your financial situation. We aim to bring you long-term focused analysis driven by fundamental data. Note that our analysis may not factor in the latest price-sensitive company announcements or qualitative material. Simply Wall St has no position in any stocks mentioned."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiRWh0dHBzOi8vd3d3LmludmVzdG9wZWRpYS5jb20vdGVjaC9iaXRjb2luLWxpZ2h0bmluZy1uZXR3b3JrLXByb2JsZW1zL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 10 Nov 2023 08:00:00 GMT", "title": "Bitcoin's Lightning Network: 3 Possible Problems - Investopedia", "content": "The Lightning Network is a second layer added to Bitcoin's network, enabling transactions between parties off the main blockchain—called off-chain transactions. Lightning Network has often been advertised as a game-changer in the cryptocurrency's evolution. It is designed to speed up transaction processing times and decrease the associated costs of Bitcoin's blockchain. The lightning network was conceived by two developers, <PERSON><PERSON><PERSON><PERSON> and <PERSON> in 2015.\n\nAlthough the Lightning Network has experienced growth and development since its inception, challenges remain. Bitcoin's price fluctuations have prevented the crypto from becoming a widespread payment method for consumer and business transactions. Also, there are costs involved in using Lightning Network.\n\nHere, we highlight what the Lightning Network is designed to solve and three problems that it faces. Also, we review recent developments that could impact and improve the network in the future.\n\nKey Takeaways The Lightning Network is a second layer added to Bitcoin's network, enabling transactions to be done off of the blockchain.\n\nThe Lightning Network is designed to speed up transaction processing times and decrease the associated costs of Bitcoin's blockchain.\n\nHowever, the Lightning Network still has costs associated with it and can be susceptible to fraud or malicious attacks.\n\nBitcoin's price swings may prevent the crypto from becoming a popular payment method, limiting the use of the Lightning Network.\n\nUnderstanding the Lightning Network\n\nAs Bitcoin gains transaction volume, more are processed on its blockchain network. This presents problems because the blockchain, in its current state, isn't designed to scale or process the volume of transactions being made.\n\nBitcoin's Scalability Issue\n\nThe Bitcoin blockchain and network are designed to process one block about every 10 minutes. Transactions are sent into a work queue, where they are prioritized by how much the user paid in fees. The more transactions there are, the larger the queue is.\n\nAs a result, Bitcoin has faced a scalability issue, meaning there are challenges when the network tries to process more transactions simultaneously. For Bitcoin to process more data, the network needs to scale, allowing more transactions to be processed quicker and more efficiently.\n\nThis network latency has led to higher transaction fees as miners take longer to validate transactions because users pay more to prioritize them.\n\nWhat It Is Designed to Do\n\nThe Lightning Network is a separate blockchain that works in conjunction with Bitcoin's blockchain. In a nutshell, the Lightning Network allows participants to transfer bitcoins between one another much quicker using payment channels. Channels can remain open for further payments or closed when a transaction is complete. Instead of waiting for the main network to get through its work queue for the payment to transfer (sometimes taking more than an hour), the Lightning Network lets users send and receive payments in seconds.\n\n1. It Does Not Solve Bitcoin’s Transaction Fee Problem\n\nLightning Network is often touted as a solution to the problem of Bitcoin’s rising transaction fees. Its proponents claimed that transaction fees, one of the direct consequences of Bitcoin’s clogged network, would come down after the technology took transactions off the main blockchain.\n\nBut Bitcoin’s congestion is among several factors influencing its transaction fees. When the Lightning Network was integrated in 2018, expectations for reduced costs and faster transactions were high among users—but as the graph below shows, average Bitcoin transaction fees increased. There could be many reasons for this, but it demonstrates how ineffective the Lightning Network was at reducing fees.\n\nBlockchain.com\n\nLightning Network Fees\n\nIn addition to standard on-chain transaction charges, users are charged for opening a channel with a routing node. The routing node operator charges fees for providing the Lightning channel to the main network. Fees consist of a base fee and a rate, both chosen by the operator. The base fee remains consistent unless manually changed, and the rate is a percentage of the transaction value.\n\nSo, a node operator could set the base fee at one satoshi and the rate at 0.1%. If a user wanted to send 1,000 satoshi via this node, they would need to pay 2 satoshi to the node (fees on the Lightning Network can be measured in increments of milli-satoshi, so the payments can actually be very small).\n\nLightning fees are generally low because hosting a node is a competitive endeavor. Fees must be low enough to attract users but high enough to bring some benefit to the host. If they are too low, there might be no reason to operate a node—if they are too high, users will likely choose a lower-priced node.\n\nInterestingly (and regardless of node fees), by design, the Lightning Network incorporates fees to try and lower overall fees.\n\n2. Remaining Online at All Times Makes Nodes Susceptible\n\nNodes on the Lightning Network are required to be online at all times to send and receive payments. Since the parties involved in the transaction must be online and use their private keys to sign in, it's possible that the coins could be stolen if the computer hosting the node was compromised.\n\nOffline Transaction Risk\n\nGoing offline creates its own set of problems on the Lightning Network. According to Dryja, it is possible for one of the two parties from a payment channel to close the channel and pocket funds while the other is away. This is known as a Fraudulent Channel Close. There is a period to contest the closing of a channel, but a prolonged absence by one of the parties could result in the expiry of that period.\n\nMalicious Attacks\n\nAnother risk to the network is congestion caused by a malicious attack. If the payment channels become congested, and there's a malicious hack or attack, the participants may not be able to get their money back fast enough due to the congestion.\n\nAccording to Dryja, \"forced expiration of many transactions may be the greatest systemic risk when using the Lightning Network.\"\n\nIf a malicious party creates numerous channels and forces them to expire simultaneously, which would broadcast to the blockchain, the congestion caused could overwhelm the capacity of the block. A malicious attacker might use the congestion to steal funds from parties who are unable to withdraw their funds due to the congestion.\n\n3. Bitcoin's Price Fluctuations\n\nThe Lightning Network is also supposed to herald Bitcoin's viability as a medium for daily transactions. Customers are able to open payment channels with businesses or people they transact with frequently. For example, they can open payment channels with their landlord or favorite e-commerce store and transact using bitcoins.\n\nHowever, Bitcoin has less traction as a mainstream payment method than as an investment instrument. The increase in transaction volume is primarily attributed to a rise in trading volume. In other words, Bitcoin's popularity with traders and investors increases its volatility—or price fluctuations—as well as congesting the network and influencing fee increases.\n\nRecent Lightning Network Developments\n\nThere remain challenges with Bitcoin's Lightning Network and its ability to boost scale while simultaneously lowering transaction fees. However, the technology's core team has incorporated new use cases and has been researching additional features. As a result, there have been significant developments that attempt to improve the network.\n\nLarger Payments Via Lightning Network\n\nLightning had initially limited channel size to a maximum of 0.1677 BTC, but in 2020, it was announced that the constraints would be removed so clients could have larger channels. These \"Wumbo\" channels are designed to increase the usage and utility of Lightning Network for consumers and businesses.\n\nCrypto Exchanges\n\nOne of the most promising initial use cases involves cryptocurrency exchanges and financial services platforms. For instance, Kraken and Block's Cash App have integrated the Lighting Network. In September 2023, Coinbase CEO Brian Armstrong announced the exchange would integrate the Lightning Network. As one of the largest cryptocurrency exchanges, this is a significant development for the network.\n\nWatchtowers\n\nWatchtowers are third parties that run to prevent fraud within the Lightning Network. For example, if Sam and Judy are transacting and one of them has malicious intent, they may be able to steal the coins from the other participant by closing the channel.\n\nSo, say Sam and Judy put up an initial deposit of 10,000 BTC, and a transaction of 3,000 BTC has taken place in which Sam purchased goods from Judy. If Judy logs off her system, it is open to possible fraud. Sam could broadcast the initial state, meaning they both get their initial deposits back as if no transactions were done. In other words, Sam would have received 3,000 BTC worth of goods for free.\n\nThis process of closing the channel based on the initial state versus the final state in which all of the transactions have been done is called a fraudulent channel close. The watchtower monitors transactions and prevents fraudulent channel closes by forcing a close on the offending party. It broadcasts a revocation transaction and causes them to forfeit their channel balance to the other party.\n\nIs the Lightning Network Part of Bitcoin? The Lightning Network is a layer 2 (a blockchain that assists a primary blockchain) for the Bitcoin blockchain. It was integrated with Bitcoin in 2018.\n\nHow Do You Use the Bitcoin Lightning Network? To complete transactions using the Bitcoin Lightning Network, you will need to use a Lightning-compatible wallet.\n\n\n\nHow Fast Is the Bitcoin Lightning Network? The Lightning Network can reportedly process millions of transactions per second. The main Bitcoin blockchain can process around seven a second.\n\n\n\nThe Bottom Line\n\nThe Lightning Network is a tool that could make a significant difference to Bitcoin's blockchain. However, the network might not solve all of the challenges Bitcoin faces. While improvements are being made, there's the potential for new problems within the cryptocurrency's ecosystem because it remains an ever-evolving technology.\n\nThe comments, opinions, and analyses expressed on Investopedia are for informational purposes only. Read our warranty and liability disclaimer for more info."}]