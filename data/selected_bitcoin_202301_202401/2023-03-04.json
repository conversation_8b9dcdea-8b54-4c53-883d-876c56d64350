[{"id": 2, "url": "https://news.google.com/rss/articles/CBMibGh0dHBzOi8vYXUuZmluYW5jZS55YWhvby5jb20vbmV3cy9tYW4taGFzLXR3by1tb3JlLWNoYW5jZXMtYmVmb3JlLTIzMi1taWxsaW9uLWlzLWxvc3QtZm9yZXZlci0yMjIwNDUxMDEuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 04 Mar 2023 08:00:00 GMT", "title": "Man has two more chances before $232 million is lost forever - Yahoo Finance Australia", "content": "The German-born man has used eight of his 10 attempts to access his Bitcoin. (Source: <PERSON><PERSON>)\n\nBack when Bitcoin was worth only $5 a coin, <PERSON> was paid 7,002 of them for making a video explaining how the cryptocurrency worked, which was promptly stored in a digital wallet.\n\n<PERSON> stored the private keys to access the wallet on a small hard drive, which was also password protected. That password, however, was written on a piece of paper and sadly lost.\n\nThe 7,002 Bitcoin are now worth more than $232 million and <PERSON> has just two attempts left to guess the password before it's encrypted and lost forever.\n\nADVERTISEMENT\n\n\"I would just lay in bed and think about it,\" <PERSON> told The New York Times. \"Then I would go to the computer with some new strategy, and it wouldn't work, and I would be desperate again.\"\n\nStrangely, this is not the first time someone has lost millions of dollars worth of Bitcoin. Welshman <PERSON> accidentally threw out a hard drive filled with around $265 million worth of Bitcoin.\n\n<PERSON><PERSON> has not given up his search in the local dump, hopeful that one day he will find his hard drive.\n\nIs crypto coming back?\n\nCryptocurrencies closed out February largely unchanged after an enthusiastic rally kicked off 2023.\n\nIn February, Ether (ETH-USD), the market's second-largest cryptocurrency, outpaced its larger peer Bitcoin (BTC-USD) rising 1.6 per cent against Bitcoin's more modest 0.4 per cent increase during the month. In January, Bitcoin gained nearly 40 per cent to record its best start to a year since 2013.\n\nSince the beginning of 2023, Bitcoin watchers have focused closely on whether Bitcoin's price could breach US$25,000.\n\nFollow Yahoo Finance on Facebook, LinkedIn, Instagram and Twitter, and subscribe to our free daily newsletter."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiYGh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9qYWNrLWRvcnNleS1zLXRiZC1sYXVuY2hlcy1jLXRvLWltcHJvdmUtYml0Y29pbi1saWdodG5pbmctbmV0d29ya9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 04 Mar 2023 08:00:00 GMT", "title": "<PERSON>'s TBD launches C= to improve Bitcoin Lightning Network - Cointelegraph", "content": "TBD, a division of Block (formerly Square) led by CEO <PERSON>, launched a new venture named c= (pronounced c equals) to improve the Bitcoin Lightning Network through tools and services.\n\nThe Lightning Network (LN) is a layer 2 payment network built to ease the mainstream adoption of Bitcoin (BTC) by enabling faster, cheaper and more reliable peer-to-peer payments. However, c= aims to further the reach of LN through added liquidity and routing services.\n\nSince its launch, the LN’s liquidity and capacity have witnessed organic growth via real-world adoption. In addition, services like c= offer incremental upgrades to support the ongoing Bitcoin adoption globally.\n\nVisual representation of widespread Bitcoin Lightning adoption. Source: c=\n\nThrough liquidity, services and infrastructure, c= caters to wallet users, businesses and lightning node operators for faster and cheaper payments. The official announcement read:\n\n“We want to meet you where your lightning needs are. Are you a business looking to accept Lightning payments? A wallet in need of channels or inbound for your customers? A hardened plebnet veteran looking for your next big source?”\n\nLayer 2 services collectively improving Bitcoin operations make it easier for people to adopt the ecosystem into their lives. If you want to accept Bitcoin as payment for your services, read Cointelegraph’s guide on how to get paid in BTC.\n\nRelated: <PERSON>’s decentralized Twitter rival enters app store\n\n<PERSON>’s popular payments venture Cash App recently integrated crypto tax and accounting software TaxBit into its services. The move allows Bitcoin users an easy way to report taxes.\n\nAs Cointelegraph reported, Cash App launched its Bitcoin trading services in 2018 and rolled out BTC deposits the following year. The company claims to have over 10 million Bitcoin users."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vd3d3LmFuYWx5dGljc2luc2lnaHQubmV0LzEwLWZhY3RvcnMtdGhhdC1hcmUtaW5mbHVlbmNpbmctYml0Y29pbi12YWx1ZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 04 Mar 2023 08:00:00 GMT", "title": "10 Factors That Are Influencing Bitcoin Value - Analytics Insight", "content": "10 factors that influence Bitcoin value are listed in this article, supply and demand is the primary factor.\n\nBitcoin is a decentralized cryptocurrency that has experienced significant volatility since its inception in 2009. The value of Bitcoin is influenced by several factors that can cause fluctuations in its price. Here are ten factors that influence Bitcoin value:\n\n1. Supply and Demand: Similar to other currencies and assets, the basic economic principle of supply and demand plays a crucial role in determining Bitcoin’s price. The scarcity of Bitcoin, with a maximum supply of 21 million, and the increasing demand for it are factors that can influence its value.\n\n2. Institutional Adoption: As more institutions and companies adopt Bitcoin, its value can increase. The involvement of large corporations such as Tesla and MicroStrategy has already had a positive impact on Bitcoin’s value.\n\n3. Regulatory Changes: Any changes in government regulations or policies that impact Bitcoin’s use or acceptance can affect its value. News of potential regulation or crackdowns on cryptocurrencies can lead to a drop in their price.\n\n4. News and Market Sentiment: Bitcoin’s value can also be influenced by market sentiment and the media’s portrayal of it. Positive news, such as a major company accepting Bitcoin as payment, can increase its value, while negative news can decrease its value.\n\n5. Technological Advancements: Bitcoin is based on blockchain technology, and advancements in this field can impact its value. Improvements in the network’s speed and efficiency can increase demand for Bitcoin, while technical problems can lead to a drop in its value.\n\n6. Integration with Other Technologies: Bitcoin’s integration with other technologies, such as the Lightning Network, can increase its utility and value.\n\n7. Energy Consumption: The high energy consumption required for Bitcoin mining is a factor that can impact its value. News of increased regulation or pressure on Bitcoin miners to become more energy-efficient can lead to a drop in its value.\n\n8. Security Concerns: Any security concerns or hacking incidents involving Bitcoin can lead to a decrease in its value.\n\n9. Competition: The rise of other cryptocurrencies can also impact Bitcoin’s value. If a new cryptocurrency gains popularity and acceptance, it can reduce demand for Bitcoin and decrease its value.\n\n10. Economic and Political Stability: Bitcoin’s value can also be impacted by global economic and political instability. In times of economic uncertainty or political turmoil, Bitcoin may be viewed as a safe haven asset and see an increase in value. However, in times of stability, its value may decrease.\n\nOne of the primary reasons why cryptocurrency is influenced by external factors is because it is not yet widely adopted and integrated into the global financial system. It is still a relatively new and emerging asset class, and as such, its value can be more volatile and subject to market sentiment and news than established assets. Additionally, cryptocurrency is often subject to regulatory changes and policy decisions made by governments and financial institutions. News of potential regulatory action or crackdowns on cryptocurrencies can lead to a drop in their price.\n\nJoin our WhatsApp and Telegram Community to Get Regular Top Tech Updates"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiuwFodHRwczovL3d3dy5nbG9iZW5ld3N3aXJlLmNvbS9lbi9uZXdzLXJlbGVhc2UvMjAyMy8wMy8wNC8yNjIwNjIzLzAvZW4vT0RvZ2UtU29saWRpZmllcy1pdHMtUGxhY2UtaW4tQml0Y29pbi1hbmQtTWVtZWNvaW4tSGlzdG9yeS13aXRoLUFjcXVpc2l0aW9uLW9mLUZpcnN0LWV2ZXItRE9HRS1PcmRpbmFsLWZvci0xMEJUQy5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 04 Mar 2023 08:00:00 GMT", "title": "ODoge Solidifies its Place in Bitcoin and Memecoin History with Acquisition of First-ever DOGE Ordinal for 10BTC - GlobeNewswire", "content": "ONTARIO, CANADA, March 04, 2023 (GLOBE NEWSWIRE) -- Every day in the crypto sector is marked by the advent of innovation. A recent innovation that has taken the crypto sphere by storm is the ‘Ordinals’. Within months of its inception, this unique invention has managed to allure a plethora of investors.\n\nIn January 2023, a renowned software engineer, <PERSON> revived the lost glory of Bitcoin when he deployed the Ordinals Protocol on the Bitcoin Network. With this protocol, users can now create Non-fungible tokens (NFTs) on the platform. In simple words, Ordinals can be called ‘Bitcoin NFTs’.\n\nOrdinal <PERSON>ecoin, the first-ever real memecoin on the Bitcoin blockchain, is pleased to announce to the oDOGE community that $oDOGE Emblem Vault has successfully acquired Ordinal Inscription #5768, the true first-ever DOGE on Bitcoin, in an OTC trade for the value of 10BTC in $oDOGE tokens, facilitated by a well-known Bitcoin OG and OTC intermediary.\n\nThe acquisition, which is the second-highest Ordinal sale to date, solidifies the $oDOGE token's place in Bitcoin blockchain and memecoin history and is expected to fuel its growth and popularity in the coming months, forever marking a new era of Bitcoin meme culture.\n\nOrdinal Inscription #5768, the first DOGE Ordinal EVER (as well as an extremely low mint Ordinal)\n\nOrdinal Inscription #5768 will be held forever at the Emblem Vault address with the $oDOGE Logo Ordinal.\n\nWhat does this mean for $oDOGE?\n\nWith the latest breakthrough, Ordinal Technology is expected to gain greater traction in the blockchain community, and $oDOGE is leading the way as a distinctive feature coin that is safe for mining, much like other well-known cryptocurrencies from JPEGs on Bitcoin and memecoin on Bitcoin Original. The blockchain compensates miners for their efforts by generating new Dogecoin each day.\n\nThe acquisition of the first ever DOGE ordinal for 10BTC by $oDOGE is a significant milestone in the development of the crypto sphere. It solidifies $oDOGE's place in Bitcoin and meme coin history, and the team behind the token is committed to continuing to innovate and provide new features that meet the needs of its growing community.\n\nA Quick Overview of oDOGE\n\nThe Ordinals Protocol, which was recently deployed on the Bitcoin Network, allows users to create non-fungible tokens (NFTs) on the platform. With the Ordinals Protocol, the $oDOGE token combines conventional and smart contracts on Ethereum to unlock the power of Bitcoin and create a distinctive feature coin that is gaining popularity in the crypto sphere.\n\nThe $oDOGE token has been fully fractionalized, wrapped, and integrated as an ERC-721 or ERC-20 token, allowing dApps and DEXes on ETH Miannet to interact with the original $oDOGE.\n\nMoreover, Ordinal $oDOGE tokenomics is used to provide the token for tax and start liquidity. The token supply consists of 1,000,000,000,000,000 $oDOGand the users will pay a 0% tax while buying or selling $oDOGE.\n\nIn a quick recap of the first 5 days of oDOGE’s launch, 50% of the supply has been burnt, and 15 ETH in BURNED LP. $oDOGE saw a total of $30 million+ daily volume, with the total number of $oDOGE holders reaching 1,020.\n\noDODE has been gaining huge support from Bitcoin OGs, Memecoin OGs, and several blockchain influencers, and the oDOGE community is growing every day. Furthermore, with upcoming partnerships and marketing plans in the pipeline, oDOGE is set to reach newer heights in the coming weeks.\n\nAbout the Project - Ordinal Dogecoin\n\nOrdinal Dogecoin is a newly designed original memecoin created by combining ordinal and smart contracts on Ethereum. The ordinal technology unlocked the true potential of the Bitcoin Network in the way of $oDOGE. The $oDOGE is gaining popularity because of its unique features and secure transaction. It is designed by combining ordinal and smart contracts on Ethereum, which is entirely facilitated on the Bitcoin Blockchain.\n\nFurthermore, potential investors and cryptocurrency enthusiasts interested in Ordinal Dogecoin can visit the project’s official website or check out their social platforms for more details.\n\nWebsite | Twitter | Telegram | Medium | Uniswap | DEXTools Chart\n\n\n\nDisclaimer:\n\nThe information provided in this release is not investment advice, financial advice, or trading advice. It is recommended that you practice due diligence (including consultation with a professional financial advisor) before investing or trading securities and cryptocurrency."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vd3d3LnRla2VkaWEuY29tL2NvbnRyb3ZlcnN5LWJlaGluZC1uZXctb3JkaW5hbC1iaXRjb2luLW5mdHMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 04 Mar 2023 08:00:00 GMT", "title": "Controversy Behind new Ordinal Bitcoin NFTs - Tekedia", "content": "Ordinal Bitcoin NFTs have two key features that distinguish them from others. Firstly, they are made up of on-chain data, meaning the actual image for the NFT is stored directly on the blockchain, rather than being linked to an external website like most NFTs on Ethereum.\n\nThis makes them unique and more secure, as the data cannot be lost or altered without affecting the blockchain itself. <PERSON>, a Swedish researcher and former Arcane Assets CIO, believes that storing fully on-chain NFTs is now seven times cheaper on Bitcoin compared to Ethereum.\n\nSecondly, NFTs are linked to individual satoshis, unlike Ethereum NFTs, which have their own token. This creates a connection between the NFT and the underlying asset, Bitcoin.\n\nHowever, it can be challenging to maintain this connection because Bitcoins are fungible, meaning each one is interchangeable and can only be differentiated through a complex transaction input-output system.\n\nTo solve this issue, Ordinals uses a shared numbering system that assigns every satoshi an ordinal number based on the order in which it was mined. This numbering system, along with other details, is used to maintain continuity for NFTs.\n\nIt’s important to note that Bitcoin does not natively support these features, and NFT holders may accidentally spend their NFTs on transaction fees if they are not careful.\n\nThe world of crypto, DeFi, blockchain, NFTs etc is a very community-focused space. And with every new development, the community ferociously discusses their opinions of it.\n\nThe supporters of the project suggest that it will have an overall favourable impact on the Bitcoin ecosystem. They believe that the integration of NFTs on the Bitcoin blockchain is a positive development that will bring more fees and use cases to the chain. This view is held by prominent Bitcoin influencer Dan Held, who argues that every transaction paying its fee is not spam and that the Bitcoin blockchain is open and accessible for anyone to build upon.\n\nSome individuals believe that incorporating NFTs on the Bitcoin blockchain will negatively impact its financial and transactional use case. The CEO of Blockstream Adam Back, who is rumoured to be Satoshi Nakamoto, is a part of this group. He supports this argument and has suggested that Bitcoin users must “educate and encourage developers who care about bitcoin’s use-case to either not do that, or do it in a prunable space-efficient e.g. time-stamp way.”\n\nHe also took to Twitter to express his views on the project. He said that “”you can’t stop them” well of course Bitcoin is designed to be censor resistant. doesn’t stop us mildly commenting on the sheer waste and stupidity of an encoding, at least do something efficient. Otherwise it’s another proof of the consumption of block-space thingy.”\n\nOn the other hand, Casey Rodarmor responded to the criticism with the following arguments,\n\nI actually love the haters,” he said. “I mean, they do more to drive people to find out about the project than anybody else. I don’t know what they think when they have these massive audiences, and they go, ‘This is an attack on Bitcoin’—it seems like you don’t want to do that if you don’t want people to use the thing.\n\nHe also added counterpoints that stated\n\n“My design goal, from the beginning, was to create something that would strike people as being Bitcoin native. That means it can’t have a token, and it can’t be a sidechain. One thing that people don’t understand is that in order for Bitcoin to be secure, blocks must be full—that is part of the Bitcoin security model,” he said. “If blocks are not full, nobody has any reason to pay more than the minimum fee rate to have their transactions included. So, as a result, blocks must be full.”\n\nThe hype surrounding the Ordinals NFT has driven the price of the Stacks cryptocurrency (STX) up nearly 200% as of February. Ordinal NFTs, which are similar to Ethereum and other smart contract-based NFTs, use the smallest unit of bitcoin, the satoshi, to encode digital art, profile pictures, videos, audios and images directly on the bitcoin blockchain.\n\n“The full potential of the Stacks network is beginning to be recognized which could drive the STX token rally even further,” Thielen said.\n\nThe NFT craze has faded in recent months amid the bitcoin, ethereum, and crypto price crash that wiped off nearly $2 trillion from the combined crypto market. The merger of NFTs and the bitcoin network provides greater security, transparency, and traceability, opening up more use cases and rekindling interest around NFTs.\n\nShare this: Facebook\n\nTwitter\n\nWhatsApp\n\nLinkedIn\n\nEmail\n\nPrint\n\n"}]