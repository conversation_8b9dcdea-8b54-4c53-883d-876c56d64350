[{"id": 4, "url": "https://news.google.com/rss/articles/CBMic2h0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA1LTI2L2JpdGNvaW4tcy13YWl0LWFuZC1zZWUtbW9kZS1wdXRzLXZvbGF0aWxpdHktYXQtbG93ZXN0LXNpbmNlLTIwMjDSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 26 May 2023 07:00:00 GMT", "title": "Bitcoin's Volatility Drops to Lowest Since 2020 While AI Tokens Take Off - Bloomberg", "content": "The typically hyper-volatile Bitcoin hasn’t been so choppy of late at all.\n\nThe largest digital asset hasn’t posted a daily move of 6% for 70 sessions, the longest streak of calm since October 2020, data compiled by Bloomberg show. And the coin is on pace for a slight loss in May after gaining each of the first four months of the year."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMic2h0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA1LTI2L2JpdGNvaW4tcy13YWl0LWFuZC1zZWUtbW9kZS1wdXRzLXZvbGF0aWxpdHktYXQtbG93ZXN0LXNpbmNlLTIwMjDSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 26 May 2023 07:00:00 GMT", "title": "Bitcoin's Volatility Drops to Lowest Since 2020 While AI Tokens Take Off - Bloomberg", "content": "The typically hyper-volatile Bitcoin hasn’t been so choppy of late at all.\n\nThe largest digital asset hasn’t posted a daily move of 6% for 70 sessions, the longest streak of calm since October 2020, data compiled by Bloomberg show. And the coin is on pace for a slight loss in May after gaining each of the first four months of the year."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMicmh0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvYml0Y29pbi1jb3JlLXZlcnNpb24tMjUuMC1yZWxlYXNlZC1icmluZ2luZy1uZXctZmVhdHVyZXMtYW5kLXRyYW5zYWN0aW9uLXVzZS1jYXNlc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 26 May 2023 07:00:00 GMT", "title": "Bitcoin Core Version 25.0 Released Bringing New Features And Transaction Use Cases - Nasdaq", "content": "Bitcoin Core version 25.0 has been released, bringing new features, bug fixes and performance improvements to the software. Users can download the latest version from the official Bitcoin Core website or update their existing installations. The release notes outline the changes and provide instructions on how to upgrade.\n\nOne notable change is the allowance of transactions of non-witness size 65 bytes and above, which opens up new use cases and enhances protections against vulnerabilities like CVE-2017-12842. Another addition is the scanblocks RPC, which allows for fast wallet rescans by returning relevant blockhashes from a set of descriptors.\n\nIn terms of RPC updates, all JSON-RPC methods now accept a new named parameter called \"args\" for passing positional parameter values conveniently. Additionally, the verifychain RPC will now return false if checks couldn't be completed at the desired depth and level, providing more accurate feedback.\n\nThe release also includes changes to the build system, updated settings and new features. For instance, the shutdownnotify option allows users to specify a command to execute before Bitcoin Core begins its shutdown sequence. In the wallet section, new options like minconf and maxconf have been added to various RPCs, providing greater control over UTXO confirmations.\n\nBitcoin Core 25.0 introduces several improvements and optimizations, enhancing the user experience and security of the software. As with any release, users are encouraged to report any bugs they encounter to the official GitHub issue tracker.\n\nBitcoin Core is widely supported and tested on major operating systems such as Linux, macOS, and Windows. However, it is not recommended to use Bitcoin Core on unsupported systems. Users should ensure they are running a compatible operating system before upgrading to the latest version. In addition, users are encouraged to review the release notes for a detailed understanding of the improvements in this version if upgrading.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 30, "url": "https://news.google.com/rss/articles/CBMicmh0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvYml0Y29pbi1jb3JlLXZlcnNpb24tMjUuMC1yZWxlYXNlZC1icmluZ2luZy1uZXctZmVhdHVyZXMtYW5kLXRyYW5zYWN0aW9uLXVzZS1jYXNlc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 26 May 2023 07:00:00 GMT", "title": "Bitcoin Core Version 25.0 Released Bringing New Features And Transaction Use Cases - Nasdaq", "content": "Bitcoin Core version 25.0 has been released, bringing new features, bug fixes and performance improvements to the software. Users can download the latest version from the official Bitcoin Core website or update their existing installations. The release notes outline the changes and provide instructions on how to upgrade.\n\nOne notable change is the allowance of transactions of non-witness size 65 bytes and above, which opens up new use cases and enhances protections against vulnerabilities like CVE-2017-12842. Another addition is the scanblocks RPC, which allows for fast wallet rescans by returning relevant blockhashes from a set of descriptors.\n\nIn terms of RPC updates, all JSON-RPC methods now accept a new named parameter called \"args\" for passing positional parameter values conveniently. Additionally, the verifychain RPC will now return false if checks couldn't be completed at the desired depth and level, providing more accurate feedback.\n\nThe release also includes changes to the build system, updated settings and new features. For instance, the shutdownnotify option allows users to specify a command to execute before Bitcoin Core begins its shutdown sequence. In the wallet section, new options like minconf and maxconf have been added to various RPCs, providing greater control over UTXO confirmations.\n\nBitcoin Core 25.0 introduces several improvements and optimizations, enhancing the user experience and security of the software. As with any release, users are encouraged to report any bugs they encounter to the official GitHub issue tracker.\n\nBitcoin Core is widely supported and tested on major operating systems such as Linux, macOS, and Windows. However, it is not recommended to use Bitcoin Core on unsupported systems. Users should ensure they are running a compatible operating system before upgrading to the latest version. In addition, users are encouraged to review the release notes for a detailed understanding of the improvements in this version if upgrading.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vd3d3LmxlZGdlci5jb20vYWNhZGVteS93aGF0LWlzLWEtYml0Y29pbi1pbXByb3ZlbWVudC1wcm9wb3NhbC1iaXDSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 26 May 2023 07:00:00 GMT", "title": "What Is a Bitcoin Improvement Proposal (BIP)? - Ledger", "content": "What Is a Bitcoin Improvement Proposal (BIP)?\n\nBy <PERSON><PERSON><PERSON>\n\nRead 5 min Beginner\n\nSHARE\n\nKEY TAKEAWAYS: — Bitcoin Improvement Proposals, or BIPs, propose changes to how the core Bitcoin blockchain or associated communication protocols work.\n\n\n\n— There are three types of BIPs: Standards Track (for network changes), Informational (for general knowledge), and Process BIPs (for procedural changes).\n\n\n\n— BIPs have helped implement some of the most famous Bitcoin upgrades, like Segwit & Taproot.\n\nBitcoin — the world’s largest cryptocurrency — is entirely decentralized and autonomous. The entire network runs on miners and nodes: computers that run Bitcoin software and work to keep the network secure.\n\nThese miners and nodes decide on any upgrades to the network by a vote, completely free of any third party. These upgrades can range from bug fixes or feature changes to even hard forks, where the entire network splits!\n\nHowever, coordinating upgrades and achieving community consensus across a big blockchain network like Bitcoin takes work. That’s where Bitcoin improvement proposals or BIPs come in.\n\nBitcoin Improvement Proposal (BIPs) allow community members to vote on protocol decisions in an organized way, even without centralized leadership.\n\nBut what are Bitcoin improvement proposals, and how do they work? If you want to understand the Bitcoin protocol, understanding the role of BIPs in the Bitcoin community is crucial. Let’s dive in!\n\nWhat Is a Bitcoin Improvement Proposal (BIP)?\n\nA Bitcoin Improvement Proposal, or BIP, is a technical document that proposes changes, ideas, or enhancements to the core Bitcoin blockchain. These BIPs can range from general community guidelines to major changes to the protocol. However, each BIP usually focuses on making one key change to the blockchain. If you want to know how Ethereum processes similar changes, check out our article on what an EIP is.\n\nHow Do Bitcoin Improvement Proposals (BIPs) Work?\n\nBIPs are the driving force behind the continuous evolution of the Bitcoin protocol, shaping the chain’s future. Let’s look at the creation and approval process.\n\nHow are Bitcoin Improvement Proposals Created?\n\nSince Bitcoin is an open-source protocol, anyone can propose a BIP by submitting an informal proposal to the Bitcoin developers’ mailing list.\n\nSo how does that work?\n\nWell, firstly, Bitcoin Improvement Proposal (BIP) authors collect initial community feedback and vet the idea. Then, once the author has reasonable confidence in the idea’s potential, they send a draft BIP to the mailing list.\n\nHere, the community discusses the idea and fleshes out the core points for and against the proposal. Proposal rejections are common at this stage, especially if there are obvious conflicts or duplicate BIPs. However, some proposals can also stay in the discussion stage for years if there is no consensus, for example, the Block size debate.\n\nIf the community agrees that the idea holds merit, the proposal gets a BIP number and becomes a BIP document. Then, BIP editors take up the task of making sure everything is accurate. The BIP moves from the “informal” stage to the “official” proposal at this stage.\n\nIt’s important to note that the BIP is not yet approved for development. Think of this as the filing and pre-trial stage of a lawsuit in court, when all of the details are written up, and the community feels the idea needs to be discussed more.\n\nThe community also publishes it to the Bitcoin Core Github repository of BIPs — a collection of all Bitcoin Proposals proposed so far.\n\nHow Are BIPs Approved?\n\nTo become part of the Bitcoin protocol, the Bitcoin Improvement Proposal (BIP) must achieve consensus among the community. To do that, it undergoes thorough review and analysis by the developers and the broader Bitcoin community. This includes the potential benefits and drawbacks, pros and cons, and the overall time investment.\n\nIf there are any necessary code changes, developers also start testing and implementing the code. This process can be long and complicated.\n\nThe community rejects the BIP if the developers find the code is bad or introduces challenges that can’t be resolved. Or, if everything looks okay, the community will begin implementing and activating it.\n\nTypes of Bitcoin Improvement Proposals (BIPs)\n\nBIP approval process depends majorly on its type. There are three types of BIPs: Standards Track, Informational, or Process BIPs. Let’s look at each of them and what they entail.\n\nStandards Track BIPs\n\nStandards Track BIPs are proposals that change the aspects of the protocol — transaction or block validation or encoding schemes. In the case of hard forks when the chain splits into two, Standard BIPs ensure interoperability between the legacy Bitcoin chains and the new ones. Since these BIPs involve significant code changes, they require community consensus.\n\nInformational BIPs\n\nInformational BIPs share knowledge and insight into Bitcoin’s upcoming plans. These documents provide general guidelines, design issues, or information relevant to the Bitcoin community. Since they are more informational, they do not require community consensus.\n\nProcess BIPs\n\nProcess BIPs, or consensus BIPs, outline procedural aspects, guidelines, or changes to the decision-making process of Bitcoin or consensus mechanism. While they are similar to informational BIPs, they require consensus (a clear miner majority of 90%).\n\nConsensus BIPs are for changes outside the Bitcoin protocol, meaning they don’t affect Bitcoin’s codebase. For example, implementing a soft fork and updating old miner software across the network requires a Process BIP.\n\nFamous Bitcoin Improvement Proposals You Should Know About\n\nBitcoin Improvement Proposals (BIPs) are key to the advancement of Bitcoin. In the last decade, BIPs have shaped the Bitcoin network, addressing critical issues and enhancing block capabilities.\n\nLet’s look at some historic BIPs, their significance in Bitcoin development, and their ripple effect on the community.\n\nBIP 001 and 002\n\nThe first-ever BIP created by Amir Taaki, BIP 001 was essentially a proposal on what a BIP should be. In other words, it explains the process of submitting and discussing a BIP, going into detail about the types of BIPs, workflow, submission formats, and more.\n\nBIP 002, on the other hand, amends some of these guidelines, elaborates on key approval processes, and answers common questions about BIPs. It also adds BIP licensing, comments, and formatting guidelines.\n\nBoth of these BIPs were Process BIPs, which required the community to agree on them. Together, these two BIPs set the stage for the ongoing evolution of Bitcoin.\n\nBIP 8 & 9\n\nBIP 8 and BIP 9 are both consensus (process) BIPs that enable soft forks, backward-compatible changes to the Bitcoin protocol. Every soft fork is a major protocol change that the majority of the network participants agree with, so miners can just upgrade their software to remain in sync with the network.\n\nBIP 8 and 9 provide mechanisms for signaling and activating these soft fork changes.\n\nSegWit\n\nOne of the most popular Bitcoin upgrades, Segwit, was the result of a series of BIPs, namely BIP-91, BIP-141, and BIP-148. BIP-141 introduced the SegWit upgrade, while BIP-148 activated the soft fork to implement it. BIP-91, on the other hand, reduced the consensus threshold to 80%, for Segwit to pass successfully.\n\nSegwit made the Bitcoin network scalable by reducing the size of transaction data. With less metadata, Bitcoin blocks can fit more transactions, increasing overall speed after the Segwit upgrade. Segwit was also key for developing popular Bitcoin Layer 2 networks, such as Lightning.\n\nTaproot\n\nTaproot, the most recent upgrade to the Bitcoin protocol, consisting of three BIPs: BIP 340 (Schnorr signatures), BIP 341 (Taproot), and BIP 342 (Tapscript). With Taproot, the Bitcoin protocol is more privacy-friendly and can also support innovations like Ordinals.\n\nTaproot’s initial idea proposal was in January 2018 by software developer Greg Maxwell. Post that, Bitcoin developers Pieter Wuille, Tim Ruffing, A.J. Townes, and Jonas Nick worked on implementing it. Almost four years after the initial proposal, Taproot was finally integrated in November 2021.\n\nNavigating The Bitcoin Network\n\nWhile the Bitcoin network evolves and changes with BIPs, Ledger has your back. Ledger Live adapts to all major Bitcoin changes quickly. For instance, Ledger Live supports both Segwit and Native Segwit accounts, ensuring inter-compatibility across the ecosystem.\n\nPlus, the Ledger ecosystem also allows you to run a full Bitcoin node yourself. As an advanced crypto user, you can take full advantage of Ledger Live while running your own Bitcoin node via Ledger SatStack. In short, you can become a part of the consensus process, and earn rewards for doing so. To learn more, make sure to check out the support article on how to run a bitcoin node.\n\nInteracting with the Bitcoin network safely is easy, if you take the right precautions that is. To stay safe while exploring the Bitcoin Ecosystem, consider protecting your assets with a hardware wallet. After all, not your keys, not your coins. Ledger devices offer you asset protection, but they also allow you full control over your assets and how you manage them."}]