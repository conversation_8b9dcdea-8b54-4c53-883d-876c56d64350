[{"id": 23, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9sZWQtYnktYml0Y29pbi1ibG9ja2NoYWluLWluZHVzdHJ5LXN0YXJ0cy0yMDIzLW9mZi1zdHJvbmctcmVwb3J00gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 13 Feb 2023 08:00:00 GMT", "title": "Led by Bitcoin, blockchain industry starts 2023 off strong: Report - Cointelegraph", "content": "2023 is off to a great start, with Bitcoin (BTC) rocketing up 40% in January. The good news is not relegated to just Bitcoin, however, as this price increase has sent ripple effects across the cryptoverse. Mining revenue jumped $22.66 million in January, and crypto-related stocks doubled on average. Despite this good news, venture capital investments are down 23% from the previous month.\n\nDownload and purchase this report on the Cointelegraph Research Terminal.\n\nFor a more detailed look into the various sectors of the crypto space, including venture capital, derivatives, decentralized finance (DeFi), regulations and much more, Cointelegraph Research publishes a monthly Investors Insights report. Compiled by leading experts on these various topics, the monthly reports are an invaluable tool to quickly get a sense of the current state of the blockchain industry.\n\nBitcoin gaining momentum in Q1 of 2023\n\nBitcoin posted its best monthly price performance since October 2021, gaining almost 40% in January. After benefiting from a Consumer Price Index print that showed inflation slowing in December 2022, crypto and stock prices began to cool as retail data missed expectations and earnings diminished. As BTC’s price hit a multi-month high of $23,920 on Jan. 29, all eyes turned to the Feb. 1 meeting of the Federal Open Market Committee, which raised its benchmark interest rate by 25 basis points, citing easing but still elevated inflation. BTC saw little volatility around the $23,000 level, suggesting the news was priced in.\n\nAs BTC gained bullish momentum in January, centralized exchange inflows and outflows normalized. The largest monthly outflow of coins in history occurred after the FTX collapse in November 2022, hitting 200,000 BTC/month across all exchanges. The net flows are now closer to neutral, with a reduction in the high outflow trend signaling investors are returning to the crypto market. BTC’s bullish month blitzed through its 50-, 100- and 200-day moving averages for the first time in over a year, with investor cohorts returning to profitability previously wiped out in 2022.\n\nDeFi TVL up $10 billion despite persistent fears and hacks\n\nSeveral altcoins, including Gala (GALA), Aptos (APT), Threshold (T), Decentraland’s MANA and Solana (SOL), experienced 100%+ monthly growth at the beginning of 2023 as Bitcoin’s price started to rise at the beginning of 2023. This was caused by the extreme dominance of negative sentiment and the over-saturation of short positions by the end of 2022. However, Solana-based protocol Friktion still announced the halting of deposits due to the “tough market for DeFi in the coming months,” suggesting that further recession may occur in the near future.\n\nSimultaneously, several events in a darker world of exploits and hacks continued, following the pathway of 2022. Namely, Avraham Eisenberg’s saga is unfolding as Mango Markets sued the hacker for $47-million compensation following his $117-million attack in October 2022. There have been several incidences of stolen funds moving around in the industry lately.\n\nThe Cointelegraph Research team\n\nCointelegraph’s Research department comprises some of the best talents in the blockchain industry. Bringing together academic rigor and filtered through practical, hard-won experience, the researchers on the team are committed to bringing the most accurate, insightful content available on the market.\n\nDemelza Hays, Ph.D., is the director of research at Cointelegraph. Hays has compiled a team of subject matter experts from finance, economics and technology to bring the premier source for industry reports and insightful analysis to the market. The team utilizes APIs from various sources to provide accurate, useful information and analyses.\n\nWith decades of combined experience in traditional finance, business, engineering, technology and research, the Cointelegraph Research team is perfectly positioned to put its combined talents to proper use with the latest Investor Insights Report.\n\nThe opinions expressed in this article are for general informational purposes only and are not intended to provide specific advice or recommendations for any individual or on any specific security or investment product."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YXJyZW4tYnVmZmV0dC1qdXN0LXNhaWQtZG9lc24tMjIwMDAwMDM0Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 13 Feb 2023 08:00:00 GMT", "title": "<PERSON> says he doesn't own bitcoin because 'it isn't going to do anything' — he'd rather own these 2 highly ... - Yahoo Finance", "content": "<PERSON> says he doesn’t own bitcoin because ‘it isn’t going to do anything’ — he’d rather own these 2 highly coveted and productive assets instead\n\nDespite its recent turbulence, bitcoin has soared by more than 95% over the past five years. But while the world’s largest crypto coin has clearly hit the mainstream, one prominent investor remains critical of the opportunity: <PERSON>.\n\nDon't miss\n\nAt the Berkshire Hathaway annual shareholders meeting last year, <PERSON><PERSON><PERSON> said that while he doesn’t know whether bitcoin will go up or down going forward, he’s pretty sure that “it doesn't produce anything.”\n\nAnd that’s why the Oracle of Omaha doesn’t own the asset.\n\n“If you told me you own all of the bitcoin in the world and you offered it to me for $25, I wouldn’t take it because what would I do with it?” he asks. “I’d have to sell it back to you one way or another. It isn’t going to do anything.”\n\nTo put that in perspective, bitcoin was trading at around $38,000 a piece when <PERSON><PERSON><PERSON> made those comments. Now, the cryptocurrency has fallen to $21,700.\n\nWhile criticizing bitcoin, <PERSON><PERSON><PERSON> touched on two assets that he would buy if given the opportunity.\n\nFarmland\n\nAgriculture and bitcoin don’t have much in common. Bitcoin was created in 2009 while agricultural communities began to form about 10,000 years ago.\n\n<PERSON><PERSON><PERSON> isn’t known for being an agricultural investor, but he sees value in an asset class that’s critical to the sector — farmland. His point is if you buy farmland, you hold a tangible asset that produces food.\n\n“If you said, for a 1% interest in all the farmland in the United States, pay our group $25 billion, I’ll write you a check this afternoon,” Buffett says.\n\nOf course, you don’t need to have $25 billion to invest in U.S. farmland. Publicly traded real estate investment trusts — that specialize in owning farms — allow you to do it with as little money as you’re willing to spend.\n\nStory continues\n\nGladstone Land (LAND), for instance, owns 169 farms totaling 115,000 acres. It pays monthly distributions of $0.0458 per share, giving the stock an annual dividend yield of 2.8%.\n\nRead more: Grow your hard-earned cash without the shaky stock market with these 3 easy alternatives\n\nThen there’s Farmland Partners (FPI), a REIT with a farmland portfolio of 190,000 acres and an annual dividend yield of 1.9%.\n\nPlus, online crowdfunding platforms allow you to buy pieces of real estate, including farmland.\n\nWith inflation running hot, the prices of agricultural commodities including corn and soybeans are soaring to new highs.\n\nApartments\n\nApartment buildings are another asset that Buffett wouldn’t mind owning at the right price.\n\n“[If] you offer me 1% of all the apartment houses in the country and you want another $25 billion, I’ll write you a check. It’s very simple,” the legendary investor says.\n\nWhether the economy is booming or in a recession, people need a place to live. And with real estate prices rising to unaffordable levels in many parts of the country, renting has become the only option for many people.\n\nYou can always buy an apartment building yourself, find tenants and collect the monthly rent checks. Of course, apartment-focused REITs can do that for you.\n\nFor instance, Camden Property Trust (CPT) owns, manages, develops and acquires multifamily apartment communities. It has investments in 171 properties containing 58,433 apartment units across the U.S. and offers an annual dividend yield of 3.1%.\n\nEssex Property Trust (ESS) invests in apartments primarily on the West Coast. The REIT currently yields 4.1%, backed by its ownership interest in 253 apartment communities — in California and Seattle — totaling approximately 62,000 units.\n\nThe bottom line\n\nBuffett prefers farmland and apartment buildings to bitcoin for a very simple reason: They produce something.\n\n“The apartments are going to produce rent, and the farms are going to produce food.”\n\nBitcoin boasts exciting long-term upside potential. But for risk-averse investors who’d like to sidestep as much volatility as possible, sticking to productive assets is a prudent idea.\n\nA better way to buy property?\n\nOf course, REITs aren't the only way to easily invest in real estate.\n\nAmid hot inflation and the uncertain economy, moguls are still finding ways to effectively invest their millions outside of the stock market.\n\nPrime commercial real estate has outperformed the S&P 500 over a 25-year period. With the help of new platforms, these kinds of opportunities are now available to retail investors. Not just the ultra rich.\n\nWith a single investment, investors can own institutional-quality properties leased by brands like CVS, Kroger and Walmart — and collect stable grocery store-anchored income on a quarterly basis.\n\nThis article provides information only and should not be construed as advice. It is provided without warranty of any kind."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YXJyZW4tYnVmZmV0dC1qdXN0LXNhaWQtZG9lc24tMjIwMDAwMDM0Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 13 Feb 2023 08:00:00 GMT", "title": "<PERSON> says he doesn't own bitcoin because 'it isn't going to do anything' — he'd rather own these 2 highly ... - Yahoo Finance", "content": "<PERSON> says he doesn’t own bitcoin because ‘it isn’t going to do anything’ — he’d rather own these 2 highly coveted and productive assets instead\n\nDespite its recent turbulence, bitcoin has soared by more than 95% over the past five years. But while the world’s largest crypto coin has clearly hit the mainstream, one prominent investor remains critical of the opportunity: <PERSON>.\n\nDon't miss\n\nAt the Berkshire Hathaway annual shareholders meeting last year, <PERSON><PERSON><PERSON> said that while he doesn’t know whether bitcoin will go up or down going forward, he’s pretty sure that “it doesn't produce anything.”\n\nAnd that’s why the Oracle of Omaha doesn’t own the asset.\n\n“If you told me you own all of the bitcoin in the world and you offered it to me for $25, I wouldn’t take it because what would I do with it?” he asks. “I’d have to sell it back to you one way or another. It isn’t going to do anything.”\n\nTo put that in perspective, bitcoin was trading at around $38,000 a piece when <PERSON><PERSON><PERSON> made those comments. Now, the cryptocurrency has fallen to $21,700.\n\nWhile criticizing bitcoin, <PERSON><PERSON><PERSON> touched on two assets that he would buy if given the opportunity.\n\nFarmland\n\nAgriculture and bitcoin don’t have much in common. Bitcoin was created in 2009 while agricultural communities began to form about 10,000 years ago.\n\n<PERSON><PERSON><PERSON> isn’t known for being an agricultural investor, but he sees value in an asset class that’s critical to the sector — farmland. His point is if you buy farmland, you hold a tangible asset that produces food.\n\n“If you said, for a 1% interest in all the farmland in the United States, pay our group $25 billion, I’ll write you a check this afternoon,” Buffett says.\n\nOf course, you don’t need to have $25 billion to invest in U.S. farmland. Publicly traded real estate investment trusts — that specialize in owning farms — allow you to do it with as little money as you’re willing to spend.\n\nStory continues\n\nGladstone Land (LAND), for instance, owns 169 farms totaling 115,000 acres. It pays monthly distributions of $0.0458 per share, giving the stock an annual dividend yield of 2.8%.\n\nRead more: Grow your hard-earned cash without the shaky stock market with these 3 easy alternatives\n\nThen there’s Farmland Partners (FPI), a REIT with a farmland portfolio of 190,000 acres and an annual dividend yield of 1.9%.\n\nPlus, online crowdfunding platforms allow you to buy pieces of real estate, including farmland.\n\nWith inflation running hot, the prices of agricultural commodities including corn and soybeans are soaring to new highs.\n\nApartments\n\nApartment buildings are another asset that Buffett wouldn’t mind owning at the right price.\n\n“[If] you offer me 1% of all the apartment houses in the country and you want another $25 billion, I’ll write you a check. It’s very simple,” the legendary investor says.\n\nWhether the economy is booming or in a recession, people need a place to live. And with real estate prices rising to unaffordable levels in many parts of the country, renting has become the only option for many people.\n\nYou can always buy an apartment building yourself, find tenants and collect the monthly rent checks. Of course, apartment-focused REITs can do that for you.\n\nFor instance, Camden Property Trust (CPT) owns, manages, develops and acquires multifamily apartment communities. It has investments in 171 properties containing 58,433 apartment units across the U.S. and offers an annual dividend yield of 3.1%.\n\nEssex Property Trust (ESS) invests in apartments primarily on the West Coast. The REIT currently yields 4.1%, backed by its ownership interest in 253 apartment communities — in California and Seattle — totaling approximately 62,000 units.\n\nThe bottom line\n\nBuffett prefers farmland and apartment buildings to bitcoin for a very simple reason: They produce something.\n\n“The apartments are going to produce rent, and the farms are going to produce food.”\n\nBitcoin boasts exciting long-term upside potential. But for risk-averse investors who’d like to sidestep as much volatility as possible, sticking to productive assets is a prudent idea.\n\nA better way to buy property?\n\nOf course, REITs aren't the only way to easily invest in real estate.\n\nAmid hot inflation and the uncertain economy, moguls are still finding ways to effectively invest their millions outside of the stock market.\n\nPrime commercial real estate has outperformed the S&P 500 over a 25-year period. With the help of new platforms, these kinds of opportunities are now available to retail investors. Not just the ultra rich.\n\nWith a single investment, investors can own institutional-quality properties leased by brands like CVS, Kroger and Walmart — and collect stable grocery store-anchored income on a quarterly basis.\n\nThis article provides information only and should not be construed as advice. It is provided without warranty of any kind."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YXJyZW4tYnVmZmV0dC1qdXN0LXNhaWQtZG9lc24tMjIwMDAwMDM0Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 13 Feb 2023 08:00:00 GMT", "title": "<PERSON> says he doesn't own bitcoin because 'it isn't going to do anything' — he'd rather own these 2 highly ... - Yahoo Finance", "content": "<PERSON> says he doesn’t own bitcoin because ‘it isn’t going to do anything’ — he’d rather own these 2 highly coveted and productive assets instead\n\nDespite its recent turbulence, bitcoin has soared by more than 95% over the past five years. But while the world’s largest crypto coin has clearly hit the mainstream, one prominent investor remains critical of the opportunity: <PERSON>.\n\nDon't miss\n\nAt the Berkshire Hathaway annual shareholders meeting last year, <PERSON><PERSON><PERSON> said that while he doesn’t know whether bitcoin will go up or down going forward, he’s pretty sure that “it doesn't produce anything.”\n\nAnd that’s why the Oracle of Omaha doesn’t own the asset.\n\n“If you told me you own all of the bitcoin in the world and you offered it to me for $25, I wouldn’t take it because what would I do with it?” he asks. “I’d have to sell it back to you one way or another. It isn’t going to do anything.”\n\nTo put that in perspective, bitcoin was trading at around $38,000 a piece when <PERSON><PERSON><PERSON> made those comments. Now, the cryptocurrency has fallen to $21,700.\n\nWhile criticizing bitcoin, <PERSON><PERSON><PERSON> touched on two assets that he would buy if given the opportunity.\n\nFarmland\n\nAgriculture and bitcoin don’t have much in common. Bitcoin was created in 2009 while agricultural communities began to form about 10,000 years ago.\n\n<PERSON><PERSON><PERSON> isn’t known for being an agricultural investor, but he sees value in an asset class that’s critical to the sector — farmland. His point is if you buy farmland, you hold a tangible asset that produces food.\n\n“If you said, for a 1% interest in all the farmland in the United States, pay our group $25 billion, I’ll write you a check this afternoon,” Buffett says.\n\nOf course, you don’t need to have $25 billion to invest in U.S. farmland. Publicly traded real estate investment trusts — that specialize in owning farms — allow you to do it with as little money as you’re willing to spend.\n\nStory continues\n\nGladstone Land (LAND), for instance, owns 169 farms totaling 115,000 acres. It pays monthly distributions of $0.0458 per share, giving the stock an annual dividend yield of 2.8%.\n\nRead more: Grow your hard-earned cash without the shaky stock market with these 3 easy alternatives\n\nThen there’s Farmland Partners (FPI), a REIT with a farmland portfolio of 190,000 acres and an annual dividend yield of 1.9%.\n\nPlus, online crowdfunding platforms allow you to buy pieces of real estate, including farmland.\n\nWith inflation running hot, the prices of agricultural commodities including corn and soybeans are soaring to new highs.\n\nApartments\n\nApartment buildings are another asset that Buffett wouldn’t mind owning at the right price.\n\n“[If] you offer me 1% of all the apartment houses in the country and you want another $25 billion, I’ll write you a check. It’s very simple,” the legendary investor says.\n\nWhether the economy is booming or in a recession, people need a place to live. And with real estate prices rising to unaffordable levels in many parts of the country, renting has become the only option for many people.\n\nYou can always buy an apartment building yourself, find tenants and collect the monthly rent checks. Of course, apartment-focused REITs can do that for you.\n\nFor instance, Camden Property Trust (CPT) owns, manages, develops and acquires multifamily apartment communities. It has investments in 171 properties containing 58,433 apartment units across the U.S. and offers an annual dividend yield of 3.1%.\n\nEssex Property Trust (ESS) invests in apartments primarily on the West Coast. The REIT currently yields 4.1%, backed by its ownership interest in 253 apartment communities — in California and Seattle — totaling approximately 62,000 units.\n\nThe bottom line\n\nBuffett prefers farmland and apartment buildings to bitcoin for a very simple reason: They produce something.\n\n“The apartments are going to produce rent, and the farms are going to produce food.”\n\nBitcoin boasts exciting long-term upside potential. But for risk-averse investors who’d like to sidestep as much volatility as possible, sticking to productive assets is a prudent idea.\n\nA better way to buy property?\n\nOf course, REITs aren't the only way to easily invest in real estate.\n\nAmid hot inflation and the uncertain economy, moguls are still finding ways to effectively invest their millions outside of the stock market.\n\nPrime commercial real estate has outperformed the S&P 500 over a 25-year period. With the help of new platforms, these kinds of opportunities are now available to retail investors. Not just the ultra rich.\n\nWith a single investment, investors can own institutional-quality properties leased by brands like CVS, Kroger and Walmart — and collect stable grocery store-anchored income on a quarterly basis.\n\nThis article provides information only and should not be construed as advice. It is provided without warranty of any kind."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vd3d3LmxlZGdlci5jb20vYmxvZy9zaG91bGQtY3J5cHRvLWZlYXItcXVhbnR1bS1jb21wdXRpbmfSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 13 Feb 2023 08:00:00 GMT", "title": "Should Crypto Fear Quantum Computing? - <PERSON><PERSON>", "content": "<PERSON>, <PERSON><PERSON> at Ledger, and <PERSON>, Security Engineer at Ledger, explore the potential implications of quantum computing on the cryptocurrency industry, and how post-quantum solutions may eventually emerge to address the challenge.\n\n\n\nThings to know:\n\n\n\n– Quantum computing, a cutting-edge technology, holds immense potential for revolutionizing computation with its unmatched computational power.\n\n\n\n– Quantum computing, despite being at least several years from a major breakthrough, is perceived as a significant threat to cryptography due to its immense data processing capabilities.\n\n\n\n– The potential impact of quantum computing on cryptography and secure systems such as Bitcoin’s proof-of-work must be carefully considered. As the world’s most secure gateway to crypto, such fundamental questions merit <PERSON><PERSON>’s full attention.\n\n\n\n\n\nQuantum Computing: The Next Big Tech Leap\n\nThe computers we use daily process information based on “bits”. A bit can only hold one of the following values: 0 or 1, and can be strung together to create a piece of binary code. Today, everything we do with a computer, from sending emails and watching videos to sharing music, is possible due to such strings of binary digits.\n\nThe binary nature of traditional computers imposes limitations on their computing power. These computers only perform operations one step at a time and struggle to accurately simulate real-world problems. In contrast, the physical world operates based on amplitudes rather than binary digits, making it much more complex. This is where quantum computers come into play.\n\nIn 1981, <PERSON> said that “nature isn’t classical, and if you want to make a simulation of nature, you’d better make it quantum mechanical.” Instead of manipulating bits, quantum computing uses “quantum bits”, or qubits, allowing it to process data in a vastly more efficient way. Qubits can be zero, one, and, most importantly, a combination of zero and one.\n\nQuantum computing stands at the crossroads of physics and computer science. To put things into perspective, a 500-qubit quantum computer would require more classical bits than… the number of atoms in the entire universe.\n\nIs Quantum A Threat To Cryptography?\n\nPublic-key cryptography, also referred to as asymmetric cryptography, forms the foundation of cryptocurrency security. It involves a combination of a public key (accessible to all) and a private key. The rapid calculation capabilities of qubits raise the potential for breaking encryption and disrupting the security of the cryptocurrency industry if quantum computing continues to advance.\n\nTwo algorithms need to be closely considered: Shor’s and Grover’s. Both algorithms are theoretical because there currently isn’t any machine to implement them, but as you will see, the potential implementation of these algorithms could be harmful to cryptography.\n\nOn the one hand, Shor’s (1994) quantum algorithm, named after Peter Shor, enables factoring large integers or solving the discrete logarithm problem in polynomial time. This algorithm could break public key cryptography with a sufficiently powerful quantum computer. Shor’s algorithm would break the vast majority of asymmetric cryptography used today since it is based on RSA (relying on the integer factorization problem) and Elliptic Curve Cryptography (depending on the discrete logarithm problem in an elliptic curve group).\n\nOn the other hand, Grover’s (1996) algorithm is a quantum search algorithm devised by Lov Grover in 1996, which can be used to solve unstructured search problems. The Grover algorithm puts a significant dent in symmetric primitives’ security but is not insurmountable. It is generally advised to double the key length to compensate for this break’s square-root complexity. Using AES256 instead of AES128 is considered enough, but it should be noted that this rule of thumb might only sometimes be valid for all ciphers[5]. As for hash functions, which are part of the symmetric primitive’s landscape, it is thought that it has no impact on collision resistance. However, researchers found instances of the problem where this is untrue[6] (multi-target preimage search, for example).\n\nIn essence, both algorithms pose potential dangers to cryptography. Shor’s algorithm simplifies the process of factoring large numbers, making it easier to uncover a private key connected to a public key, and Grover’s algorithm is capable of compromising cryptographic hashing more efficiently than current computers.\n\nWhen Will Encryption-Breaking Quantum Computers Emerge?\n\nLet’s walk through some of the latest experiments and see how fast research is going. The first real quantum computer remains far off, but that doesn’t prevent a global race from reaching “quantum supremacy.” For Ayal Itzkovitz, managing partner in a quantum-focused VC fund, “if three years ago we didn’t know if it was altogether possible to build such a computer, now we already know that there will be quantum computers that will be able to do something different from classic computers.”\n\nOne event that everyone has probably heard about was Google’s “quantum supremacy experiment” in 2019 using a device with 54 qubits. In 2021, the University of Science and Technology of China solved a more complex calculation using 56 qubits, reaching 60 qubits later. Its goal was to perform a computation not involving Shor’s algorithm that would equally demonstrate a quantum speedup over classical computing.\n\nBy definition, these experiments do not show progress toward breaking cryptography because they were designed to avoid the size and complexity of performing quantum integer factorization. However, they show that building more qubits into a quantum computer is no longer difficult, with different hardware solutions available, Google’s ‘Sycamore’ chip qubits being fundamentally different from the USTC’s photons. The next vital step to getting to an encryption-breaking computer is generally considered to be building fault-tolerant computation and error-correcting qubits.\n\nBSI’s Status of quantum computer development [1] shows how far from breaking a 160 bits discrete logarithm (lowest blue line in the following image) current quantum computers are. The abscissa shows how reducing the error rate through pure hardware improvements, or fault-tolerant computing helps reach such computing levels without dramatically scaling the number of available qubits (y-axis).\n\nImplementing Shor’s algorithm in a scalable way requires fault-tolerant computation on a few thousand logical qubits: 2124 qubits at a minimum in order to break a 256-bit elliptic curve like bitcoin’s secp256k1, from Improved quantum circuits for elliptic curve discrete logarithms[7]. A ‘logical’ qubit in such a system is composed of several qubits designed to work as an error-corrected version of a single qubit.\n\nA thousand logical qubits roughly translate into several million qubits, covering the size of a soccer field. A practical demonstration of such a fault-tolerant computation was recently made in Fault-tolerant control of an error-corrected qubit[2], where a single logical qubit whose error probability is lower than that of the qubits it is made of. This area’s improvement is expected to follow quickly as it will become the focus.\n\nProgress in this direction will directly translate into a concrete threat to public key cryptography. Lastly, another possibility for rapid progress can come from purely algorithmic improvements or hardware-only discoveries. BSI’s Status of quantum computer development[1] explains: “There can be disruptive discoveries that would dramatically change [the current state of knowledge], the main ones being cryptographic algorithms that can be run on near-term non-error-corrected machines or dramatic breakthroughs in the error rate of some platforms.” In other words, it’s not just a problem of being able to build large computers with a lot of qubits (actually building more qubits reliably is not the main focus, fault-tolerant computing is), but also an algorithmic one and possibly a material research one.\n\n\n\nAs we were writing this article, IBM published its results on a 127-qubit chip with a 0.001 error rate, and plans on issuing a 433-qubit chip next year, and 1121-qubit chip in 2023.\n\nAll in all, it remains hard to predict how fast a quantum computer will come to life. Still, we can rely on expert opinion on the matter: A Resource Estimation Framework for Quantum Attacks Against Cryptographic Functions – Recent Developments[3] and Expert poll on quantum risk[4] show that many experts agree that in 15 to 20 years, we should have a quantum computer available.\n\nQuoting A Resource Estimation Framework for Quantum Attacks Against Cryptographic Functions – Recent Developments [3] as a summary:\n\n“The currently deployed public key schemes, such as RSA and ECC, are completely broken by Shor’s algorithm. In contrast, the security parameters of symmetric methods and hash functions are reduced by, at most, a factor of two by the known attacks – by “brute force” searches using Grover’s searching algorithm. All those algorithms require large-scale, fault-tolerant quantum machines, which are not yet available. Most of the expert community agree that they will likely become a reality within 10 to 20 years.”\n\nNow that we’ve examined why quantum algorithms could harm cryptography, let’s analyze the substantial risks implied for the crypto and Web3 fields.\n\nQuantum: What Risks for cryptocurrencies?\n\nThe Bitcoin case:\n\nLet’s start with Pieter Wuille’s analysis of the problem for Bitcoin, sometimes considered “quantum-safe” due to addresses being hashes of public keys and thus not exposing them.\n\nNot being able to break a Bitcoin private key based on the assumption that hashes make it impossible also relies on never disclosing one’s public key, whatever the means, which is already wrong for many accounts.\n\nReferring to another thread, Pieter Wuille gives an idea of the impact of having the ~37% of exposed funds (at the time) stolen. Bitcoin would probably tank, and even unexposed, everyone else loses as well.\n\nThe crucial point here is the mention that the progress towards building a quantum computer will be incremental: billions of dollars are publicly invested in this field and any improvement resonates worldwide, as Google’s quantum supremacy experiment showed.\n\nThis means that ending up with funds at risk will take time, and alternative solutions can be laid out correctly. One can imagine setting up a fork of the chain using post-quantum cryptographic algorithms for signing and allowing people to transfer their funds to that new chain from the old once news of a reasonably beefy quantum computer seems imminent.\n\nThe Ethereum case:\n\nThe case of Ethereum is interesting as ETH 2.0 includes a backup plan for a catastrophic failure in EIP-2333.\n\nIn case ETH2’s BLS signatures break, which would happen at the same time as ECDSA since they are both equally vulnerable in the face of Shor’s algorithm, a hard fork of the blockchain will be executed before the algorithm is suspected to be compromised. Then, users reveal a preimage of their key that only legitimate owners can possess. This excludes keys retrieved by breaking a BLS signature. With that preimage, they sign a specific transaction allowing them to move to the hard fork and use new post-quantum algorithms.\n\nThis is not a switch to a post-quantum chain yet but it provides an escape hatch. Some more information here.\n\nPost-quantum signatures:\n\nA few things could be improved regarding switching to a post-quantum signature scheme for use in a cryptocurrency. The current NIST finalists have rather large memory requirements. When the signature size is not unreasonably larger than that of an ECDSA, the public key size increases block sizes and the associated fees.\n\nCandidate name Size Rainbow 58.3 kB Dilithium 3.5 kB Falcon 1.5 kB GeMSS 352 kB Picnic 12 kB SPHINCS+ 7 kB\n\nThe Falcon algorithm was designed to minimize the size of the public key and signature. However, its 1563 bytes are still far from the 65 bytes ECDSA currently reaches.\n\nCryptographic techniques can reduce block sizes, like aggregating several signatures together. This [multisignature scheme](https://eprint.iacr.org/2020/520) for the GeMSS signature does just that and reduces the cost of storage per signature to something acceptable, despite the vast one-time fee of a GeMSS signature.\n\nThreats to cryptographic hardware:\n\nSignature sizes also impact hardware wallets where memory is highly constrained: a Ledger Nano S has 320 KB of Flash memory available and only 10 Kilobytes of RAM. If suddenly we needed to use Rainbow signatures, generating the public key in a native way would not be feasible.\n\nSince, however, the whole cryptographic community is affected by the problem, including the banking, telecommunication, and identity industry, which make up most of the market for secure chips, we expect hardware to adapt quickly to the need for post-quantum algorithm-friendly hardware and remove that memory (or sometimes performance) all together in time.\n\nThe consequences of those breaks are the downfall of the current banking system, telecommunications, and identity systems such as passports. What to do in the face of such an apocalyptic future? Fear not, or a little, as cryptographers have got it covered.\n\nIs There A Cure, Doctor?\n\nWhile our current computers would need thousands of years to break public key cryptography, fully developed quantum computers would do this in minutes or hours. “Quantum security” standards will inevitably be needed to counter this threat and ensure the security of our future financial transactions and online communications.\n\nWork is already underway regarding what is commonly called “Post-quantum cryptography” that would possibly be “compatible with today’s computers but which will also be capable of withstanding attackers from quantum computers in the future.” Post-quantum cryptography brings algorithms and mathematical standards to the next level while allowing compatibility with current computers.\n\nThe NIST competition created just for the occasion has already reached its third round and produced a list of potential candidates for standardization. The Post-Quantum security Conference was launched as far back as 2006 to study cryptographic primitives that would resist known quantum attacks.\n\nThe foundations of this research stem from expert warnings that encrypted data is already at risk of being compromised, as the first practical quantum computers are expected to emerge within the next 15 years.\n\nThis kind of attack is known as “hoard data now, attack later,” where a large organization stores encrypted information from other parties it wishes to break and waits until a powerful enough quantum computer allows it to do so. This is the same worry of this article for example, “The US is worried that hackers are stealing data today so quantum computers can crack it in a decade“, but it does not say what state-level actors might be doing in the same vein. They have a lot more resources and storage available.\n\nClosing Thoughts\n\nThe exact speed at which encrypted communications will become vulnerable to quantum research remains hard to determine.\n\nOne thing is sure: although significant progress is being made in quantum computing, we are still far from possessing the capability to crack cryptography with these machines. The likelihood of a sudden breakthrough resulting in the design of such a computer is minimal, giving us time to prepare for its arrival. If it were to occur overnight, the ramifications would be disastrous, affecting not only cryptocurrencies, but a wide range of sectors.\n\nFortunately, solutions, including post-quantum cryptography, are available to address the threat, but the crypto industry has yet to see the urgency in investing in these measures.\n\nThe cryptocurrency market must closely monitor quantum developments. With regards to hardware, there is little cause for concern as we anticipate the development of new secure elements to meet the demand. It is crucial to stay abreast of the latest advancements in side-channel and fault-resistant versions of these algorithms, in order to provide a reliable implementation for our users.\n\nReferences:\n\n[1]: BSI’s Status of quantum computer development\n\n[2]: Fault-tolerant control of an error-corrected qubit\n\n[3]: A Resource Estimation Framework for Quantum Attacks Against Cryptographic Functions – Recent Developments\n\n[4]: Expert poll on quantum risk\n\n[5]: Beyond quadratic speedups in quantum attacks on symmetric schemes\n\n[6]: An Efficient Quantum Collision Search Algorithm and Implications on Symmetric Cryptography\n\n[7]: Improved quantum circuits for elliptic curve discrete logarithms"}]