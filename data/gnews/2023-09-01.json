[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vdS50b2RheS9ldGhlcmV1bS1ldGgtcHJpY2UtYW5hbHlzaXMtZm9yLXNlcHRlbWJlci0xLTDSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 01 Sep 2023 07:00:00 GMT", "title": "Ethereum (ETH) Price Analysis for September 1 - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nBears have turned out to be more powerful than bulls on the first day of September.\n\nAdvertisement\n\nTop coins by CoinMarketCap\n\nETH/USD\n\nThe rate of Ethereum (ETH) has fallen by almost 4% over the last 24 hours.\n\nImage by TradingView\n\nDespite today's fall, there are no reversal signals yet, even on the hourly chart. The price of Ethereum (ETH) is on its way to testing the local support level of $1,630. If the bar closes near it, the breakout may lead to a sharp drop to the $1,600 zone soon.\n\nImage by TradingView\n\nA similar situation can be seen on the daily time frame. Traders should focus on the closest level of $1,622. If buyers lose it, the accumulated energy might be enough for a more profound drop to the $1,550-$1,600 range within the next few days.\n\nImage by TradingView\n\nA less positive picture can be see on the weekly time frame, as the rate is far from the support level. However, the bar is far from the closure, which means it is too early to draw any conclusions. But if the candle closes below the $1,600 zone, there is a good possibility of seeing the test of the $1,543 mark next week.\n\nEthereum is trading at $1,629 at press time."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMijwFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wOS8wMS9yZXRhaWwtdHJhZGluZy1naWFudC1yb2Jpbmhvb2QtdG8tZW5hYmxlLWluLWFwcC1zd2Fwcy1vbi10aGUtZXRoZXJldW0tbmV0d29yay1mb3ItbW9yZS10aGFuLTIwMC1jcnlwdG8tYXNzZXRzL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 01 Sep 2023 07:00:00 GMT", "title": "Retail Trading Giant Robinhood To Enable In-App Swaps on the Ethereum Network for More Than 200 Crypto Assets - The Daily Hodl", "content": "Popular retail trading platform Robinhood says its self-custodial web3 wallet now supports in-app swapping on the Ethereum (ETH) network.\n\nIn a statement, Robinhood says select users can now swap for more than 200 tokens on Ethereum starting Wednesday, August 30th, and the feature will be available for all users in the coming weeks.\n\nUnlike other wallets, the Robinhood Wallet does not require users to hold ETH tokens to swap since the platform will automatically deduct the network fees from the crypto assets that its users already have.\n\n<PERSON>, general manager of Robinhood Crypto, says the new in-app swap feature will make decentralized finance (DeFi) more accessible.\n\n“With Robinhood Wallet, we stripped away many of the complexities of DeFi and the broader Web3 ecosystem, and reduced some of the challenges and barriers to entry for everyday people.”\n\nRobinhood says it is launching Ethereum in-app swaps, along with custody, send, and receive support for Bitcoin (BTC) and Dogecoin (DOGE), in response to users’ request for access to more coins on more chains.\n\nThe trading platform also previously launched a crypto on-ramp that allows users to directly access and fund their Robinhood wallet without leaving decentralized applications (DApps) and needing to log into their accounts.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nGenerated Image: Midjourney"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vY3J5cHRvLm5ld3MvZ2lkZHktYS1zZWxmLWN1c3RvZHktc21hcnQtd2FsbGV0LWV4dGVuZHMtdG8tZXRoZXJldW0v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 01 Sep 2023 07:00:00 GMT", "title": "Giddy, a self-custody smart wallet, extends to Ethereum - crypto.news", "content": "Giddy, a self-custody smart wallet with advanced private key technology, has integrated Ethereum, the pioneer smart contract platform.\n\nThe aim is to enhance the wallet functionalities and accessibility across multiple networks, enhancing user experience.\n\nGiddy expands, supports Ethereum\n\nThe Giddy smart wallet aims to provide its users with an easy retail experience, allowing them to access decentralized finance (defi) earning opportunities. The project initially launched on Polygon.\n\nIn a press release shared exclusively with crypto.news, this expansion will set the course for future expansions into EVM-compatible laye-1 and layer-2 platforms. Specifically, the wallet provider aims to integrate Arbitrum, Base, BNB Chain, and many more, in the future.\n\nYou might also like: PancakeSwap launches on Ethereum layer-2 solution, Base\n\nThere are also solid plans to list Bitcoin.\n\nWith this, users can buy multiple coins using fiat straight from the Giddy wallet. At the same time, they can swap tokens, buy items of supported brands from 150 countries, and send assets.\n\nPrivate key technology\n\nGiddy wallet has a unique private key technology that splits the recovery phrases into three parts for security.\n\nThe first is a user-generated password, the second is their personal hardware or mobile device, and the third is a social login like Google Gmail or Apple ID. Each component alone doesn’t pose a major risk, minimizing the chance of compromise. If any part is lost, users can recover their wallet and funds, and they have the flexibility to export their private keys to a destination of their choice.\n\n<PERSON>, the CEO and co-founder of Giddy, said their private key management solution is a “ game changer among crypto wallets” and will simplify user experience since the technology “breaks down the private key into multiple factors owned by the user”, empowering them as a result.\n\nRead more: US SEC postpones ruling on 6 spot Bitcoin ETF applications\n\nDisclosure: This content is provided by a third party. crypto.news does not endorse any product mentioned on this page. Users must do their own research before taking any actions related to the company.\n\nFollow Us on Google News"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiZ2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9zdGFyay13YXJlLWhlcm9kb3R1cy1sYXVuY2gtdGVjaC10by12ZXJpZnktZGF0YS1mcm9tLWV0aGVyZXVtLWJsb2NrY2hhaW7SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 01 Sep 2023 07:00:00 GMT", "title": "StarkWare, Herodotus launch tech to verify data from any point in Ethereum's history - Cointelegraph", "content": "A new zero-knowledge proof (ZK-proof) technology is set to improve the ability to access and verify historical data from the Ethereum blockchain, with deep chain validation cited as a usability barrier of the network.\n\nTechnology firm Herodotus has released its on-chain accumulator, which uses storage-proof cryptography, allowing users to verify data from any point of Ethereum’s blockchain without needing a third party. The solution makes use of StarkWare’s STARK proofs, the ZK-proof technology co-invented by mathematician <PERSON>.\n\nStarkWare presented Herodotus with a custom-built instance of its shared prover service SHARP, which enables advanced scaling efficiency using recursive proofs. The latter allows a virtual machine to provide “proofs of proofs” by generating proofs of transactions or blocks in parallel and real-time, batching them into a subsequent proof.\n\nRelated: More TPS, less gas: Ethereum L2 Starknet outlines performance upgrades\n\nAt a slightly more technical level, the accumulator acts as a cache that stores block headers. If the accumulator has a header in its cache, the respective storage-proof computation can use it for validation.\n\nA visual representation of the possible use of recursive proofs to batch a variety of information into subsequent verified and cached proofs. Source: StarkWare\n\nIf the header is not cached, the prover has to generate a proof to cover the requested block range, add the block header to the accumulator and then complete the requested storage proof computation.\n\nAs the name suggests, the on-chain accumulator essentially accumulates proofs that rollup prior proofs, drastically reducing the time it takes to verify the Ethereum blockchain and associated data at any point in the network’s history.\n\nHerodotus chief technology officer Marcello Bardus notes that the technology removes the need to traverse the entire blockchain on the blockchain itself:\n\n“We can do it off chain, generate an accumulator and just cherrypick one specific block without iterating from the entire chain on the chain itself.”\n\nStarkWare notes that Storage proofs could prove groundbreaking as an alternative to cross-chain bridges that rely on third-party oracles to track and verify data.\n\nRelated: StarkNet overhauls Cairo programming language to drive developer adoption\n\nHerodotus co-founder Kacper Koziol added that the accumulator is an innovation that Ethereum has long needed to align with blockchain principles of transparency and accessibility. The technology will essentially allow any user to access any point in Ethereum’s history.\n\n“This will be very powerful. For the first time in the history of blockchains, people are going to be able to prove the correctness of any aspect of anyone’s on-chain information.”\n\nThe two teams highlight the potential for storage proofs to build “Web2 equivalent applications,” tapping into the pioneering ability to access and verify Ethereum blockchain data autonomously.\n\nAccount recovery is touted as one potential use case, where the ability to verify on-chain data could trigger a proverbial dead man’s switch or automate insurance protocols that use historical on-chain events to trigger smart contract payouts.\n\nCollect this article as an NFT to preserve this moment in history and show your support for independent journalism in the crypto space.\n\nMagazine: Recursive inscriptions: Bitcoin ‘supercomputer’ and BTC DeFi coming soon"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vd3d3LmNjbi5jb20vbmV3cy9ldGgtc3Rha2luZy1zZXJ2aWNlcy1hZ3JlZS1saW1pdC1mb3ItdmFsaWRhdG9ycy1hbWlkLWNyaXRpY2lzbS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 01 Sep 2023 07:00:00 GMT", "title": "Ethereum Staking Services Accept 22% Validators Limit Amid Criticism From ETH Community - CCN.com", "content": "Key Takeaways\n\nSome ETH staking service providers agreed to a self-imposed limit.\n\nThe limit restricts the amount of the staking market they control.\n\nThey promise to hold no more than 22% of the Ethereum staking market.\n\nPotential centralization of ETH liquid staking behind the move.\n\nEthereum has always been at the forefront of the debate about decentralization and now this is being protected by the adoption of a ground-breaking self-limit regulation by Ethereum staking providers.\n\nThe 22% Self-Limit Rule\n\nIn order to maintain Ethereum blockchain’s decentralized nature, at least five Ethereum liquid staking providers, including Rocket Pool , Stader Labs , Diva Staking , Puffer Finance , and StakeWise , have either adopted or are actively working to implement a self-limit rule. This regulation limits their ownership to no more than 22% of the Ethereum staking market.\n\nThis figure was selected due to Ethereum’s consensus algorithm requiring a 66% validator agreement on the network’s state. This rule mandates the collaboration of at least four major companies for finalizing the chain, making transactions unchangeable by setting the limit at 22%.\n\nStruggling Against Centralization\n\nThe Ethereum community has had ongoing concerns about staking service centralization, as indicated by the self-limit proposal presented by Superphiz, an Ethereum core developer, in May 2022 . The main goal is to maintain the network’s decentralized nature by preventing any one entity from obtaining disproportionate influence over it.\n\nThese providers are committed (or are in the process of committing) to self-limit to <22% of Ethereum validators. This is how our chain will be successful: Coordination above greed. Cooperation instead of winner-take-all.@Rocket_Pool @stakewise_io @staderlabs @divastaking — superphiz.eth 🦇🔊🛡️ (@superphiz) August 30, 2023\n\nLido Finance Mysteries\n\nThe biggest Ethereum liquid staking service, Lido Finance, stands out as an important exception to this trend. According to data from Dune Analytics , Lido has now a commanding 32.4% of all staked Ethereum, leaving Coinbase with just an 8.7% market share.\n\nIn an unexpected move, Lido Finance voted against self-limiting in June, with a whopping 99.8% majority.\n\n“They have expressed an intention to control the majority of validators on the beacon chain,” Superphiz said in an August 31 post.\n\nVarious Views in ETH Community\n\nThe self-limit rule has received a mixed reception from the Ethereum community. Some claim that the rule ignores the Ethereum tenets in favor of concentrating on economic self-interest. They argue that if proponents of the self-limit rule were in Lido’s position of power, they may view things differently.\n\nEasy to commit to self-limit when it seems that most of these will likely never reach 22% market share of Liquid staking on Ethereum. Folks in the ETH community should not shame more user-friendly solutions as greedy products. https://t.co/65vgti3Jrm — Neel Daftary 🦇🔊 (@DaftaryNeel) August 31, 2023\n\nOn the other hand, several members of the Ethereum community voiced worries about the risks of centralization posed by Lido’s substantial market share. They call this domination “selfish” and push for measures to maintain the health and decentralization of the larger Ethereum ecosystem.\n\nYeah because they have way less market share than that now… easy to chirp from the cheap seats. This has nothing to do with “Ethereum alignment.” None of these teams would self limit were they in Lido’s place. Everyone is doing the economically selfish and rational thing… — Mippo 🟪 (@MikeIppolito_) August 31, 2023\n\nThe debate over Ethereum’s decentralization will persist as it evolves. The 22% self-limit rule is a significant step in striking a balance between market growth and network decentralization.\n\nIt underscores the Ethereum community’s commitment to preserving decentralization for impartiality and permissionless innovation on the platform.\n\nThe Ethereum community’s commitment to decentralization is demonstrated by its acceptance of the 22% self-limit rule. This novel strategy aims to protect the fundamental values of the Ethereum platform by preventing any one company from exerting undue control over the Ethereum staking market.\n\nWhile opinions may vary, it underscores the Ethereum community’s ongoing commitment to striking the right balance between market dynamics and network integrity."}]