[{"id": 8, "url": "https://news.google.com/rss/articles/CBMieGh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA4LzIzL2V0aGVyZXVtLWJvdHRvbS1pcy1pbi1hcy1mYW50YXN0aWMtZXRoLW9wcG9ydHVuaXR5LXByZXNlbnRzLWl0c2VsZi1zYXlzLWNyeXB0by1hbmFseXN0L9IBfGh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA4LzIzL2V0aGVyZXVtLWJvdHRvbS1pcy1pbi1hcy1mYW50YXN0aWMtZXRoLW9wcG9ydHVuaXR5LXByZXNlbnRzLWl0c2VsZi1zYXlzLWNyeXB0by1hbmFseXN0L2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 23 Aug 2023 07:00:00 GMT", "title": "Ethereum Bottom Is In As ‘Fantastic’ ETH Opportunity Presents Itself, Say<PERSON> Crypto Analyst - The Daily Hodl", "content": "A closely followed crypto strategist believes that Ethereum (ETH) has already carved a bear market bottom.\n\nPseudonymous analyst <PERSON><PERSON><PERSON>o tells his 345,900 followers on the social media platform X that Ethereum has likely printed a price floor after last week’s marketwide correction.\n\nAccording to the trader, he expects Ethereum to break out from an ascending triangle pattern as long as ETH’s immediate support at around $1,440 holds.\n\n“I think it’s worth pointing out that on this latest dip, we hit the high timeframe zone that I previously said would be a ‘fantastic buy’ if we get there back in May, and my opinion on that hasn’t changed.\n\nIf I’m right about BTC, then I’m likely right about ETH too, and that green zone below us was likely our bottom.\n\nAs long as it holds, I expect this ascending triangle-like structure to actually play out.”\n\nLooking at the trader’s chart, he appears to predict that Ethereum will consolidate between $1,600 and $2,000 for the rest of the year before igniting a parabolic surge in early 2024.\n\nCredible Crypto holds the contrarian view that Bitcoin (BTC) will print a new all-time high before this year expires. According to the crypto strategist, he expects ETH to closely follow in the footsteps of BTC.\n\n“I want to be clear: while I am bearish on ETH/BTC in the short term (as I’ve said I’m bearish on most ALT/BTC pairings in the short term), I am very bullish on ETH/USD and expect it to be one of the first alts that follows BTC’s lead to the upside.”\n\nAt time of writing, Ethereum is worth $1,630.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nGenerated Image: Midjourney"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiXGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9mYXJjYXN0ZXItbWlncmF0ZS1ldGhlcmV1bS1vcHRpbWlzbS1zdXBlcmNoYWluLTA0NDk1MjYxOS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 23 Aug 2023 07:00:00 GMT", "title": "Farcaster To Migrate From Ethereum To Optimism Superchain - Yahoo Finance", "content": "Farcaster, an Ethereum-based social media graph, plans to migrate onto Optimism, a leading Layer 2 network.\n\n<PERSON>, Farcaster’s co-founder and CEO, noted that many of its “most active users and developers” are currently building on Optimism or another network using Optimism’s OP Stack, such as Base or Zora.\n\n“We’re planning to migrate the Farcaster identity and storage contracts to OP Mainnet in the coming weeks,<PERSON> <PERSON> added. “After that, we plan to enable permissionless sign-ups.”\n\nWith Ethereum’s Layer 2s now processing five times as many transactions per second (TPS) as the mainnet, many projects in the Ethereum ecosystem are pivoting the focus of their operations onto L2.\n\nFarcaster To Migrate From Ethereum To Optimism Superchain\n\nThe growth of Ethereum’s scaling ecosystem also promises to unlock new applications requiring high transaction throughput and low fees, including decentralized social media protocols. Lens, the social media protocol from Aave, is hosted on a bespoke Layer 3 appchain.\n\nfriend.tech Reignites Interest In SocialFi\n\nWeb3 is buzzing about social dApps following the meteoric rise of friend.tech, an app allowing users to speculate on ‘keys’ associated with public Twitter profiles.\n\nJust ten days after its launch, friend.tech ranked as the top dApp by fee volume on Aug. 21, but has since receded to third behind Lido and Uniswap, according to DeFi Llama. On Aug. 18, Friend Tech revealed it had received seed funding from Paradigm, a top venture capital firm active in the web3 sector.\n\nFarcaster closed a $30M funding round led by Andreessen Horowitz last July.\n\nimg,[object Object]\n\nBase Bolsters Optimism’s ‘Superchain’\n\nAnalysts also attribute the emergence of Base, Coinbase’s OP Stack-based Layer 2, as the top Layer 2 by transaction throughput to friend.tech’s surging user base. The network hosted 69,000 active users over the past day, according to Token Terminal.\n\nMore than 109,000 unique wallets have traded on friend.tech, according to Dune Analytics.\n\nBase’s success serves as a boon to Optimism’s Superchain roadmap, which outlines a unified ecosystem of symbiotic Layer 2s leveraging its open-source OP Stack. Coinbase serves as a core developer for OP Stack and endorses Optimism’s vision for the Superchain.\n\nRead the original post on The Defiant"}, {"id": 13, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9iaXRzdGFtcC1oYWx0cy1ldGhlci1ldGgtc3Rha2luZy11bml0ZWQtc3RhdGVz0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 23 Aug 2023 07:00:00 GMT", "title": "Bitstamp halts ETH staking in the United States - Cointelegraph", "content": "Crypto exchange Bitstamp will discontinue staking services for customers based in the United States. In a message shared with Cointelegraph, the exchange announced the ending of Ether (ETH) staking as of Sept. 25.\n\n“Customers will continue earning staking rewards up until September 25, 2023, and after that, all staked assets will be unstaked. Rewards, along with the principal, will be credited to users’ main Bitstamp account balances,\" said <PERSON>, U.S. CEO and global chief commercial officer at Bitstamp, warning that it could take a few days for users’ balances to reflect the changes.\n\nAccording to Bitstamp’s website, it charges a 15% commission on all staking rewards. The monthly reward rate for staking ETH on the exchange is 4.50%; by comparison, the monthly reward for staking Algorand (ALGO) is 1.60%. With the move, the U.S. joins other countries where Bitstamp staking services aren't available, including Canada, Japan, Singapore and the United Kingdom.\n\nThe decision appears to be related to recent legal developments in the United States. In early August, Bitstamp announced at least seven altcoins would no longer be offered in the country. They were Axie Infinity (AXS), Chiliz (CHZ), Decentraland (MANA), Polygon (MATIC), Near (NEAR), The Sandbox (SAND) and Solana (SOL). While the company didn’t specify why it suspended trading, all seven tokens were deemed unregistered securities by the U.S. Securities and Exchange Commission (SEC) in June as part of its lawsuits against crypto exchanges Binance and Coinbase.\n\nUpdate for our US users\n\n\n\nStarting August 29: AXS, CHZ, MANA, MATIC, NEAR, SAND, and SOL trading will be halted after evaluating recent market developments.\n\n\n\nExecute any open trades. Holding and withdrawing tokens afterwards will be unaffected.\n\n\n\nMore info:… — Bitstamp (@Bitstamp) August 8, 2023\n\nEther is the native cryptocurrency of the Ethereum blockchain and the second largest by market cap behind Bitcoin (BTC). A central issue surrounding the ongoing regulatory environment in the U.S. relates to whether ETH could be classified as a commodity or a security. The Commodity Futures Trading Commission has repeatedly called Ether a commodity, while SEC Chair Gary Gensler said at a hearing in April that Bitcoin was a commodity but would not specify whether ETH should be deemed a security.\n\nMagazine: Crypto regulation — Does SEC Chair Gary Gensler have the final say?"}, {"id": 14, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1ncmluZHMtbG93ZXItMTU1MC_SAUpodHRwczovL3d3dy5uZXdzYnRjLmNvbS9hbmFseXNpcy9ldGgvZXRoZXJldW0tcHJpY2UtZ3JpbmRzLWxvd2VyLTE1NTAvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 23 Aug 2023 07:00:00 GMT", "title": "Ethereum Price Grinds Lower As Bears Target Fresh Low Below $1,550 - NewsBTC", "content": "Ethereum price is facing many hurdles below the $1,700 resistance against the US Dollar. ETH could extend its decline below the $1,580 and $1,550 levels.\n\nEthereum is still struggling to recover above the $1,700 and $1,720 levels.\n\nThe price is trading below $1,680 and the 100-hourly Simple Moving Average.\n\nThere is a major bearish trend line forming with resistance near $1,665 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could start another decline if it breaks the $1,580 support in the near term.\n\nEthereum Price Faces Uphill Task\n\nEthereum’s price failed to climb above the $1,700 resistance zone. ETH started a fresh decline below the $1,650 and $1,620 levels, similar to Bitcoin.\n\nThe price even spiked below the $1,600 support and tested $1,580. A low was formed near $1,580 and the price is now attempting a fresh recovery. There was a move above the $1,600 level. The price is now testing the 50% Fib retracement level of the downward move from the $1,693 swing high to the $1,580 low.\n\nEther is also trading below $1,680 and the 100-hourly Simple Moving Average. Besides, there is a major bearish trend line forming with resistance near $1,665 on the hourly chart of ETH/USD.\n\nOn the upside, the price might face resistance near the $1,650 level. It is close to the 61.8% Fib retracement level of the downward move from the $1,693 swing high to the $1,580 low. The next resistance is near $1,665 or the 100-hourly Simple Moving Average or the trend line.\n\nSource: ETHUSD on TradingView.com\n\nThe main barrier is still near the $1,700 zone. A close above the $1,700 level could start a decent increase in the near term. The next major resistance is near the $1,750 level. Any more gains might send the price toward the $1,820 resistance.\n\nAnother Drop in ETH?\n\nIf Ethereum fails to clear the $1,665 resistance, it could start another decline. Initial support on the downside is near the $1,600 level.\n\nThe first major support is near the $1,580 zone. If there is a downside break below $1,580, there could be more losses. The next major support is near the $1,550 support level. Any more losses might send the price toward the $1,520 level or even to a new low below $1,500.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is losing momentum in the bearish zone.\n\nHourly RSI – The RSI for ETH/USD is now below the 50 level.\n\nMajor Support Level – $1,580\n\nMajor Resistance Level – $1,665\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMiNWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2RhdGEtYXZhaWxhYmlsaXR5LWV0aGVyZXVt0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 23 Aug 2023 07:00:00 GMT", "title": "Why data availability sampling matters for blockchain scaling - Blockworks", "content": "On-chain data availability has become an increasingly common topic as Ethereum continues to scale.\n\nToday, Ethereum developers are looking at where and how data should be stored on blockchain networks as they work towards resolving the so-called blockchain trilemma, referring to the tradeoffs between security, scalability and decentralization.\n\nIn crypto, data availability refers to the concept that data that is stored on a network is accessible and retrievable by all network participants.\n\nOn Ethereum layer-1, the network’s nodes download all the data in each block, making it difficult for invalid transactions to be executed.\n\nAlthough this can guarantee security, this process can be relatively inefficient — asking a network node to verify and store all data in a block can drastically reduce throughput and hinder blockchain scalability.\n\nEthereum layer-2 scaling solutions are designed to resolve this problem.\n\nOne popular solution today is the optimistic rollup, such as Arbitrum and Optimism. Optimistic rollups are “optimistic” in nature as they assume transactions are valid until proven otherwise.\n\nMost rollups today only have one single sequencer, meaning there is a centralization risk, <PERSON><PERSON><PERSON>, co-founder of modular blockchain Avail, told Blockworks.\n\nThis is not a major problem at present, as rollup solutions must put the raw transaction data on Ethereum using something called calldata — the cheapest form of storage on Ethereum today, as <PERSON><PERSON><PERSON> notes.\n\nOnce a calldata is submitted to Ethereum mainnet, anyone can challenge whether or not it is accurate within a set period of time, according to <PERSON><PERSON>, the founder of blockchain scaling solution Eclipse.\n\nIf no one challenges the validity of the rollup, it will be accepted on Ethereum once the period of time is up.\n\nThe problem, Somani notes, is how someone can then prove that a transaction was executed inaccurately if they don’t have the data.\n\n“If I don’t tell you what I executed, there’s no way for you to possibly prove that it is wrong, so you need to know exactly what I executed in order to fix that,” Somani said. “So all blockchains must prove data availability in some way, shape or form.”\n\nData availability sampling\n\nAs all blockchains must prove data availability, it can be inefficient to download a full block onto a network, which again invokes the initial data availability problem.\n\n“So as someone who doesn’t want to download the full block, I still want the confidence that the information on the block is not being withheld,” Somani said.\n\nThe solution, according to Somani, is the use of data availability sampling to gain confidence that the block is actually there.\n\nData availability sampling involves sampling random parts of the block to obtain arbitrarily high confidence that the block is there, Somani explains.\n\nThis technology utilizes polynomials — a mathematical expression comprising variables, coefficients and exponentiation — to model relationships between variables in a block.\n\nA common misinterpretation of data availability sampling is that if you sample half the block, you only gain 50% confidence that the information in the block is accurate, Somani said. This isn’t true, he explains, because as with data availability sampling, users must make sure that they have enough points to recover the original polynomial.\n\nProjects like Celestia and Avail are currently building out data availability sampling solutions.\n\n“What we sincerely believe is that every base layer is going to be a data availability layer,” Arjun told Blockworks. “The main directional fight that we are having is wanting to scale data availability at the base layer, and have execution and roll up on the second layer.”\n\nDon’t miss the next big story – join our free daily newsletter."}]