[{"id": 6, "url": "https://news.google.com/rss/articles/CBMibmh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzAzLzA2LzU0MzA4NDY4Nzg2Mzg2LXNoaWJhLWludS1zaGliLW5vdy1pbi1oYW5kcy1vZi13b3JsZHMtbGFyZ2VzdC1ldGhlcmV1bS13aGFsZXMv0gFyaHR0cHM6Ly9kYWlseWhvZGwuY29tLzIwMjMvMDMvMDYvNTQzMDg0Njg3ODYzODYtc2hpYmEtaW51LXNoaWItbm93LWluLWhhbmRzLW9mLXdvcmxkcy1sYXJnZXN0LWV0aGVyZXVtLXdoYWxlcy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 06 Mar 2023 08:00:00 GMT", "title": "54,308,468,786,386 <PERSON><PERSON> (SHIB) Now in Hands of World’s Largest Ethereum Whales - The Daily Hodl", "content": "Shiba Inu (SHIB) has cemented its status as the largest altcoin holding among the 500 biggest whales on the Ethereum (ETH) network, according to the blockchain tracking service WhaleStats.\n\nOver $606.3 million in SHIB, representing about 54 trillion tokens at current prices, are sitting in the deep-pocketed investor’s wallets.\n\nAccording to WhaleStats, the whales SHIB holdings outweigh their other altcoin positions by a vast margin.\n\nEthereum scaling solution Polygon (MATIC), the second biggest altcoin holding among the whales, currently trails SHIB by nearly $440 million.\n\n“The top 500 ETH whales are hodling:\n\n$606,354,054 SHIB\n\n$166,807,633 MATIC\n\n$155,922,150 BEST\n\n$146,459,691 LINK\n\n$131,748,690 CHSB\n\n$95,129,958 UNI\n\n$70,172,580 LOCUS\n\n$68,858,117 MANA”\n\nA number of Ethereum whales have been spotted rapidly accumulating Shiba Inu over the last several months as the Dogecoin rival’s ecosystem gears up for the launch of Shibarium, a layer-2 scaling project meant to make the network more efficient.\n\nLate last year, a wallet that has yet to be identified began rapidly accumulating SHIB, and is now the single largest unknown address holding SHIB.\n\nAnd last week, the wallet tracking platform Etherscan identified a large crypto whale who received 118,058,494,947 SHIB worth $1.34 million in one day.\n\nSHIB had an explosive start to the year, rising from $0.00000814 on January 1st to a high of $0.00001549 on February 4th – representing a 90% increase.\n\nIt has since retraced and at time of writing, SHIB is trading at $0.00001117.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nCheck Price Action\n\nFollow us on Twitter, Facebook and Telegram\n\nSurf The Daily Hodl Mix\n\n\n\n\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/Andi syaputra/LongQuattro"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3N0YWJsZWNvaW5zLWluZmxvdy10by1ldGhlcmV1bS1sMnMtcmlzZS01LXRvLW92ZXItMmIv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 06 Mar 2023 08:00:00 GMT", "title": "Stablecoins inflow to Ethereum L2s rise 5% to over $2B - CryptoSlate", "content": "Stablecoin inflow into Ethereum (ETH) layer 2 (L2) networks increased 5% in the last seven days to over $2 billion, according to DeFillama data.\n\nArbitrum averaged a $10M inflow per day in February\n\nIn February, Arbitrum’s stablecoin inflow averaged over $10 million daily as the network saw a total influx of around $300 million.\n\nThe inflows continued during the early days of March as it recorded a cumulative sum of $32.52 million. The network saw an outflow of $10.15 million during the same period.\n\nThe network’s significant inflow occurred on Mar. 5 when it recorded an inflow of $16.57 million in one day.\n\nDeFillama data showed that USD Coin (USDC) accounts for 67% of all stablecoins on Arbitrum with $968.63 million. Other dominant stablecoins on the network include Tether’s USDT and DAI, with a cumulative balance of $394.81 million.\n\nThe increased stablecoin inflow also coincided with a period when transaction activity on Arbitrum increased. The L2 network saw its transaction volume reach a new all-time high of 690,000 in Feb. Besides that, the increased network activity saw its transaction volume surpass Ethereum for the first time.\n\nMeanwhile, Arbitrum is the second most popular network for decentralized exchanges (DEX). For context, its total volume in the last 24 hours was $207.29 million, ahead of rivals like Polygon, Binance Smart Chain, and Optimism. However, it is miles below Ethereum, which is $1.03 billion.\n\nAccording to L2Beat, the total value of assets (TVL) locked on Arbitrum is $3.37 billion, rising 0.17% in the last seven days.\n\nOptimism stablecoin inflow rises 5% in seven days.\n\nSince the beginning of March, Optimism’s (OP) stablecoin inflow increased 4.89% to $669.11 million, according to DeFillama data.\n\nOptimism’s significant inflow occurred on Mar. 3 when it recorded around $6.3 million.\n\nThe increased inflow is in contrast to February when it recorded an outflow of nearly $120 million.\n\nUSDC is also the dominant stablecoin on Optimism. The Circle-issued stablecoin accounts for roughly 55% of the stablecoins on the network, with $364.56 million. The other stablecoins in the top three include Synthetix protocol’s sUSD and USDT, with a combined value of $145.77 million.\n\nOptimism’s TVL stands at $1.89 billion, according to L2 beats data.\n\nMentioned in this article"}, {"id": 32, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb3ZlcmVpZ24tcm9sbHVwLXJvbGxraXQtcGl0Y2hlcy1zZWNvbmQtMDIwMzA4MTQ2Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 06 Mar 2023 08:00:00 GMT", "title": "What Is a Sovereign Rollup? Rollkit Pitches Second Layer for Bitcoin - Yahoo Finance", "content": "Just a few months after the very first NFT was inscribed into Bitcoin, developers are trying to imbue the age-old blockchain with another new-age technology: rollups.\n\nAt least that’s what Rollkit—a self-described “modular framework for rollups”—claimed over Twitter on Sunday, revealing a research integration that would allow “sovereign rollups” on Bitcoin.\n\nAccording to Rollkit, its new rollup solution lets users produce rollups by retrieving and storing data on the Bitcoin blockchain. It was inspired by Ordinals, the Bitcoin NFT protocol which showed developers that—using Taproot—users could post arbitrary data to the blockchain.\n\nHow did we do it? 💡 Ordinals showed us it was possible to publish arbitrary data to Bitcoin. We saw this and decided to follow a similar process. At its core, all that was needed was two functions: one to submit rollup blocks, and another to retrieve them. pic.twitter.com/u6etICUAtg — Rollkit (@RollkitDev) March 5, 2023\n\nIn the traditional sense, rollups are batches of transactions that are “rolled up” off chain into one transaction, and then posted to Ethereum as a single on-chain transaction. This helps free up blockspace and lower transaction costs on the network, while still inheriting the settlement assurances of the base chain.\n\nA sovereign rollup is one that does not need a smart contract or use a settlement layer for validation—scalable and secure and with the \"sovereignty\" of a layer 1.\n\nHowever, some proponents of Ethereum, a network known to use layer-2 “rollups” as a transaction scaling solution, have taken issue with Rollkit’s use of the term.\n\nRyan Berckmans, a Web3 and Ethereum investor, said on Twitter that the sovereign rollup “is actually an alt L1 that stores its block data on Bitcoin.”\n\nAccording to Alexei Zamyatin, founder of the Bitcoin DeFi protocol Interlay, sovereign rollups as proposed by Rollkit inherit “nothing” of Bitcoin’s security.\n\nStory continues\n\n“Data availability—okay, but honestly, that's been used since 2012,” he continued. “The entire post describes 'I write some data to Bitcoin' with fancy buzzwords.”\n\nRollkit is still bullish on the possibilities.\n\n“This new integration expands the possibilities for rollups, and also has the potential to help bootstrap a healthy blockspace fee market on Bitcoin, enabling a more sustainable security budget,” the company writes.\n\nSome are likening sovereign rollups to Stacks, a high functionality layer 1 protocol that settles its blocks on the Bitcoin blockchain for security purposes. Stacks co-founder Muneeb Ali said on Twitter that this is an apt comparison, but noted some key differences.\n\nOn one hand, it takes 150 Stacks blocks before a transaction reaches”Bitcoin finality” on Stacks, but only 1 with sovereign rollups. On the other, Stacks blocks publish their data to Bitcoin in an efficient manner by merely hashing the data on-chain, whereas sovereign rollups publishes the full data.\n\n“The technically challenging part for both the Stacks layer and sovereign rollups is moving BTC in and out of the layer, said Ali. “The decentralized BTC peg is the most important part.”"}, {"id": 22, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb3ZlcmVpZ24tcm9sbHVwLXJvbGxraXQtcGl0Y2hlcy1zZWNvbmQtMDIwMzA4MTQ2Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 06 Mar 2023 08:00:00 GMT", "title": "What Is a Sovereign Rollup? Rollkit Pitches Second Layer for Bitcoin - Yahoo Finance", "content": "Just a few months after the very first NFT was inscribed into Bitcoin, developers are trying to imbue the age-old blockchain with another new-age technology: rollups.\n\nAt least that’s what Rollkit—a self-described “modular framework for rollups”—claimed over Twitter on Sunday, revealing a research integration that would allow “sovereign rollups” on Bitcoin.\n\nAccording to Rollkit, its new rollup solution lets users produce rollups by retrieving and storing data on the Bitcoin blockchain. It was inspired by Ordinals, the Bitcoin NFT protocol which showed developers that—using Taproot—users could post arbitrary data to the blockchain.\n\nHow did we do it? 💡 Ordinals showed us it was possible to publish arbitrary data to Bitcoin. We saw this and decided to follow a similar process. At its core, all that was needed was two functions: one to submit rollup blocks, and another to retrieve them. pic.twitter.com/u6etICUAtg — Rollkit (@RollkitDev) March 5, 2023\n\nIn the traditional sense, rollups are batches of transactions that are “rolled up” off chain into one transaction, and then posted to Ethereum as a single on-chain transaction. This helps free up blockspace and lower transaction costs on the network, while still inheriting the settlement assurances of the base chain.\n\nA sovereign rollup is one that does not need a smart contract or use a settlement layer for validation—scalable and secure and with the \"sovereignty\" of a layer 1.\n\nHowever, some proponents of Ethereum, a network known to use layer-2 “rollups” as a transaction scaling solution, have taken issue with Rollkit’s use of the term.\n\nRyan Berckmans, a Web3 and Ethereum investor, said on Twitter that the sovereign rollup “is actually an alt L1 that stores its block data on Bitcoin.”\n\nAccording to Alexei Zamyatin, founder of the Bitcoin DeFi protocol Interlay, sovereign rollups as proposed by Rollkit inherit “nothing” of Bitcoin’s security.\n\nStory continues\n\n“Data availability—okay, but honestly, that's been used since 2012,” he continued. “The entire post describes 'I write some data to Bitcoin' with fancy buzzwords.”\n\nRollkit is still bullish on the possibilities.\n\n“This new integration expands the possibilities for rollups, and also has the potential to help bootstrap a healthy blockspace fee market on Bitcoin, enabling a more sustainable security budget,” the company writes.\n\nSome are likening sovereign rollups to Stacks, a high functionality layer 1 protocol that settles its blocks on the Bitcoin blockchain for security purposes. Stacks co-founder Muneeb Ali said on Twitter that this is an apt comparison, but noted some key differences.\n\nOn one hand, it takes 150 Stacks blocks before a transaction reaches”Bitcoin finality” on Stacks, but only 1 with sovereign rollups. On the other, Stacks blocks publish their data to Bitcoin in an efficient manner by merely hashing the data on-chain, whereas sovereign rollups publishes the full data.\n\n“The technically challenging part for both the Stacks layer and sovereign rollups is moving BTC in and out of the layer, said Ali. “The decentralized BTC peg is the most important part.”"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vdGhlZGVmaWFudC5pby9ldGhlcmV1bS1kZXZlbG9wZXJzLWluLWNvbmZlcmVuY2UtYXQtdGhlLXdpbGQtd2VzdC1hcmUtd2FybWluZy10by1jb21wbGlhbmNl0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 06 Mar 2023 23:44:13 GMT", "title": "Ethereum Developers in Conference at the “Wild West” are Warming to Compliance - The Defiant - DeFi News", "content": "“Compliance” would seem like an unlikely buzzword at ETHDenver, one of Ethereum’s largest conferences, set literally in the “Wild West.”\n\nBy: <PERSON> • Loading...\n\nDive\n\n“Compliance” would seem like an unlikely buzzword at ETHDenver, one of Ethereum’s largest conferences, set literally in the “Wild West.”\n\nBut developers and founders have warmed up to the idea that crypto projects will need to comply with rules and regulations in order to survive.\n\nAt least seven projects participating in the event were directly involved in compliance solutions with at least five presentations addressing policy and regulation issues.\n\n<PERSON>, a former lawyer in the startup and venture capital space who is building a compliance solution called Lexproof, noted a change in attitude toward regulation at the conference.\n\n“I think there’s definitely a tone shift at ETHDenver compared to other events I went to last year,” he told The Defiant. “I think a lot of people are now accepting that there will probably have to be, for a lot of different applications, especially if the founders are in the U.S., some type of compromise.”\n\n<PERSON><PERSON><PERSON> named the know-your-customer (KYC) process and accredited investor verification as examples of traditional financial designations which may need to be more deeply accounted for in the crypto space.\n\nIndeed, it’s clear that, amidst a flurry of regulatory actions taken against crypto, compliance has moved from a sign of betrayal of crypto’s self-regulating ethos to something closer to a value proposition.\n\nAnd it makes sense — many in the crypto space are on edge after the US’s Securities and Exchange Commision (SEC) took perhaps one of its strongest actions yet last month alleging that the stablecoin UST, and its sister token LUNA, are securities.\n\nSome of those who bet early on tackling the hard problems around compliance say their work is paying off.\n\n“People laughed at us because it’s f*cking hard to bring things from the real world in a legally compliant way on-chain,” Maex Ament, co-founder and current board member of Centrifuge, a project built to put debt products on blockchains, told The Defiant at ETHDenver.\n\nCentrifuge started in 2017 and is seeing traction as the so-called “real world asset” space has shown signs of taking off — the project has facilitated over $300M in loans, according to a data dashboard by RWA.xyz.\n\nDeFi Alpha Premium Content DeFi Daily | Weekdays\n\nDeFi Alpha Letter | Weekly\n\nDefiant Podcast Transcript | Weekly\n\nInbox Dump | Saturday\n\nWeekly Recap | Sunday Looking for Alpha? Become a premium member of The Defiant and join our DeFi Alpha community. Start for free\n\nOriginating a loan using Centrifuge necessitates creating a Special Purpose Vehicle (SPV), an independent legal entity, according to the project’s documentation. And those looking to invest in the financing opportunities must go through a KYC and Anti-Money Laundering process.\n\nTo raise money, Centrifuge also did a traditional equity round 2017, said Ament. That was a time when the dominant funding mechanism in crypto was an initial coin offerings (ICO), a simple process whereby investorssent a major crypto asset like ETH to a project in exchange for its tokens.\n\nThe SEC’s website states that some ICOs may constitute securities offerings.\n\nOther projects involved with regulatory compliance at ETHDenver included Yug Network, kycDAO, both of which are working to bring off-chain credentials like KYC, on-chain. Others like Opolis, are focusing on enabling crypto’s swath of freelancers to remain or become tax compliant.\n\nAment said the team didn’t think it could fight the regulation battle around ICOs. Despite the traditional raise, Centrifuge is deep into the decentralization process and is giving control of its platform over to token holders, Ament added.\n\nDavid Liebowitz, who hosts a podcast called Flywheel DeFi, thinks that stablecoins are the most important area to watch in terms of regulation in the digital asset space.\n\n“Stablecoins are really the first and last line of defense when it comes to regulation of all of crypto,” he told The Defiant at ETHDenver. Liebowitz gave a talk on stablecoin regulation at the event and cited guidance issued by the state of New York, and also legislation drafted by the former senator Pat Toomey, as guiding lights for U.S. dollar-pegged assets.\n\nLexproof’s Cosme, too, said that he thought that the stablecoin space had a higher chance of seeing new legislation drafted into law, rather than the digital asset industry as a whole.\n\nCosme isn’t optimistic that the United States’ legislative branch will pass new laws for the digital asset industry in the near term. “Congress can pass legislation to make registration easy, in a way that makes sense for crypto companies and their business models and for their value proposition,” Cosme said, but he added that, given the legislative body’s current constituents, it is unlikely to push forward any laws of the sort.\n\nBoth Liebowitz and Cosme acknowledged the SEC’s aggressive stance taken in regards to the crypto space — Cosme said that the general outlook among lawyers is that the federal agency isn’t going to let up anytime soon.\n\nLiebowitz echoed a similar sentiment. “The SEC isn’t playing around,” he said. “They’re really taking the mandate of protecting investors to the widest possible conclusion.”\n\nIf ETHDenver is an accurate barometer, the crypto space as a whole is adapting to a more adversarial regulatory environemnt — and some are even looking to build products for it."}]