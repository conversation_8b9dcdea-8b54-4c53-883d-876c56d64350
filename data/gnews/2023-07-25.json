[{"id": 1, "url": "https://news.google.com/rss/articles/CBMieGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA3LTI1L2V0aGVyZXVtLXNvZnR3YXJlLWluZnJhc3RydWN0dXJlLXByb3ZpZGVyLWZsYXNoYm90cy1yYWlzZXMtNjAtbWlsbGlvbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Jul 2023 07:00:00 GMT", "title": "Ethereum Software Infrastructure Provider Flashbots Raises $60 Million - Bloomberg", "content": "Flashbots, a provider of software used to package Ethereum blockchain transactions, raised $60 million to help finance the development of a new version of the technology.\n\nThe San Francisco-based firm Paradigm led the series B round, a shift from the recent trend among Silicon Valley venture capitalists pivoting to artificial intelligence companies from crypto. The financing was raised through a so-called “beauty contest for decentralization” in which investors were selected based on their reverse pitches, according to a statement Tuesday from Cayman Islands-based Flashbots. The funding gives it a valuation of of at least $1 billion, a representative of Flashbots said."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMieGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA3LTI1L2V0aGVyZXVtLXNvZnR3YXJlLWluZnJhc3RydWN0dXJlLXByb3ZpZGVyLWZsYXNoYm90cy1yYWlzZXMtNjAtbWlsbGlvbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Jul 2023 07:00:00 GMT", "title": "Ethereum Software Infrastructure Provider Flashbots Raises $60 Million - Bloomberg", "content": "Flashbots, a provider of software used to package Ethereum blockchain transactions, raised $60 million to help finance the development of a new version of the technology.\n\nThe San Francisco-based firm Paradigm led the series B round, a shift from the recent trend among Silicon Valley venture capitalists pivoting to artificial intelligence companies from crypto. The financing was raised through a so-called “beauty contest for decentralization” in which investors were selected based on their reverse pitches, according to a statement Tuesday from Cayman Islands-based Flashbots. The funding gives it a valuation of of at least $1 billion, a representative of Flashbots said."}, {"id": 40, "url": "https://news.google.com/rss/articles/CBMiugFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtMjUtanVseS0yMDIzL2FydGljbGVzaG93LzEwMjEwMjA4NC5jbXPSAb4BaHR0cHM6Ly9tLmVjb25vbWljdGltZXMuY29tL21hcmtldHMvY3J5cHRvY3VycmVuY3kvY3J5cHRvLXByaWNlcy10b2RheS1saXZlLW5ld3MtYml0Y29pbi1kb2dlY29pbi1ldGhlcmV1bS1zaGliaGEtaW51LWNyeXB0b2N1cnJlbmN5LWxhdGVzdC11cGRhdGVzLTI1LWp1bHktMjAyMy9hbXBfYXJ0aWNsZXNob3cvMTAyMTAyMDg0LmNtcw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Jul 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin slips below $29,200 ahead of Fed meeting; XRP, Solana shed up to 5% - The Economic Times", "content": "\n\n\n\n\n\n\n\nThe cryptocurrency markets were majorly trading lower on Tuesday ahead of the US Federal Reserve's monetary policy decision tomorrow.Bitcoin (BTC) fell 2.24% to $29,120, whereas Ethereum (ETH) was just above the 1,850 level. BTC volume stood at approximately $14.6 billion, rising 45.6% in the last 24 hours.\"Bitcoin (BTC) volatility has dipped to its lowest level in a year as investors await the Federal Open Market Committee (FOMC) meeting this week. The 30-day estimate for BTC volatility has fallen to just 0.74%, which is the lowest level since January 16, 2023,\" said CoinDCX Research Team.\"The FOMC is expected to raise interest rates by 25 basis points this week with an expectation that this may be the last rate hike by the Federal Reserve in the foreseeable future,\" it said.Other top crypto tokens were also trading in the red on Tuesday. XRP, Cardano, Litecoin, Polygon, and Solana were also trading lower. Meanwhile, the global cryptocurrency market cap was trading lower, around $1.17 trillion, falling 1.85% in the last 24 hours.The total volume in DeFi is currently $2.51 billion, 7.33% of the total crypto market 24-hour volume. The volume of all stablecoins is now $30.82 billion, which is 90.2% of the total crypto market 24-hour volume.The market cap of Bitcoin, the world's largest cryptocurrency, was around $566 billion. Bitcoin's dominance is currently 48.41%, a decrease of 0.18% over the day, according to CoinMarketCap.\"Bitcoin is trading just above $29,000 today with a bearish momentum. The high possibility of a rate hike by the US Fed this week seems to have contributed to this decline,\" Vikram Subburaj, CEO of Giottus Crypto Platform, said.\"BTC needs to hold $28,500 in case of a further fall to regain lost value. A rally in the next few weeks is likely once the market accepts a rate hike. Its dominance continues to be below 50%,\" Subburaj said.Rajagopal Menon, Vice President at WazirX, said, \"The 7-day Bitcoin was down by 2.95%. The Simple Moving Average for 10-days and 200-day SMA indicate 'Sell' and 'Buy' at 29808 and 26325, respectively. Simple moving averages show a strong combination of 'Sell' and; Buy' sentiments.Crypto Cart: Quick Glance (Source: coinmarketcap.com , data as of 12.34 hours, IST on July 25, 2023)Bitcoin $29,120 -2.24%Ethereum $1,852 -0.96%Tether $0.9999 -0.02% BNB $237 -1.42%XRP $0.6891 -5.03%Cardano $0.3041 -2.82% Dogecoin $0.07743 8.57%Solana $23.23 -4.93%Polygon $0.7246 -2.39%Litecoin $89.2 -3.44%Polkadot $5.21 -2.55%Tron $0.08169 0.55%Shiba Inu $0.********* 0.31%(Note: Price change in last 24 hours)(Disclaimer: Recommendations, suggestions, views and opinions given by the experts are their own. These do not represent the views of The Economic Times)"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiNmh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLW9wdGltaXN0aWMtcm9sbHVwc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Jul 2023 07:00:00 GMT", "title": "Staying optimistic about Ethereum's optimistic rollups - Blockworks", "content": "Ethereum’s layer-2 ecosystems are growing.\n\nTo date, nearly 30 different layer-2, or L2, networks have deployed on Ethereum. Recent entrants include Mantle Network and Consensys’ Linea. Projects like Scroll, Taiko and Honeypot are geared up to enter the game.\n\nThese second-layer protocols are intended to help scale the network by shifting some of the computational processes off Ethereum itself. For users, this means cheaper and faster transactions compared to how they might proceed on the mainnet.\n\nL2 networks have, over time, become economies of their own, collecting billions of dollars worth of digital assets in locked-up deposits along the way.\n\nToday’s crop of L2 networks chiefly break down into two categories: optimistic rollups and zero-knowledge (ZK) rollups.\n\nOptimistic rollups move user transactions off-chain and then post the data back to Ethereum as “calldata.” Zero-knowledge rollups, on the other hand, also post their transactions off-chain, but they can validate that it is accurate without revealing the information inside.\n\nOf those building optimistic rollups, prominent players in the game are Arbitrum, Optimism, Base and Mantle. The first two, Arbitrum and Optimism, have amassed nearly $9 billion in total value locked (TVL) between the two protocols.\n\nToday’s zkSync-centric rollups include Polygon zkEVM, Starknet, Scroll and Linea.\n\nSo optimistic\n\nToday, optimistic rollups are the dominant protocols in the L2 space, according to data from L2BEAT, an information portal for the ecosystem.\n\nArbitrum currently possesses the highest market share, with a TVL of $5.82 billion.\n\nArbitrum developer Offchain Labs is still exploring ways for the protocol to become a stage-3 optimistic rollup, according to co-founder and Chief Technology Officer Harry Kalodner. Under that framework, the rollup would be completely run via smart contracts.\n\n“I firmly believe that ZK rollups are much more complicated than optimistic rollups, and the amount of people who actually understand the tech is very tiny,” Kalodner said.\n\nIn conversation, Kalodner noted that L2 growth often involves a trade-off between permissionless and accessibility of the technology.\n\n“We want to see everybody who wants to have an Arbitrum chain, have an Arbitrum chain, and we’re balancing that with the competitive realities of the market and being the technical leader,” Kalodner said.\n\nOptimism is the second-biggest rollup, with a TVL of $2.42 billion. Unlike Arbitrum, Optimism’s source code is completely open-source, meaning that anyone can use the code to build their own L2.\n\nOf those, Base Protocol, Coinbase’s decentralized L2 network, has most prominently used the Optimism stack to help build out its so-called superchain vision.\n\nJesse Pollak, head of protocols at Coinbase, believes that on-chain computation will compete directly with the likes of Amazon Web Services and Google Cloud over the next decade. In his view, future apps will be able to run their own infrastructure at a much higher rate.\n\n“The mental model I have for this is you can think of Base and OP mainnet as a shared compute model where you’re in the same compute environment, then certain apps will scale out of the compute environment…they’re going to be app chains or L3s,” he said.\n\nPollak notes that Base is focused on maximizing decentralization and security for as cheap as possible.\n\n“The strategy for us is to create an open source code base, that’s standardized, configurable, modular, creates decentralized governance and funding infrastructure that attracts developers and makes them feel like they’re part of something bigger than themselves,” Pollak said.\n\nPollak added: “This way we have a bottoms-up groundswell of technology that even if it looks a little bit chaotic in the beginning, and maybe a bit slower. In the long run, it ends up being way faster and way bigger.”\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiPmh0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvaG93LXRvLWNoZWNrLWFuLWV0aGVyZXVtLXRyYW5zYWN0aW9u0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 25 Jul 2023 07:00:00 GMT", "title": "How To Check An Ethereum Transaction? - Watcher <PERSON>", "content": "How To Check An Ethereum Transaction: A Comprehensive Guide\n\n\n\nNavigating the world of blockchain can be challenging, especially when you’re trying to keep track of your transactions.\n\nIn this guide, we’ll explain how to check an Ethereum transaction by walking you through the process step-by-step.\n\nWe’ll also answer common questions like “How do I track an Ethereum address?”, “How do I check my crypto transaction?”, and “How long does an Ethereum transaction take?”.\n\nAlso read: Investor Poll Shows 47% Confidence in Ethereum’s Rise Above Bitcoin\n\nUnderstanding Ethereum Transactions\n\nBefore we delve into how to check an Ethereum transaction, it’s essential to grasp what Ethereum transactions are.\n\nEthereum is a decentralized platform that permits users to conduct transactions with digital assets.\n\nEach Ethereum transaction is recorded on the blockchain network, providing transparency and public accessibility. Validators or miners on the network verify these transactions, making Ethereum a truly decentralized system.\n\nYou initiate a transaction when you send Ether (ETH) from one address to another. This transaction is then broadcast to the Ethereum network, where it is checked and verified.\n\nOnce the transaction gets a confirmation, it is added to a new block and permanently recorded on the blockchain.\n\nHow Ethereum Transactions Work\n\nEthereum transactions function through a series of steps:\n\nAn external account initiates a transaction, such as sending ETH to another user. The transaction request is broadcast to the Ethereum network. Validators on the network execute the transaction and propagate the change to the entire network. A transaction fee is deducted during the validation process. The validated transaction is included in a block.\n\nThere are different types of transactions on the Ethereum network, including regular transactions (from one account to another), contract-execution transactions (interactions with deployed smart contracts), and contract-deployment transactions (deploying the code of a smart contract).\n\nAlso read: Ethereum: Users Holding 1000+ ETH, Drops to 5-Year Low\n\nTracking an Ethereum Transaction: A Step-by-Step Guide\n\nNow that we’ve covered the basics, let’s dive into the steps on how to check an Ethereum transaction:\n\nStep 1: Acquiring the Transaction Hash\n\nYour journey to tracking your Ethereum transaction starts with obtaining the transaction hash. This unique identifier is given to every transaction on the Ethereum network. You can find this hash in the history or activity section of the crypto wallet or exchange platform you used to conduct the transaction.\n\nStep 2: Using an Ethereum Blockchain Explorer\n\nWith your transaction hash at hand, the next step is to visit an Ethereum blockchain explorer. These are online tools that allow you to view information about transactions, blocks, and addresses on the Ethereum blockchain. Some popular Ethereum blockchain explorers include Etherscan and Etherchain.\n\nStep 3: Input the Transaction Hash\n\nOn the blockchain explorer website, you’ll find a search field. Paste the transaction hash you obtained in Step 1 into this field and initiate the search. The explorer will then retrieve and display the transaction details.\n\nStep 4: Review the Transaction Details\n\nOnce you input the transaction hash, the transaction details will display. This information includes the sender and recipient addresses, the transaction amount, the gas fee paid, and the transaction’s current status. You can use these details to verify if the transaction was successful.\n\nBesides blockchain explorers, several tools can help you monitor your Ethereum transactions. These tools provide real-time updates and additional features for your transactions. Some popular options include:\n\nMetamask: Metamask is a renowned Ethereum wallet and browser extension that lets you manage your Ether and interact with decentralized applications. It also provides a transaction history and notifications for your transactions.\n\nMetamask is a renowned Ethereum wallet and browser extension that lets you manage your Ether and interact with decentralized applications. It also provides a transaction history and notifications for your transactions. MyEtherWallet (MEW): MyEtherWallet is a web-based wallet with a user-friendly interface for managing Ethereum transactions. It provides transaction tracking features and the ability to customize gas fees for faster transactions.\n\nMyEtherWallet is a web-based wallet with a user-friendly interface for managing Ethereum transactions. It provides transaction tracking features and the ability to customize gas fees for faster transactions. Etherscan Mobile App: Etherscan has a mobile app available for both iOS and Android devices. This app allows you to check your Ethereum transactions on the go, receive push notifications for transaction updates, and explore the blockchain.\n\nSource: Forbes\n\nHow Long Does an Ethereum Transaction Take?\n\nThe duration of an Ethereum transaction confirmation can vary based on several factors, including network congestion, the gas price of ETH, and the transaction’s complexity. Typically, Ethereum transactions can take anywhere from a few seconds to several minutes. However, during periods of high network activity, your transaction might take longer to process.\n\nConclusion: How To Check An Ethereum Transaction?\n\nIn conclusion, checking an Ethereum transaction involves using a hash and a blockchain explorer to review the details of the transaction.\n\nBlockchain explorers like Etherscan offer transparency and enable users to track their transactions on the Ethereum blockchain.\n\nAlso, tools like Metamask, MyEtherWallet, and the Etherscan Mobile App provide convenient ways to manage and monitor your Ethereum transactions.\n\nBy understanding and utilizing these tools, you can ensure smooth and successful transactions on the Ethereum network."}]