[{"id": 1, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDYvMTkvZXRoZXJldW0tZGV2ZWxvcGVycy1wcm9wb3NlLXJhaXNpbmctdmFsaWRhdG9yLWxpbWl0LXRvLTIwNDgtZXRoZXItZnJvbS0zMi1ldGhlci_SAX1odHRwczovL3d3dy5jb2luZGVzay5jb20vdGVjaC8yMDIzLzA2LzE5L2V0aGVyZXVtLWRldmVsb3BlcnMtcHJvcG9zZS1yYWlzaW5nLXZhbGlkYXRvci1saW1pdC10by0yMDQ4LWV0aGVyLWZyb20tMzItZXRoZXIvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 19 Jun 2023 07:00:00 GMT", "title": "Ethereum Developers Propose Raising Validator Limit to 2,048 Ether From 32 Ether - CoinDesk", "content": "The data indicates the demand for validators to enter the network and earn a nearly 5% annual yield. Such strong demand is likely stemming from large ether holders, who do not want to cash out and instead just want to earn some passive income on their holdings."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS12YWxpZGF0b3JzLW1heS1oYXZlLXRvLXN0YWtlLTIwNDgtZXRoLWRldnMtZGlzY3Vzc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 19 Jun 2023 07:00:00 GMT", "title": "Ethereum devs discuss increasing staking limit of validators by 64x - Cointelegraph", "content": "Ethereum core developers plan to implement a 64-fold increase in the maximum amount of staked Ether (ETH) required to become a validator, from 32 ETH to 2048 ETH while the minimum staking amount remain at 32 ETH.\n\nThe proposal was made during a June 15 Ethereum core developer consensus meeting by Ethereum Foundation researcher <PERSON>. The researcher noted that although the current limit of 32 ETH allows more validators to join the Ethereum network, making it more decentralized, it also leads to an inflation of the validator set size.\n\n<PERSON><PERSON><PERSON> added that such a large increase would ultimately help the Ethereum network become more efficient over time. Besides the proposal to increase the minimum required staked ETH for validators, <PERSON><PERSON><PERSON> also called for auto-compounding validator rewards.\n\nEthereum consensus layer meeting. Source: YouTube\n\nThe auto-compounding of rewards would allow validators to make more money on their staked ETH. Currently, to produce any staking income, rewards received in excess of the 32 ETH cap must be transferred to another account. These benefits could be rapidly compounded if the cap were raised, giving validators a practical way to increase their earn reward.\n\n<PERSON><PERSON><PERSON> claimed the current proposal would not only make the Ethereum network more efficient and make way for validators to earn more money, but it would also help large node operators, such as exchanges, which currently manage thousands of validators.\n\nRelated: Hong Kong legislator invites Coin<PERSON> to the region despite SEC scrutiny\n\nThe 32 ETH limit has led to a significant surge in validator addresses after Ethereum’s transition to a proof-of-stake network. At present, there are over 700,000 validators, with around 90,000 awaiting activation in the queue.\n\nTotal Ethereum validators. Source: Beaconscan\n\nThe proposal received mixed reactions from the crypto community, with several users pointing out that such a significant change in staked ETH would lead to fewer validators and thus make the network more centralized. Other users dismissed the idea and claimed it wouldn’t benefit the network.\n\nMagazine: Crypto regulation: Does SEC Chair Gary Gensler have the final say?"}, {"id": 18, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhlcmV1bS1sYXllci0yLW5ldHdvcmstemtzeW5jLTA3NTQ1NzUyMC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 19 Jun 2023 07:00:00 GMT", "title": "Ethereum Layer 2 Network zkSync Era's Locked Value Surpasses $500M - Yahoo Finance", "content": "Matter Labs' zkSync Era, a zero-knowledge (ZK) rollup aimed at scaling Ethereum, continues to attract capital at a brisk pace.\n\nThe total value locked (TVL) on zkSync Era rose above $500 million early Monday, marking a 12% increase in one week, according to data source L2Beat. The TVL is a metric widely used to track the total value of digital assets locked or staked on a decentralized finance platform.\n\nThe scaling solution, which retains Ethereum Virtual Machine (EVM) compatibility while ensuring native account abstraction, is already the third-largest rollup by total value locked next to Arbitrum and Optimism.\n\nRead more: Where Is the Ethereum Virtual Machine Headed in 2023? (Hint: Beyond Ethereum)\n\nAt press time, more than 220,000 ether (ETH), or around $378.3 million, about 121 million USDC (a dollar-pegged stablecoin) and 14.43 million MUTE, worth $7.53 million, were locked on zkSync. MUTE is the native cryptocurrency of a zkRollup-based decentralized exchange, Mute.\n\nThe daily active addresses have steadily increased since May, averaging 175,000 in the past four weeks. zkSync was launched in March 2023.\n\nRecently, liquid staking solution Rocketpool went live on zkSync Era, joining the list of decentralized applications moving to layer 2 platforms. The increased user demand for rollups is likely driven by Ethereum core developers' focus on implementing the ethereum Improvement Proposal (EIP) 4844, according to Galaxy Digital.\n\nThe EIP 4844 will introduce a new transaction type to Ethereum, which accepts \"blobs\" of data and reduce transaction fees on rollups.\n\n\"The prioritization of EIP 4844 as the next major code change in Ethereum’s forthcoming Cancun/Deneb upgrade slated to activate sometime this Fall or Winter next year affirms the importance of rollups for the long-term scalability of Ethereum and the need for dapps built on Ethereum to eventually migrate the majority of their operations to a more cost-effective rollup,\" Alex Thorn, head of research at Galaxy Digital, said in a newsletter dated June 2.\n\nStory continues\n\nNote that activity on other ZK rollups like Starknet and Polygon zkEVM also remains brisk. That has some observers calling a Zk season ahead – a crypto market slang for a period where ZK rollups outperform other market sectors by a significant margin.\n\nLooks like its going to be Zk season.\n\n\n\nPolygon zkEVM, Starknet and zkSync Era are continuing to put all time highs as TVL, Daily Active addresses and Daily transactions continue their uptrend. pic.twitter.com/vfUcnHNoPA — Emperor Osmo🧪 (@Flowslikeosmo) June 18, 2023\n\nRead more: Ethereum Layer 2 Network zkSync Era Jumps to Nearly $250M in Locked Value"}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS1vcmRpbmFscy1ldGhzY3JpcHRpb25zLWFycml2ZS1vbi1ibG9ja2NoYWlu0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 19 Jun 2023 07:00:00 GMT", "title": "Watch out, Ordinals — 30,000 'Ethscriptions' land on Ethereum - Cointelegraph", "content": "Ethereum users have been given another way to create nonfungible tokens (NFTs) and other digital assets on the blockchain with the launch of a new protocol.\n\nLaunched on June 17, the protocol, dubbed “Ethscriptions,” is a nod to the Bitcoin (BTC) Ordinals protocol — where assets are known as “inscriptions.”\n\nEthscriptions was developed by music website Genius.com co-founder <PERSON>, who uses the pseudonym <PERSON><PERSON><PERSON> on Twitter. <PERSON><PERSON> declared the project a “huge success” in a series of tweets on June 17 and noted nearly 30,000 Ethscriptions had been created within the first 18 hours of the protocol going live.\n\nWith almost 30k Ethscriptions in\n\nThank you for seeing the massive potential here!\n\n\n\nI am on Ethscriptions 24/7, but I need your help!\n\n\n\nDM @proroketh to join our protocol Twitter chat. Ideas, bug reports, NO alpha, NO trading, NERDS ONLY! pic.twitter.com/udKdsVT0L8 — Middlemarch (@dumbnamenumbers) June 17, 2023\n\nAccording to <PERSON><PERSON>, Ethscriptions assets utilize Ethereum “calldata” — the data within a smart contract — to allow for a “cheaper” and “more decentralized” minting process compared to conventional smart contract-based methods.\n\nUsers are currently limited to image-only inscriptions, but <PERSON><PERSON> says the protocol will allow for different file types to be uploaded in the future. Currently, users are able to “ethscribe” any image as long as it’s less than 96 kilobytes in size.\n\n<PERSON><PERSON>’s debut project on the Ethscriptions protocol, dubbed “Ethereum Punks,” witnessed a considerably positive response from the community, with all 10,000 assets being claimed near-instantaneously.\n\nIntroducing Ethscriptions: a new way of creating and sharing digital artifacts on Ethereum using transaction calldata.https://t.co/XfQ7RdtblD\n\n\n\nALSO: Introducing Ethereum Punks. Because why should all non-contract Punk collecting happen on Bitcoin?https://t.co/d3Ycwbdc2Z… — Middlemarch (@dumbnamenumbers) June 16, 2023\n\nLehman added the project’s launch garnered so much user activity that the API interface for the official Ethscriptions website temporarily crashed.\n\nRelated: Bitcoin Ordinals to bridge Ethereum NFTs with the launch of BRC-721E\n\nDue to pre-existing infrastructure that allows for the creation of NFTs and other digital assets on the Ethereum network, it’s unclear if Ethscriptions will witness the same level of popularity as Bitcoin Ordinals has.\n\nIn less than six months, the total number of Ordinals inscribed on Bitcoin surged from zero to 10 million. The enormous surge in activity was driven in large part by users embracing the novelty of being able to mint assets, which later included entirely new tokens — by way of the BRC-20 token standard — on the Bitcoin network.\n\nNFT Creator: ‘Holy shit, I’ve seen that!’ — Coldie’s Snoop Dogg, Vitalik and McAfee NFTs"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vd3d3LmNyeXB0b3RpbWVzLmlvLzIwMjMvMDYvMTkvZXRoZXJldW0tdmFsaWRhdG9yLWxpbWl0LXRvLWluY3JlYXNlLWZyb20tMzItdG8tMjA0OC1ldGgv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 19 Jun 2023 07:00:00 GMT", "title": "Ethereum Validator Limit to Increase from 32 to 2,048 ETH - Crypto Times", "content": "In a recent development, Ethereum’s core developers have been deliberating on a proposal that suggests a significant increase in the maximum validator balance. The current limit of 32 ether (ETH) per validator could potentially be raised to 2,048 ETH.\n\nThis proposed change aims to enhance the efficiency of the Ethereum network while maintaining decentralization. Let’s delve into the details of this proposal and its potential implications.\n\nPresently, Ethereum validators face an effective balance cap, which restricts them to a minimum and maximum stake of 32 ETH. This limitation forces large-scale staking operations to create multiple validators in order to earn yield on amounts exceeding this threshold. Consequently, there has been a surge in the number of validators, with approximately 600,000 active validators and an additional 90,000 waiting to be activated in the queue.\n\nDuring a recent Ethereum core developer consensus meeting, <PERSON>, an Ethereum Foundation researcher and a prominent advocate of the proposed change, put forth a compelling argument in favor of raising the validator cap. <PERSON><PERSON><PERSON> highlighted that while the current cap promotes decentralization, it inadvertently leads to an inflationary growth of the validator set size.\n\nOne of the key benefits of raising the validator cap is the potential to improve the network’s efficiency in achieving finality within a single Ethereum slot. By slowing down the expansion of the active validator set, this change would enable more effective utilization of resources and enhance the overall performance of the network.\n\nAnother significant advantage that the proposed increased cap brings is the possibility of auto-compounding validator rewards. Currently, any rewards earned beyond the 32 ETH cap must be directed elsewhere to generate staking yield. However, if the cap is lifted, validators would have the opportunity to immediately compound these rewards, thereby maximizing their earnings from staked ETH.\n\nThe proposal also addresses operational concerns faced by larger node operators, including exchanges like Coinbase and defi protocols such as Lido. These operators currently maintain tens of thousands of validators due to the existing 32 ETH cap per validator. By raising the maximum effective validator balance, they would be able to manage fewer but higher-stake validators, reducing complexity and streamlining their operations.\n\nWhile the proposed change offers several advantages, it is crucial to consider the associated risks. With a higher validator balance, there is a potential increase in penalties for accidental double attestations or proposals, commonly known as “slashing.” It is essential to carefully evaluate and mitigate these risks to ensure the smooth functioning of the Ethereum network.\n\nAlso Read: Ethscriptions a new NFT Protocol live on ETH Blockchain\n\nThe proposal is currently under debate among the core developers, who have agreed to continue discussing its implementation details on social platforms such as ETHMagicians and Discord. This open dialogue allows for a comprehensive evaluation of the proposal’s impact and the incorporation of valuable insights from the Ethereum community."}]