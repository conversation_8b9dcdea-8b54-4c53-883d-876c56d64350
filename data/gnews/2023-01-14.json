[{"id": 6, "url": "https://news.google.com/rss/articles/CBMiOWh0dHBzOi8vZGFpbHljb2luLmNvbS9wb2x5Z29uLXNlY29uZC1sYXllci1vZi1ibG9ja2NoYWluL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 14 Jan 2023 08:00:00 GMT", "title": "Polygon: The Second Layer of Blockchain - DailyCoin", "content": "The Ethereum blockchain is the second biggest blockchain in the world. It’s also the poster boy, as it were, of flexibility. The chain hosts hundreds of dApps and thousands of transactions are completed on it every day. Ethereum can host this number of transactions because it has smart contract compatibility, which means anyone can build anything they want on it via a contract.\n\nBut there’s one problem. The popularity of Ethereum means that there are too many projects built on it. Since Ethereum has a scalability problem, the chain struggles to complete all these transactions in time. This means that people experience slow and buggy transactions on the Ethereum blockchain. It also means that the transaction fees can rise to unviable heights.\n\nThis was a huge structural problem for Ethereum as it meant that it couldn’t fulfill its potential. However, the solution to this problem wasn’t to tinker with Ethereum’s code to make it faster. It was to create a new parallel chain.\n\nPolygon was that new and parallel chain. The Polygon chain is a fast parallel blockchain that’s running alongside Ethereum. It’s a side chain of Ethereum that makes it faster and connects users, at the same time, to all the projects on Ethereum.\n\nAside from the fact that Polygon is hitched onto the Ethereum blockchain, it’s just like every other Layer-1 (L1) blockchain. This means it also has a consensus mechanism, blocks, and validators.\n\nPolygon, like Ethereum, runs a Proof-of-Stake (PoS) consensus mechanism. This means that validators have to stake their MATIC tokens— which means they agree not to use or sell them— and hope that the system picks one of the validators to validate a particular block.\n\nValidators of Polygon then validate the new block of transactions and add them to the accepted pile. In exchange for this, they get a cut of the newly minted MATIC. Validators who are malicious or mischievous will lose much of their staked MATIC, and this incentivizes honest behavior amongst validators."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vdS50b2RheS9vcHRpbWlzbS1vcC1wb2x5Z29uLW1hdGljLW9yLWFyYml0cnVtLXdoaWNoLWV0aGVyZXVtLWwyLWlzLWRvbWluYW500gFXaHR0cHM6Ly91LnRvZGF5L29wdGltaXNtLW9wLXBvbHlnb24tbWF0aWMtb3ItYXJiaXRydW0td2hpY2gtZXRoZXJldW0tbDItaXMtZG9taW5hbnQ_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 14 Jan 2023 08:00:00 GMT", "title": "Optimism (OP), Polygon (MATIC) or Arbitrum: Which Ethereum L2 Is Dominant? - U.Today", "content": "Advertisement\n\nCryptocurrency analyst <PERSON><PERSON><PERSON> compared the key indicators of Optimism (OP), Polygon Network (MATIC) and Arbitrum to find out who is actually using these second-layer solutions.\n\nPolygon Network (MATIC) usage spiked in Q4, 2022, thanks to this catalyst\n\n<PERSON><PERSON><PERSON> took to Twitter to share the analysis of network activity on Ethereum (ETH) second-layer solutions, including Polygon Network (MATIC), Optimism (OP) and Arbitrum. The researcher used on-chain data from Dune Analytics custom-made dashboards.\n\nWith the coming of Shanghai and the sudden run up of tokens and LSDs, an obvious narrative for 2023 is @ethereum Layer 2s.\n\n\n\nWho is winning the L2 wars and what does on chain wallet and transaction data tell us? I crunch data with @DuneAnalytics 📊 to help you find out!\n\n\n\n👇\n\n0/18 pic.twitter.com/tto3JAa0n2 — <PERSON><PERSON>_<PERSON> (🍡, 📊) (@defi_mochi) January 14, 2023\n\nAll L2 networks witnessed a massive inflow of users after the collapse of the FTX exchange in November as more traders decided to migrate to the decentralized futures trading platform GMX.\n\nWhile Polygon (MATIC) is still the leader when it comes to daily transactions, Optimism (OP) managed to quadruple its ERC-20 transfers market share in the last three months. It siphoned a part of Arbitrum's activity.\n\nThanks to its NFT-centric partnership with Reddit, Polygon (MATIC) is the undisputed leader of ERC-721 transactions. Out of 13 million Polygon (MATIC) wallets with NFTs, 4 million took part in this collaboration.\n\nArbitrum is whale-dominated, while Optimism (OP) approaches Bedrock\n\nMeanwhile, Optimism has a higher number of wallets with ERC-20 balances: over 789,000 crypto enthusiasts use it for transfers of Ethereum-based tokens.\n\nAnalysts also noticed that Arbitrum remains a beloved network of whales (wallets with over $100,000 in equivalent). A total of 3,968 large wallets on Arbitrum are in charge of its 25% funds.\n\nAlso, Arbitrum demonstrated the most impressive growth in total value locked (TVL), a crucial DeFi metric. While Polygon (MATIC) and Optimism (OP) are lagging, things can change with the Bedrock upgrade activation on Optimism."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiXGh0dHBzOi8vd3d3LmZvb2wuY29tL2ludmVzdGluZy8yMDIzLzAxLzE0L3doeS1iaXRjb2luLWV0aGVyZXVtLWFuZC1zb2xhbmEtanVtcGVkLW92ZXItMTAtaW4v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 14 Jan 2023 08:00:00 GMT", "title": "Why Bitcoin, Ethereum, and Solana Jumped Over 10% in the Last 24 Hours - The Motley Fool", "content": "What happened\n\nThe cryptocurrency market suddenly came to life on Friday night for the first time since FTX collapsed in early November. Nearly every asset was up big, and some with ties to FTX are significantly higher.\n\nBitcoin (BTC 0.56%) jumped 10.9% and is trading at $20,909 at 8 a.m. ET, surpassing the $20,000 level for the first time since FTX became insolvent in early November. Ethereum (ETH 0.04%) is up 10.1% in the last day to $1,540 and Solana (SOL -0.87%) is the big mover, popping 36.1% to $22.53. Over the past week, the cryptocurrencies are up 23.2%, 21.5%, and 69.9%, respectively.\n\nSo what\n\nMomentum can be a powerful force in cryptocurrencies, especially on the weekend. Trading on weekends often leads to some of the biggest moves, as traders take time off and there's less liquidity (buyers and sellers) in the market.\n\nFueling some of the moves is liquidations of short positions. According to coinglass.com, $730 million of short positions have been liquidated in the last 24 hours with $240 million of that being in Bitcoin, $261 million in Ethereum, and $26.3 million of Solana short trades.\n\nThe two macro news items this week were inflation data that showed a reduction in prices over the past month, leading to speculation the Federal Reserve will soon stop raising rates and may even be forced to lower rates sooner than expected. This pushed growth stocks and high-risk assets higher this week, but the momentum really hit crypto hard on Friday evening into Saturday morning.\n\nSince FTX got us here, it may in part be FTX that has driven the rally. By that, I mean that liquidators of FTX's assets have recovered $5 billion in assets to return to customers and they've been selling leveraged positions as quickly as possible. That added selling pressure to a market with lower liquidity, and when added to the short positions this can drop prices. But when they slow selling and short positions get liquidated (as they have today), the bounce higher can be swift.\n\nNow what\n\nThe price of cryptocurrencies fundamentally comes down to supply and demand. Supply isn't rising very quickly, so demand is the key. When FTX collapsed and the crypto winter wore on, many investors and traders either gave up on the market or took short positions to take advantage of weakness. But over time there should be more and more users of the blockchain, helped lately by significant corporate interest.\n\nI think the long-term trend for cryptocurrencies is still higher, but the volatility seen over the last two months will continue. There needs to be more use cases that disrupt old businesses or create new businesses, and that's when mass adoption will come. That's why Solana is my top cryptocurrency long term, given its low costs and high speed. But a rising tide lifts all boats, and bullishness on crypto overall is helping everything trade higher today."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vZGVjcnlwdC5jby8xMTkxODUvd2h5LWV0aGVyZXVtLWJ1bGxzLWFyZS10dXJuaW5nLXRvLWxzZNIBRWh0dHBzOi8vZGVjcnlwdC5jby8xMTkxODUvd2h5LWV0aGVyZXVtLWJ1bGxzLWFyZS10dXJuaW5nLXRvLWxzZD9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 14 Jan 2023 08:00:00 GMT", "title": "Why Ethereum Bulls Are Turning to LSD - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nDecrypting DeFi is Decrypt's DeFi email newsletter. (art: <PERSON>)\n\nThe second week of the new year has been a heady week for the coins, with Bitcoin, Ethereum, and Solana racking up serious gains.\n\nZooming in closer, however, the market’s biggest winners are none other than liquid staking tokens, also called “liquid staking derivatives” (LSD).\n\nAD\n\nAD\n\nThe tokens behind projects like Lido Finance (up 50.3%) and Rocket Pool (up 23.3%) have absolutely soared over the last few days. The reason? Ethereum devs are rolling up their sleeves ahead of a key upgrade to the network: Shanghai.\n\nLet’s break that down.\n\nSince executing the merge last September, Ethereum has swapped to a proof-of-stake (PoS) consensus mechanism. This means no more power-hungry mining machines, and, in their place, so-called validators. Validators and miners effectively do the same thing, verifying transactions and ensuring little on-chain mischief occurs.\n\nStill, validators can be better distributed than miners due to their lower cost of capital and maintenance. Instead of having to buy out a multi-million-dollar mining farm somewhere in Siberia and hiring a team of engineers to keep those miners running non-stop, all you need to become a validator is 32 ETH and the know-how to keep a single node connected to the Ethereum network at all times.\n\n32 ETH, though, is still roughly $45,000 at press time, so it’s a hefty sum. And that’s where these LSD projects come into play.\n\nThey let you stake any amount of Ethereum you can afford. In exchange, they’ll give you another token (Lido’s staked ETH token is called “stETH,” for example) that can be put to use elsewhere.\n\nAD\n\nAD\n\nToday, you can earn as much as 301% when you stake your stETH in certain parts of the ecosystem, according to DeFi Llama. Its wide adoption in DeFi is likely one of the reasons why it’s so popular; of this type of offering (excluding centralized exchange-based equivalents), Lido commands more than 88% of the LSD market.\n\nBeacon chain depositors over time. (Source: Dune)\n\nWhen compared with centralized platforms like Kraken, Bitcoin Suisse, or services like Staked.US, Lido still enjoys 28.9% of the market. Runner-up Kraken has just 5.57%.\n\nWhat does this have to do with the Shanghai update?\n\nLike the merge, Shanghai is another key upgrade to Ethereum. It will bundle several key improvements, but the most important is the one which will let the above-mentioned stakers finally withdraw their holdings from the network. Currently, that’s not possible (and last year it had a lot of folks rattled about whether it would ever happen).\n\nThis meant that stakers who rushed into the Beacon Chain with their 32 Ethereum or a liquid-staking alternative with a smaller sum were basically depositing funds with merely a promise that one day they’d be able to withdraw.\n\nNow, though, that promise appears to be approaching reality (and reducing stakers risks considerably).\n\nFor more evidence of folks rushing to try out LSDs, look no further than Lido overtaking DeFi’s unofficial central bank MakerDAO as the largest DeFi protocol.\n\nDecrypting DeFi is our DeFi newsletter, led by this essay. Subscribers to our emails get to read the essay before it goes on the site. Subscribe here.\n\nAD\n\nAD"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9wb2x5Z29uLXRlc3RzLXplcm8ta25vd2xlZGdlLXJvbGx1cHMtbWFpbm5ldC1pbnRlZ3JhdGlvbi1pbmJvdW5k0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 14 Jan 2023 08:00:00 GMT", "title": "Polygon tests zero-knowledge rollups, mainnet integration inbound - Cointelegraph", "content": "Ethereum layer-2 scaling protocol Polygon (MATIC) is carrying out performance testing of zero-knowledge rollup (zk-Rollups) technology ahead of full integration with its mainnet.\n\nThe development of the technology, called Polygon zkEVM (Ethereum Virtual Machine), has been ongoing for over three years by the Polygon Hermez team. The team has already confirmed that zero-knowledge proofs are possible on Ethereum by generating over 12,000 zk-Proofs in a primary version of the zkEVM testnet.\n\n<PERSON>, project lead of Polygon zkEVM and PolygonID, unpacked the development of the functionality in correspondence with Cointelegraph. Layer-2 platforms have continued to evolve and improve functionality which has played a key role in driving Ethereum’s scalability.\n\nAs he explained, zero-knowledge rollups have increased the speed at which layer-2 platforms can achieve finality while ensuring secure validation of transactions with zero-knowledge technology. In blockchain terms, finality is the point at which a block of transactions is considered to have been permanently and irreversibly added to the blockchain:\n\n“Finally, we have zkEVMs, such as Polygon zkEVM, that offer all the above in addition to the equivalence to Ethereum Virtual Machine with its advanced methods of zk-STARKs and zk-SNARKs.”\n\nAccording to <PERSON>, Polygon zkEVM includes the first complete source code available EVM-equivalent zkProver, which passes all Ethereum vector tests at over 99%. He described the completion of validity proofs for conventional user transactions as “the most challenging and rewarding effort” since his team began developing its native zkEVM.\n\nTwo years ago, the Polygon team estimated that developing zk-Rollups with EVM compatibility would take up to ten years. Given the strides made, the team describes zkEVM as the end game, combining advances with layer-2 scalability and fast finality. This offers a myriad of benefits to users when adding greater throughput and lower fees.\n\nCointelegraph also queried the difference between Polygon zkEVM and fellow Ethereum layer-2 scaling network StarkNet’s proprietary ZK-SNARK and ZK-STARK technology. As previously reported, Zero-Knowledge Scalable Transparent Argument of Knowledge, or zk-SNARKS, primarily increases scalability by batching thousands of transactions with a single proof to confirm validity on-chain.\n\nRelated: What the Ethereum Merge means for the blockchain’s layer-2 solutions\n\nSchwartz said the main difference between the projects is that zkEVM focuses on natively scaling the Ethereum ecosystem instead of other zk-Rollups just scaling transactions and enhancing performance in a different VM format.\n\nPolygon’s approach purports to meet the classification of a type 2 zkEVM described by Ethereum co-founder Vitalik Buterin in August 2022. As per Buterin’s summary, type 2 zkEVMs aim to be fully compatible with existing applications, but make minor modifications to Ethereum for easier development and faster proof generation. Schwartz added:\n\n“In contrast, StarkNet is positioned as a Type 4, introducing a new high-level language and requiring transpilers to translate solidity code into their language.”\n\nAt the same time, Schwartz welcomed the opportunity to have more benchmarks and source code available from other projects in order to learn from different approaches. Activity on Ethereum layer-2 solutions continues to grow, with blockchain data showing that transaction volume Arbitrum and Optimism overshadowed transactions on the Ethereum mainnet going into 2023."}]