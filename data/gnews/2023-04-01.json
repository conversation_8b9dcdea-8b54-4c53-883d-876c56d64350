[{"id": 5, "url": "https://news.google.com/rss/articles/CBMiaWh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3ZpdGFsaWstYnV0ZXJpbi1yZXZlYWxzLW1ham9yLWNoYWxsZW5nZS1mb3ItZXRoZXJldW1zLWZ1dHVyZS1hbmQtaG93LXRvLXNvbHZlLWl0L9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 01 Apr 2023 07:00:00 GMT", "title": "<PERSON><PERSON> reveals major challenge for Ethereum's future – and how to solve it - CryptoSlate", "content": "What is CryptoSlate Alpha? A web3 membership designed to empower you with cutting-edge insights and knowledge, powered by Access Protocol. Learn more ›\n\nConnected to Alpha Welcome! 👋 You are connected to CryptoSlate Alpha. To manage your wallet connection, click the button below.\n\nImportant: You must lock a minimum of 20,000 ACS If you don't have enough, buy ACS on the following exchanges: Coinbase Kucoin Orca\n\nConnect via Access Protocol Access Protocol is a web3 monetization paywall. When users stake ACS, they can access paywalled content. Learn more ›\n\nDisclaimer: By choosing to lock your ACS tokens with CryptoSlate, you accept and recognize that you will be bound by the terms and conditions of your third-party digital wallet provider, as well as any applicable terms and conditions of the Access Foundation. CryptoSlate shall have no responsibility or liability with regard to the provision, access, use, locking, security, integrity, value, or legal status of your ACS Tokens or your digital wallet, including any losses associated with your ACS tokens. It is solely your responsibility to assume the risks associated with locking your ACS tokens with CryptoSlate. For more information, visit our terms page."}, {"id": 13, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vY29pbmdhcGUuY29tL2V0aGVyZXVtLXNjYWxpbmctc29sdXRpb24temtzeW5jLWVyYS1tYWlubmV0LW1hbGZ1bmN0aW9uZWQtcmVzdW1lcy1ibG9jay1wcm9kdWN0aW9uLWFmdGVyLTRocnMv0gF4aHR0cHM6Ly9jb2luZ2FwZS5jb20vZXRoZXJldW0tc2NhbGluZy1zb2x1dGlvbi16a3N5bmMtZXJhLW1haW5uZXQtbWFsZnVuY3Rpb25lZC1yZXN1bWVzLWJsb2NrLXByb2R1Y3Rpb24tYWZ0ZXItNGhycy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 01 Apr 2023 07:00:00 GMT", "title": "zkSync Era Mainnet Malfunctioned, Resumes Block Production After 4Hrs - CoinGape", "content": "zkSync Era malfunctioned and went down for over 4hrs on Saturday as the Layer-2 blockchain produced no blocks during these hours. Zero-knowledge proof roll-up (zk-Rollup) zkSync Era is one of the main Ethereum Layer-2 scaling solutions compatible with Ethereum Virtual Machine (EVM), with mainnet launched by Matter Labs on March 24.\n\nzkSync Era Down For Over 4 Hours\n\nAccording to transaction data on zkSync Era Block Explorer, no blocks were produced on the zkSync Era mainnet blockchain from 01:52 AM to 6:02 AM CET on April 1. Several users reported pending transactions on zkSync Era, claiming the blockchain is down.\n\nadvertisement\n\nThere was no communication about the issue on the official Twitter handles of zkSync, Matter Labs, and others. zkSync team confirmed that they are aware of the issue with the zkSync Era mainnet and working to bring the network online. The team asked users to avoid initiating transactions now and wait for for mainnet recovery.\n\nAfter 4hrs of downtime, zkSync Era Block Explorer showed normal block production on the zkSync Era mainnet.\n\nzkSync Era – First zkEVM Mainnet\n\nMatter Labs on March 24 launched the zkSync Era mainnet alpha, becoming the first zkEVM mainnet to go live before competitors rollout their EVM-compatible solution. Polygon launched its zkEVM mainnet beta on March 27.\n\nIn addition to enabling faster and cheaper transactions, ZkSync Era supports native account abstraction — improved ERC-4337 account abstraction, LLVM compiler, data compression, and Hyperscalability.\n\nWhile the zkSync Era is not fully decentralized yet as the team monitors, audits, and evaluates vulnerabilities and risks, the team will soon remove the Alpha label.\n\nAlso Read: Elon Musk Might Stop Promoting Dogecoin; Will Doge Take A Hit?"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1vdXRzaGluZXMtZG90LWFuZC1hZGEtaW4tZGV2ZWxvcGVycy1jb3VudC10aGFua3MtdG8v0gFXaHR0cHM6Ly9hbWJjcnlwdG8uY29tL2V0aGVyZXVtLW91dHNoaW5lcy1kb3QtYW5kLWFkYS1pbi1kZXZlbG9wZXJzLWNvdW50LXRoYW5rcy10by9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 01 Apr 2023 07:00:00 GMT", "title": "Ethereum outshines DOT and ADA in developers’ count, thanks to… - AMBCrypto News", "content": "Ethereum surpassed Cardano and Polkadot in developers’ count.\n\nTraders are taking ETH positions before the Shapella update.\n\nAhead of the 12 April completion of the Shanghai upgrade, Ethereum [ETH] has taken the mantle as the blockchain with the most active developers in the Layer one (L1) and Layer Two (L2) ecosystems.\n\nRecall that projects like Polkadot [DOT], and Cardano [ADA] were mostly at the crest of this activity for the last few months.\n\nRealistic or not, here’s ETH’s market cap in BTC terms\n\nThe active developers metric, as measured by Token Terminal, tracks the number of GitHub developers engaged in polishing the features of a project.\n\nAccording to the blockchain data and dApp aggregator, Ethereum had 199 developers, while Polkadot could only register 172 in second place.\n\nApart from the top two, L2 projects including the ones getting hyped as Polygon [MATIC], and Arbitrum [ARB] improved in this regard. However, they remained far behind Ethereum. Meanwhile, as Ethereum ramps up preparations to enable staked Ether [stETH] withdrawal, traders seem to get involved in taking ETH positions.\n\nNot a question of “Options”\n\nAccording to Glassnode, the options Open Interest (OI) across all exchanges had been increasing since 24 March. The options OI reveals the total amount of funds dedicated to opening options contracts.\n\nAnd, at press time, the metric was $7.56 billion. This indicated that traders are increasingly forecasting which direction ETH will head given the blockchain’s forthcoming upgrade.\n\nFurthermore, the Ethereum fear and greed index was 61 out of a possible 100, at the time of writing. The indicator uses price volatility, market volume, and social trend data to assess if an asset is at a fair price.\n\nEthereum Fear and Greed Index is 61 – Greed\n\nCurrent price: $1,792https://t.co/w9g6chjUEShttps://t.co/9mfbj9d3uH pic.twitter.com/4be0oO6UxM — Ethereum Fear and Greed Index (@EthereumFear) March 31, 2023\n\nSo, the current condition implies that market mentions do not see a buying opportunity. Similarly, the sentiment does not expect a notable correction in the short term.\n\nHowever, institutions who trade Over The Counter (OTC) contracts seem not to share the same perceptions with the overall derivatives market. According to the CME Group, ETH’s futures OI was down to 4,246 while the volume increased to 5,103 ETH.\n\nHow much are 1,10,100 ETHs worth today?\n\nThe goal is to keep providing\n\nEither way, predicting ETH’s price action around developments like this remains a dicey decision. In times past like the year of the Merge, several long positions were open. But unfortunately, most liquidated as the ETH price plunged.\n\nMoreover, the number of ETH 2.0 deposit contracts by staking providers had reached new peaks via different networks and exchanges. And, as expected Lido Finance [LDO] edged out every other protocol while Binance dominated exchange activity."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiRWh0dHBzOi8vZGVjcnlwdC5jby8xMjUxODYvZXRoZXJldW0tc2hhbmdoYWktdXBncmFkZS1tZWFucy15b3UtZXRoLXNlY9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 01 Apr 2023 07:00:00 GMT", "title": "What the Ethereum Shanghai Upgrade Means for You, ETH, and the SEC - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nIn just shy of two weeks, if all goes to plan, Ethereum’s eagerly anticipated Shanghai upgrade will go live, enabling the withdrawal of staked ETH from the blockchain network and effectively completing its years-long transition to proof of stake.\n\nSince December 2020, when Ethereum began that journey to a proof-of-stake model—in which users stake cryptocurrency with a network to validate on-chain transactions, and then are rewarded for that participation with newly generated cryptocurrency—network participants have deposited over $32.95 billion worth of ETH with the network.\n\nIn September, Ethereum’s merge event successfully upgraded the network’s mainnet to a proof-of-stake consensus mechanism, forever changing the way Ethereum transactions are processed and reducing the network’s carbon footprint by 99%, according to figures from the Ethereum Foundation.\n\nAD\n\nAD\n\nBut the merge did not grant stakers on the network the ability to withdraw deposited ETH or the rewards generated by those deposits. Those funds remain captive on Ethereum; Shanghai will finally, after over two years, make them accessible.\n\nOn April 12, at 11:27 pm UTC, Shanghai will activate. What will that seismic moment in Ethereum’s history mean for the network, for its participants, and for the broader crypto ecosystem?\n\nETH better have my money\n\nAs a technical matter, Shanghai will be less involved than one might expect. The vast majority of Ethereum stakers, who have deposited their ETH with the network through intermediaries like Lido and Coinbase, won’t need to do anything on their ends once Shanghai goes live—besides wait.\n\nStaked ETH, and the rewards generated by those funds, will be made available for withdrawal by intermediaries at varying dates following Shanghai’s successful implementation. Lido, the largest ETH staking intermediary, recently announced that such capability will be introduced about a month after the upgrade, after a series of audits and safety checks.\n\nCoinbase, meanwhile, has not offered a firm timetable for rolling out staked ETH withdrawals, saying the process could take up to several months for some customers. All Ethereum network participants staking via third parties should check with those companies as to when their funds will be made available.\n\nAD\n\nAD\n\nFor the smaller number of independent validators who have staked directly with Ethereum (that pool is smaller because Ethereum requires validators to deposit at least 32 ETH, or just over $58,000 at writing, to stake with the network), matters will be only slightly more hands-on.\n\nValidators must first provide a withdrawal address for staked funds to be sent to; typically, most validators already submitted that address during the staking deposit process. Once a withdrawal address has been submitted to Ethereum, it cannot be changed.\n\nValidators can then opt for either a partial or full withdrawal. A partial withdrawal will send all funds and rewards generated beyond the minimum deposit amount of 32 ETH to a validator’s withdrawal address. If a validator’s withdrawal credentials are updated, then partial withdrawals will be sent to their withdrawal address automatically.\n\nIndependent stakers can also opt for a full withdrawal, which removes a user’s full stake, including original deposits of 32 ETH, from Ethereum, ending a validator’s participation in the transaction validation process. To fully withdraw from Ethereum’s staking process, a validator need only send a single exit message to the network using their validator keys and validator client.\n\nPartial and full withdrawals will be processed in the order they are received by the network; based on the expected amount of traffic to come immediately following Shanghai’s implementation, that initial queue could last up to 2 to 3 days.\n\nWhat will Shanghai mean for the Ethereum ecosystem?\n\nWhile Shanghai will certainly be a notable event for individual stakers, and carries great symbolic significance as the culmination of Ethereum’s transformation to a functional proof-of-stake network, the upgrade will not meaningfully change the way that users interact with Ethereum, nor the underlying economics of the network itself.\n\n“Most people have been able to sell [staked ETH] for quite some time, because the majority of ETH is being staked through platforms with liquid staking tokens, like Lido or Rocket Pool,” Jacob Cantele, head of product at Ethereum layer-2 Mantle, told Decrypt. “So I don't actually think [Shanghai] represents a major shift in the economics of Ethereum.”\n\nThe vast majority of ETH staked with Ethereum has, up to this point, been deposited with the network via third-party intermediaries, including staking pools like Lido, Rocket Pool, and Stakefish, as well as centralized crypto exchanges such as Coinbase, Kraken, and Binance. This is largely due to the fact, as mentioned above, that an individual validator must possess 32 ETH, or just over $58,000 at writing, to stake directly with Ethereum. Intermediary staking services generally allow for ETH deposits of any amount, in exchange for a small service fee.\n\nAD\n\nAD\n\nMany of those intermediaries have issued tokens representing staked ETH to their customers, meaning that a sizable portion of the capital supposedly held captive in Ethereum’s staking deposit contract has been traveling freely around the crypto ecosystem for years. And with Ethereum’s internal mechanisms fully converted to proof of stake since September, the network looks poised to chug along post-Shanghai without much noticeable difference.\n\n“Shanghai’s very exciting, but I do think it's just another step in the progression forward,” Alison Mangiero, executive director of Proof of Stake Alliance, an advocacy group for proof-of-stake blockchain networks like Ethereum, told Decrypt.\n\nWhat will Shanghai mean for the broader crypto climate?\n\nBut whereas matters within Ethereum appear likely to look largely unchanged following Shanghai, the political landscape beyond the network’s virtual borders may be much more substantially impacted by the upgrade.\n\nIn the last year, and particularly since the stunning collapse of crypto exchange FTX in November, American regulators have cracked down heavily on crypto companies, most recently those offering staking services. In February, the SEC hit centralized crypto exchange Kraken with a $30 million fine, alleging the company’s intermediary staking services constituted illegal securities offerings.\n\nKraken competitors like Coinbase took the opportunity to clarify that their own staking services did not determine rates of return on staked deposits in-house, as Kraken’s had, and therefore should not be considered yield products. Last week, regardless, the SEC issued Coinbase with a Wells Notice, alleging that company’s staking services also constitute unregistered securities. Such a notice indicates that an enforcement action in the form of a lawsuit from the SEC is likely forth-coming.\n\nShanghai’s implementation alone may not immediately change the calculus of the SEC’s escalating war with staking intermediaries. But it could place another, much larger staking-related target in the federal agency’s crosshairs: Ethereum itself.\n\nThe very day the merge successfully transitioned Ethereum to proof of stake in September, SEC chair Gary Gensler, according to the Wall Street Journal, told reporters that proof of stake networks could be considered securities offerings due to their rewards mechanisms, all-but calling out Ethereum by name. Since that date, Gensler has slowly but surely built the case that ETH is likely a security, primarily by suggesting that “only Bitcoin” is a commodity.\n\nIt is possible that the SEC—which has long-defined “investment contracts,” a type of security, as investments made with an “expectation of profits to be derived from the efforts of others”—might use the implementation of Shanghai as evidence of the fulfillment of a securities arrangement between ETH stakers and the core Ethereum team that implemented the upgrade.\n\nAD\n\nAD\n\n“People aren't going to get the [Ethereum] that they've earned as staking rewards, unless the Shanghai upgrade is successful,” Michael Selig, an attorney specializing in crypto regulation, who previously worked for the CFTC, told Decrypt. “Who's coordinating that? The SEC might have a list of people. It’s the essential efforts being performed by these guys to make it happen.”\n\nWhile Selig adamantly believes such a move on the SEC’s part would constitute a misinterpretation of securities law, particularly given the decentralization of the Ethereum core development team, he fears it may be a path forward to censoring the Ethereum network as a whole.\n\n“What do they have to say? ‘Look, these are investment contracts, and here’s evidence that there’s management or efforts being made by certain people,’” Selig said.\n\nSuch an argument would not likely be made until withdrawals are enabled on Ethereum, and the theoretical contract between Ethereum and its users is fulfilled. Should the Shanghai upgrade fail—an unlikely scenario, given the diligence and track record of Ethereum’s core developers—the SEC would have an even better case, arguing on behalf of swarms of disgruntled stakers that the network failed to fulfill its end of an investment bargain.\n\nIt remains to be seen whether the SEC’s appetite for crypto regulation might have grown so large as to attempt to snuff out one of the linchpins of the entire blockchain ecosystem. But whereas April 12—the date of Shanghai’s implementation—will remain a largely symbolic milestone within the crypto industry, symbolism can often have much more tangible repercussions in the realm of politics."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZGFpbHljb2luLmNvbS9zdGFraW5nLWNyeXB0by1tb3N0LWNvbW1vbi1wYXNzaXZlLWluY29tZS1zdHJhdGVneS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 01 Apr 2023 07:00:00 GMT", "title": "What is Staking? Crypto's Most Common 'Passive Income' Strategy Explained - DailyCoin", "content": "Crypto Staking is one of the blockchain industry’s most misunderstood concepts. Staking has become a buzzword that entices holders and investors to lock up their tokens with the promise of generous passive income and crypto rewards.\n\nMany staking reward contracts and platforms don’t technically offer any staking at all. You can’t stake Bitcoin (BTC), for example, so take care around any provider who tells you it’s possible.\n\nCrypto staking is a broad topic, with different blockchain networks living by slightly different staking rules. What is true for Ethereum (ETH) staking is not set in stone on Cardano (ADA).\n\nWith the SEC cracking down on crypto exchanges like Kraken and Coinbase over their staking programs, there’s never been a better time to revisit the basics.\n\nWhat is staking in cryptocurrency? What on earth does Proof-of-Stake even mean and what are the risks?\n\nWhat Is Crypto Staking?\n\nStaking cryptocurrency is a popular way to earn passive income on your digital assets. Many crypto investors like to see it as a kind of savings account, where you can earn an attractive APY (annual percentage yield) by staking coins.\n\nDespite the inherent volatility of the crypto market, staking helps holders grow their crypto investment over a long period of time. They don’t need to actively trade or inject fresh capital into their portfolio to increase their exposure.\n\nBut as the saying goes: ‘There’s no such thing as a free lunch.’\n\nWhere do these generous interest rates and seemingly endless staking rewards come from?\n\nThe Proof-of-Stake Consensus Mechanism\n\nThe Proof-of-Stake (PoS) consensus is, unfortunately, not a group of friends that tell you whether or not your beef is rare or medium-rare. It’s the process used by several blockchains to secure the network and produce new blocks.\n\nGenerally, a network validator is someone who locks up staking coins to a blockchain to help decentralize the network and verify transactions. For ensuring the security of the network and producing new blocks, validators earn staking rewards. These rewards are generally new tokens entering the supply based on the network’s emission schedule.\n\nIn a Proof-of-Work blockchain, like Bitcoin, miners solve complex computational puzzles to compete for the right to create new blocks and earn mining rewards. This is why successful Bitcoin miners consume so much energy.\n\nSource: Ethereum\n\nTo give you an idea of just how much energy a PoW chain uses, Ethereum reduced its consumption by 99% after transitioning to a PoS consensus.\n\nOn the other hand, PoS chains randomly select a validator for each new block. This process uses considerably less energy than PoW and incentivizes crypto investors to stake more crypto assets, increasing their chances of winning the staking reward.\n\nThis exercise is a win-win, the blockchain attracts a greater variety of stakers and decentralizes network operation, while stakers earn cryptocurrency rewards and help support the ecosystem.\n\nHow Much Cryptocurrency Can I Earn Through Staking Rewards?\n\nCryptocurrency staking interest rates vary between different blockchains, providers and lock-up periods. Generally speaking, rewards fluctuate between 4-18% APY. Sites like StakingRewards.com aggregate data to tell us where the best pure staking opportunities are. However, it doesn’t include options from centralized exchanges.\n\nIn some cases, cryptocurrency exchanges will offer staking rewards at boosted rates to entice holders to lock-up their staking coins for longer periods of time.\n\nFor example, this crypto exchange is offering 35% APY if we’re willing to lock up our Cosmos (ATOM) tokens for 120 days, but just 1% for a flexible deposit.\n\nWhat Are the Risks of Staking?\n\nLike in every corner of the crypto space, staking comes with its own risks. For example, if you’ve delegated your tokens to a bad validator, you might be at risk of ‘slashing.’ Slashing is a defense mechanism used by staking protocols to ensure that validators are behaving properly and fulfilling their tasks.\n\nIf a PolkaDot (DOT) validator is misbehaving and running malicious software, its stake is at risk of being ‘slashed.’ If this happens, a portion of their stake is forcibly removed and given to the protocol treasury.\n\nEach network and staking protocol has its own slashing rules, so if you’re running a validator it’s worth checking what might put you at risk.\n\nOther risks include loss of value due to price volatility. If you’ve locked up your staking coins for a long period of time, you might find yourself unable to sell quickly in case of a black swan event.\n\nSource: CoinMarketCap\n\nRemember when LUNA crashed from $80 to a few cents in a few days? Thanks to the 14 day unlock period, thousands of LUNA stakers could do nothing but watch their holdings burn to the ground before their eyes.\n\nThe same goes for those who stake their coins through centralized exchanges. When FTX collapsed, anyone staking their crypto through FTX staking pools had zero access to their assets.\n\nCrypto Staking Myths and Misconceptions\n\nLike any misunderstood crypto topic, there is plenty of misinformation that can lead you astray. Some of this confusion is centered around the definition of staking drifting away from its original purpose and being loosely applied to any circumstance where you deposit tokens in exchange for rewards.\n\nLet’s shed light on some of these misconceptions.\n\n“I Can Stake My Tokens in DeFi Apps to Earn Other Tokens”\n\nWhile you can deposit certain tokens in DeFi apps, like liquidity farms, to earn other tokens, this isn’t technically staking. In this example, PancakeSwap says we can earn tokens by staking their native CAKE token in staking pools.\n\nWhat we’re actually doing here is depositing CAKE into a smart contract and earning emissions of other tokens. We’re not helping to secure the non-existent CAKE blockchain. DeFi apps like PancakeSwap offer these ‘staking pools’ because it brings utility and demand to the CAKE token.\n\n“Staking My Tokens Means I Don’t Control Them Anymore”\n\nIn many cases, staking cryptocurrency means delegating them to a validator who uses them on your behalf. However some networks, like Cardano, don’t follow this rule.\n\nStaked ADA never leaves your crypto wallet, making it one of the most secure staking protocols in the market.\n\n“Staking Rewards Are Fixed and Accurate”\n\nJust because staking emissions are fixed and regular, doesn’t guarantee that your validator will be randomly selected and receive their estimated rewards. This is a question of statistics more than anything, so if you’re staking long term, you probably won’t even notice.\n\nSource: Solana Docs\n\nIt’s worth mentioning that staking APY doesn’t take into account token inflation and emissions. For example, Solana’s token inflationary emission rate means that SOL’s supply increases by anywhere from 1.5-5% each year.\n\nEven though you might be earning 6-8% APY by staking SOL, the supply is still inflating and potentially losing value. Some crypto enthusiasts prefer to calculate their staking rewards by subtracting a tokens inflation rate from the original APY.\n\n“I Can Stake NFTs to Earn Crypto Tokens”\n\nLike PancakeSwap, this is a liberal use of the phrase ‘Stake.’ Yes, you can deposit your Bored Ape Yacht Club NFT into a smart contract to earn $APE tokens, but you’re not helping anyone secure the network and produce new blocks.\n\nCyberkongz earn $BANANA, DeGods earn $DUST, Neo Tokyo Citizens earn $BYTES, the list goes on. Why do so many NFT collections do this?\n\nDepositing NFTs in staking contracts takes them off the market and makes the available supply look scarcer. A collection with only 2% of its total supply listed looks like a much more desirable asset than a collection with 20% of its pieces up for sale.\n\nCrypto Staking Pros and Cons\n\nCryptocurrency staking is a pretty nuanced subject, with a lot of moving parts and specific rules. To make it easier to wrap your head around why staking might be interesting to you specifically, here are the benefits and drawbacks.\n\nPros\n\nPassive Income – Cryptocurrency staking is a great way to earn passive income and organically grow your holdings with minimal risk.\n\n– Cryptocurrency staking is a great way to earn passive income and organically grow your holdings with minimal risk. Secure the blockchain – By staking your crypto, you’re helping validators produce new blocks and protect the network from malicious actors\n\n– By staking your crypto, you’re helping validators produce new blocks and protect the network from malicious actors Decentralize the network – The more validators a blockchain has, the less likely it is to suffer from a centralized point of failure. This also helps to distribute ownership and governance of the chain itself.\n\n– The more validators a blockchain has, the less likely it is to suffer from a centralized point of failure. This also helps to distribute ownership and governance of the chain itself. Increase token scarcity – Staking cryptocurrency takes your coins off the market and reduces the number of tokens available for buyers. In a bullish market scenario, this creates a good environment for price appreciation.\n\nCons\n\nLock up periods – To get the juiciest staking rewards, you’ll usually need to lock up your tokens for a long period of time. This is problematic if you find yourself needing to sell quickly\n\n– To get the juiciest staking rewards, you’ll usually need to lock up your tokens for a long period of time. This is problematic if you find yourself needing to sell quickly Relinquish full control of your funds – In most cases, staking means you need to deposit your crypto into a staking contract. To be honest, this sounds a lot scarier than it really is. Slashing and validator hacks are uncommon, and operators have a vested interest in the security of the chain they’re validating.\n\nHow Can I Start Staking My Cryptocurrency?\n\nAlmost every crypto exchange under the sun offers staking in some shape or form. If you want to get started with crypto staking for the first time, this is by far the easiest method.\n\nYour preferred crypto exchange should have an ‘Earn’ section where you can browse available staking subscriptions. All you need to do is search for the crypto you’d like to stake and choose a lock-up period that you’re happy with.\n\nRunning your own validator is a different story. This can be quite complicated, especially if you don’t have a background in development. The best place to start looking for answers is in the developer documents of the blockchain of your choice.\n\nOn the Flipside\n\nCrypto staking, like any financial growth strategy, has its risks. Everyone loves getting free cryptocurrency, but that doesn’t mean that you should trust everything at face value. Take care when choosing your staking provider.\n\nAs explained above, the term ‘Staking’ has drifted away from its technical definition. These days, staking is used to describe most circumstances where you deposit digital assets in exchange for some kind of token reward, rather than contributing to a Proof-of-Stake network.\n\nWhy You Should Care\n\nStaking your cryptocurrency helps decentralize and support your favorite blockchain ecosystem while rewarding you with tokens. It’s important to understand what staking is and how it works to ensure that you’re ‘putting your crypto to work’ safely.\n\nFAQs"}]