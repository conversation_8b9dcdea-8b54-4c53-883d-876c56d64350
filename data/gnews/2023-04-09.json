[{"id": 15, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vYW1iY3J5cHRvLmNvbS9hcy1wb2x5Z29uLXN1cnBhc3Nlcy1ldGhlcmV1bS1pbi10aGlzLW1ldHJpYy13aGVyZS1kb2VzLW1hdGljLXN0YW5kL9IBXmh0dHBzOi8vYW1iY3J5cHRvLmNvbS9hcy1wb2x5Z29uLXN1cnBhc3Nlcy1ldGhlcmV1bS1pbi10aGlzLW1ldHJpYy13aGVyZS1kb2VzLW1hdGljLXN0YW5kL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 09 Apr 2023 07:00:00 GMT", "title": "As Polygon surpasses Ethereum in this metric, where does MATIC stand - AMBCrypto News", "content": "Polygon outperformed Ethereum in terms of activity over the last 24 hours.\n\nHowever, TVL growth for Polygon remains low as DEX volume declined.\n\nPolygon [MATIC] has emerged as a prominent L2 solution in recent years, with several partnerships and an influx of NFT migrations contributing to its ability to remain competitive with other layer 2 solutions.\n\nRead Polygon’s [MATIC] Price Prediction 2023-2024\n\nAs per the data shared by Token Terminal, Polygon surpassed Ethereum [ETH] in daily active addresses owing to these factors. In the previous 24 hours, the count of daily active addresses on the Polygon network stood at 399,950, while Ethereum recorded 376,350 users.\n\nThe surge in activity on Polygon can be attributed to several factors, and one of them is the performance of its dApps. Notably, well-known dApps like Planet IX observed a significant increase in both active addresses and volume, with the latter rising by 15.29% in the last month.\n\nThe NFT market of Polygon also experienced a surge in interest, and this can be linked to the migration of y00ts NFT. After the transfer to the Polygon network, the y00ts NFT witnessed a notable rise in both volume and the count of holders.\n\nSince the migration started on 28 March, y00ts’ secondary sales saw $4.6M in volume from 1,043 sales, averaging $4,461 per sale.\n\nNot all roses and sunshine\n\nHowever, Polygon faced some challenges in the DeFi space, particularly in its DEX volume. Compared to other Layer 2 solutions, such as Arbitrum [ARB], Polygon’s DEX volume saw a significant decline. This had a knock-on effect on the total value locked (TVL) on the network, which also experienced a decrease.\n\nPolygon’s native token, MATIC, was also facing some setbacks. Santiment’s data revealed that the overall transaction count on the network saw a decline, and the price of MATIC dropped from $1.16 to $1.096 during this period.\n\nIs your portfolio green? Check the Polygon Profit Calculator\n\nDespite the price correction, there was a lack of interest in MATIC among new addresses, which was evident from the declining network growth.\n\nOnly time will tell whether Polygon’s surge in daily activity will have a positive impact on the price in the future."}, {"id": 33, "url": "https://news.google.com/rss/articles/CBMilgFodHRwczovL3d3dy5jcnlwdG8tbmV3cy1mbGFzaC5jb20vc2hpYmEtaW51LXRvLWJlY29tZS1tb3JlLWRlY2VudHJhbGl6ZWQtdGhhbi1ldGhlcmV1bS13aXRoLXNoaWJhcml1bS1vbmJvYXJkaW5nLXRob3VzYW5kcy1vZi1iaWxsaW9uLXBhcnRuZXJzLXJlcG9ydC_SAZwBaHR0cHM6Ly93d3cuY3J5cHRvLW5ld3MtZmxhc2guY29tL3NoaWJhLWludS10by1iZWNvbWUtbW9yZS1kZWNlbnRyYWxpemVkLXRoYW4tZXRoZXJldW0td2l0aC1zaGliYXJpdW0tb25ib2FyZGluZy10aG91c2FuZHMtb2YtYmlsbGlvbi1wYXJ0bmVycy1yZXBvcnQvP2FtcD0x?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 09 Apr 2023 07:00:00 GMT", "title": "Shiba Inu to become more decentralized than Ethereum with Shibarium onboarding thousands of billion-$-partners – Report - Crypto News Flash", "content": "Shiba Inu is expected to become more decentralized than Ethereum.\n\nShiba Inu would also receive some critical infrastructure from Unification as UNODE plays a pivotal role in the ecosystem.\n\nShibarium is the project expected to transition Shiba Inu from a meme coin to a technologically focused ecosystem. It is set to operate as a blockchain layer that runs on top of Ethereum. Shibarium would take over a large portion of SHIB transactions to prevent the asset from solely relying on Ethereum. Recently, the beta testnet was launched. Shiba Inu could compete with Ethereum on the grounds of decentralization as development firm Unification, which is known as the main developer of the Shibarium project, considers this as an important objective.\n\n\n\n\n\n\n\n<PERSON><PERSON>, a popular validator of the Unification (FUND) network tweeted a screenshot on April 5 featuring an interaction between the Unification Foundation and a community user. As captured in the screenshot, Unification was attempting to address the centralization of Ethereum Virtual Machine (EVM) node operations issue. The Unification’s upcoming tool UNODE for decentralized applications (dapps) in the Shibarium ecosystem would, therefore, be used by SHIB’s layer 2.\n\nIt is worth noting that the native token of Unification is FUND. This is said to play a critical role as the only crypto that would be used by UNODE. According to <PERSON><PERSON><PERSON>, UNODE would be very useful in the SHIB ecosystem.\n\nUnification is now tackling the next major point of centralization for Shibarium operations – EVM node operations. UNODE’s launch in 2023 offers a more secure and decentralized infrastructure for developers & Dapps – using FUND as the gas!\n\nShiba Inu is currently bullish\n\nThe decentralization aspect of the project is very important considering how Ethereum has been a topic of discussion after its recent ETH.20 upgrade. According to Messari’s study conducted in the fall of 2022, 70 percent of leading Ethereum applications run on four centralized node providers. In a blog post, Unification mentioned that Alchemy, Infura, Moralis, and Quicknode are the four centralized industry incumbents that provide the most ETH nodes. This is said to create a vulnerability in the technology stack of decentralized applications. Many also require card payments through DApps, hence, are permissioned and identifiable. In this case, tracking could be triggered.\n\nUnification’s UNODE addresses this Achilles’ Heel, by democratizing, decentralizing, and incentivizing EVM node operations. In accordance with our ethos, this will be pure Web3.\n\nOracle of Oracles (OoO), is also part of the Unification technology. OoO provides real-time asset prices by combining data from decentralized and centralized exchanges. This would be integrated into the SHIB ecosystem and includes ShibaSwap, Shiboshi NFTs, and Shibarium.\n\n\n\n\n\n\n\nThis means important infrastructure from Unification would be received by Shiba Inu.\n\nUnification is doing an excellent job with their technology for Shibarium, and no one else can match it. We’re still in an early beta stage with a focus on delegating/staking. People know that BONE is gas, there will be REAL governance, and TRUE SHIB users will be able to earn rewards from using the blockchain.\n\nThe current sentiment of Shiba Inu is bullish and is trading at $0.000011 as of press time. In the last seven days, the asset has surged by 2 percent.\n\nThis article is provided for informational purposes only and is not intended as investment advice. The content does not constitute a recommendation to buy, sell, or hold any securities or financial instruments. Readers should conduct their own research and consult with financial advisors before making investment decisions. The information presented may not be current and could become outdated."}, {"id": 29, "url": "https://news.google.com/rss/articles/CBMimAFodHRwczovL3d3dy5hbmFseXRpY3NpbnNpZ2h0Lm5ldC9ldGhlcmV1bXMtY29tbW9kaXR5LXN0YXR1cy1saWtlLWJpdGNvaW4tc3BhcmtzLWJ1bGxpc2gtbWFya2V0LWRvZ2V0dGktbWVtZS1jb2luLXByZS1zYWxlLXdpdG5lc3Nlcy1za3lyb2NrZXRpbmctZGVtYW5kL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 09 Apr 2023 07:00:00 GMT", "title": "Ethereum's Commodity Status Like Bitcoin Sparks Bullish Market, Dogetti Meme Coin Pre-Sale Witnesses Skyrocketing ... - Analytics Insight", "content": "Bitcoin (BTC) has reversed its downward trend and regained momentum, while Ethereum (ETH) has outperformed BTC, rising by more than 4%. This upward rally in the cryptocurrency market is attributed to several factors, including an increase in consumer confidence in March, recent advancements in the cryptocurrency industry, and Bitcoin’s reputation as a safe-haven asset. Additionally, consumers are losing trust in traditional banks following recent bank collapses, which is considered a key factor in the Bitcoin price increase.\n\nEthereum Now Recognised As A Commodity Like Bitcoin\n\nThe crypto market cap has risen by 2.56% to $1.16 trillion, with Bitcoin and ETH leading the charge. The head of the US CFTC has reiterated that Ether is a commodity, not a security, which could boost its value. Ethereum’s current price is $1,807.78, with a 24-hour trading volume of $7,635,430,660. It is likely to find an immediate resistance at $1,850, and a bullish breakout above this level could raise the price on to the next resistance level of $1,960.\n\nThe CFTC (Commodities Futures Trading Commission) has acknowledged Ethereum (ETH) and Litecoin (LTC) as commodities, similar to Bitcoin (BTC). This means that both of these altcoins are now considered trading assets, like gold or oil. The CFTC made this statement as part of an investigation into Binance, a popular cryptocurrency exchange. This development is likely to have a positive impact on BTC and ETH prices, as it verifies their commodity status, potentially enhancing their legitimacy and adoption in traditional financial markets. It could also lead to increased regulatory oversight and institutional investor approval.\n\nBitcoin Hitting Bull Market\n\nRecent strong predictions regarding Bitcoin’s future are also contributing to the rise in BTC prices. For instance, former Coinbase technical director Balaji Srinivasan has bet $2 million that bitcoin would reach $1 million in value within 90 days, implying that the value of Bitcoin will increase sharply in the near-term future. Marshall Beard, the Chief Strategy Officer of Gemini, an American-based cryptocurrency exchange, is more cautious and interested, estimating that Bitcoin will hit $100,000 this year. These forecasts are being influenced by several factors, including the growing adoption of cryptocurrencies by institutional investors, which may increase demand for Bitcoin and ultimately its price.\n\nAs of this writing, Bitcoin’s price is $28,259.78, and its 24-hour trading volume is $16.5 billion. A breakout above this level could potentially push Bitcoin’s price toward the next resistance level of $28,900. The bullish potential for Bitcoin is also supported by the violated downward trendline.\n\nDogetti Strives To Be The Top Dog Coin\n\nDogetti (DETI) is one of the new altcoins that has caught the attention of investors in the meme coin cryptocurrency space. Unlike traditional cryptocurrencies, meme coins are designed to be fun and engaging while providing investors with an opportunity to make a profit.\n\nThe Dogetti (DETI) team values community and aims to provide a healthy income for all investors. Dogetti NFTs are a fun way to interact with other DETI holders besides generating a steady stream of income. These NFTs are modelled after canine companions and can be bought, sold, and traded for $DETI. In the future, the platform plans to allow Dogetti NFTs to reproduce, which would result in more NFTs and more funds for investors.\n\nInvestors who want to get in on the ground floor of this exciting altcoin can participate in Dogetti’s presale. This presale offers investors the opportunity to purchase $DETI tokens before the official launch, giving them a chance to make a profit if the coin’s value increases. The presale also offers various bonuses and incentives to investors who participate, making it an attractive opportunity for those who believe in Dogetti’s potential.\n\nFor more information about Dogetti (DETI):\n\nPresale: https://dogetti.io/how-to-buy\n\nWebsite: https://dogetti.io/\n\nTelegram: https://t.me/Dogetti\n\nTwitter: https://twitter.com/_Dogetti_\n\nJoin our WhatsApp and Telegram Community to Get Regular Top Tech Updates"}, {"id": 34, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vYml0Y29pbmlzdC5jb20vZXRoZXJldW0tcHJpY2UtcHJlZGljdGlvbi1waWthbW9vbi1yZWFkeS10by1yYWxseS0yMDAwMC_SAVNodHRwczovL2JpdGNvaW5pc3QuY29tL2V0aGVyZXVtLXByaWNlLXByZWRpY3Rpb24tcGlrYW1vb24tcmVhZHktdG8tcmFsbHktMjAwMDAvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 09 Apr 2023 07:00:00 GMT", "title": "Ethereum Price Prediction: Pikamoon Ready to Rally 20,000% | Bitcoinist.com - Bitcoinist", "content": "If anything, the last couple of months have taught us that the crypto industry is a rollercoaster that goes both up and down—and sometimes really down. The market is seriously bearish across the board, and investors are sitting on their hands, waiting for the perfect time to buy the dip.\n\nWhen is the perfect time? Investors are itching to get into buy positions as many coins on their watchlist are etching close to their all-time low price. This means bigger returns for investors if they get in early and if these tokens rally as high as they’ve done in the past. The top item on this list for the majority of investors is Ethereum.\n\nIn this article, we’ll explore expert opinions on the Ethereum price prediction for 2023 and when exactly it is the best time to buy Ethereum in order to maximize profit. We’ll also dive into the world of Pikamoon, the new GameFi project that experts believe will outperform Ethereum in 2023.\n\nShould I Buy Ethereum Now? – Ethereum Price Prediction For 2023\n\nWhile Ethereum may have gained 2.5% in the last 7 days and 3.1% in the last 30 days, it is noteworthy to mention that the overall Ethereum trend is still bearish. Ethereum has dropped over 60% from its all-time high price and may still go lower if certain criteria are not met quickly.\n\nReports from Coinmarketcap show that 66% of investors holding ETH tokens are in profit, 29% are in loss, and a small 5% are at breakeven. With the majority of investors in profit, it is easy to get excited at the thought of loading up on your buy positions on Ethereum.\n\nExperts believe this may not be the best course of action at the moment as ethereum’s price is not quite settled yet. The price of the ETH token is presently trading above the $1920 support level. This is a good indicator that prices may reach as high as $2500 without major obstructions. Before taking any more buy positions, wait for a clear close of the daily or weekly candle above the $2121 price region and a retest of the $1890 support level.\n\nNonetheless, the $2500 resistance zone is a critical region for the price of Ethereum. If the price of Ethereum fails to break the level to the upside, the price of its token may fall to $1800 lows or even lower.\n\nThis is why analysts are telling ETH investors who are looking to diversify their portfolios to load up on as many $PIKA tokens in the ongoing Pikamoon presale as the GameFi token is poised to rally as much as 20,000% before the end of the year.\n\nWhat is Pikamoon?\n\nPikamoon is a leading P2E in the crypto industry developed by crypto experts and blockchain analysts who are fully doxxed and properly vetted by Coinsult, a leading blockchain audit company. This is one of the many reasons big investors, the crypto community, and big brands trust the Pikamoon project.\n\nThat’s not all. When it comes to community support, Pikamoon is way ahead of its peers in the industry. The Pikamoon community is made up of crypto enthusiasts and expert investors who are fully involved and invested in the project’s success. This is why experts believe Pikamoon is on its way to becoming an industry leader.\n\nTo top it all off, the Pikaverse, which is the metaverse of the Pikamoon game, is built on the best gaming engine in the industry, Unreal Engine 5. This is the same gaming engine that the legendary Fortnite game is built on. So when it comes to your expectations of the Pikamoon game, you can let your imaginations run wild.\n\nBoth gamers and crypto enthusiasts are patiently waiting for Pikamoon’s launch so they can experience the Pikaverse firsthand. This is why investors are loading up on $PIKA tokens on the ongoing presale because they know the value of Pikamoon will skyrocket once it launches.\n\nPikamoon Presale: The Easiest Way To Flip Your Portfolio In 2023\n\nPikamoon is the game changer you need to transform your financial situation, and there’s no better time to invest in the project than now. Pikamoon is in the third and last stage of its presale, where $PIKA tokens are selling for as little as $0.0006.\n\nThe price of the Pikamoon token will never be this low again. Experts believe the $PIKA token will rally as high as 20,000% after its launch on major exchanges. Don’t miss out on another massive price move that can make you a crypto millionaire.\n\nGet on the Pikamoon presale now!\n\nFind out more about Pikamoon (PIKA):\n\nBuy Now: https://pikamoon.io/buy\n\nWebsite: https://pikamoon.io\n\nTwitter: https://twitter.com/Pikamooncoin\n\nTelegram: https://t.me/pikamoonofficial\n\nDisclaimer: This is a paid release. The statements, views and opinions expressed in this column are solely those of the content provider and do not necessarily represent those of Bitcoinist. Bitcoinist does not guarantee the accuracy or timeliness of information available in such content. Do your research and invest at your own risk."}, {"id": 25, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZGFpbHljb2luLmNvbS9kaWZmZXJlbmNlcy1iZXR3ZWVuLWJsb2NrY2hhaW4taGFyZC1mb3Jrcy1hbmQtc29mdC1mb3Jrcy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 09 Apr 2023 07:00:00 GMT", "title": "Forks in Crypto: Hard Forks vs Soft Forks - DailyCoin", "content": "Forks in Crypto are a perfect example of decentralization in action. The beauty of open-source, decentralized blockchain protocols is that anyone can suggest new rules and functionalities that improve the network.\n\nDespite blockchain forks having existed for years, there’s still plenty of confusion around what they are and how they work. Key milestones like Cardano’s (ADA) Vasil upgrade or Ethereum’s (ETH) Merge are much more than catchy names, they’re hard forks that improve the chain with new features.\n\nWhy do cryptocurrency forks happen and what do they mean for a blockchain network? What are the different kinds of blockchain forks and are they always for the best of the ecosystem?\n\nWhat is a Fork in Cryptocurrency?\n\nAs we all know, a blockchain network is a chain of data interconnected by units called blocks. When new blocks are created, they reference the data and events of previous blocks, which is why we can trace every transaction on the Bitcoin network right back to its genesis.\n\nWhen a blockchain fork occurs, it creates a permanent divergence from the network’s latest version. Like branches on a tree, a blockchain split causes a new version of the network to run, often with new features and new rules, like a change in block size.\n\nThis process is called forking and essentially creates a new blockchain without changing the old version of the blockchain. In most cases, the new chain benefits from software upgrades voted in by community members.\n\nWhy Do Forks Happen?\n\nForks in crypto help blockchain networks improve. For example, the Alonzo hard fork introduced smart contracts to the Cardano blockchain. Ethereum’s iconic hard fork, ‘The Merge,’ transitioned the network to a Proof-of-Stake algorithm, making it more scalable and energy efficient.\n\nOf course, not all forks stand the test of time and fulfill their vision of becoming new and improved versions of old chains. Bitcoin forks, despite being cheaper and more efficient networks, were never able to usurp Satoshi Nakamoto’s original and become the world’s largest cryptocurrency.\n\nJust like in your kitchen drawer, not all blockchain forks are created equal. Let’s take a look at what types of forks exist and how they’re different.\n\nThe Different Types of Blockchain Forks\n\nThere are two primary kinds of forks in the crypto world. In most cases, blockchain forks are recognized as either hard forks or soft forks. Both varieties serve the same purpose but have slight differences.\n\nAn easy way to understand the difference between hard forks and soft forks is to imagine upgrading your computer. In this example, a soft fork is like installing new software like an updated operating system. On the other hand, a hard fork is like replacing your computer entirely.\n\nHard Forks\n\nIn a hard fork, a significant upgrade means that the network effectively splits into two separate chains. Changes to the blockchain protocol are not backward-compatible, meaning that the new chain will not recognize the old chain. Node operators need to upgrade their software, or else the network won’t recognize their new blocks.\n\nWhen this happens, node operators and community members need to make an important decision. Will they continue to use the updated software protocols of the new chain? Or would they rather stick to the old rules and the previous version of the software?\n\nIf there is enough community support for the old chain and the old rules, the pre-fork chain may continue to exist. This is why we still have networks like Ethereum Classic.\n\nIf a hard fork creates a new chain that means it also creates a new coin. Because a fork is a split of an existing chain, you can usually expect to have new cryptocurrency tokens. For example, if you held 1 Ether during The Merge, you would also have 1 Ether on the Ethereum PoW chain that still exists alongside the new Ethereum PoS chain.\n\nSoft Forks\n\nA soft fork occurs when changes occur to the network, but nodes and miners aren’t required to upgrade themselves to continue participating in the blockchain. These nodes can still create new blocks under the new rules.\n\nIn blockchain technology, a soft fork is like a light software update. Any new blocks still recognize the existing network and node operators, so it doesn’t force a blockchain split and result in a new blockchain.\n\nDeFi Application Forks\n\nIf you have any experience in the wild world of decentralized finance, you’ve probably heard the word ‘fork’ mentioned in a slightly different way. DeFi application forks are essentially copies of existing platforms and smart contracts that might feature slight alterations.\n\nTo give you an example, think of the UniSwap decentralized exchange. The project is completely open source, so anyone can dive into the application’s source code to see how it works. As more blockchain networks, like BNB Chain and Polygon, emerged, these networks needed their own DEXes for users to trade tokens on.\n\nInstead of building new apps and code from scratch, it’s much easier to simply copy, or ‘fork’ an existing platform that works well. If you check the source code for QuickSwap, the leading DEX on Polygon, you’ll see that all its basic code and functionality are directly copied from UniSwap’s code.\n\nForking dApps is a common practice in the DeFi world. A comprehensive list of crypto projects created from forks is available on DefiLlama.\n\nHistoric Blockchain Forks\n\nCrypto history is full of crucial forks that have helped blockchain protocols address dangerous security issues and improve the most popular networks. Some of these hard forks have resulted in new coins that have held their position in the Top 100 cryptos by market cap for years on end.\n\nEven Dogecoin (DOGE), the most famous meme coin in the crypto market, was born from a fork of other networks. The Dogecoin blockchain was forked from LuckyCoin, which was in turn a Litecoin fork.\n\nBitcoin (BTC) Forks\n\nThe Bitcoin blockchain is the oldest and best-known cryptocurrency network in the world. Naturally, many developers have tried to fork Bitcoin and create new and improved networks.\n\nBitcoin Cash\n\nIn August 2017, Bitcoin Cash (BCH) made history by becoming the first-ever fork of the Bitcoin blockchain. Like the original Bitcoin, BCH has a maximum supply of 21,000,000 coins.\n\nBitcoin Cash features a larger block size, meaning that the network can process more transactions per second than the original Bitcoin network. It’s also cheaper to use, with lower transaction fees.\n\nThis Bitcoin fork is also called the SegWit upgrade, named after the initial proposal that eventually resulted in the Bitcoin Cash hard fork.\n\nBitcoin Gold\n\nAnother fork of the world’s first cryptocurrency, Bitcoin Gold aims to provide a more scalable alternative to Bitcoin as a payment network. Bitcoin Gold’s vision was to make Bitcoin mining accessible to everyone. The forked network used common GPUs to mine new coins, instead of Bitcoin’s competitive and intensive ASIC mining rigs.\n\nUltimately, Bitcoin Gold was more of a passion project than a genuine competitor. Its community appears to have abandoned the network. There have been no updates to the Bitcoin Gold site since 2021.\n\nEthereum Forks\n\nThe Ethereum blockchain has certainly witnessed a messy history, but you don’t become the future of finance without overcoming some challenges along the way. Fortunately, the Ethereum community is resilient, as proven through several significant hard forks throughout the network’s lifespan.\n\nEthereum Classic\n\nThe story of Ethereum Classic is an essential part of crypto history. It involves a historic hack worth hundreds of millions and a ‘gross misuse’ of blockchain technology from Vitalik Buterin and the Ethereum community.\n\nThe DAO was an investment group created in the early days of the Ethereum blockchain. In 2016, the DAO successfully raised over 150 million dollars worth of ETH, with the intention of becoming a venture capitalist firm in the emerging DeFi space.\n\nWhen hackers found a smart contract exploit in the DAO’s wallet, they were able to siphon over $60 million in Ether, giving the hackers an enormous stake in the network and raising concern over whether the network was truly usable. At the time, the DAO held over 14% of all circulating ETH.\n\nAfter much negotiating between Vitalik Buterin, the Ethereum community, and the hackers themselves, it was eventually decided that a network hard fork could kickstart a new network from before the hack occurred, returning funds to investors and rewriting Ethereum’s history.\n\nThis was an extremely controversial decision. It went against all the principles of blockchain technology, such as decentralization and immutable transactions. The Ethereum community was split in two; those who agreed with the hard fork proposal and those who did not.\n\nThe Ethereum blockchain we know and love today is the new chain created from this historic hard fork. The old chain, whose community did not accept the display of power and control, is now called Ethereum Classic.\n\nEthereum POW (Proof-of-Work)\n\nIn late 2022, the Ethereum blockchain finally completed its long-awaited migration to a Proof-of-Stake consensus mechanism. This new chain was the result of years of work and reduces Ethereum’s annual energy consumption by as much as 99%.\n\nEthereum miners who were against The Merge hard-forked the old chain and created Proof-of-Work Ethereum. The Ethereum PoW functions just like the normal Ethereum blockchain did pre-merge. However, the new coin, ETHPOW has little demand and the network doesn’t see much user traffic.\n\nOn the Flipside\n\nForks in crypto are essential processes that improve blockchain networks and help drive progress in the industry. However, they do pose security risks if poorly executed.\n\nWhy You Should Care\n\nHard fork events like The Merge, the Alonzo upgrade and the Shanghai update are crucial moments in blockchain evolution. Being informed about the processes involved can help you prepare for potential outcomes and help you better understand blockchain technology.\n\nFAQs"}]