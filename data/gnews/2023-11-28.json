[{"id": 48, "url": "https://news.google.com/rss/articles/CBMimQFodHRwczovL3d3dy5iZW56aW5nYS5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS8yMy8xMS8zNTk3MjM5NC9ldGhlcmV1bS1jby1mb3VuZGVyLXZpdGFsaWstYnV0ZXJpbi10aGlua3MtaWYtYWktdHVybnMtYWdhaW5zdC11cy1pdC1tYXktd2VsbC1sZWF2ZS1uby1zdXLSAS1odHRwczovL3d3dy5iZW56aW5nYS5jb20vYW1wL2NvbnRlbnQvMzU5NzIzOTQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Tu<PERSON>, 28 Nov 2023 08:00:00 GMT", "title": "Ethereum Co-Founder <PERSON><PERSON> Thinks If AI Turns Against Us, It May 'Well Leave No Survivors And En - Benzinga", "content": "Ethereum’s ETH/USD co-founder, <PERSON><PERSON>, on Monday shed light on his own version of “techno-optimism,” influenced by <PERSON>‘s Techno-Optimist <PERSON><PERSON><PERSON> penned last October, in which <PERSON><PERSON><PERSON> took a positive stance on AI’s future.\n\nWhat Happened: <PERSON><PERSON><PERSON> in a blog post said, \"This is an extreme claim: as much harm as the worst-case scenario of climate change, or an artificial pandemic or a nuclear war, might cause, there are many islands of civilization that would remain intact to pick up the pieces.\"\n\nHowever, the risks posed by a superintelligent AI that may turn against humanity could signify complete annihilation. \"But a superintelligent AI, if it decides to turn against us, may well leave no survivors and end humanity for good,\" he remarked, worrying that not even Mars could be a refuge.\n\nHe advocated for a future that preserves “human” traits and agency, stating, “If we want a future that is both superintelligent and ‘human’—one where human beings are not just pets, but actually retain meaningful agency over the world—then it feels like something like this is the most natural option.”\n\nSee More: Dogecoin HODLERs Are Beating Shiba Inu With 57% Landing In Profits, IntoTheBlock Data Reveals\n\nWhy It Matters: Addressing concerns about the future evolution of Ethereum during an AMA on Farcaster, <PERSON><PERSON><PERSON> disclosed that two of his greatest fears were the stagnation of cryptocurrency and the potential dangers associated with AI.\n\nWhen questioned on what occupies his thoughts frequently, the influential developer cited, “AI-related issues,” specifically existential risks derived from it.\n\nPrice Action: At the time of writing, ETH was trading at $1,999, down 1.12% in the last 24 hours according to Benzinga Pro.\n\nRead Next: Here’s How Much You Should Invest In Shiba Inu Today For A $1M Payday If SHIB Hits 1 Cent?\n\nPhoto by TechCrunch on Flickr"}, {"id": 50, "url": "https://news.google.com/rss/articles/CBMimQFodHRwczovL3d3dy5iZW56aW5nYS5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS8yMy8xMS8zNTk3MjM5NC9ldGhlcmV1bS1jby1mb3VuZGVyLXZpdGFsaWstYnV0ZXJpbi10aGlua3MtaWYtYWktdHVybnMtYWdhaW5zdC11cy1pdC1tYXktd2VsbC1sZWF2ZS1uby1zdXLSAS1odHRwczovL3d3dy5iZW56aW5nYS5jb20vYW1wL2NvbnRlbnQvMzU5NzIzOTQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Tu<PERSON>, 28 Nov 2023 08:00:00 GMT", "title": "Ethereum Co-Founder <PERSON><PERSON> Thinks If AI Turns Against Us, It May 'Well Leave No Survivors And En - Benzinga", "content": "Ethereum’s ETH/USD co-founder, <PERSON><PERSON>, on Monday shed light on his own version of “techno-optimism,” influenced by <PERSON>‘s Techno-Optimist <PERSON><PERSON><PERSON> penned last October, in which <PERSON><PERSON><PERSON> took a positive stance on AI’s future.\n\nWhat Happened: <PERSON><PERSON><PERSON> in a blog post said, \"This is an extreme claim: as much harm as the worst-case scenario of climate change, or an artificial pandemic or a nuclear war, might cause, there are many islands of civilization that would remain intact to pick up the pieces.\"\n\nHowever, the risks posed by a superintelligent AI that may turn against humanity could signify complete annihilation. \"But a superintelligent AI, if it decides to turn against us, may well leave no survivors and end humanity for good,\" he remarked, worrying that not even Mars could be a refuge.\n\nHe advocated for a future that preserves “human” traits and agency, stating, “If we want a future that is both superintelligent and ‘human’—one where human beings are not just pets, but actually retain meaningful agency over the world—then it feels like something like this is the most natural option.”\n\nSee More: Dogecoin HODLERs Are Beating Shiba Inu With 57% Landing In Profits, IntoTheBlock Data Reveals\n\nWhy It Matters: Addressing concerns about the future evolution of Ethereum during an AMA on Farcaster, <PERSON><PERSON><PERSON> disclosed that two of his greatest fears were the stagnation of cryptocurrency and the potential dangers associated with AI.\n\nWhen questioned on what occupies his thoughts frequently, the influential developer cited, “AI-related issues,” specifically existential risks derived from it.\n\nPrice Action: At the time of writing, ETH was trading at $1,999, down 1.12% in the last 24 hours according to Benzinga Pro.\n\nRead Next: Here’s How Much You Should Invest In Shiba Inu Today For A $1M Payday If SHIB Hits 1 Cent?\n\nPhoto by TechCrunch on Flickr"}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YWtlLW9wZW4tc291cmNlLXRvb2xpbmctZXRoZXJldW0tMTQxNTAwODQxLmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Tu<PERSON>, 28 Nov 2023 08:00:00 GMT", "title": "Wake: New Open-Source Tooling on Ethereum to Stop Bugs - Yahoo Finance", "content": "Wake\n\nPrague, Czech Republic, Nov. 28, 2023 (GLOBE NEWSWIRE) -- Quicktake:\n\n\n\nWake is a Python-based Solidity development and testing framework with built-in vulnerability detectors. It was used in audits by Ackee Blockchain and is now being open-sourced and free to use for everyone.\n\nSecurity experts are on high alert, as history shows that hack risks increase as market activity rises. Open-source tooling like Wake empowers application builders to conduct swift and thorough checks for code vulnerabilities.\n\n\n\nBattlefield-proven audit tooling goes open-source\n\nAckee Blockchain, a team of smart contract auditors and creators of community tools, introduces Wake, a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs.\n\nWake’s features include a development and testing framework, a fuzzer, vulnerability detectors, and printers.\n\nWake was previously used in smart contract audits performed by Ackee Blockchain for IPOR, Axelar, and Solady and helped the auditors find critical, high, and medium bugs. The firm has announced it will make the tool open-source to contribute to a safer blockchain space.\n\n\n\nSecurity experts on alert as market activity rises\n\nAs DeFi activity rises, hackers see more lucrative targets and grow emboldened. In bull markets, projects often rush to ship product updates, creating a high demand for experienced smart contract auditor services. In the peak of the 2021 bull market, projects were typically quoted three month+ audit wait times by reputable firms. They then face a difficult decision to delay launches amid frenzied “land grabs” or launch with hastily conducted audits, increasing security risks.\n\nAfter a week when crypto funds saw their highest inflows since the 2021 bull market and DeFi volumes rose by approximately 50%, security experts are on higher alert. Wake’s release is timely - it allows application developers to expertly test the smart contracts of DEXs or any protocol that plugs into their service or upon which they are reliant.\n\n\n\nStory continues\n\nDesigned to generate fewer false positives and reduce manual audit time\n\nOn top of being the testing framework, Wake allows users to run static analysis. It has a set of ready-to-use high-precision vulnerability and code quality detectors and ready-to-use printers for extracting and pretty-printing useful information such as smart contract control flow and inheritance graphs.\n\n“A common problem of static analysis tools is a high false-positives ratio requiring extra manual investigation time. Our philosophy is to include only the most precise detectors and reduce all noise that creates extra overhead,” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake underwent performance testing with other frameworks, namely Hardhat, Brownie, and Ape, on three different development chains – Anvil, Ganache, and Hardhat. Wake proved to be the fastest Python framework.\n\nThe new release of Wake allows the customization of detectors and printers. It also allows third parties to create and implement their custom printers and detectors. The newest version also introduced Github action for automatically executing detectors in a pipeline. “The low false-positive rate, fastest industry test execution, and ready-made GitHub action make Wake an ideal candidate for CI/CD integration in every project” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake powers Tools for Solidity, a popular Visual Studio Code Solidity extension that does syntax highlighting and detections from Wake vulnerability and code quality detectors and provides instant feedback to the developers using the extension access to all references of a symbol across the whole project.\n\n\n\nAbout Wake\n\nWake is a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs created by Ackee Blockchain, which became possible thanks to a grant Ackee Blockchain received from Coinbase in 2022.\n\nTo learn more, please visit getwake.io.\n\nWebsite | Twitter | Telegram\n\n\n\nAbout Ackee Blockchain\n\nAckee Blockchain is a team of security researchers auditing top-tier protocols: Safe, 1inch, Axelar, LayerZero, Trader Joe, or CoW Protocol.\n\nAckee Blockchain is backed by the largest VC fund focused on blockchain and DeFi in Europe, RockawayX, and received grants from the Ethereum Foundation, Tezos Foundation, Coinbase, and Solana Foundation.\n\nAckee Blockchain’s mission is to build a stronger blockchain community by sharing knowledge: the team runs a free certification course School of Solidity, School of Solana and teaches at the Czech Technical University in Prague. This mission is also fulfilled in initiatives such as the ERC-7512 draft, developing the Wake toolkit, or the open-source fuzzer for Solana.\n\nWebsite | Twitter\n\n\n\nDisclaimer: The information provided in this press release is not a solicitation for investment, or intended as investment advice, financial advice, or trading advice. It is strongly recommended that you practice due diligence (including consultation with a professional financial advisor) before investing in or trading securities and cryptocurrency.\n\nCONTACT: For media inquiries or further information, please contact Josef Gattermayer marketing(at)ackeeblockchain.com\n\n\n\n"}, {"id": 15, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YWtlLW9wZW4tc291cmNlLXRvb2xpbmctZXRoZXJldW0tMTQxNTAwODQxLmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Tu<PERSON>, 28 Nov 2023 08:00:00 GMT", "title": "Wake: New Open-Source Tooling on Ethereum to Stop Bugs - Yahoo Finance", "content": "Wake\n\nPrague, Czech Republic, Nov. 28, 2023 (GLOBE NEWSWIRE) -- Quicktake:\n\n\n\nWake is a Python-based Solidity development and testing framework with built-in vulnerability detectors. It was used in audits by Ackee Blockchain and is now being open-sourced and free to use for everyone.\n\nSecurity experts are on high alert, as history shows that hack risks increase as market activity rises. Open-source tooling like Wake empowers application builders to conduct swift and thorough checks for code vulnerabilities.\n\n\n\nBattlefield-proven audit tooling goes open-source\n\nAckee Blockchain, a team of smart contract auditors and creators of community tools, introduces Wake, a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs.\n\nWake’s features include a development and testing framework, a fuzzer, vulnerability detectors, and printers.\n\nWake was previously used in smart contract audits performed by Ackee Blockchain for IPOR, Axelar, and Solady and helped the auditors find critical, high, and medium bugs. The firm has announced it will make the tool open-source to contribute to a safer blockchain space.\n\n\n\nSecurity experts on alert as market activity rises\n\nAs DeFi activity rises, hackers see more lucrative targets and grow emboldened. In bull markets, projects often rush to ship product updates, creating a high demand for experienced smart contract auditor services. In the peak of the 2021 bull market, projects were typically quoted three month+ audit wait times by reputable firms. They then face a difficult decision to delay launches amid frenzied “land grabs” or launch with hastily conducted audits, increasing security risks.\n\nAfter a week when crypto funds saw their highest inflows since the 2021 bull market and DeFi volumes rose by approximately 50%, security experts are on higher alert. Wake’s release is timely - it allows application developers to expertly test the smart contracts of DEXs or any protocol that plugs into their service or upon which they are reliant.\n\n\n\nStory continues\n\nDesigned to generate fewer false positives and reduce manual audit time\n\nOn top of being the testing framework, Wake allows users to run static analysis. It has a set of ready-to-use high-precision vulnerability and code quality detectors and ready-to-use printers for extracting and pretty-printing useful information such as smart contract control flow and inheritance graphs.\n\n“A common problem of static analysis tools is a high false-positives ratio requiring extra manual investigation time. Our philosophy is to include only the most precise detectors and reduce all noise that creates extra overhead,” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake underwent performance testing with other frameworks, namely Hardhat, Brownie, and Ape, on three different development chains – Anvil, Ganache, and Hardhat. Wake proved to be the fastest Python framework.\n\nThe new release of Wake allows the customization of detectors and printers. It also allows third parties to create and implement their custom printers and detectors. The newest version also introduced Github action for automatically executing detectors in a pipeline. “The low false-positive rate, fastest industry test execution, and ready-made GitHub action make Wake an ideal candidate for CI/CD integration in every project” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake powers Tools for Solidity, a popular Visual Studio Code Solidity extension that does syntax highlighting and detections from Wake vulnerability and code quality detectors and provides instant feedback to the developers using the extension access to all references of a symbol across the whole project.\n\n\n\nAbout Wake\n\nWake is a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs created by Ackee Blockchain, which became possible thanks to a grant Ackee Blockchain received from Coinbase in 2022.\n\nTo learn more, please visit getwake.io.\n\nWebsite | Twitter | Telegram\n\n\n\nAbout Ackee Blockchain\n\nAckee Blockchain is a team of security researchers auditing top-tier protocols: Safe, 1inch, Axelar, LayerZero, Trader Joe, or CoW Protocol.\n\nAckee Blockchain is backed by the largest VC fund focused on blockchain and DeFi in Europe, RockawayX, and received grants from the Ethereum Foundation, Tezos Foundation, Coinbase, and Solana Foundation.\n\nAckee Blockchain’s mission is to build a stronger blockchain community by sharing knowledge: the team runs a free certification course School of Solidity, School of Solana and teaches at the Czech Technical University in Prague. This mission is also fulfilled in initiatives such as the ERC-7512 draft, developing the Wake toolkit, or the open-source fuzzer for Solana.\n\nWebsite | Twitter\n\n\n\nDisclaimer: The information provided in this press release is not a solicitation for investment, or intended as investment advice, financial advice, or trading advice. It is strongly recommended that you practice due diligence (including consultation with a professional financial advisor) before investing in or trading securities and cryptocurrency.\n\nCONTACT: For media inquiries or further information, please contact Josef Gattermayer marketing(at)ackeeblockchain.com\n\n\n\n"}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy93YWtlLW9wZW4tc291cmNlLXRvb2xpbmctZXRoZXJldW0tMTQxNTAwODQxLmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Tu<PERSON>, 28 Nov 2023 08:00:00 GMT", "title": "Wake: New Open-Source Tooling on Ethereum to Stop Bugs - Yahoo Finance", "content": "Wake\n\nPrague, Czech Republic, Nov. 28, 2023 (GLOBE NEWSWIRE) -- Quicktake:\n\n\n\nWake is a Python-based Solidity development and testing framework with built-in vulnerability detectors. It was used in audits by Ackee Blockchain and is now being open-sourced and free to use for everyone.\n\nSecurity experts are on high alert, as history shows that hack risks increase as market activity rises. Open-source tooling like Wake empowers application builders to conduct swift and thorough checks for code vulnerabilities.\n\n\n\nBattlefield-proven audit tooling goes open-source\n\nAckee Blockchain, a team of smart contract auditors and creators of community tools, introduces Wake, a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs.\n\nWake’s features include a development and testing framework, a fuzzer, vulnerability detectors, and printers.\n\nWake was previously used in smart contract audits performed by Ackee Blockchain for IPOR, Axelar, and Solady and helped the auditors find critical, high, and medium bugs. The firm has announced it will make the tool open-source to contribute to a safer blockchain space.\n\n\n\nSecurity experts on alert as market activity rises\n\nAs DeFi activity rises, hackers see more lucrative targets and grow emboldened. In bull markets, projects often rush to ship product updates, creating a high demand for experienced smart contract auditor services. In the peak of the 2021 bull market, projects were typically quoted three month+ audit wait times by reputable firms. They then face a difficult decision to delay launches amid frenzied “land grabs” or launch with hastily conducted audits, increasing security risks.\n\nAfter a week when crypto funds saw their highest inflows since the 2021 bull market and DeFi volumes rose by approximately 50%, security experts are on higher alert. Wake’s release is timely - it allows application developers to expertly test the smart contracts of DEXs or any protocol that plugs into their service or upon which they are reliant.\n\n\n\nStory continues\n\nDesigned to generate fewer false positives and reduce manual audit time\n\nOn top of being the testing framework, Wake allows users to run static analysis. It has a set of ready-to-use high-precision vulnerability and code quality detectors and ready-to-use printers for extracting and pretty-printing useful information such as smart contract control flow and inheritance graphs.\n\n“A common problem of static analysis tools is a high false-positives ratio requiring extra manual investigation time. Our philosophy is to include only the most precise detectors and reduce all noise that creates extra overhead,” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake underwent performance testing with other frameworks, namely Hardhat, Brownie, and Ape, on three different development chains – Anvil, Ganache, and Hardhat. Wake proved to be the fastest Python framework.\n\nThe new release of Wake allows the customization of detectors and printers. It also allows third parties to create and implement their custom printers and detectors. The newest version also introduced Github action for automatically executing detectors in a pipeline. “The low false-positive rate, fastest industry test execution, and ready-made GitHub action make Wake an ideal candidate for CI/CD integration in every project” - Josef Gattermayer, CEO and co-founder of Ackee Blockchain.\n\nWake powers Tools for Solidity, a popular Visual Studio Code Solidity extension that does syntax highlighting and detections from Wake vulnerability and code quality detectors and provides instant feedback to the developers using the extension access to all references of a symbol across the whole project.\n\n\n\nAbout Wake\n\nWake is a Python-based development and testing framework for Solidity and cross-chain fuzzing with a proven track record of stopping bugs created by Ackee Blockchain, which became possible thanks to a grant Ackee Blockchain received from Coinbase in 2022.\n\nTo learn more, please visit getwake.io.\n\nWebsite | Twitter | Telegram\n\n\n\nAbout Ackee Blockchain\n\nAckee Blockchain is a team of security researchers auditing top-tier protocols: Safe, 1inch, Axelar, LayerZero, Trader Joe, or CoW Protocol.\n\nAckee Blockchain is backed by the largest VC fund focused on blockchain and DeFi in Europe, RockawayX, and received grants from the Ethereum Foundation, Tezos Foundation, Coinbase, and Solana Foundation.\n\nAckee Blockchain’s mission is to build a stronger blockchain community by sharing knowledge: the team runs a free certification course School of Solidity, School of Solana and teaches at the Czech Technical University in Prague. This mission is also fulfilled in initiatives such as the ERC-7512 draft, developing the Wake toolkit, or the open-source fuzzer for Solana.\n\nWebsite | Twitter\n\n\n\nDisclaimer: The information provided in this press release is not a solicitation for investment, or intended as investment advice, financial advice, or trading advice. It is strongly recommended that you practice due diligence (including consultation with a professional financial advisor) before investing in or trading securities and cryptocurrency.\n\nCONTACT: For media inquiries or further information, please contact Josef Gattermayer marketing(at)ackeeblockchain.com\n\n\n\n"}]