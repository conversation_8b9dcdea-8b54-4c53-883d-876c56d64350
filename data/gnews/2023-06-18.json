[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhzY3JpcHRpb25zLWxhdW5jaC1ldGhlcmV1bS1mb2xsb3dpbmctaW5zY3JpcHRpb25zLTAzMTY1MTAxMi5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 18 Jun 2023 07:00:00 GMT", "title": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin - Yahoo Finance", "content": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin\n\nThe Protocol Leverages Ethereum \"Calldata\" and is Considered Cheaper and More Decentralized Than Using Contract Storage\n\nA new protocol called Ethscriptions has generated significant interest within the Ethereum community, allowing users to create and share digital objects on the Ethereum blockchain. Similar to the Ordinals project on the Bitcoin network, Ethscriptions enable users to inscribe non-financial data under 96 kilobytes in size, particularly images for now, directly into the Ethereum main chain. Developed by <PERSON>, the project gained substantial traction with nearly 30,000 Ethscriptions created in less than 18 hours after its launch. The protocol leverages Ethereum \"calldata\" and is considered cheaper and more decentralized than using contract storage. While some argue that similar technology has existed for years, the widespread awareness and adoption of Ethscriptions mark a new trend in the Ethereum community's openness to innovation and experimentation.\n\nThe debut of Ethscriptions has drawn parallels to the Ordinals launch on Bitcoin, where NFT-style images and non-financial data were incorporated into the main chain's ledger. However, the Ethereum community has shown greater acceptance and enthusiasm for this form of experimentation. Ethscriptions experienced intermittent periods of downtime due to the overwhelming interest from users, prompting apologies from <PERSON><PERSON> for the technical glitches. As the Ethscriptions protocol gains traction, its longevity and impact within the community will be determined by ongoing community engagement and adoption."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhzY3JpcHRpb25zLWxhdW5jaC1ldGhlcmV1bS1mb2xsb3dpbmctaW5zY3JpcHRpb25zLTAzMTY1MTAxMi5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 18 Jun 2023 07:00:00 GMT", "title": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin - Yahoo Finance", "content": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin\n\nThe Protocol Leverages Ethereum \"Calldata\" and is Considered Cheaper and More Decentralized Than Using Contract Storage\n\nA new protocol called Ethscriptions has generated significant interest within the Ethereum community, allowing users to create and share digital objects on the Ethereum blockchain. Similar to the Ordinals project on the Bitcoin network, Ethscriptions enable users to inscribe non-financial data under 96 kilobytes in size, particularly images for now, directly into the Ethereum main chain. Developed by <PERSON>, the project gained substantial traction with nearly 30,000 Ethscriptions created in less than 18 hours after its launch. The protocol leverages Ethereum \"calldata\" and is considered cheaper and more decentralized than using contract storage. While some argue that similar technology has existed for years, the widespread awareness and adoption of Ethscriptions mark a new trend in the Ethereum community's openness to innovation and experimentation.\n\nThe debut of Ethscriptions has drawn parallels to the Ordinals launch on Bitcoin, where NFT-style images and non-financial data were incorporated into the main chain's ledger. However, the Ethereum community has shown greater acceptance and enthusiasm for this form of experimentation. Ethscriptions experienced intermittent periods of downtime due to the overwhelming interest from users, prompting apologies from <PERSON><PERSON> for the technical glitches. As the Ethscriptions protocol gains traction, its longevity and impact within the community will be determined by ongoing community engagement and adoption."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhzY3JpcHRpb25zLWxhdW5jaC1ldGhlcmV1bS1mb2xsb3dpbmctaW5zY3JpcHRpb25zLTAzMTY1MTAxMi5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 18 Jun 2023 07:00:00 GMT", "title": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin - Yahoo Finance", "content": "Ethscriptions Launch on Ethereum Following Inscriptions on Bitcoin\n\nThe Protocol Leverages Ethereum \"Calldata\" and is Considered Cheaper and More Decentralized Than Using Contract Storage\n\nA new protocol called Ethscriptions has generated significant interest within the Ethereum community, allowing users to create and share digital objects on the Ethereum blockchain. Similar to the Ordinals project on the Bitcoin network, Ethscriptions enable users to inscribe non-financial data under 96 kilobytes in size, particularly images for now, directly into the Ethereum main chain. Developed by <PERSON>, the project gained substantial traction with nearly 30,000 Ethscriptions created in less than 18 hours after its launch. The protocol leverages Ethereum \"calldata\" and is considered cheaper and more decentralized than using contract storage. While some argue that similar technology has existed for years, the widespread awareness and adoption of Ethscriptions mark a new trend in the Ethereum community's openness to innovation and experimentation.\n\nThe debut of Ethscriptions has drawn parallels to the Ordinals launch on Bitcoin, where NFT-style images and non-financial data were incorporated into the main chain's ledger. However, the Ethereum community has shown greater acceptance and enthusiasm for this form of experimentation. Ethscriptions experienced intermittent periods of downtime due to the overwhelming interest from users, prompting apologies from <PERSON><PERSON> for the technical glitches. As the Ethscriptions protocol gains traction, its longevity and impact within the community will be determined by ongoing community engagement and adoption."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vY3J5cHRvLm5ld3MvZXRoZXJzY2FuLWludGVncmF0ZXMtY2hhdGdwdC10by1hbmFseXplLWV0aGVyZXVtLXNvdXJjZS1jb2RlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 18 Jun 2023 07:00:00 GMT", "title": "Etherscan integrates ChatGPT to analyze Ethereum source code - crypto.news", "content": "Etherscan, a popular Ethereum (ETH) block explorer, has announced the addition of ChatGPT to its suite of tools for analyzing the coin’s blockchain.\n\nThe new tool, called Code Reader, utilizes the ChatGPT API to provide users with the ability to retrieve and interpret the source code of a specific contract address. This integration aims to enhance the understanding of Ethereum source code and make the blockchain analysis process more efficient.\n\n🆕 Code Reader (Beta)\n\n\n\nLeverage the power of AI to seamlessly learn about any smart contract source code! ✨ pic.twitter.com/GTbULisudk — Etherscan (@etherscan) June 19, 2023\n\nBlockchain explorers allow users to view transactions and other information on a blockchain network. Etherscan is one of the leading block explorers for the Ethereum blockchain, providing valuable insights into the Ethereum ecosystem. With the integration of ChatGPT, Etherscan aims to streamline the process of scanning and analyzing the blockchain, which can often be tedious and time-consuming.\n\nYou might also like: Etherscan introduces advanced filtering tool in beta for blockchain analysis\n\nEtherscan is not the only company leveraging ChatGPT for blockchain exploration. Last week, Alchemy, a prominent blockchain platform developer, launched its ChatGPT-based tool called AlchemyAI, which includes a GPT-4 plugin for blockchain analysis. Additionally, Solana Labs introduced its own ChatGPT plugin in May.\n\nWhile the integration of ChatGPT brings advanced AI capabilities to blockchain analysis, Etherscan emphasizes the tool’s limitations. The company warns users not to rely solely on ChatGPT’s responses and always verify the information independently.\n\nThis caution stems from the well-known issue of AI chatbots generating false or fake information, a phenomenon referred to as hallucinations. OpenAI, the creator of ChatGPT, has been working on improving the model to prevent hallucinations and ensure the accuracy of generated responses.\n\nEtherscan’s Code Reader tool is currently in beta, and the company encourages user feedback to enhance its functionality further. As the blockchain industry continues to evolve, integrating AI technologies like ChatGPT is expected to facilitate blockchain exploration and analysis significantly.\n\nFollow Us on Google News"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZGVjcnlwdC5jby8xNDUxNTMvZXRoc2NyaXB0aW9ucy1ldGhlcmV1bS1vcmRpbmFscy1iaXRjb2luLWluc2NyaXB0aW9uc9IBVGh0dHBzOi8vZGVjcnlwdC5jby8xNDUxNTMvZXRoc2NyaXB0aW9ucy1ldGhlcmV1bS1vcmRpbmFscy1iaXRjb2luLWluc2NyaXB0aW9ucz9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 18 Jun 2023 07:00:00 GMT", "title": "Ordinals Brought Inscriptions to Bitcoin—Now Ethscriptions Land on Ethereum - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nThere is a new protocol that allows users to create and share digital objects on Ethereum. They are called Ethscriptions—an apparent tip of the hat to Inscriptions, a similar project by Ordinals deployed on the Bitcoin network.\n\nDeveloped by <PERSON>, co-founder and former CEO of Genius.com, the project launched on Saturday and saw explosive interest. Nearly 30,000 Ethscriptions were created in less than 18 hours, according to <PERSON><PERSON>, who tweeted that the launch was a “huge success.”\n\nEthscriptions enable non-financial and arbitrary data to be written into the main Ethereum blockchain. Users can inscribe any type of file provided it is under 96 kilobytes in size. According to its creator, however, it currently only allows for images—but that will change in the future.\n\nAD\n\nAD\n\nThese inscriptions leverage what’s known as Ethereum “calldata,” which refers to the data provided within a call made to a smart contract. According to <PERSON><PERSON>, the protocol is cheaper and more decentralized than using contract storage.\n\nThe [protocol] “guarantees global uniqueness of the content of all valid Ethscriptions,” the creator tweeted, saying that “it's skating to where the puck is going in an L2 world.<PERSON>\n\n<PERSON><PERSON>, perhaps in a bid for notoriety, called for famed NFT collection Crypto Punks to be “ethscribed.” It successfully sparked a frenzy, and copies of the 10,000 images were inscribed in a matter of hours.\n\nThe launch echoes the Ordinals launch on Bitcoin, NFT-style images and other non-financial data included in the main chain’s ledger. The arrival of Ordinals, however, triggered dismay from many in the maximalist community.\n\nThe difference in reception is the Ethereum community’s apparent openness to innovation and experimentation.\n\nAD\n\nAD\n\n“The exciting thing for me about Ethscriptions, similar to Ordinals, is that developers are playing around again with blockchain technology,” self-proclaimed NFT archaeologist Adam McBride told Decrypt. “It’s this experimentation that drives innovation and adoption,”\n\nMuch like the Ordinal craze, this weekend has seen significant interest from new protocol users. This led Ethscriptions to suffer several periods of downtime, with Lehman tweeting, “Sorry everything is crashing! Too many people.”\n\nAlthough this weekend brought an impressive debut for the protocol, some say it already existed. McBride says this technology has been around for years, with certain artists already using it in their work.\n\nStill, he told Decrypt “this is the first time it’s gained widespread awareness.”\n\nWhether Ethscriptions are a fly-by-night fad or an exciting new trend will ultimately be determined by the community."}]