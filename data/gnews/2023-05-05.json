[{"id": 2, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9tYXJrZXRzLzIwMjMvMDUvMDUvZXRoZXJldW1zLW1lZGlhbi1nYXMtcHJpY2Utc3VyZ2VzLXRvLTEyLW1vbnRoLWFzLXBlcGUtZnJlbnp5LWdyaXBzLW1hcmtldC_SAXpodHRwczovL3d3dy5jb2luZGVzay5jb20vbWFya2V0cy8yMDIzLzA1LzA1L2V0aGVyZXVtcy1tZWRpYW4tZ2FzLXByaWNlLXN1cmdlcy10by0xMi1tb250aC1hcy1wZXBlLWZyZW56eS1ncmlwcy1tYXJrZXQvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 05 May 2023 07:00:00 GMT", "title": "Ethereum Gas Fee Surges to 12-Month High as PEPE Frenzy Grips Market - CoinDesk", "content": "PEPE surpassed the $500 million mark in market capitalization within three weeks of the launch. Similar frenzied activity has been seen other smaller coins. For instance, CHAD and 4TOKEN have rallied 450% and 250% in one week, while DINO has risen 500% in two weeks."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vdW5jaGFpbmVkY3J5cHRvLmNvbS9ldGhlcmV1bS1kZXZlbG9wZXJzLW91dGxpbmUtcGxhbnMtZm9yLWRlbmN1bi1uZXR3b3JrLXVwZ3JhZGUv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 05 May 2023 07:00:00 GMT", "title": "Ethereum Developers Outline Plans for 'Dencun' Network Upgrade - Unchained - Unchained", "content": "The core feature of the Dencun upgrade is EIP-4844, or Proto-Danksharding, which will make Layer 2 transactions significantly cheaper.\n\nDencun will be the next major network upgrade after Sha<PERSON>, and has the potential to lower transaction costs on Ethereum.\n\nIn a blog post published this week, Ethereum core developer <PERSON> revealed the next set of major Ethereum Improvement Proposals (EIP) that have been tentatively included in the Dencun upgrade.\n\nDencun, a term coined by a combination of the star Deneb and Mexican city Cancun, follows a similar naming convention to Shapella, which was named after the Chinese city Shanghai and northern-constellation star Capella.\n\nThe upgrade will focus on bringing improvements and changes to Ethereum’s execution layer through four EIPs that have been decided upon so far. Just like most of the attention in the Shapella upgrade was centered around EIP-4895, which enabled validator withdrawals from the Beacon Chain, the focus of this upgrade will likely be EIP-4844, otherwise known as “Proto-Danksharding” or “The Surge.”\n\nThis EIP will introduce temporary “data blobs” to the Ethereum network, which Layer 2 networks can use to post the transaction/proof data currently stored elsewhere. The fact that blobs are store ephemerally means that gas costs will be significantly lower, which will lead to much cheaper Layer 2 transactions, explained <PERSON><PERSON>.\n\n“While not quite as big as The Merge, EIP-4844 is a significant change to Ethereum: it introduces a whole new data layer to the network, which both the current consensus and execution layers must interact with,” he said.\n\nIntroducing EIP-4844 will also mean there is less room for other changes to the network, but developers have still included three other EIPs alongside it. These include EIP-1153, which improves blockspace availability, EIP-6475, that makes transaction formats forward-compatible, and EIP-6780, which deactivates the code used to terminate a smart contract.\n\nBeiko did not specify when this upgrade would be deployed, but said that the next update on Dencun’s finalized scope would likely be the first testnet fork announcement."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZGVjcnlwdC5jby8xMzg5MTYvZGNnLWJhcnJ5LXNpbGJlcnQtc2VsbHMtZ3JheXNjYWxlLWV0aGVyZXVtLWNsYXNzaWMtdHJ1c3Qtc2hhcmVz0gFfaHR0cHM6Ly9kZWNyeXB0LmNvLzEzODkxNi9kY2ctYmFycnktc2lsYmVydC1zZWxscy1ncmF5c2NhbGUtZXRoZXJldW0tY2xhc3NpYy10cnVzdC1zaGFyZXM_YW1wPTE?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 05 May 2023 07:00:00 GMT", "title": "DCG’s <PERSON>lls $755K Worth of Grayscale Ethereum Classic Trust Shares - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\n<PERSON>, the founder and CEO of Digital Currency Group (DCG), has sold some of his Grayscale Ethereum Classic Trust (ETCG) shares, according to a recent SEC filing.\n\nDCG is one of the largest crypto conglomerates on the market and counts Grayscale Investments, the trust’s issuer, as one of its subsidiaries.\n\nAccording to the filing, <PERSON>lbert has moved to liquidate nearly 120,000 ETCG shares, worth an aggregate market value of approximately $755,295 dollars. This also appears to be the first time <PERSON><PERSON> has sold any of his ETCG shares, according to SEC filings.\n\nAD\n\nAD\n\nThe ETCG fund, which launched in 2017, allows investors to gain exposure to Ethereum Classic (ETC) through a brokerage account. ETC and Ethereum (ETH) used to be the same thing until 2016, when developers implemented a hard fork of the network to return millions worth of stolen funds to investors after a hack. The fork was notably backed by Ethereum co-founder <PERSON><PERSON>.\n\nThe ETCG sale represents a small portion of the fund’s $225 million assets under management and 14 million outstanding shares—of which <PERSON><PERSON> is a 10% shareholder. He’s also listed as one of the fund’s directors.\n\nThe sale was brokered by New York-based firm Cannacord Genuity on April 28 on the OTCQX, the highest tier of over-the-counter (OTC) market for trading securities, according to the SEC filing.\n\n<PERSON><PERSON>’s shares were originally purchased during two privately negotiated transactions over the 2017-2018 period. So far, he hasn’t made any official comments on the recent transaction.\n\nIt’s a somewhat surprising development to see the crypto permabull selling his ETCG shares. Silbert has been in the crosshairs over the past months, due to a $630 million dollar debt DCG-owned lending desk Genesis owes to Gemini, a crypto exchange and custodian owned by the Winklevoss twins.\n\nAD\n\nAD\n\nEarlier this week, the companies announced they had entered into a 30-day mediation process to settle the outstanding loan balance. “If DCG is unable to pay and/or restructure its debt, DCG risks defaulting on its obligations,” Gemini said in a statement."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiygJodHRwczovL2ZpbmFuY2UueWFob28uY29tL25ld3MvcGVwZS1tZW1lLWNvaW4taHlzdGVyaWEtcHVzaGVzLTA5MzIzNzkzMS5odG1sP2d1Y2NvdW50ZXI9MSZndWNlX3JlZmVycmVyPWFIUjBjSE02THk5M2QzY3VaMjl2WjJ4bExtTnZiUzgmZ3VjZV9yZWZlcnJlcl9zaWc9QVFBQUFEZmxTYVYyUXFOVGNkbWgzYUpscE13VGRCazhIT1NVV2hVbnNUVUQxSlBYQXVTbzNGVUYxMGI3Nm4yQmNod0pOYWlDTnlpdXhEVVF2b3puZ3lXRGRNWnhrTUZ5UXJIa2RpTFVhRm1IQW1FeDRTRnhiNG9RQzlocU9vLXJTaUczTV9jRjN2dF8xZHIwY0VnRTFzMDlvbzV1azhyYzZxLTYxbXY4MmVqSnhiRHrSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 05 May 2023 07:00:00 GMT", "title": "PEPE Meme Coin Hysteria Pushes Ethereum Gas Fees to 1-Year High - Yahoo Finance", "content": "Ethereum’s gas fees have surged above a one-year high as the demand for trading the PEPE meme coin continues to soar.\n\nThe average cost of a single transaction on Ethereum jumped above $15.82 on May 4.\n\nWhile Ethereum’s mean transaction fees have reached much higher levels of between $50 to $70 in 2021, things have been quiet since the market crashed in May 2022.\n\nDaily median gas price. Source: Etherscan .\n\nIt’s likely that excitement around meme coins, specifically the newly-launched PEPE, is responsible for the revival in gas fees.\n\nIn the last 24 hours, the trading volume for PEPE surpassed $150 million on Uniswap, higher than Wrapped Bitcoin (WBTC) and USDT stablecoin volumes.\n\nThe PEPE token also entered the top 100 largest cryptocurrencies by market capitalization on May 1, obtaining a valuation of over $500 million. The token has continued to surge since then, last trading at a market value of $879 million.\n\nFollowing the success of PEPE coin, numerous copycats have also emerged representing different internet memes.\n\nData from crypto analytics firm Nansen shows that the most active smart contracts on Ethereum are currently Uniswap trading pools for meme coins such as SPONGE, TURBO, BOB, and TRBNR.\n\nMost used DeFi contracts on Ethereum on May 5, 2023. Source: Nansen\n\nThe increased gas fees witnessed 9,392 in ETH burned over the last 24 hours, at par with one-year high levels.\n\nEthereum executed a hard fork in 2021 which meant that a portion of all Ethereum transactions were destroyed. Thus, as activity increases on the network, more ETH is destroyed.\n\nThe rise in Ethereum’s transaction fees has led to a rise in gas fees for Layer-2 transactions as well.\n\nAre Ethereum scaling solutions helping?\n\nThe gas fees for layer-2 transactions have also surged with mainnet fees as related data for layer-2 transactions are also posted in layer-1 blocks for security.\n\nThere are two kinds of layer-2 scaling solutions: zero-knowledge proofs and optimistic. Both use another technology called rollups, which batch transactions off of the mainnet into a single, smaller transaction before settling on the mainnet.\n\nStory continues\n\nNotably, zero-knowledge proof (ZK)-based rollups were hit relatively worse than optimistic rollups. The fees for swapping tokens on ZK-based scaling solutions like Polygon zkEVM, Starknet, and zkSync Era mainnet surged between $2 to $11.\n\nA zkSync Discord community member noted they had to pay $11 swap ETH to USDC, noting in amazement that “Is it gonna cost that much?”\n\nAnother user complained about $30 in fees for DeFi deposits on zkSync.\n\n$30 gas fee on Layer 2 $ZKS $ZKSYNC @zksync\n\nI don't understand, I thought layer 2 were made to reduce gas fees 😱😱 pic.twitter.com/Uz6k5p4bC5 — Kool Crypto ⚙️🛸 (@healthymofin) May 5, 2023\n\nAccording to L2 Fees data, the gas fees for sending ETH on Arbitrum One and Optimism, both of which are Optimistic rollup solutions, are between $0.2 to $0.6 for swap tokens, which is around ten times higher than the mean value."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiQ2h0dHBzOi8vZGFpbHljb2luLmNvbS9jYXJkYW5vcy1oeWRyYS12cy1saWdodG5pbmctbmV0d29yay1zaGFyZGluZy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 05 May 2023 07:00:00 GMT", "title": "How Cardano's Hydra Layer-2 Is Challenging Bitcoin Lightning Network and Ethereum Sharding - DailyCoin", "content": "Cardano released its Layer-2 scalability solution, Hydra.\n\nHydra introduces a novel scaling solution that challenges Bitcoin, Ethereum, and others.\n\nAccording to <PERSON>, Cardano’s new solution could make it the fastest, most scalable network.\n\nCardano has long been vying for a spot among the top cryptocurrency projects. Now, with its scalability solution Hydra up and running, it has solidified its position as a major player in the industry, standing alongside its more established peers, Bitcoin and Ethereum.\n\nHydra, the Layer-2 solution from the Proof of Stake (POS) Chain, is one of the most highly anticipated upgrades. Now that it is here, people are excited to see it deliver Cardano’s promise of being the fastest and most scalable payment network in the world, challenging the likes of Bitcoin and Ethereum.\n\nWhat Is Hydra?\n\nThe Hydra Head Protocol or Hydra is Cardano’s open-source Layer-2 scalability solution that, according to <PERSON>, would make Cardano the fastest payment network in the world.\n\nAnnounced in September 2019, after much anticipation, delays, and hurdles, Hydra finally made it to the Cardano main chain in May 2023, thanks to engineers from the Cardano Foundation and IOHK.\n\nHydra is an isomorphic scaling solution. This means it replicates Cardano’s functionality and security guarantees on an off-chain ledger network while maintaining communication between the two layers.\n\nCardano Hydra use case. Source: Hydra Head Protocol.\n\nIt operates almost similarly to state-channel solutions such as Bitcoin’s Lightning Network and Ethereum’s Raiden Network. Users can securely and instantly perform transactions on their private networks, in this case, Hydra heads, and settle the result on the main chain, enabling faster and cheaper transactions while keeping the integrity and security of the network.\n\nWhat’s unique about Hydra is that off-chain ledgers through Head nodes can act as special dApps for developers who look to scale their apps. With Hydra, developers can independently run on-chain scripts or software stack on their private nodes and securely publish the result on the main chain instantly and at a low price.\n\nHydra brings a solid solution, allowing Cardano to compete with Ethereum and Bitcoin.\n\nIn Contrast with Other Scalability Solutions\n\nBoth Bitcoin and Ethereum have long established themselves in the cryptocurrency world, with the latter powering a myriad of Layer-2 protocols. Additionally, Ethereum’s sharding and Bitcoin’s lightning network are some of the best solutions in the space, earning the networks a collective market cap of $807 billion.\n\nAlthough Cardano is often compared with Bitcoin and Ethereum, its string of delays and issues drew much criticism from the community, leading to slow growth evident with its $13 billion market cap. However, with Hydra in the mix, it can finally sit at the table with its solution.\n\nEthereum, Cardano, and Bitcoin Compared\n\nWhile Ethereum Sharding, Bitcoin Lightning Network, and Cardano’s Hydra solution may come from the same family or operate similarly, comparing or saying one is better wouldn’t be fair. Each scalability solution has its pros and cons and addresses different challenges.\n\nSharding is a Layer-1 solution that focuses on increasing the network’s capacity and increasing the overall transaction throughput of the network. It breaks the blockchain into shards that process transactions parallel to the main chain.\n\nHowever, according to Charles Hoskinson, Sharding is “very complex.” Because of this complexity, network security and performance drastically defer because it operates on the main chain. Additionally, it requires a lot of resources and can be expensive to implement.\n\nHydra, on the other hand, creates an independent off-chain ledger network that replicates the security and functionality of the Cardano chain, allowing it to maintain both security and performance without congesting the main chain. It doesn’t rely on soft or hard forks, making it inexpensive and straightforward to implement.\n\nConversely, Bitcoin Lightning Network is a Layer-2 solution on top of Bitcoin, which uses bi-directional off-chain payment channels between users, enabling fast, cheap, and scalable transactions.\n\nHowever, because it uses payment channels, it only supports asset transfer, unlike Hydra, which allows developers to run scripts and elaborate transactions instantly off-chain as it would on the main chain.\n\nOn the Flipside\n\nAn Ethereum founder accused Cardano Founder of contributing nothing to Ethereum.\n\nMany competitors called Cardano’s EUTXO design a security flaw; Hoskin replied that the model made it more secure.\n\nWhy You Should Care\n\nCardano’s Hydra solution brings competition to the space. The better the competition, the better the solutions, leading to greater adoption. Cardano’s new protocol could significantly elevate the network’s status in the cryptocurrency industry and attract more developers to build apps on the network.\n\nFAQs\n\nWhat is Hydra on Cardano? Hydra is Cardano’s Layer-2 scaling solution that creates independent off-chain ledgers with functionality, features, and security similar to the Cardano main chain. Is Hydra built on Cardano? Yes, Hydra is built on Cardano. It is a Layer-2 scaling solution that processes transactions on an off-chain ledger and settles the result on the main chain. Is Hydra a Layer-2? Yes, Hydra is a Layer-2 solution on Cardano. It operates on top of Cardano to maximize the throughput and functionality of the underlying blockchain. Is Cardano Layer-1 or Layer-2? In blockchain-speak, Layer-1 is the base blockchain where all network activity and transactions are validated and finalized. Cardano is Layer-1.\n\nRead about other blockchains’ developments:\n\nHow Flare’s New Blockchain APIs on Google Cloud Impact Web3\n\nRead what Charles Hoskinson has to say:\n\nCardano Founder Charles Hoskinson Responds to Jab by Ethereum Co-Founder"}]