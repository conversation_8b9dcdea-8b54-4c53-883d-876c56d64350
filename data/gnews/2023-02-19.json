[{"id": 11, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vd3d3LmZvb2wuY29tL3RoZS1hc2NlbnQvY3J5cHRvY3VycmVuY3kvYmVzdC1jcnlwdG8tc3Rha2luZy1wbGF0Zm9ybXMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 19 Feb 2023 08:00:00 GMT", "title": "Best Crypto Staking Platforms for 2024 - The Motley Fool", "content": "Staking is when you commit your crypto assets to a blockchain to support the network and validate transactions. In return, you receive crypto rewards.\n\nThis is only an option with cryptocurrencies that confirm transactions using a model called proof of stake. To process transactions, these cryptocurrencies choose validators who have staked their own crypto. The validator checks a block of transactions, adds it to the blockchain, and receives a staking reward for their contribution. Examples of cryptocurrencies that use proof of stake include:\n\nYou can stake crypto on your own without a staking service, but it's not quite as easy. You'll need to store your crypto in a blockchain wallet, and you'll also likely need to join a staking pool with other crypto investors. Staking platforms, on the other hand, allow you to stake your crypto in a few clicks. This is also a big advantage if you're not comfortable storing your cryptocurrency.\n\nIt's worth mentioning that \"staking\" is often used as a catch-all to cover any way you earn rewards on your crypto. For example, some people refer to crypto lending programs as staking. They're not, but crypto lending can also be an effective way to earn crypto rewards, so we've included lending programs on our list of staking platforms.\n\nREAD MORE: What Is Staking in Crypto?\n\nAre there fees for crypto staking?\n\nCryptocurrency staking fees depend on the staking method and cryptocurrency you choose. Many platforms don't charge any staking fees, but there are some that do. You can find this information on the platform's fees page.\n\nIf you decide to stake crypto without using a platform, you will most likely need to join a staking pool. These pools are made up of groups of investors who pool their crypto for a better chance of earning rewards. Most staking pools charge a pool fee. This amount can vary, but anywhere from 2% to 5% is common."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vY29pbmdhcGUuY29tL2JsdXItYmVjb21lcy10b3AtZ2FzLWd1enpsZXItcHJpY2Utc29hcnMtMTAxMTEv0gFIaHR0cHM6Ly9jb2luZ2FwZS5jb20vYmx1ci1iZWNvbWVzLXRvcC1nYXMtZ3V6emxlci1wcmljZS1zb2Fycy0xMDExMS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 19 Feb 2023 08:00:00 GMT", "title": "BLUR Becomes Top \"Gas Guzzler\" On Ethereum, Binance To List? - CoinGape", "content": "BLUR, the native token of NFT marketplace Blur, is the number one gas burner on Ethereum. Earlier this week, Coinbase said they would launch trading of the BLUR-USD pair when there’s sufficient liquidity. And now, according to Etherscan.io, BLUR overtook UniSwap and Seaport in daily Eth trading volume.\n\nComputational resources on Ethereum are measured in units of gas and paid for by users on the network in the native cryptocurrency ETH. Its 24-hour fees have surpassed $457,006.19, representing nearly 9.91% of all Ethereum network fees of the past day. This week’s airdrop has resulted in over $4 million worth of Ethereum being burned in the last seven days.\n\nadvertisement\n\nBlur’s Growing Dominance\n\nBLUR’s total value locked (TVL) has witnessed a massive surge in the past 48 hours, reaching $121.42 million. This cemented its position as a dominant player in the NFT marketplace ecosystem. Interestingly, it also acts as an NFT marketplace aggregator. According to statistics, it was the largest in the ecosystem with a market dominance of almost 70%.\n\nAs reported previously by Coingape, Blur airdropped its BLUR tokens to more than 100,000 NFT traders. And just the following day, the company recommended NFT project creators prevent trades using OpenSea. There is no fee assessed to artists for using the marketplace. Read more crypto news here…\n\nMassive Price Surge\n\nBLUR price at the time of writing is $1.30 which represents an increase of 11.34% over the past 24 hours. Currently, its market capitalization is $504,594,132 and its 24-hour trading volume is up by 18.83%. Now it stands at $686,428,266. At the same time, the circulating supply is approximately 386,316,751 BLUR, according to CoinMarketCap’s crypto market tracker.\n\nAlso Read: Shibarium Beta To Go Live Next Week: Rumors or Reality?\n\nDisclaimer: The information provided in this article “Blur Is the Number One Gas Guzzler on Ethereum” is solely the author’s opinion and not investment advice, it is provided for educational purposes only."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL25mdC1tYXJrZXRwbGFjZS1ibHVyLWlvLWJ1cm5zLTI1Mi1ldGgtdG8taGl0LXRvcC1vZi1idXJuLWxlYWRlcmJvYXJkL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 19 Feb 2023 08:00:00 GMT", "title": "NFT marketplace Blur.io burns 252 ETH to hit top of burn leaderboard - CryptoSlate", "content": "NFT marketplace aggregator Blur.io has surpassed Uniswap, OpenSea, and Etherum transactions as the top ETH burner over the past 24 hours.\n\nData shared by Access Protocol CEO <PERSON><PERSON> from Ultrasoundmoney highlighted that over 252 ETH was burned as a result of Blur.io activity between Feb. 18 and Feb. 19. Furthermore, the two contracts identified by Ultrasoundmoney combined to a total of 310.7 ETH compared to 239.39 ETH and 177.8 ETH from Uniswap and OpenSea, respectively.\n\nHowever, when Uniswap V2 and V3 contracts are combined, Uniswap burned a total of 328 ETH, narrowly pushing Blur.io off the top spot.\n\nBlur.io\n\nBlur.io is an NFT marketplace aimed at “pro traders” with the ability to sweep the floor of multiple marketplaces from one dashboard. Blur gained notoriety through its airdrop campaign in late 2022 and has since seen a meteoric rise in popularity.\n\nAnalytics platform DappRadar placed Blur.io at the top of its leaderboard for volume over the past 24 hours as activity rose 41% to hit $114.34 million traded.\n\nEthereum burn mechanics\n\nA base fee is paid by the sender of an ETH transaction and then burned to reduce the overall supply of ETH. This burn mechanic reduces the overall supply of ETH over time with the implication of increasing its scarcity and potentially increasing its value.\n\nBurning the base fee, introduced in EIP-1559, also helps create a more stable fee market on the network. As the base fee is dynamically adjusted based on network congestion, burning the fee helps to ensure that the fee market remains stable and that fees do not become excessively high or volatile.\n\nBurn by sector\n\nBlur.io is also closing in on a 30-day timeframe which sees Uniswap and OpenSea top the charts with a combined 14,273 ETH burned in the past month. In addition, Blur.io was responsible for burning 3,012 ETH across the same period.\n\nFurthermore, the NFT sector took up 23% of the burned ETH over the past 30 days with 18,666 ETH. Comparingly, the DeFi sector took the majority at 32%, with a total of 25,403 ETH burned. However, in the past 24 hours, NFT transactions have burned 549 ETH, and DeFi burned 690 ETH.\n\nOverall, the data suggests that both the NFT and DeFi sectors are significant contributors to ETH burn activity, with the Blur.io marketplace emerging as a new notable player in the space.\n\nMentioned in this article"}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGgtdGVzdHMtNS1tb250aC1oaWdoLWFzLXdoYWxlcy1yZXRhaW4tdGhpcy1vZi1zdXBwbHktbW9yZS1pbnNpZGUv0gFdaHR0cHM6Ly9hbWJjcnlwdG8uY29tL2V0aC10ZXN0cy01LW1vbnRoLWhpZ2gtYXMtd2hhbGVzLXJldGFpbi10aGlzLW9mLXN1cHBseS1tb3JlLWluc2lkZS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 19 Feb 2023 08:00:00 GMT", "title": "ETH tests 5-month high as whales retain this % of supply, more inside - AMBCrypto News", "content": "Ethereum rose to $1,700 on February 17.\n\nWhale and shark address holding have not slowed down their accumulation.\n\nThe price of Ethereum [ETH] reached $1,700 on 17 February after five months. Is this ascent an indicator of things to come? Or will the whale accumulation result in dumping before the Shanghai upgrade?\n\nRead Ethereum’s [ETH] Price Prediction 2023-24\n\nETH witnesses brief surge\n\nEthereum gained 3.45% on 17 February, according to a daily period analysis of the cryptocurrency. According to additional research into that trading period, it peaked at $1,721 before ending trade at $1,694.\n\nIt was five months since ETH’s price had last reached the $1,700 range during that trading period. Its price was roughly $1,694 at the time of writing.\n\nFurthermore, the Relative Strength Index (RSI) indicated that ETH was in a bull trend because its line was above the 60 mark. The price movement was also noted above both the long and short Moving Averages (blue and yellow lines). Therefore, the asset’s price moving above the (MAs) suggests a good price move and may also point to a possible future uptrend.\n\nShark and whale hold on\n\nRecent data from Santiment showed that whale and shark addresses were still tightly clutching onto their ETH bags. The graph shows that whale and shark addresses with 100–100,000 ETH still retained close to 47% of the entire supply of ETH. Furthermore, the absence of a sell-off following the most recent price increase suggested that investors anticipated further price increases.\n\nIn addition, an examination of the supply owned by the top addresses revealed that the addresses at the top had been on an accumulating binge. For most of January, the graph showing the quantity held by the top addresses as a proportion of the overall supply of Ethereum was rising. It has now leveled off, but at the time of writing, it was at 123.\n\nVolatility incoming?\n\nEthereum’s Shanghai upgrade will be the next big thing for the cryptocurrency sector. In March, users can withdraw more than $16.5 million worth of Ethereum (ETH) off the blockchain. The Merge was the last significant improvement to the network; however, it had little effect on the price of Ethereum.\n\nIs your portfolio green? Check out the Ethereum Profit Calculator\n\nThe Shanghai upgrade will affect the supply and demand of ETH, whereas the Merge was a purely technological development with no apparent economic consequences. However, because of the long-term and short-term nature of the upcoming development, it has the potential to affect the ETH price significantly.\n\nWhen staked ETHs are released, it is unknown how the shark and whale addresses will respond. But if they, too, decide to sell their assets, ETH’s value will plummet. So, in terms of Ethereum’s price movement, March will be a crucial month."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vZGFpbHljb2luLmNvbS96ay1yb2xsdXBzLXVucmF2ZWxsaW5nLWV0aGVyZXVtcy1zY2FsYWJpbGl0eS1pc3N1ZXMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 19 Feb 2023 08:00:00 GMT", "title": "ZK-Rollups: Unravelling Ethereum's Scalability Issues - DailyCoin", "content": "Zk-rollups have long been touted as the ultimate solution to Ethereum’s woes. As blockchain and crypto adoption enjoy exponential growth worldwide, scaling solutions are needed to ensure the world’s leading smart contract platform can handle millions of users.\n\nZero-knowledge proofs and rollup technology aim to boost transaction throughput in the Ethereum ecosystem, but there are still a lot of questions about how zk-rollups work.\n\nIn this quick guide, we’ll be simplifying the complex jargon around zk-rollup solutions and making sure that you don’t have zero knowledge about zero-knowledge.\n\nWhat’s Wrong with the Ethereum Blockchain?\n\nSince the earliest days of Ethereum, every blockchain network has struggled to overcome a single concept: How to solve the Blockchain Trilemma. Coined by <PERSON><PERSON>, the Blockchain trilemma dictates that the perfect blockchain successfully balances decentralization, security and scalability.\n\nDecentralization – In the true spirit of blockchain, the perfect network is fully distributed. The network is self-sufficient and governed by its users, with miners and validators owned and operated independently worldwide. Bitcoin is a perfect example of decentralization in action.\n\n– In the true spirit of blockchain, the perfect network is fully distributed. The network is self-sufficient and governed by its users, with miners and validators owned and operated independently worldwide. Bitcoin is a perfect example of decentralization in action. Scalability – The perfect blockchain is fast and affordable, making it effective and efficient for all users, regardless of the size of their wallet. Low gas fees and fast transactions are the norm, rather than a unique feature.\n\n– The perfect blockchain is fast and affordable, making it effective and efficient for all users, regardless of the size of their wallet. Low gas fees and fast transactions are the norm, rather than a unique feature. Security – The perfect blockchain is safe. No one can break into your wallet by force, transactions are protected by complex cryptography, and the correct distribution of tokens prevents hackers from taking control of the network with a 51% attack.\n\nEven with dozens of Layer-1 blockchains competing daily, the blockchain trilemma remains unsolved. The irony of the trilemma is that balancing all three options is impossible because engineers always need to sacrifice one option to achieve the others.\n\nTake Solana, for example. Solana is often considered the fastest and most affordable blockchain available. The network boasts over 4,000 transactions per second (TPS), costing a single transaction less than a penny. Solana is also secure and just as safe as any other leading chain.\n\nOn the other hand, Solana suffers from the final point of the trilemma, decentralization. The network is often criticized for constant outages due to centralized points of failure. Solana’s strength and resources came from venture capitalists, who hold a somewhat heavy allocation of SOL tokens.\n\nDespite being the leading Layer-1 blockchain and smart contract platform in the crypto industry, Ethereum has difficulties overcoming the blockchain trilemma.\n\nEthereum Scalability\n\nIf you’ve ever used the Ethereum mainnet at peak traffic, you’ll know that scalability is not Ethereum’s strength. Throughout 2021 and 2022, Ethereum transaction costs were completely out of control. While basic send-and-receive transactions were around $20-30 each, more complex DeFi token swaps or NFT mints often cost over $100 each.\n\nSource: BitInfoCharts.\n\nTransaction fees and throughput are undoubtedly the biggest issues facing Ethereum scaling. During high congestion periods, dApps on the Ethereum network become almost unusable. In many cases, the transaction fee for buying an NFT costs more ETH than the NFT itself. These scalability issues make blockchain technology and decentralized finance (DeFi) inaccessible.\n\nUntil these problems are solved, cryptocurrency and blockchain adoption will struggle to onboard the masses. Thankfully, there are plenty of different ways for Ethereum to become the global powerhouse we know it has the potential to be.\n\nSome of the most revolutionary Ethereum scaling solutions are ZK and optimistic rollups.\n\nWhat are Rollups?\n\nRollups help reduce network congestion on the Ethereum main chain. They take computation and state-storage off-chain process transaction data on other blockchains and then send it back to the main chain. Essentially, rollups allow the network to ‘roll up’ a batch of transactions into one cost-effect transaction. This means users can enjoy the security of the Ethereum layer while benefitting from the cheaper gas fees and faster throughput of Layer-2 networks, like Polygon or Arbitrum.\n\nRollups aren’t the only Ethereum scaling solutions out there. Alternative methods include sharding, sidechains, and state channels. While these are all viable solutions, Buterin himself is most bullish on rolllups and believes that they’ll be an integral part of Ethereum’s future.\n\nRollups are typically divided into two groups, optimistic rollups, and zk-rollups. The main difference between the two is how they verify transactions. Optimistic rollups rely on fraud proofs to validate transaction data, while zk-rollups use zero-knowledge proofs (ZKPs).\n\nOptimistic Rollups\n\nAs the name suggests, optimistic rollups are, well, optimistic. Adopting an ‘innocent-until-proven-guilty’ approach, optimistic rollups assume that each transaction in a rollup is valid. The network all users a set period, usually around a week, to resolve disputes around fraudulent transactions.\n\nBy optimistically assuming that every transaction is valid, the network doesn’t have to devote excess time and computation to confirming transactions are accurate. Some of the most popular Layer-2 solutions, like Arbitrum and Optimism, use this technology to settle off-chain transactions before sending them back to the Ethereum main chain.\n\nZK-Rollups\n\nZk-rollups use complex cryptography called a zkproof to ensure that all rollup transactions are valid without sharing sensitive information about the transactions themselves. They rely on cryptographic proof and submit validity proofs to smart contracts on Ethereum instead of assuming that all transactions are valid.\n\nThis means that all zk-rollup transactions can be processed off-chain, while also reducing the amount of transaction data shared, making them focused on privacy. Moreover, because they don’t need to wait a week for fraudulent transactions to be contested, zk-rollups are considered a much faster and superior technology to Optimistic rollups.\n\nThe most common zk-proofs we see in cryptography are zk-snarks and zk-starks. Simply put, these systems allow users to prove that they know vital or sensitive information, such as transaction data, without publicly revealing the data.\n\nExamples of a few zk-rollup projects include Starknet and Immutable X.\n\nPros and Cons of Zero-Knowledge Rollups\n\nZk-rollups have made significant progress in addressing Ethereum’s scalability issues. They’ve introduced faster and more affordable transactions secured on the main chain and made DeFi more accessible than ever. That being said, they’re far from a perfect solution.\n\nAdvantages of Zk-rollups\n\nValidity proofs ensure that off-chain transactions are correct before being secured on the main chain.\n\nIncreased transaction throughput and low gas fees.\n\nTrustless cryptographic proofing ensures security is enforced by code instead of relying on validators, as we see with optimistic rollups.\n\nUsers can withdraw funds from zk-rollups without experiencing the week-long delays common on optimistic rollups.\n\nDisadvantages of Zk-rollups\n\nZero-knowledge rollups and proof systems are complicated to build and deploy.\n\nYou need specialized hardware to produce validity proofs, making them a centralization risk.\n\nZk-snark proof systems rely on an established, trusted setup for ongoing use. If misused, this setup is a security risk.\n\nThe Ethereum Virtual Machine (EVM)\n\nIf you’ve ever hopped between the most popular blockchain networks like Ethereum, Binance Smart Chain or Avalanche, you’ve probably noticed that many dApps and wallets have a similar interface and user experience. This is thanks to a convenient technology called the Ethereum Virtual Machine, or EVM.\n\nIn its purest form, the Ethereum Virtual Machine is a database and machine state for interacting with Ethereum accounts and contracts. Developers use the EVM to build and deploy dApps on the Ethereum network, as well as by users who interact with these applications using their crypto wallets.\n\nWhat’s special about EVMs is that they’re not Ethereum-exclusive. Many other blockchains run their own EVM, which makes it possible for Ethereum-based dApps and tools to be easily ported over and deployed on other networks.\n\nFor example, many decentralized exchanges like PancakeSwap on the Binance Smart Chain or Quickswap on Polygon were created using Ethereum smart contracts. See the similarities between UniSwap and PancakeSwap’s UI?\n\nEach respective network’s EVM makes it possible to write Ethereum-based code and applications and replicate them on other networks. This makes blockchain development between EVM-compatible chains far more interoperable and gives developers and users greater flexibility.\n\nzkEVM\n\nAs the name would suggest, a zkEVM uses ZK technology and proofing within an Ethereum Virtual Machine. Most importantly, they support smart contract development on zk-rollups, giving these powerful scaling solutions greater utility. Without a zkEVM, zk-rollups would be limited to simple transactions like basic send and receive functions.\n\nEssentially, a fully functional zkEVM will bring the complete Ethereum experience, including DeFi and NFTs, to the scalable and secure environment of a zk-rollup network.\n\nAn effective zkEVM can securely process thousands of transactions per second. This is a significant improvement from the Ethereum main chain, which struggles to clear more than 20 TPS.\n\nThe zkEVM Race\n\nCompetition within the zkEVM field is tense, with some of the biggest names in the crypto space, like Polygon, hustling to be among the first to deploy a functional zkEVM. The Polygon team has had their eyes set on a zkEVM for some time after purchasing the Hermez Network for $250M back in 2021.\n\nPolygon has since launched a zk-rollup solution named Polygon Hermez in mid-2022. While this represented a key milestone in the zk-rollup sector, Polygon Hermez is limited to simple token transactions, such as sending and receiving payments.\n\nThe Polygon zkEVM is expected to launch in 2023 and will bring greater utility and smart contract support to their zk-rollup scaling solution.\n\nOther projects like Matter Lab’s zkSync are leading contenders to release the world’s first zkEVM, alongside notable competitors like Starknet and Scroll.\n\nOn the Flipside\n\nWhile zk-rollups are arguably more scalable and secure, Optimistic rollups like Arbitrum attract more users and process more transactions than other Layer-2 scaling solutions. However, this could change with the launch of the first zkEVM, which will bring greater utility to zk-rollups.\n\nWhy You Should Care\n\nThe Ethereum network is ill-prepared to accommodate growing adoption and an influx of new users. Zk-rollup scaling solutions and zkEVMs will make blockchain technology and cryptocurrency far more accessible than before and drive mass adoption worldwide.\n\nFAQs"}]