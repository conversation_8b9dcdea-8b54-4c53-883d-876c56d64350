[{"id": 34, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhlcmV1bS1kZW5jdW4tdXBncmFkZS1kZXBsb3llZC1zdWNjZXNzZnVsbHktMDQyNTIzMjU5Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 17 Jan 2024 08:00:00 GMT", "title": "Ethereum's Dencun Upgrade Deployed Successfully on Goerli Testnet - Yahoo Finance", "content": "Ethereum’s Dencun Upgrade Deployed Successfully on Goerli Testnet\n\nEthereum's core developers have taken a significant step towards improving the network's scalability with the deployment of the Dencun upgrade on the Goerli testnet on January 24, 2024, at around 1:35 a.m. ET. However, the upgrade faced issues in the validator clients' ability to sync with each other.\n\n<PERSON><PERSON><PERSON>, DevOps engineer at the Ethereum Foundation, later announced that the fix was patched in and the Dencun fork on Goerli was finalized. In preparation for this transition, the development team has opted for a phased approach, initially implementing the upgrade on various testnets.\n\nThe successful deployment on the Goerli testnet marks the first phase of a gradual rollout, with subsequent implementations planned for the Sepolia and Holesky testnets before the final mainnet release in Q1 2024.\n\nThe Dencun upgrade introduces EIP-4844, a novel transaction mechanism known as ephemeral blobs or proto-danksharding. This approach allows Ethereum nodes to temporarily store and access off-chain data, reducing storage demands and lowering transaction costs. By reducing transaction costs, EIP-4844 will make it more affordable to use Ethereum dApps and Layer 2 solutions.\n\nThe Dencun upgrade is a positive development for the Ethereum ecosystem, bringing the network closer to enhanced scalability and lower transaction costs. The phased rollout approach minimizes risks and allows for thorough testing, ensuring a successful transition to the upgraded network.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 20, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhlcmV1bS1kZW5jdW4tdXBncmFkZS1nb2VzLWxpdmUtMDc0MzUyMzAyLmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 17 Jan 2024 08:00:00 GMT", "title": "Ethereum's Dencun Upgrade Goes Live, But Fails to Finalize on Testnet - Yahoo Finance", "content": "Ethereum’s much-awaited Dencun upgrade went live on the Goerli testnet earlier Wednesday but failed to finalize in the expected time.\n\nThe upgrade was pushed at 6:32 UTC, blockchain data shows, but did not initially finalize on the testnet.\n\nA few hours later, Ethereum core developer <PERSON><PERSON><PERSON> confirmed that the chain was finalizing again, and told CoinDesk over Telegram that the issue was due to a bug, now patched, in Prysm nodes.\n\nAnd it seems like we have a chain split! Client teams are looking into it, but it's likely to a while to pin down + fix the issues. Keep an eye out for updates! — timbeiko.eth ☀️ (@TimBeiko) January 17, 2024\n\nFinality refers to irreversibility once a transaction has been confirmed and added to a block in a blockchain network. A testnet is a network that mimics real-world blockchains and is used to test applications and important upgrades before they can be pushed live on a mainnet.\n\nDencun’s implementation of Goerli is part of a three-phased approach to eventually enacting a new, less costly method of storing data on the main Ethereum blockchain.\n\nThat method, “proto-danksharding,” is a mechanism that will add capacity for data availability as well as help reduce the cost of transactions for layer-2 blockchains. These auxiliary networks have proliferated in the past year as an alternative to processing transactions on the main Ethereum blockchain, but analysts say their growth is hampered by the steep data costs under the current setup.\n\nThe next phase will happen sometime in the next few weeks, with an upgrade to the Sepolia testnet, followed by the Holesky testnet.\n\nDencun will be the biggest upgrade – technically a “hard fork” in blockchain terminology – for Ethereum since the Shapella upgrade last March, which enabled the withdrawals of staked ether [stETH]. That milestone marked the second step for Ethereum’s transition to a proof-of-stake blockchain, away from the more energy intensive proof-of-work chain that it was before the Merge.\n\nUPDATE (Jan. 17, 08:15 UTC): Adds additional details.\n\nUPDATE (Jan. 17, 14:49 UTC): Updates that the blockchain is finalizing again."}, {"id": 89, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9keWR4LXRvcHMtdW5pc3dhcC1sYXJnZXN0LWRleC0yMDIwMzgxNzYuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 17 Jan 2024 08:00:00 GMT", "title": "dYdX Tops Uniswap as Largest DEX by Volume - Yahoo Finance", "content": "Decentralized exchange dYdX, which recently migrated from Ethereum to Cosmos, has topped one of Uniswap's markets to become the largest DEX by daily trading volume, according to data from CoinMarketCap.\n\nThe Cosmos-based v4 version of dYdX just saw $757 million of volume over a 24-hour period, topping Uniswap v3, which had $608 million, the data shows. dYdX's v3 market, which still operates, had $567 million, enough for third place.\n\nAccording to dYdX, the total trade volume so far for its v4 market since launch is $17.8 billion. In 2023, dYdX's v3 saw a total of over $1 trillion in trading volume with several days exceeding $2 billion of trading volume.\n\nThere were concerns when dYdX departed Ethereum that it might struggle to recoup the same level of activity that it experienced in previous iterations because Ethereum, while a more expensive chain, has significantly higher usage than the Cosmos ecosystem. dYdX's high trading volumes, which now surpass that of Uniswap and other Ethereum-based exchanges (including dYdX's own v3 DEX), might serve as a kind of validation of the company's decision to switch ecosystems.\n\ndYdX focuses on facilitating the trading of perpetual futures, which are contracts with no expiration date, thus allowing investors to speculate on the price of an underlying asset while bypassing the physical settlement of goods involved in standard futures trading.\n\nThe platform recently transitioned to v4, which it coined as a \"fully decentralized\" chain, unlike its previous v3 chain, which the company said was not. dYdX said v3 on Ethereum will eventually be closed, but no firm date is set for the closure.\n\nAccording to Pantera Capital's Paul Veradittakit, decentralized finance (DeFi) users seek platforms that offer \"high throughput for rapid, continuous trading.\" Veradittakit added that \" high gas fees further compound the issue, diminishing user profits and platform appeal.\"\n\nStory continues\n\nVeradittakit said that dYdX v4's transition to a standalone blockchain using the Cosmos SDK addresses challenges head-on by \"promising significantly improved trading throughput, reduced transaction costs and customized on-chain logic tailored to sophisticated and high-frequency trading needs.\"\n\ndYdX is backed by the likes of Patnera, Paradigm and Delphi Digital.\n\nSam Kessler contributed to reporting."}, {"id": 25, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9wcm90b2NvbC1tZXRhbWFza3Mtc2VjcmV0LXdlYXBvbi1ldGhlcmV1bXMtMjIyMDM0NDk4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 17 Jan 2024 08:00:00 GMT", "title": "The Protocol: MetaMask's Secret Weapon and Ethereum's <PERSON><PERSON><PERSON> - Yahoo Finance", "content": "Last month, our feature in The Protocol highlighted how the blockchain design principle of \" intents \" was gaining ascendancy among developers on the industry's cutting edge. This week our <PERSON> is back with a scoop on how the popular crypto wallet MetaMask, from the Ethereum developer Consensys, has quietly deployed a version of an intents-based routing mechanism that could revolutionize how users interact with blockchains.\n\nWe've also got:\n\nA post-mortem on Ethereum's unexpectedly ugly Dencun upgrade on the Goerli test network.\n\nReactions to Vitalik Buterin's proposed \"gas limit\" increase.\n\nJob cuts at NEAR Foundation as new CEO <PERSON><PERSON> \"Black Dragon\" <PERSON><PERSON><PERSON><PERSON> goes to work.\n\nHighlights from this week's Protocol Village column on blockchain project updates, featuring <PERSON><PERSON>, Chainlink, He<PERSON>a, <PERSON>grange and <PERSON><PERSON><PERSON>.\n\nNetwork news\n\nEthereum co-founder <PERSON><PERSON>. (CoinDesk)\n\nMOORE IS MORE. Ethereum co-founder <PERSON><PERSON> , the de facto high priest of the world's largest smart-contracts blockchain, tossed out last week on a Reddit \"Ask Me Anything\" that it would be \"reasonable\" to raise the network's \"gas limit\" – a very technical way of referring to the amount of transactions that can get jammed into each new block . He suggested an increase to \"40M or so,\" implying a 33% increase over the current limit of 30 million gas. (Yes, for the underinitiated, a unit of gas, in this context, is just… a gas .) The main reason this is now possible, according to <PERSON><PERSON><PERSON>, is Moore's law – the observation that computing power seems to double every year . That's relevant because of the amount of data that it takes to store Ethereum's \"state\" – the complete record of the blockchain's history; as computers become more powerful, they should theoretically be able to handle the higher transaction capacity – potentially helping to reduce fees for end-users. \"There appears to be a constructive willingness to explore this topic further,\" analysts at Coinbase Institutional wrote. But some members of the Ethereum community have raised yellow flags. Péter Szilágyi, an Ethereum developer, tweeted that such an increase could slow the network's \"sync time.\" Galaxy Research's Christine Kim wrote in a weekly newsletter that \"larger blocks would certainly increase block propagation latency and potentially result in a higher number of missed blocks. \" Marius van der Wijden, an Ethereum software developer, estimated that the network's state is currently around 87 gigabytes (GB), and growing at 2 GB per month. That would put it at 111 GB in a year and 207 GB in five years. In an era where a 1 terabyte thumb drive can be bought on Amazon.com for $19.99 , it doesn't sound too terribly daunting. \"The problem here is not the size itself,\" van der Wijden wrote. \"Everyone will be able to store that amount of data. However, accessing and modifying it will become slower and slower.\" One thing there seems to be some agreement on: It's worth waiting a bit to observe the impact of the upcoming \" Dencun \" upgrade on the network, which will introduce a new way of storing data as \"blobs,\" effectively providing a capacity increase .\n\nStory continues\n\nBLACK DRAGON BREATHES FIRE: Just two months after NEAR Protocol co-founder Illia Polosukhin took over as CEO of the supporting NEAR Foundation, he announced drastic cuts last week in the organization's workforce – impacting 35 colleagues, or a 40% reduction. According to Polosukhin, the decision came after a \"thorough review of the foundation's activities,\" resulting in feedback that \"the foundation has not always been as effective as it could be.\" Polosukhin noted that the financial picture remains sound, with over $285 million in \"fiat\" or government-issued currencies and 305 million NEAR tokens \"worth over $1B,\" along with $70 million of investments and loans. Referred to in the foundation's communications as the \" Black Dragon ,\" Polosukhin may be under pressure to breathe new life into the alternative layer-1 blockchain, which ranks 32nd among networks based on the much-watched metric of total value locked, or TVL, according to DeFi Llama . Notably NEAR is trying some fresh strategies, pivoting last year to serve as a \" data availability \" network and cutting a deal with restaking pioneer EigenLabs to create a \" fast finality layer \" for Ethereum layer-2 networks.\n\nONE BETTER THAN TWO: The Binance-incubated BNB Chain ecosystem has announced a plan called \" Fusion \" that will see the original BNB Beacon Chain decommissioned while reinforcing the primacy of the sister BNB Smart Chain. According to a statement on the open-source software platform GitHub, the proposal will help to \"overcome legacy services and technical debt, enabling faster iteration and development.\" According to the newsletter Wu Blockchain , \"BNB Beacon Chain is set to exit the stage within the next six months.\"\n\nALSO:\n\nImage tweeted early Wednesday by Ethereum's Tim Beiko with the comment,\n\nEthereum’s Dencun upgrade went live on the Goerli test network early Wednesday but failed to finalize in the expected time. Developers expect the apparent issues to be fixed in the coming days. These likely occurred due to low participation and validators not upgrading parts of their software that would have helped with finalization. ( Link ) Interoperability service Socket and its bridging platform, Bungee, restarted operations after an apparent $3.3M exploit led to a temporary pause in trading activity. A wallet connected to the exploit believed to be the attackers' holds nearly $3 million in ether (ETH) and $300,000 worth of other tokens. ( Link ) Vivek Ramaswamy, crypto-friendly U.S. presidential candidate, suspended his campaign. ( Link ) The U.S. Securities and Exchange Commission issued a statement on the hack of its X account and the resulting fake Bitcoin ETF approval announcement. ( Link ) Venezuela has reportedly ended its controversial Petro cryptocurrency. ( Link ) Moody's, the bond-rating service, said tokenized fund adoption is growing but brings the risk of \"technological failure.\" ( Link ) Solana Mobile plans to launch a second smartphone, after its predecessor, Saga, sold out quickly once opportunistic crypto traders realized it came with an allocation of BONK tokens that more than covered the price of the device. ( Link ) Uri Kolodny, CEO of StarkWare, the developer behind the Ethereum layer-2 network Starknet, announced he is stepping down due to a family health issue. ( Link ) Taproot Wizards, the Bitcoin Ordinals project led by influencers Udi Wertheimer and Eric Wall that raised an astonishing $7.5M last year, is now moving forward with its first sale of a collection , Quantum Cats.\n\nProtocol Village\n\nHighlighting blockchain tech upgrades and developments.\n\n1. Taiko , developing a so-called type-1 zkEVM to help scale the Ethereum blockchain, announced the launch of \"Katla,\" its alpha-6 testnet, according to a message from the team: \"Katla is laying the foundation for Taiko's mainnet launch in 2024.\"2. Parallel Network has officially launched on mainnet and is open to developers, according to the team , claiming to be the first layer-2 network on Arbitrum Orbit to go live. \"It is also the first non-custodial omni-chain margin protocol, which allows liquidity to be pooled across multiple chains and makes it immediately available on the Parallel Network.\"\n\n3. Chainlink’s Cross-Chain Interoperability Protocol (CCIP) has integrated Circle’s Cross-Chain Transfer Protocol (CCTP) to make it easy for users to transfer USDC across chains, according to a press release. Developers can now build cross-chain use cases via CCIP that involve cross-chain transfers of USDC, including payments and other DeFi interactions, the statement said.\n\n4. The Hedera Council announced its newest member, the electronics maker Hitachi America, Ltd. (Hitachi). According to the team: \"Hitachi aims to begin creating proof-of-concepts for end-to-end supply chain and sustainability solutions on Hedera in the next year.\"\n\n5. Lagrange Labs, developer of a blockchain proving system based on zero-knowledge cryptography, has integrated its light client protocol, Lagrange State Committees (LSC), for the Ethereum layer-2 network Mantle, according to the team . LSCs \"are a ZK light client protocol for optimistic rollups (ORUs), designed through combining Lagrange’s ZK MapReduce Coprocessor and EigenLayer restaking. Each state committee borrows security from Ethereum by dual staking, both through EigenLayer restaking and with the rollup’s native token.\"\n\n6. Hacken, a blockchain security auditor, has introduced an open-source Rust library for code coverage generation for WASM-based protocols, according to the team: \"Code Coverage utilities are crucial for automation testing to ascertain the thoroughness of code examination. Without it, some critical components can remain untested. While it is available for Ethereum-based projects, WASM-based protocols don't have it. Wasmcov by Hacken is already integrated into the Radix ecosystem, which enables all Radix-built projects to utilize code coverage measurement. The next protocol to get it will be NEAR. The rest can set it up manually.\"\n\nSee the entire Protocol Village list from this past week here.\n\nMetaMask’s Secret ‘Intents’ Project Could Radically Change How Users Interact With Blockchains\n\nMetaMask has quietly rolled out a limited version of its new routing tech into the new Smart Swaps feature. (MetaMask, modified by CoinDesk)\n\nMetaMask, the most popular crypto wallet on Ethereum, is testing a new \"transaction routing\" technology that's likely to have major ramifications for how value flows through the second-biggest blockchain network.\n\nCoinDesk learned of the new technology from developers briefed on the plan, and key details were subsequently confirmed by officials with MetaMask's parent company, Consensys.\n\nThe effort capitalizes on a concept known in blockchain circles as \" intents \" that is rapidly gaining momentum, potentially leading to a radical shift in how people interact with blockchains: Rather than specifying how they want to get something done (e.g. \"sell X tokens on Y exchange for Z price\"), blockchain users may only need to specify what they want the outcome to be (e.g. \"I want the best price for my tokens\").\n\nThe \"what\" versus \"how\" distinction might seem subtle, but it's a big departure from how MetaMask and other crypto wallets worked originally – as neutral, relatively simple pieces of software for connecting users to blockchains. The goal with the new tech is for users to get better execution on their transactions and improved ease of use, but intent-based programs ultimately represent a big shift to where – and to whom – value flows on blockchains.\n\nThe new technology is being built by Special Mechanisms Group (SMG), a blockchain infrastructure firm that MetaMask owner Consensys purchased last year.\n\nRead the full story by Sam Kessler here\n\nMoney Center\n\nFundraisings\n\nRenzo , an interface for the liquid restaking protocol EigenLayer , has raised $3.2M, according to the team: \"Maven11 led the Renzo seed round which also saw follow-on investments from Figment Capital, SevenX, IOSG and Paper Ventures.\"\n\nHashKey Group , which operates the Hong Kong-based crypto exchange, has “nearly” met its $100 million fundraising target, the firm said Tuesday. HashKey announced the fundraising round in August, shortly after it won a license from Hong Kong’s security regulator to offer retail crypto trading . The firm did not disclose the investors in the round and now claims a valuation of $1.2 billion post-raise, giving it “unicorn” status.\n\nBitfinity Network , a Web3 infrastructure firm, announced it has successfully secured over $7 million in funding from notable backers, including Polychain Capital and ParaFi Capital, advancing its mission to establish off-chain infrastructure for Bitcoin and Ordinals.\n\nDeals and Grants\n\nPush Protocol , the communication protocol of Web3, recently concluded their Billion Reasons to Build (BRB) developer tour in India, according to the team: \"During the hackathon, Aditya Bisht successfully solved one of its hardest coding challenges belonging to the Ethereum Foundation – quantum proofing the Ethereum Network. Bisht's creation of an account abstraction smart contract effectively conceals public keys, enhancing the network's defense against quantum decryption.\"\n\nThe Hedera and Algorand ecosystems have joined to Form DeRec Alliance. (DeRec stands for \"decentralized recovery.\") According to the team: \"Entities from across the Hedera and Algorand ecosystems including the HBAR Foundation, Algorand Foundation, Hashgraph Association, Swirlds Labs, and DLT Science Foundation, along with industry partners The Building Blocks and BankSocial, are partnering to develop a new interoperability recovery standard which will simplify the recovery & adoption of crypto and other assets.\"\n\nSui , a layer-1 blockchain, is getting DePIN and DeWi through a groundbreaking partnership with Karrier One, according to the team: \"The deal also includes strategic investment from Sui to fuel the expansion of Karrier One’s global footprint and deployment on Sui. The technical integration will feature DePIN services powered by the Sui blockchain and the launch of a Karrier One Decentralized Wireless (DeWi) network token on Sui.\"\n\nData and Tokens\n\nAmong Major Layer-2 Networks, ZkSync Era Has the Cheapest Average Transactions\n\nWe finally got around to reading Messari analyst Seth Bloomberg's excellent report out a few weeks ago titled, \" The Onchain Economics of ZK Rollups .\" It provides a good snapshot of the competition between major Ethereum layer-2 networks as 2023 closed out. The report reinforced the oft-repeated observation that \"Data publishing (or data availability) generally remains the highest on-chain cost for rollups\" – thus underscoring the significance of Ethereum's upcoming \"Dencun\" upgrade, which is supposed to lead to drastic reductions in those expenses. But there are different ways to handle the data, of course, and the zkSync Era project stands out in that it \"only posts state diffs to Ethereum,\" while most rollup networks \"publish the full rollup transaction data.\" Per the report: \"In simple terms, publishing state diffs means that if two users send ETH and other tokens to each other multiple times, only the net differences in their account balances need to be published to Ethereum instead of the full transaction history between the two users.\" The upshot? ZkSync Era has the lowest average transaction costs, at 18 cents, seen as a nice bargain compared with Polygon zkEVM's 45 cents.\n\nCalendar"}, {"id": 27, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9wcm90b2NvbC1tZXRhbWFza3Mtc2VjcmV0LXdlYXBvbi1ldGhlcmV1bXMtMjIyMDM0NDk4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 17 Jan 2024 08:00:00 GMT", "title": "The Protocol: MetaMask's Secret Weapon and Ethereum's <PERSON><PERSON><PERSON> - Yahoo Finance", "content": "Last month, our feature in The Protocol highlighted how the blockchain design principle of \" intents \" was gaining ascendancy among developers on the industry's cutting edge. This week our <PERSON> is back with a scoop on how the popular crypto wallet MetaMask, from the Ethereum developer Consensys, has quietly deployed a version of an intents-based routing mechanism that could revolutionize how users interact with blockchains.\n\nWe've also got:\n\nA post-mortem on Ethereum's unexpectedly ugly Dencun upgrade on the Goerli test network.\n\nReactions to Vitalik Buterin's proposed \"gas limit\" increase.\n\nJob cuts at NEAR Foundation as new CEO <PERSON><PERSON> \"Black Dragon\" <PERSON><PERSON><PERSON><PERSON> goes to work.\n\nHighlights from this week's Protocol Village column on blockchain project updates, featuring <PERSON><PERSON>, Chainlink, He<PERSON>a, <PERSON>grange and <PERSON><PERSON><PERSON>.\n\nNetwork news\n\nEthereum co-founder <PERSON><PERSON>. (CoinDesk)\n\nMOORE IS MORE. Ethereum co-founder <PERSON><PERSON> , the de facto high priest of the world's largest smart-contracts blockchain, tossed out last week on a Reddit \"Ask Me Anything\" that it would be \"reasonable\" to raise the network's \"gas limit\" – a very technical way of referring to the amount of transactions that can get jammed into each new block . He suggested an increase to \"40M or so,\" implying a 33% increase over the current limit of 30 million gas. (Yes, for the underinitiated, a unit of gas, in this context, is just… a gas .) The main reason this is now possible, according to <PERSON><PERSON><PERSON>, is Moore's law – the observation that computing power seems to double every year . That's relevant because of the amount of data that it takes to store Ethereum's \"state\" – the complete record of the blockchain's history; as computers become more powerful, they should theoretically be able to handle the higher transaction capacity – potentially helping to reduce fees for end-users. \"There appears to be a constructive willingness to explore this topic further,\" analysts at Coinbase Institutional wrote. But some members of the Ethereum community have raised yellow flags. Péter Szilágyi, an Ethereum developer, tweeted that such an increase could slow the network's \"sync time.\" Galaxy Research's Christine Kim wrote in a weekly newsletter that \"larger blocks would certainly increase block propagation latency and potentially result in a higher number of missed blocks. \" Marius van der Wijden, an Ethereum software developer, estimated that the network's state is currently around 87 gigabytes (GB), and growing at 2 GB per month. That would put it at 111 GB in a year and 207 GB in five years. In an era where a 1 terabyte thumb drive can be bought on Amazon.com for $19.99 , it doesn't sound too terribly daunting. \"The problem here is not the size itself,\" van der Wijden wrote. \"Everyone will be able to store that amount of data. However, accessing and modifying it will become slower and slower.\" One thing there seems to be some agreement on: It's worth waiting a bit to observe the impact of the upcoming \" Dencun \" upgrade on the network, which will introduce a new way of storing data as \"blobs,\" effectively providing a capacity increase .\n\nStory continues\n\nBLACK DRAGON BREATHES FIRE: Just two months after NEAR Protocol co-founder Illia Polosukhin took over as CEO of the supporting NEAR Foundation, he announced drastic cuts last week in the organization's workforce – impacting 35 colleagues, or a 40% reduction. According to Polosukhin, the decision came after a \"thorough review of the foundation's activities,\" resulting in feedback that \"the foundation has not always been as effective as it could be.\" Polosukhin noted that the financial picture remains sound, with over $285 million in \"fiat\" or government-issued currencies and 305 million NEAR tokens \"worth over $1B,\" along with $70 million of investments and loans. Referred to in the foundation's communications as the \" Black Dragon ,\" Polosukhin may be under pressure to breathe new life into the alternative layer-1 blockchain, which ranks 32nd among networks based on the much-watched metric of total value locked, or TVL, according to DeFi Llama . Notably NEAR is trying some fresh strategies, pivoting last year to serve as a \" data availability \" network and cutting a deal with restaking pioneer EigenLabs to create a \" fast finality layer \" for Ethereum layer-2 networks.\n\nONE BETTER THAN TWO: The Binance-incubated BNB Chain ecosystem has announced a plan called \" Fusion \" that will see the original BNB Beacon Chain decommissioned while reinforcing the primacy of the sister BNB Smart Chain. According to a statement on the open-source software platform GitHub, the proposal will help to \"overcome legacy services and technical debt, enabling faster iteration and development.\" According to the newsletter Wu Blockchain , \"BNB Beacon Chain is set to exit the stage within the next six months.\"\n\nALSO:\n\nImage tweeted early Wednesday by Ethereum's Tim Beiko with the comment,\n\nEthereum’s Dencun upgrade went live on the Goerli test network early Wednesday but failed to finalize in the expected time. Developers expect the apparent issues to be fixed in the coming days. These likely occurred due to low participation and validators not upgrading parts of their software that would have helped with finalization. ( Link ) Interoperability service Socket and its bridging platform, Bungee, restarted operations after an apparent $3.3M exploit led to a temporary pause in trading activity. A wallet connected to the exploit believed to be the attackers' holds nearly $3 million in ether (ETH) and $300,000 worth of other tokens. ( Link ) Vivek Ramaswamy, crypto-friendly U.S. presidential candidate, suspended his campaign. ( Link ) The U.S. Securities and Exchange Commission issued a statement on the hack of its X account and the resulting fake Bitcoin ETF approval announcement. ( Link ) Venezuela has reportedly ended its controversial Petro cryptocurrency. ( Link ) Moody's, the bond-rating service, said tokenized fund adoption is growing but brings the risk of \"technological failure.\" ( Link ) Solana Mobile plans to launch a second smartphone, after its predecessor, Saga, sold out quickly once opportunistic crypto traders realized it came with an allocation of BONK tokens that more than covered the price of the device. ( Link ) Uri Kolodny, CEO of StarkWare, the developer behind the Ethereum layer-2 network Starknet, announced he is stepping down due to a family health issue. ( Link ) Taproot Wizards, the Bitcoin Ordinals project led by influencers Udi Wertheimer and Eric Wall that raised an astonishing $7.5M last year, is now moving forward with its first sale of a collection , Quantum Cats.\n\nProtocol Village\n\nHighlighting blockchain tech upgrades and developments.\n\n1. Taiko , developing a so-called type-1 zkEVM to help scale the Ethereum blockchain, announced the launch of \"Katla,\" its alpha-6 testnet, according to a message from the team: \"Katla is laying the foundation for Taiko's mainnet launch in 2024.\"2. Parallel Network has officially launched on mainnet and is open to developers, according to the team , claiming to be the first layer-2 network on Arbitrum Orbit to go live. \"It is also the first non-custodial omni-chain margin protocol, which allows liquidity to be pooled across multiple chains and makes it immediately available on the Parallel Network.\"\n\n3. Chainlink’s Cross-Chain Interoperability Protocol (CCIP) has integrated Circle’s Cross-Chain Transfer Protocol (CCTP) to make it easy for users to transfer USDC across chains, according to a press release. Developers can now build cross-chain use cases via CCIP that involve cross-chain transfers of USDC, including payments and other DeFi interactions, the statement said.\n\n4. The Hedera Council announced its newest member, the electronics maker Hitachi America, Ltd. (Hitachi). According to the team: \"Hitachi aims to begin creating proof-of-concepts for end-to-end supply chain and sustainability solutions on Hedera in the next year.\"\n\n5. Lagrange Labs, developer of a blockchain proving system based on zero-knowledge cryptography, has integrated its light client protocol, Lagrange State Committees (LSC), for the Ethereum layer-2 network Mantle, according to the team . LSCs \"are a ZK light client protocol for optimistic rollups (ORUs), designed through combining Lagrange’s ZK MapReduce Coprocessor and EigenLayer restaking. Each state committee borrows security from Ethereum by dual staking, both through EigenLayer restaking and with the rollup’s native token.\"\n\n6. Hacken, a blockchain security auditor, has introduced an open-source Rust library for code coverage generation for WASM-based protocols, according to the team: \"Code Coverage utilities are crucial for automation testing to ascertain the thoroughness of code examination. Without it, some critical components can remain untested. While it is available for Ethereum-based projects, WASM-based protocols don't have it. Wasmcov by Hacken is already integrated into the Radix ecosystem, which enables all Radix-built projects to utilize code coverage measurement. The next protocol to get it will be NEAR. The rest can set it up manually.\"\n\nSee the entire Protocol Village list from this past week here.\n\nMetaMask’s Secret ‘Intents’ Project Could Radically Change How Users Interact With Blockchains\n\nMetaMask has quietly rolled out a limited version of its new routing tech into the new Smart Swaps feature. (MetaMask, modified by CoinDesk)\n\nMetaMask, the most popular crypto wallet on Ethereum, is testing a new \"transaction routing\" technology that's likely to have major ramifications for how value flows through the second-biggest blockchain network.\n\nCoinDesk learned of the new technology from developers briefed on the plan, and key details were subsequently confirmed by officials with MetaMask's parent company, Consensys.\n\nThe effort capitalizes on a concept known in blockchain circles as \" intents \" that is rapidly gaining momentum, potentially leading to a radical shift in how people interact with blockchains: Rather than specifying how they want to get something done (e.g. \"sell X tokens on Y exchange for Z price\"), blockchain users may only need to specify what they want the outcome to be (e.g. \"I want the best price for my tokens\").\n\nThe \"what\" versus \"how\" distinction might seem subtle, but it's a big departure from how MetaMask and other crypto wallets worked originally – as neutral, relatively simple pieces of software for connecting users to blockchains. The goal with the new tech is for users to get better execution on their transactions and improved ease of use, but intent-based programs ultimately represent a big shift to where – and to whom – value flows on blockchains.\n\nThe new technology is being built by Special Mechanisms Group (SMG), a blockchain infrastructure firm that MetaMask owner Consensys purchased last year.\n\nRead the full story by Sam Kessler here\n\nMoney Center\n\nFundraisings\n\nRenzo , an interface for the liquid restaking protocol EigenLayer , has raised $3.2M, according to the team: \"Maven11 led the Renzo seed round which also saw follow-on investments from Figment Capital, SevenX, IOSG and Paper Ventures.\"\n\nHashKey Group , which operates the Hong Kong-based crypto exchange, has “nearly” met its $100 million fundraising target, the firm said Tuesday. HashKey announced the fundraising round in August, shortly after it won a license from Hong Kong’s security regulator to offer retail crypto trading . The firm did not disclose the investors in the round and now claims a valuation of $1.2 billion post-raise, giving it “unicorn” status.\n\nBitfinity Network , a Web3 infrastructure firm, announced it has successfully secured over $7 million in funding from notable backers, including Polychain Capital and ParaFi Capital, advancing its mission to establish off-chain infrastructure for Bitcoin and Ordinals.\n\nDeals and Grants\n\nPush Protocol , the communication protocol of Web3, recently concluded their Billion Reasons to Build (BRB) developer tour in India, according to the team: \"During the hackathon, Aditya Bisht successfully solved one of its hardest coding challenges belonging to the Ethereum Foundation – quantum proofing the Ethereum Network. Bisht's creation of an account abstraction smart contract effectively conceals public keys, enhancing the network's defense against quantum decryption.\"\n\nThe Hedera and Algorand ecosystems have joined to Form DeRec Alliance. (DeRec stands for \"decentralized recovery.\") According to the team: \"Entities from across the Hedera and Algorand ecosystems including the HBAR Foundation, Algorand Foundation, Hashgraph Association, Swirlds Labs, and DLT Science Foundation, along with industry partners The Building Blocks and BankSocial, are partnering to develop a new interoperability recovery standard which will simplify the recovery & adoption of crypto and other assets.\"\n\nSui , a layer-1 blockchain, is getting DePIN and DeWi through a groundbreaking partnership with Karrier One, according to the team: \"The deal also includes strategic investment from Sui to fuel the expansion of Karrier One’s global footprint and deployment on Sui. The technical integration will feature DePIN services powered by the Sui blockchain and the launch of a Karrier One Decentralized Wireless (DeWi) network token on Sui.\"\n\nData and Tokens\n\nAmong Major Layer-2 Networks, ZkSync Era Has the Cheapest Average Transactions\n\nWe finally got around to reading Messari analyst Seth Bloomberg's excellent report out a few weeks ago titled, \" The Onchain Economics of ZK Rollups .\" It provides a good snapshot of the competition between major Ethereum layer-2 networks as 2023 closed out. The report reinforced the oft-repeated observation that \"Data publishing (or data availability) generally remains the highest on-chain cost for rollups\" – thus underscoring the significance of Ethereum's upcoming \"Dencun\" upgrade, which is supposed to lead to drastic reductions in those expenses. But there are different ways to handle the data, of course, and the zkSync Era project stands out in that it \"only posts state diffs to Ethereum,\" while most rollup networks \"publish the full rollup transaction data.\" Per the report: \"In simple terms, publishing state diffs means that if two users send ETH and other tokens to each other multiple times, only the net differences in their account balances need to be published to Ethereum instead of the full transaction history between the two users.\" The upshot? ZkSync Era has the lowest average transaction costs, at 18 cents, seen as a nice bargain compared with Polygon zkEVM's 45 cents.\n\nCalendar"}]