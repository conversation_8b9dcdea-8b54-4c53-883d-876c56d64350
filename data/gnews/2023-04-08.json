[{"id": 9, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ob3ctd2lsbC10aGUtc2hhbmdoYWktdXBncmFkZS1pbXBhY3QtZXRoLXByaWNlLWV4cGVydC1leHBsYWluc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 08 Apr 2023 07:00:00 GMT", "title": "How will the Shanghai upgrade impact ETH price? <PERSON><PERSON> explains - Cointelegraph", "content": "While it may have some short-term negative impact on the price of Ether (ETH), the upcoming Shanghai upgrade will be highly bullish for Ethereum’s native token, as it will attract more capital to staking and increase the network’s security, according to Ethereum researcher <PERSON><PERSON><PERSON>.\n\nThe Shanghai upgrade, scheduled for April 12, will allow network validators to withdraw funds that have been locked to secure the network since December 2020. The upgrade will complete the network’s transition to a proof-of-stake system, which started in October 2022 with the Merge.\n\nAround 18 million ETH will be available for withdrawal following Shanghai. According to <PERSON><PERSON>, that may lead to some selling pressure on ETH’s price in the short term.\n\nHowever, in the long run, the ability to unstake Ether will “de-risk the ETH investment in a tremendous way,” he pointed out. In particular, institutional investors that couldn’t get involved earlier in staking will feel more comfortable once ETH can be unstaked. More capital entering ETH staking will improve the Ethereum network in the long run.\n\n“The more native proof-of-stake asset that’s staked, the higher the cost to attack the chain,” <PERSON><PERSON> pointed out.\n\nTo find out more about the implications of the upcoming Ethereum upgrade, check out the full interview on our YouTube channel and don’t forget to subscribe!\n\nMagazine: ‘Account abstraction’ supercharges Ethereum wallets: Dummies guide"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1kZXZlbG9wZXItY2FsbC1hbGwtYWJvdXQtZGVuZWItdXBncmFkZS1ldGgtc3Rha2luZy1hbmQtbW9yZS_SAV9odHRwczovL2FtYmNyeXB0by5jb20vZXRoZXJldW0tZGV2ZWxvcGVyLWNhbGwtYWxsLWFib3V0LWRlbmViLXVwZ3JhZGUtZXRoLXN0YWtpbmctYW5kLW1vcmUvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 08 Apr 2023 07:00:00 GMT", "title": "Ethereum developer call: All about Deneb upgrade, ETH staking, and more - AMBCrypto News", "content": "Ethereum’s developer call tackled new areas in development for the ETH network.\n\nSentiment towards Ethereum remained positive.\n\nThe Ethereum [ETH] network is known for being a dynamic and constantly evolving ecosystem, and its developers are always working to improve it. Recently, on 7 April, the network’s consensus developers held their weekly call to discuss several pressing issues related to Ethereum’s functionality and performance.\n\nRead Ethereum’s [ETH] Price Prediction 2023-2024\n\nWhat’s new with Ethereum?\n\nIn the call, Ethereum’s developers discussed the Deneb upgrade progress and MEV-Boost software releases on their weekly call. The Deneb upgrade is the next upgrade after Capella.\n\nMeanwhile, the Capella upgrade, set for 12 April, will enable staked ETH withdrawals. The Shanghai upgrade will activate simultaneously on the mainnet. The developers also launched their third mainnet shadow fork. This would to test the final client software versions for both upgrades. Fortunately, it went smoothly.\n\n<PERSON> from the Flashbots team gave an update on the MEV-Boost releases, which were created in response to a rogue validator stealing MEV from searchers. Two significant changes were pushed to the MEV relay software, causing an increase in the number of orphaned blocks.\n\nThe developers agreed that more monitoring and collaboration with relay operators were necessary to identify and analyze data. The developers also stated that more security research on the MEV-Boost codebase was needed, and a coordinated bug bounty program was suggested.\n\nThe sentiment around ETH\n\nAmidst all the uncertainty before the upcoming Shanghai/Shapella upgrade, the sentiment around Ethereum remained positive at press time. On 7 April, Crypto exchange firm OKX conducted a survey amongst crypto users, according to which 63% of respondents predicted that ETH would hit a record high of over USD5,000 by the end of 2023.\n\nThis positive sentiment around ETH may help in gauging retail behavior in terms of ETH in the future.\n\nWhat’s at stake?\n\nAs the Shanghai Upgrade gets closer, the behavior of users who have staked their ETH on the beacon chain will impact Ethereum.\n\nRealistic or not, here’s ETH market cap in BTC’s terms\n\nAccording to data provided by Flipside Crypto, there’s 19.2 million ETH locked in the beacon chain. Out of which, a majority of stakers hold between 0-2 ETH. In the last 24 hours, each validator on the beacon chain has earned an APY of 3.7%.\n\nOnly time will determine how these stakers will react after they unstaking their holdings following the Shapella Upgrade."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS1zaGFuZ2hhaS1oYXJkLWZvcmstZXRoLXByaWNlLXNldC1mb3ItbW9yZS1nYWlucy12ZXJzdXMtYml0Y29pbi1pbi1hcHJpbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 08 Apr 2023 07:00:00 GMT", "title": "Ethereum Shanghai hard fork: ETH price set for more gains versus Bitcoin in April - Cointelegraph", "content": "Ether (ETH) dropped by over 7.5% in its Bitcoin (BTC) pair in 2023. But ETH/BTC may wipe its year-to-date losses entirely in April, as Ethereum’s long-awaited Shanghai hard fork is just days away.\n\nThe upgrade is set for April 12, enabling Ethereum stakers to withdraw around 1.1 billion ETH in rewards — worth over $2 billion as of April 8.\n\nETH price undergoes key technical bounce\n\nMany experts see the hard fork as bullish for Ether in the long term. For instance, the Shanghai buzz has helped Ether outperform Bitcoin in April.\n\nAs a result, the ETH/BTC pair has risen by about 4.75% month-to-date to reach 0.066 BTC as of April 8, a nearly 8% rebound since March 20.\n\nThe bounce was largely expected, particularly as ETH/BTC dropped to its historical ascending trendline support. Now, the upside move raises the prospects of an extended bullish retracement toward its descending trendline resistance, marked as a “sell zone” in the chart below.\n\nETH/BTC three-day price chart. Source: TradingView\n\nThe fractal-based outlook puts Ether on target for 0.075 BTC by June, up 10% versus current price levels. Meanwhile, the pair’s upside target for April appears to be its 50-3D exponential moving average (50-3D EMA; the red wave) near 0.069 BTC.\n\nConversely, a decisive close below the 200-3D EMA (the blue wave) near 0.066 BTC, coinciding support/resistance level near 0.067 BTC, risks delaying or — in the worst case scenario — invalidating the bullish retracement setup.\n\nThis bearish argument echoes independent market analyst CrediBULL Crypto who expects strong selling pressure near the 0.067 BTC resistance level that would lead to a 50% drop in 2023.\n\nETH/BTC weekly price chart. Source: TradingView/CrediBULL Crypto\n\nEthereum vs. U.S. dollar outlook\n\nThe ETH/USD pair has rallied by more than 50% in 2023, primarily due to similar uptrends elsewhere in the crypto market.\n\nA weakening dollar, lower U.S. Treasury yields and expectations of a Federal Reserve pivot on interest rate hikes have helped cryptocurrencies rise across the board in Q1. These catalysts will likely remain in the spotlight until May’s Federal Open Market Committee meeting.\n\nShanghai bringing the first greenshoots of #AltcoinSeason ?$ETH is perking up to an 8 month high as we approach one week until the Shanghai fork update @ 10:27:35 PM UTC on the 12th (Epoch #620,9536)\n\n\n\nThe rally has mostly been a Fed/USD rates story, causing BTC to lead the way.… pic.twitter.com/dI0bpywR16 — Rich Rosenblum (@Rich_GSR) April 4, 2023\n\nAs a result, Ether could sustain its yearly gains in April, consolidating inside the $1,800–2,000 range until the Fed decision.\n\nRelated: 3 key Ethereum price metrics cast doubt on the strength of ETH’s recent rally\n\nMoreover, a decisive breakout at current levels could result in extended gains with a second-quarter ETH price target of over $3,000.\n\nETH/USD three-day price chart. Source: TradingView\n\nOn the other hand, the bears will attempt to pull the price down for a close below $1,800, with the triangle’s lower trendline near $1,600 as its downside target.\n\nThis article does not contain investment advice or recommendations. Every investment and trading move involves risk, and readers should conduct their own research when making a decision."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vd3d3LmZvb2wuY29tL2ludmVzdGluZy8yMDIzLzA0LzA4L3doYXRzLWEtYmV0dGVyLWJ1eS1iaXRjb2luLW9yLWV0aGVyZXVtL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 08 Apr 2023 07:00:00 GMT", "title": "What's a Better Buy: Bitcoin or Ethereum? - The Motley Fool", "content": "The debate between Bitcoin (BTC 0.96%) and Ethereum (ETH 1.61%) as a better investment has been a hot topic in the cryptocurrency community for years. Both are popular and established cryptocurrencies, but there are key differences between them that make Bitcoin a better investment. The reason for this belief can be boiled down to three simple reasons.\n\n1. The market cap difference\n\nMarket capitalization, or \"market cap,\" refers to the total value of a company or asset. In the case of cryptocurrencies, it is calculated by multiplying the total number of coins in circulation by the current market price of each coin. As of April 1, 2023, Bitcoin has a market cap of over $545 billion, while Ethereum's market cap is just under $220 billion.\n\nEssentially what this means is that Bitcoin is currently a more established asset than Ethereum and makes up a disproportionate amount of value in the entire crypto asset class. As of today, Bitcoin accounts for more than 45% of all the value in crypto.\n\nIts higher market cap indicates that it has more adoption and more trust among investors. Additionally, it suggests that Bitcoin is less volatile than Ethereum, as it would take a larger amount of money to move its price significantly.\n\n2. Increasing scarcity\n\nOne of the most significant differences between Bitcoin and Ethereum is their supply. Bitcoin has a hard cap of 21 million coins, which means that there will never be more than 21 million bitcoins in circulation. Currently, there are around 19.3 million circulating, with the remaining 1.7 million yet to be mined. Even better, though, these remaining 1.7 million bitcoins will be released at a diminishing rate for the next 117 years until the last bitcoin is mined.\n\nCurrently, Bitcoin's inflation rate is a minimal 1.7%. However, due to the gradual decrease in the rate of new coins being created, it is estimated that by 2056, this number will fall below 0.1%. By the year 2100, Bitcoin's inflation rate will be somewhere around 0.000001%. No matter the asset, an inflation rate this low helps to ensure that prices are not only maintained but grow as demand competes for a more limited supply.\n\nEthereum, on the other hand, has no hard cap. While it does have a mechanism known as burning to remove ether from circulation, there is, technically, no overall limit on the number of ether which could enter the market. Unlike Bitcoin, this means that Ethereum is subject to unknown levels of inflation, which can decrease the value of each individual coin over time.\n\n3. Decentralization and security\n\nFurthermore, Bitcoin has a stronger track record when it comes to security and decentralization. Bitcoin's blockchain is the most secure and decentralized of any cryptocurrency, with thousands of nodes and miners around the world helping to verify transactions and maintain the network. This makes it less susceptible to hacking or manipulation than Ethereum, which has had several high-profile security incidents in the past. Additionally, Bitcoin's decentralized nature means that it is not subject to the same level of centralization or regulation as Ethereum, which has been criticized for being too closely tied to its founders and developers.\n\nIn addition, Bitcoin has a more established and secure network than Ethereum. Bitcoin's network has been operating securely for well over a decade, and its underlying proof-of-work technology has proven to be reliable and resistant to attacks. Ethereum, on the other hand, has had some security issues in the past, including a major hack in 2016 that resulted in the loss of millions of dollars worth of ether. While Ethereum's security has improved over time, it still lags behind Bitcoin in terms of reliability and security.\n\nKeep it simple\n\nWhile Ethereum might be deserving of a spot in your portfolio, Bitcoin provides investors with a safer and more dependable option. Likely the greatest advantage Bitcoin has over Ethereum is its simplicity. Bitcoin's value proposition is clear and easy to understand: it is a highly decentralized and secure digital store of value that provides holders with reliability.\n\nEthereum, on the other hand, has a more complex value proposition that is tied to its smart contract functionality and decentralized applications. While this complexity can be appealing to some investors, it also makes Ethereum more difficult to understand and evaluate as an investment, as many of these use cases are in their beginning stages.\n\nFor investors looking to keep it simple and wanting to invest in cryptocurrency, look no further than the original digital asset, Bitcoin."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vZGFpbHljb2luLmNvbS9oZWRlcmEtaGFzaGdyYXBoLWhiYXItbm90LXRyYWRpdGlvbmFsLWJsb2NrY2hhaW4v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 08 Apr 2023 07:00:00 GMT", "title": "<PERSON><PERSON><PERSON> (HBAR): Nothing Like a Traditional Blockchain - DailyCoin", "content": "Like all blockchains in the cryptocurrency industry, Hedera Hashgraph promises fast, cheap transactions and unmatched security in DLT (Distributed Ledger Technology). It’s nothing we haven’t heard of before, but the Hedera network brings a unique twist to Web3.\n\nIt’s not technically a blockchain.\n\nAt face value, Hedera Hashgraph serves all the same purposes as traditional blockchains like Ethereum (ETH) or Cardano (ADA). You can store digital assets, use dApps and trade NFT collectibles. Despite the similarities, the Hedera Network is built differently under the hood.\n\nWhat exactly is a hashgraph? How is it different from a blockchain, and why does it enable the Hedera Network to charge a flat transaction fee of just $0.0001?\n\nWhat Is Hedera Hashgraph?\n\nHedera Hashgraph is a decentralized public ledger and cryptocurrency ecosystem. Unlike Bitcoin (BTC) and Ethereum, which are blockchains, the Hedera Network stores and transmits data on a directed acyclic graph.\n\nFor the end user, this doesn’t make much difference to how we experience the network. Hedera Hashgraph smart contracts are mostly written in Solidity, the same as Ethereum. Hedera Hashgraph is EVM-compatible, meaning if you are familiar with Ethereum, you’ll be right at home on <PERSON>der<PERSON>.\n\nHow Does Hedera Hashgraph Work?\n\nHedera Hashgraph claims its unique hashgraph consensus algorithm is faster, safer, and more secure than traditional blockchain infrastructure. This is crucial for a public network, as it means that the Hedera network should be able to support thousands of users worldwide.\n\nThe Hashgraph – A Directed Acyclic Graph\n\nThe Hashgraph network is a type of directed acyclic graph. But how is a DAG different from a blockchain? Traditional blockchains form a line of interconnected blocks, or units of data. In a blockchain, network nodes or miners reach a consensus and validate new blocks using data from the previous block.\n\nIn a DAG, nodes can reference data from any block in the chain’s lifespan, allowing them to verify information and confirm transactions faster. This is one of the main reasons behind the Hedera Network’s high throughput of transactions.\n\nThe Hashgraph takes this one step further. It actively registers the timestamp of every transaction and digital signature in real-time, as well as the last hash sent and received by each node in the network. Hedera calls this phenomenon the gossip about gossip protocol, which helps put transactions in order on the network and ensures that all nodes are ‘telling the truth’ and reaching consensus.\n\nSource: Hedera Hashgraph\n\nThe gossip about gossip protocol is powered by a Proof-of-Stake mechanism. HBAR coins are staked to the network by node operators to secure the network and validate the hashgraph consensus.\n\nThanks to the hashgraph consensus, Hedera Network achieves asynchronous byzantine fault tolerance, or ABFT. This is a fancy crypto jargon term that means nodes agree with network transactions. ABFT isn’t foolproof because it only requires 66% of nodes to agree for a consensus to be reached. Anyone controlling over 34% of the network might be able to get away with malicious behavior.\n\nHBAR – The Hedera Token\n\nHBAR is the native cryptocurrency of the Hedera hashgraph network. The use cases for HBAR are similar to other Layer-1 altcoins. HBAR is required to pay transaction fees on the mainnet and secure the Hashgraph through the PoS consensus.\n\nA Hedera crypto wallet and HBAR tokens let you explore the range of permissionless dApps, like decentralized exchanges on the hashgraph. HBAR is also the exchange currency in which Hedera non-fungible tokens, or NFTs, are priced.\n\nIt’s worth mentioning Hedera Hashgraph’s impressive network services and performance metrics. According to the team, the Hedera Network can process over 10,000 transactions per second. This significantly outperforms existing blockchain technologies like Ethereum, which struggle to clear 20 transactions per second.\n\nSource: Hedera Hashgraph\n\nWhat’s more, the hashgraph enables the network to charge a flat fee of $0.0001 for network transactions, regardless of network congestion and HBAR price.\n\nHedera Governing Council\n\nThe Hedera Governing Council is an elite group of up to 39 international companies that oversee the growth and development of the Hedera Hashgraph ecosystem. The Council was formed to ensure the Hedera network would always enjoy decentralized governance and network stability and is made up of some of the world’s largest companies, including IBM, Google, and Boeing.\n\nSource: Hedera Hashgraph\n\nIs Hedera Hashgraph Truly Decentralized?\n\nOne of the Hedera Governing Council’s unique responsibilities is to decide who is and isn’t allowed to operate a validator node and secure the hashgraph. This raises some questions about whether or not Hedera Hashgraph is truly as decentralized as they promise to be.\n\nIn contrast, anyone with sufficient ETH can launch a node on the Ethereum network. Whether or not you’re allowed to operate a network node on Hedera Hashgraph is decided by a centralized source. This is expected to change as the Hedera network finds its feet in the cryptocurrency industry.\n\nMoreover, when Hedera Hashgraph initially launched, its codebase was not open-source. This was done to prevent the chain from being forked but raised concerns about whether or not the Hedera Network was operating in the true spirit of decentralization. Developers needed a license from Hedera before building applications on the network.\n\nFortunately, the Hedera Governing Council followed through on their promise to make their infrastructure open source in 2022.\n\nHedera Hashgraph History & Founders\n\nHedera Hashgraph was launched by co-founders Leemon Baird and Mance Harmon in 2018. The Hashgraph itself is actually a product of Baird and Harmon’s software company Swirlds, which they founded together in 2015.\n\nSwirlds is a key player in the Hedera Ecosystem. The company has patented the hashgraph software and is guaranteed a place on the Hedera Governing Council for the entire lifespan of the Hedera ecosystem.\n\nPros and Cons of Hedera Hashgraph\n\nThe ins and outs of directed acyclic graphs and asynchronous byzantine fault tolerance are dense and complicated. To bring things back to earth, let’s bypass the complex jargon and clarify the simple pros and cons of the Hedera Network.\n\nPros\n\nFast – Hedera Hashgraph can process over 10,000 transactions per second, making it one of the most performant Layer-1 networks available\n\n– Hedera Hashgraph can process over 10,000 transactions per second, making it one of the most performant Layer-1 networks available Affordable – Every transaction on the Hedera network will cost the user less than a penny, regardless of network congestion and HBAR price\n\n– Every transaction on the Hedera network will cost the user less than a penny, regardless of network congestion and HBAR price Energy efficient – The Hedera Hashgraph network uses significantly less power than traditional blockchains and has implemented carbon offsetting initiatives to go fully carbon negative\n\n– The Hedera Hashgraph network uses significantly less power than traditional blockchains and has implemented carbon offsetting initiatives to go fully carbon negative Sustained growth – There have been over 1.9 million accounts created on the Hedera mainnet, with more being added every day\n\n– There have been over 1.9 million accounts created on the Hedera mainnet, with more being added every day Accessible – The Hedera network is EVM-compatible, meaning its user experience will be familiar to anyone who’s used Ethereum before. What’s more, developers can easily port over Ethereum-based dApps to the Hedera network with minimal friction.\n\nCons\n\nHedera is not as decentralized as it appears – The Hedera Governing Council still controls who is allowed to operate a node on the network. It could also be argued that Swirlds, the company that invented the hashgraph has greater control, power, and influence over the fate of network.\n\n– The Hedera Governing Council still controls who is allowed to operate a node on the network. It could also be argued that Swirlds, the company that invented the hashgraph has greater control, power, and influence over the fate of network. Undeveloped ecosystem – Despite its position as a top 50 cryptocurrency in terms of market cap, few decentralized applications are built on the Hedera network. According to DeFiLlama there are only six DeFi apps on the network.\n\nOn the Flipside\n\nFrom a technical standpoint, Hedera Hashgraph is a fascinating network with excellent growth potential. However, it will struggle to attract users until it has a more developed ecosystem.\n\nThe Hedera network is not the only cryptocurrency network that uses a directed acyclic graph. Other layer 1s like Fantom Opera also use this technology and have attracted far more users and developers.\n\nWhy You Should Care\n\nThe Web 3 world is expanding beyond simple blockchain systems. Projects like Hedera Hashgraph prove there are always creative new ways to improve network infrastructure and drive innovation in the crypto industry.\n\nFAQs"}]