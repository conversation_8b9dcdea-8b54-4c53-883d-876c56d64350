[{"id": 20, "url": "https://news.google.com/rss/articles/CBMiggFodHRwczovL3d3dy5jb2luZGVzay5jb20vd2ViMy8yMDIzLzA2LzA4L2tyYWtlbi1uZnQtbWFya2V0cGxhY2UtbGF1bmNoZXMtd2l0aC1zdXBwb3J0LWZvci1ldGhlcmV1bS1zb2xhbmEtYW5kLXBvbHlnb24tY29sbGVjdGlvbnMv0gGGAWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS93ZWIzLzIwMjMvMDYvMDgva3Jha2VuLW5mdC1tYXJrZXRwbGFjZS1sYXVuY2hlcy13aXRoLXN1cHBvcnQtZm9yLWV0aGVyZXVtLXNvbGFuYS1hbmQtcG9seWdvbi1jb2xsZWN0aW9ucy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 08 Jun 2023 07:00:00 GMT", "title": "Kraken NFT Marketplace Launches With Support for Ethereum, Solana and Polygon Collections - CoinDesk", "content": "Kraken has remained in Canada despite its crypto crackdown while competitors like Binance and OKX have announced their departures, allowing it to usurp the market and grow its customer deposits by 25% in the weeks following the exits. Kraken remains available to U.S. customers with some state-by-state trading restrictions."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMibmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDYvMDgvZXRoZXJldW0tZGV2ZWxvcGVycy1jZW1lbnQtZmluYWwtbGluZXVwLW9mLWNoYW5nZXMtaW4tZGVuY3VuLXVwZ3JhZGUv0gFyaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL3RlY2gvMjAyMy8wNi8wOC9ldGhlcmV1bS1kZXZlbG9wZXJzLWNlbWVudC1maW5hbC1saW5ldXAtb2YtY2hhbmdlcy1pbi1kZW5jdW4tdXBncmFkZS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 08 Jun 2023 07:00:00 GMT", "title": "Ethereum Developers Cement Final Lineup of Changes in ‘Dencun’ Upgrade - CoinDesk", "content": "Dencun includes simultaneous upgrades happening on the two sides of the blockchain. The “Cancun” upgrade will happen on the execution layer, where all protocol rules reside, while the consensus layer, which makes sure that blocks are validated, will go through its own fork known as “Deneb.” The name “Dencun” is a portmanteau of the names of the simultaneous upgrades."}, {"id": 19, "url": "https://news.google.com/rss/articles/CBMidWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDYvMDgvb3B0aW1pc20tc2F5cy1ldGhlci1ub3ctdHJlYXRlZC1hcy1uYXRpdmUtY3J5cHRvY3VycmVuY3ktYWxvbmdzaWRlLW9wLXRva2VuL9IBeWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDYvMDgvb3B0aW1pc20tc2F5cy1ldGhlci1ub3ctdHJlYXRlZC1hcy1uYXRpdmUtY3J5cHRvY3VycmVuY3ktYWxvbmdzaWRlLW9wLXRva2VuL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 08 Jun 2023 07:00:00 GMT", "title": "Optimism Says <PERSON><PERSON> Treated as Native Cryptocurrency Alongside OP <PERSON> - CoinDesk", "content": "“With Bedrock, we’ve added a new method of making deposits via the new Optimism Portal contract. This approach treats ETH as a native token, so that an L1 to L2 transaction can have both ETH and data associated with it,” said <PERSON><PERSON><PERSON>. “To achieve this, we needed to decouple the storage of ETH from the rest of the ERC-20 tokens in our bridge, and move it into the Optimism Portal.”"}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiOmh0dHBzOi8vdW5jaGFpbmVkY3J5cHRvLmNvbS9qYWNrLWRvcnNleS1ldGhlcmV1bS1zZWN1cml0eS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 08 Jun 2023 07:00:00 GMT", "title": "Is Ethereum a Security? Former Twitter CEO <PERSON>irs the Debate - Unchained - Unchained", "content": "Former Twitter CEO and long-time Bitcoin advocate <PERSON> sparked fresh debate in the cryptosphere this week. In response to a tweet asking him if he believed Ethereum (ETH) was a security, he tweeted, “Yes.”\n\nThe Twitter user raised the initial question in response to <PERSON>’s other tweet, where he shared a screenshot of a 2015 post from Coinbase CEO <PERSON>, which suggests Coinbase should focus on Bitcoin and referred to all altcoins as “distraction.”\n\nTesla CEO and Dogecoin Evangelist <PERSON><PERSON> also weighed in on the debate to promote his favorite memecoin Doge. Bitcoin Ordinals developer <PERSON><PERSON> lashed out against <PERSON> and called him a “clown.<PERSON>\n\n<PERSON> later accentuated his pro-Bitcoin stance by retweeting a video of <PERSON> reprimanding <PERSON>’s business model for Coinbase. The online altercation comes in the wake of the SEC’s latest lawsuit against Binance and Coinbase this week.\n\nMany experts have chimed in with their thoughts on the matter. One such comment came from <PERSON>, vice president of research at Riot Platform, on Bitcoin maximalism.\n\nIt's understandable why some might perceive #Bitcoin maximalism as cult-like due to the community's staunch conviction in Bitcoin's potential. However, this is a misconception primarily based on misunderstanding the principles that underpin Bitcoin maximalism. Bitcoin maximalism… — <PERSON> (@BitcoinPierre) June 7, 2023\n\n<PERSON><PERSON> has been a longstanding supporter of Bitcoin and made similar comments in an online discussion in 2021."}, {"id": 15, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9zdWJzb2NpYWwtY2hhdC1wcm9ncmFtLWltcGxlbWVudHMtZXRoZXJldW0tdXNlcm5hbWVzLXBvbHlnb24tZG9uYXRpb25z0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 08 Jun 2023 07:00:00 GMT", "title": "Subsocial chat program implements Ethereum usernames, Polygon donations - Cointelegraph", "content": "Grill.chat, a chat app based on the Subsocial network, has now implemented Ethereum Virtual Machine (EVM) wallet compatibility, allowing users to chat using their Ethereum identities and send crypto to each other via Polygon, according to a June 7 announcement.\n\nSubsocial is a Polkadot parachain designed for social media applications.\n\nGrill.chat allows users to participate in over 70 chat rooms focused on mostly crypto-related topics. The development team seeks to attract new Web3 projects to build their communities on the platform.\n\nPolkadot Decoded chat room on Grill.chat. Source: Grill.chat\n\nThe integration means that users can now connect their Subsocial wallets to their EVM wallets by signing a transaction verifying that they are the wallet’s owner. This prevents them from needing to own SUB tokens to donate to other users, and it allows them to prove their Ethereum identities to users in chat rooms. In the future, the developers plan to use this integration to allow nonfungible token (NFT) collections to be shown to other users.\n\nIn a conversation with Cointelegraph, Subsocial content lead <PERSON> stated that Grill.chat is targeting crypto projects as potential sponsors of chat rooms. Currently, many crypto communities are built around Discord servers and Telegram channels. These channels cannot be integrated into the developer’s website, which means that users need to load a separate program to join the community or interact with it, creating what <PERSON> sees as unneeded friction for users.\n\nBy contrast, chat groups on Grill.chat can be integrated into a development team’s website or application interface. <PERSON> pointed to the example of Zeitgeist, a blockchain-based predictions market app. Its interface features a “chat” icon that opens directly into a Zeitgeist chat room on Grill.chat.\n\nZeitgeist user interface with Grill.chat window open. Source: Zeitgeist\n\nHowever, the app’s users previously encountered friction from another source, Edwards said. Many of the biggest crypto projects are on EVM-based networks such as Ethereum, Polygon and Avalanche. Crypto users are accustomed to using wallets from these networks and don’t necessarily want to download a new wallet to use a chat app.\n\nRelated: Decentralized social media: The next big thing in crypto?\n\nTo partially solve this problem, the team implemented a feature that lets users create a Subsocial wallet directly from the app’s interface. They also paid for users’ gas fees via a system that can delegate signing privileges for a limited number of functions.\n\nBut this still didn’t completely solve the problem, as it siloed the user’s Ethereum identity from their Grill.chat posts. The team implemented EVM compatibility to make it easier for Ethereum users to switch to the app, Edwards explained.\n\n“The cool thing is it's sort of using this off-chain signer technology because, at the end of the day, it’s all running on Substrate [Polkadot parachain technology], and EVM wallets aren’t compatible with Substrate,” Edwards stated. “But we can still connect those two accounts together so you can chat on the substrate chain, but your EVM account is linked, so you can have identity, donations, NFTs, all of this sort of stuff.”\n\nGrill.chat isn’t the only social media app trying to entice crypto projects into building communities on it. OpenChat is a chat app on the Internet Computer network. Its team told Cointelegraph it’s working on a similar feature to show OpenChat chat rooms on a project’s website.\n\nWeb3 companies are racing to create a blockchain-based social media app that will see mass adoption. On April 26, the Polygon-based Lens network announced a new scaling solution that it said would allow for “instant posts.” The same day, MeWe said it would move its 20 million users to the Polkadot parachain network Frequency."}]