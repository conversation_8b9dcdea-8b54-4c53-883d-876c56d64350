[{"id": 5, "url": "https://news.google.com/rss/articles/CBMiQmh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2N1cnZlLWRlcGxveXMtc3RhYmxlY29pbi1zbWFydC1jb250cmFjdNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 04 May 2023 07:00:00 GMT", "title": "Curve Deploys Stablecoin Smart Contract on Ethereum - Blockworks", "content": "Curve Finance has deployed the smart contract for its stablecoin crvUSD on Ethereum.\n\nThe project designed the stablecoin using the Lending-Liquidating Automated Market Maker Algorithm (LLAMMA), which is a combination of both a traditional AMM and a lending market.\n\nAccording to a white paper from last October, LLAMMA uses an external oracle that ensures that crvUSD pools are made up of two different assets: crvUSD and the asset used to mint the stablecoin (such as ETH or BTC). This differs from other AMM designs, where assets are swapped at the price determined by the AMM curve.\n\nWhen the price of the collateral is higher, all of the user’s deposits will be in the deposited collateral, but as the price of the collateral goes lower, the oracle will convert the deposits to crvUSD.\n\nCurve deployed its initial crvUSD smart contracts yesterday, but a minor issue where fees were not flowing to the project’s DAO was noted by its team, Blockworks Research Senior Analyst <PERSON> said.\n\nThis protocol resolved this minor issue after Curve Finance redeployed an updated smart contract this morning.\n\nEven though information on Etherscan shows that Curve minted an initial 20 million crvUSD, <PERSON> notes that this was part of the deployment process and the tokens themselves did not leave the system.\n\n“crvUSD only becomes circulating after it has been borrowed by an end user,” he said.\n\nAt the time of writing, <PERSON>, the founder and CEO of Curve Finance, has the only open loan of $1.05 million.\n\nThe final step, <PERSON> noted, will be to push the user interface onto the Curve website.\n\n“With two major releases during the depths of the bear market (crvUSD and upgraded tri crypto contracts), Curve is well positioned for success when liquidity and volume returns,” Smith said.\n\n“Curve now controls multiple parts of [the] DeFi stack with a DEX, lending market, and stablecoin.”\n\nTo learn more about Curve Finance and its Q1 performance, check out this detailed report by Blockworks Research.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiMGh0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvaG93LXRvLXVud3JhcC1ldGhlcmV1bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 04 May 2023 07:00:00 GMT", "title": "How to Unwrap Ethereum? - Watcher <PERSON>", "content": "The Ethereum network has made the switch from proof-of-work mining to proof-of-stake mining. The transition was completed successfully on September 15, 2022. Additionally, the blockchain went through some major changes as the miners were replaced by validators.\n\nRecently, the much-awaited Shapella upgrade went live, allowing these stakers to withdraw their staked ETH. However, ETH is the second-largest cryptocurrency and was created way back before the emergence of dApps.\n\nAlso read: How To Change Slippage Tolerance on PancakeSwap?\n\nThen the real question emerges as to what it is and why it exists. Wrapped Ether was created to give Ethereum-based decentralized applications (dApps) a more standardized approach to interface with Ether, the network’s native coin.\n\nWhat is Wrapped Ethereum (wETH)?\n\nwETH is just Ether wrapped in a smart contract to provide it with a standardized ERC-20 interface, allowing it to be exchanged and utilized within decentralized exchanges (DEXs) and other Ethereum-based services.\n\nGiven that Ether lacked the most widely used standard for tokens, the ERC-20 interface, it was necessary to create wETH. The process of wrapping ether includes moving the ETH to a smart contract. wETH will then be generated in return for this process. Additionally, this is to ensure that there is a reserve backing every wETH.\n\nAlso read: Is MetaMask Safe (2023)?\n\nBefore we look into how to unwrap Ethereum, let us look at how we can wrap it in just a few simple steps.\n\nHow to wrap Ether?\n\nThere are different ways to wrap Ethereum. One method involves the swapping of ETH for wETH through a cryptocurrency exchange. Another common method is the use of a smart contract. Let us look at the step-by-step guide on how to wrap ETH using OpenSea and MetaMask.\n\nGo to OpenSea and connect your MetaMask wallet.\n\nClick on the “Wallet” option. You will see three dots in the top-right corner. Click on the three dots next to Ethereum.\n\nClick on “Wrap” from the drop-down menu.\n\nEnter the amount and click on “Wrap ETH.”\n\nOnce it is done, confirm the MetaMask pop-up for the transaction to be complete.\n\nThat’s it. You will receive wETH which will shine brightly in pink.\n\nNow that we have covered how to wrap Ether, let us look at how to unwrap Ether using MetaMask and OpenSea.\n\nAlso read: How to Buy Crypto with Western Union\n\nHow to unwrap Ethereum?\n\nUnwrapping Ethereum is also a straightforward process. It works similarly to how you convert ETH to wETH. Let us look at the steps below."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1ldGgtZGVjb2RpbmctdGhlLXBvc3NpYmlsaXR5LW9mLWEtcHJpY2UtY29ycmVjdGlvbi1hcy1mYWlsZWQtdHJhbnNhY3Rpb25zLXN1cmdlL9IBc2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1ldGgtZGVjb2RpbmctdGhlLXBvc3NpYmlsaXR5LW9mLWEtcHJpY2UtY29ycmVjdGlvbi1hcy1mYWlsZWQtdHJhbnNhY3Rpb25zLXN1cmdlL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 04 May 2023 07:00:00 GMT", "title": "Ethereum [ETH]: Decoding the possibility of a price correction as failed transactions surge - AMBCrypto News", "content": "The total number of failed Ethereum transactions exceeded 500,000.\n\nETH’s price craters as network activity experienced a dip.\n\nAccording to data from CryptoQuant, there was a significant surge in the total number of failed Ethereum[ETH] transactions, reaching over 500,000 as of 2 May.\n\nHow much are 1,10,100 ETHs worth today?\n\nTaking a cue from ETH’s historical performance, pseudonymous CryptoQuant Analyst MACD, found an interesting fact. Whenever the total count of failed ETH transactions exceeded 200,000, it indicated “market overheating” and was often followed by price corrections.\n\n“In the past, when Ethereum’s transaction failures rise above 200,000, it shows a market overheating and has often seen a price correction.”\n\nTo brace for a price correction or not?\n\nIn the last month, ETH’s price oscillated between $2100 and $1800. At press time, the leading altcoin exchanged hands at $1,869, with a 2% price rally within the past 24 hours, data from CoinMarketCap showed.\n\nOn-chain assessment of ETH’s performance revealed a decline in network activity since 14 April. According to Santiment, the count of daily active addresses that have since traded the alt fell by 41%.\n\nAdditionally, the number of new addresses created daily on the ETH network has plummeted as well. Information from the on-chain data provider revealed the creation of 12,492 new addresses on the ETH network on 1 May. This represented an 86% drop from the 91,560 new addresses created on 14 April.\n\nWith the continued decline in the count of new and active addresses trading ETH, the coin’s value fell from $2100 on 14 April to $1,869 at press time.\n\nIn addition to a fall in its network activity, ETH’s funding rates fell to a one-month low of -0.013 on 3 May. When a crypto asset records negative funding rates, it means that most traders in the market are taking short positions or betting against the asset.\n\nAs a result, it is often taken as an indicator of bearish sentiment and sometimes precipitates a decline in an asset’s value.\n\nFurthermore, as ETH’s price fell since mid-April, holders of 1000 to 100,000 ETH coins gradually distributed their ETH holdings, contributing to the decline in value, per data from Santiment.\n\nIs your portfolio green? Check the Ethereum Profit Calculator\n\nInterestingly, bigger whales, that hold between 100,000 to 10,000,000 ETH coins, increased their holdings during the same period. However, with bearish sentiments still lingering in the market, this has failed to result in any significant price rally.\n\nAt press time, ETH’s Relative Strength Index (RSI) and Money Flow Index (MFI) were positioned beneath their respective neutral spots, in downtrend positions. This indicated waning buying pressure and highlighted the need for new demand to drive up the alt’s value."}, {"id": 30, "url": "https://news.google.com/rss/articles/CBMitwFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtNC1tYXktMjAyMy9hcnRpY2xlc2hvdy85OTk4MTQ1OS5jbXPSAbsBaHR0cHM6Ly9tLmVjb25vbWljdGltZXMuY29tL21hcmtldHMvY3J5cHRvY3VycmVuY3kvY3J5cHRvLXByaWNlcy10b2RheS1saXZlLW5ld3MtYml0Y29pbi1kb2dlY29pbi1ldGhlcmV1bS1zaGliaGEtaW51LWNyeXB0b2N1cnJlbmN5LWxhdGVzdC11cGRhdGVzLTQtbWF5LTIwMjMvYW1wX2FydGljbGVzaG93Lzk5OTgxNDU5LmNtcw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 04 May 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin rises above $29k; Ethereum, Polygon surge up to 3% - The Economic Times", "content": "\n\n\n\n\n\n\n\nThe cryptocurrency markets were trading higher in Thursday's trade after the US Fed raised its benchmark lending rate by 25 bps again to cool inflation. Bitcoin (BTC) rose 2.44% to $29,152, whereas Ethereum (ETH) was above the $1,900 level. BTC volume in the last 24 hours stood at approximately $19.5 billion, rising 20.54% in the last 24 hours.\"Following the Federal Reserve's decision to raise interest rates by 25 basis points, Bitcoin is regaining its strength. The Fed indicated that this week's increase could be the last for the time being. At present, Bitcoin is being traded at the US$29,000 level and is gradually approaching its resistance levels of US$29,420 and US$30,000,\" <PERSON><PERSON>, Co-founder and CEO at Mudrex, said.“As the upcoming US employment figures are scheduled to be released on Friday, investors and traders are keeping a watchful eye on them,” <PERSON><PERSON> added.The global cryptocurrency market cap was trading higher around $1.20 trillion, rising 2% in the last 24 hours.The total volume in DeFi is currently 3,45 billion, 8.04% of the total crypto market 24-hour volume. The volume of all stablecoins was at $38.99 billion, which is 90.76% of the total crypto market 24-hour volume.The market cap of Bitcoin, the world's largest cryptocurrency, was around $563 billion. Bitcoin’s dominance is currently 47.13%, an increase of 0.21% over the day, according to CoinMarketCap.\"Bitcoin (BTC) rallied 2% overnight as the US Fed indicated that it may pause interest rate hikes in the future. Given concerns about the US banking crisis, investors are likely to move capital towards Gold and Bitcoin in the short term. BTC has to reclaim $30,000 to strengthen its 2023 rally,\" Vikram Subburaj, CEO of Giottus Crypto Platform, said.Sathvik Vishwanath, Co-Founder & CEO of Unocoin said that Bitcoin bounced back from the $28,100 level but faces strong resistance at the $29,295 level. “A break above this level could lead to a target of $29,975 and eventually $30,000,” he said.\"However, the candlestick patterns indicate bearish sentiment, while the RSI and MACD indicators indicate a possible downtrend. The 50-day EMA also acted as a support area around $28,800. Immediate support lies near $27,600 and a break below this level could lead to further declines towards $27,200,\" Sathvik added.Crypto Cart: Quick Glance (Source: coinmarketcap.com , data as of 11.57 hours, IST on May 04, 2023)Bitcoin $29,157 2.45%Ethereum $1,901 2.14%Tether $1.00 0.01% BNB $326 1.12% XRP $0.4628 0.72%Cardano $0.3946 2.12% Dogecoin $0.0794 1.26%Polygon $0.9976 2.80%Solana $22.23 1.74%Polkadot $5.76 1.46%Shiba Inu $0.00001006 1.08% Litecoin $88.47 0.49%Tron $0.06934 0.28%(Note: Price change in last 24 hours)(Disclaimer: Recommendations, suggestions, views and opinions given by the experts are their own. These do not represent the views of The Economic Times)"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbWFnYXppbmUvam9lLWx1YmluLXRoZS10cnV0aC1hYm91dC1ldGgtZm91bmRlcnMtc3BsaXQtYW5kLWNyeXB0by1nb29nbGUv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 04 May 2023 07:00:00 GMT", "title": "<PERSON>: The truth about ETH founders split and 'Crypto Google' - Cointelegraph", "content": "\n\nThere’s a narrative that’s grown up around Ethereum’s two most important co-founders, <PERSON> and <PERSON><PERSON>, to explain how they went in different directions almost a decade ago.\n\n\n\nIt suggests the pair fell out over the blockchain’s future direction, with the idealistic 20-year-old <PERSON><PERSON><PERSON> determined to turn Ethereum into a nonprofit foundation, while <PERSON><PERSON> and others wanted to commercialize the technology via a for-profit company.\n\n“That wasn’t really what happened,” the billionaire founder of Ethereum infrastructure and software firm ConsenSys tells Magazine during an in-depth interview in Tel Aviv.\n\n“What happened was people were looking for a way to explain why these two people were bumped out of the project. And that was a convenient way to label it. But that wasn’t the reason they were moved.”\n\n<PERSON><PERSON>’s referring to Ethereum’s infamous “Red Wedding” in 2014 when the eight co-founders and the team gathered to incorporate Ethereum as a company.\n\nFormer Ethereum CEO <PERSON> (right) with creator <PERSON><PERSON> (left) from back in the day. (Flickr)\n\nThe meeting descended into bickering and infighting over internal politics that saw a devastated CEO <PERSON> pushed out of the team, along with underperforming co-founder <PERSON>.\n\n“I think it’s true that I and several people on the team — like maybe everybody else — believed that you need to draw businesses in, you needed economic, commercial validation in order to build better things, even open-source software,” the 58-year-old says in his slow, measured tones.\n\n\n\n“But that wasn’t the root of why I started ConsenSys or why two people were bumped off the project.”\n\nRed Wedding and Crypto Google\n\nAs documented in Camilla Russo’s history of Ethereum, The Infinite Machine, the co-founders had gathered in Zug, Switzerland on June 7, 2014, to sign a document transforming Ethereum into a for-profit company. But instead of signing the contract, tensions boiled over Hoskinson’s management style and personality, Chetrit’s contribution to the project, Ethereum’s future direction and other internal political issues.\n\n\n\n\n\nAfter much back and forth, the decisions were all left to the gangly 20-year-old math genius who’d created the project in the first place. After some time alone on the terrace, he returned to say Hoskinson and Chetrit were out, and Ethereum would become a nonprofit foundation instead of a company.\n\n\n\n“Vitalik wrote an amazing white paper — it was right place, right time, incredible vision — and it attracted lots of people of disparate backgrounds, and we worked together well for chunks of time,” Lubin says by way of context.\n\nJoe Lubin in conversation with Magazine in Tel Aviv.\n\n“We had differences of opinion, at times, those differences of opinion boiled over famously… infamously. And there was a moment where two people were bumped out of leadership, and up to that point, we were having discussions about whether we were going to be purely nonprofit, or whether we were going to pursue a nonprofit track, put it under a foundation, and then the same group of people who worked so nicely together would build Crypto Google together.\n\n“And it became apparent to all of us that we probably weren’t going to build Crypto Google. But it was also clear to all of us that nobody was even close to being able to build Crypto Google and that we’re just building the foundation and the platform for a long time.”\n\nLubin was already planning his own for-profit company to build out Ethereum’s application layer when the decision was made, and it spun into life not long afterward.\n\nWhile other co-founders, such as Gavin Wood (Polkadot), contributed more to the early protocol itself, arguably none of them, apart from Buterin, has since contributed as much as Lubin to what Ethereum is today. While ConsenSys didn’t turn into Crypto Google, its infrastructure and apps are as important to Ethereum now as Google is to the web.\n\n“ConsenSys wasn’t formed to commercialize it. It was formed to continue the vision and the mission of the Ethereum platform,” Lubin explains.\n\n\n\nRelated: The Vitalik I know — Dmitry Buterin\n\nWho is Joe Lubin?\n\nBorn in Toronto in 1964, Lubin studied electrical engineering and computer science at Princeton in the mid-1980s, where his roommate was another future crypto billionaire, Mike Novogratz of Galaxy Digital. Amazon founder Jeff Bezos was in the same faculty, though Lubin tells Magazine they never met.\n\nJoe Lubin was almost 50 before he jumped on board the Ethereum train and made his first billion.\n\nLubin has had a surprisingly diverse career, working in AI, robotics and autonomous music creation for a number of different employers. He founded a hedge fund and was the vice president of private wealth management at Goldman Sachs, but nothing world-changing, according to Novogratz, as quoted in the Financial Times in 2021.\n\n“Joe was one of the brightest among us, a forward thinker, but by 45 hadn’t done anything to stand out,” Novogratz recalled. “I don’t think any of our gang would have guessed how things would turn out.”\n\n\n\nRelated: Here’s how Ethereum’s ZK-rollups can become interoperable\n\nThe combination of his near-front-row seat to the September 11 attacks on the World Trade Center and then the global financial crisis shook him to his core. He said at the ConsenSys Ethereal Summit in May 2017 that the events had made him feel as though “we were living in a global society and economy that was figuratively, literally and morally bankrupt.”\n\n\n\nHe believed a slow, cascading financial collapse was taking place, which made him receptive to the ideas in the Bitcoin white paper, which he read in 2011. The following year he moved to Jamaica with his girlfriend, who was trying to forge a career in dancehall music, and he became a music producer while investing in Bitcoin and waiting for the collapse.\n\nDuring a trip home to Toronto in late 2013, he attended a Bitcoin meetup alongside another co-founder, Anthony Di Iorio, and encountered a kid named Vitalik Buterin, who was touting his just-written white paper for an improved version of Bitcoin called Ethereum. Lubin was “blown away,” and he became an official co-founder in early 2014.\n\nThe core early Ethereum team at the house rented for Bitcoin Miami 2014, including Joe Lubin in the back row, second from right. (yanislav.medium.com)\n\n\n\nApproaching 50, he was an odd fit with a bunch of anti-establishment 20-something-year-old coders, but his Jamaican music production background gave him just enough cachet with the team to get by. And, of course, Lubin and Di Iorio personally bankrolled around $500,000 to $800,000 of the funding necessary to get Ethereum off the ground.\n\nLubin’s experience also helped the team avoid potential pitfalls and roadblocks, and he insisted on early meetings with the United States Securities and Exchange Commission and hiring high-priced lawyers to minimize the extraordinary legal risks.\n\nConsenSys arises!\n\nConsenSys was founded in Switzerland in October 2014 for legal reasons, which subsequently led to a nasty ongoing court battle between employees and shareholders who claim they weren’t properly compensated when the assets were transferred to an American entity.\n\nRelated: ‘Account abstraction’ supercharges Ethereum wallets: Dummies guide\n\nBut it actually operated from a graffiti-covered warehouse in Bushwick, Brooklyn. The aim was to build out applications and infrastructure for Ethereum by investing in startups, incubating projects and consulting with firms like JPMorgan and BHP Billiton on how to incorporate this new technology. It spawned more than 50 businesses early on, including a poker site, a prediction market and a healthcare records firm. But by all accounts, its early years were pretty slapdash, with no real corporate structure.\n\nMetaMask co-founder Dan Finlay spoke about the early days on the Epicenter podcast.\n\n\n\n“ConsenSys was this wonderful, just kind of chaotic incubator at the early stages. I don’t know, there must have been hundreds of different experiments getting validated and tried out there. And there was a really exciting energy,” he says, adding that a lot of projects got built before Ethereum could support them:\n\n“Back then, it was very normal to just kind of build your application as if the blockchain was going to scale or did scale already.”\n\nIn 2018, a Forbes investigation suggested that pretty much all of ConsenSys’ projects were in the red, and the company was burning $100 million a year on non-profitable projects, including an asteroid mining company.\n\nForbes took aim at ConsenSys in a 2018 investigation. (Forbes)\n\nNot long after, Lubin axed a bunch of underperforming projects, culled the 1,200-strong headcount and reset the company into ConsenSys 2.0 with a much more corporate and accountable culture.\n\nDespite being worth $7 billion after its most recent $450-million fundraising round in 2022, ConsenSys let go of another 11% of its staff in January of this year. Lubin tells Magazine it was readying itself to survive bad conditions as “macroeconomic and geopolitical” storm clouds gathered.\n\n“We wanted to ensure that we had significant runway so that we can stay strong and build,” he says, revealing it was eyeing a number of acquisitions that “if we’re able to bring some on board that will add really valuable pieces.”\n\nI am excited to share news of our Series D fundraise. https://t.co/SZP36mgBfl — Joseph Lubin (@ethereumJoseph) March 15, 2022\n\nCentralization vs. decentralization\n\nAnyone who’s listened to Lubin speak will know that he’s genuinely committed to, and a proponent of, the benefits of decentralization.\n\n\n\nSo, is there tension between running a centralized company like ConsenSys that provides the crucial infrastructure to a decentralized blockchain?\n\n“I don’t think there’s a tension,” he says.\n\n“It’s all about progressive decentralization. There’s nothing wrong with having an entity that is organized in one way that is trying to build something that is organized in a different way.”\n\nLubin explains that the products ConsenSys is building need to achieve “product-market fit; otherwise, they’re kind of useless, and so bringing something forth, wholly and perfectly decentralized, is very difficult — it may be impossible.”\n\nConsenSys plays a big role in the Ethereum ecosystem.\n\nConsenSys’ most crucial infrastructure is called Infura, which offers Ethereum nodes as a service, making it easier for developers and users to connect to the network. It’s basically an intermediary service between decentralized apps (DApps) and the blockchain that projects rely on to stay up and running.\n\nInfura probably works a little too well, as much of the Ethereum ecosystem is dependent on it. That means if Infura goes down, so too do half the network’s projects, including Uniswap, Compound, MetaMask and Aave.\n\nIt’s also a weak point for censorship and was criticized by some for complying with the Tornado Cash sanctions.\n\nDecentralizing Infura\n\nConsenSys has been working on a plan for some time now to “decentralize Infura.” This will take the form of a marketplace of competing infrastructure providers that offer similar services, of which Infura itself would be one.\n\nLubin believes it’s “extremely important” to make this happen.\n\n“I’ve been a proponent of decentralizing Infura since the start but more actively since five years ago,” he says.\n\n“What we’ve run into is that our ecosystem keeps having these wicked growth spurts,” he continues, adding, “It was a sub-priority to keep things going rather than to start a parallel project to parallelize and decentralize — and that’s going pretty well right now.”\n\nThe protocol will either be called XFura or the Decentralized Infura Network Protocol.\n\n“The idea is that we believe now that we can take a high-performance product and federate the protocol, initially do a lot of hand-holding with other providers and then we situate Infura on the protocol,” he says.\n\n“It’s pretty close. There are a bunch of very sophisticated partners that are working closely with EG [Galano], the lead of the project. I can’t give you a date.”\n\nAlthough Infura researcher Patrick McCorry went out of his way in an interview with Cointelegraph to say censorship resistance was not the point of decentralizing Infura, that’s certainly one of the benefits.\n\n\n\nA decentralized network would enable DApps to pick and choose providers, allowing them potentially to get around censored protocols or addresses like Tornado Cash.\n\n“I like the idea that there’s optionality,” says Lubin, carefully noting that different providers would operate in different nation-states and jurisdictions.\n\n“I think that works well if there’s a lot of them and if there’s real choice, so you can always go to an uncensored service and be sure that they’ve got enough validating power so that you’re gonna get your transaction processed fairly rapidly.”\n\nHowever, he adds it’s equally possible that future aspects of the protocol are obfuscated so that no one actually knows what’s in a packet or a transaction. He says he knows of people already “working on protocol enhancements” who will make this happen, and the explosion of layer 2s and layer 3s makes it even more likely.\n\n“If they’re already glommed in and impossible to read, then it’s hard to imagine that regulators will either care that much or have the ability to do anything,” he says.\n\n\n\n“I’m sure [there is] lots of criminal activity that flows through AWS and Azure and every mail server everywhere. So, there’s a level of infrastructure that you just can’t halt because it’s doing mostly useful activity.”\n\nDecentralizing MetaMask\n\nThe other core bit of infrastructure provided by ConsenSys that underpins the entire Ethereum ecosystem is its ubiquitous browser wallet MetaMask. It’s also being sort of decentralized by crowdsourcing the development of new features and the addition of new blockchains.\n\n\n\nCalled MetaMask Snaps, it’ll turn the browser wallet into a permissionless platform for others to build on — one proof-of-concept Snap enables MetaMask to act as a Bitcoin wallet.\n\nJoe Lubin has arguably made a bigger contribution to Ethereum than any other co-founder apart from Vitalik Buterin.\n\n“The MetaMask Grants DAO [decentralized autonomous organization] will get increasingly decentralized and will incentivize people to build cool things, to start companies that permissionlessly innovate that we have nothing to do with,” says Lubin.\n\nHe explains that over the years, MetaMask was approached by numerous blockchains looking for support, but after they’d crunched the numbers, there wasn’t enough activity to justify splitting its focus from Ethereum. Snaps, though, will open the doors to everyone.\n\nCrypto regulations\n\nLubin is unconcerned about the possibility of Ethereum being declared a security, saying, “It’s as likely and would have the same impact as if Uber was made illegal.”\n\n\n\n“There would be tremendous outcry from not just the crypto community but different politicians, certain regulators.”\n\nThere’s a sense of frustration from Lubin that this ground even needs to be covered again, saying that ConsenSys has been through all of this in discussions with the SEC and Commodity Futures Trading Commission over many years.\n\n“We went in there on a voluntary basis five years ago or something like that, when they’re just trying to wrap their heads around what tokens were,” he says.\n\n\n\n“They thought back then that everything was a security; we think [we] helped them significantly understand that lots of tokens are not securities and then they went away, and Gary and his team now think almost everything’s a security.”\n\nBut he believes that the renewed focus on regulations in the wake of the FTX and stablecoin collapses will ultimately be a good thing.\n\n“We now have the world’s attention, and smart people who care will prevail because it just makes sense,” he says.\n\n\n\n“And sure, there will be people with agendas who don’t want to see it that way. Maybe the banking lobby will help them not see it that way. But in terms of finally paying a lot of attention to trying to regulate an important space, I do believe that clear heads will think through this and that people will start to understand the benefits of decentralization and make good regulation for CeFi [centralized finance] and no regulation for tech, crypto.”\n\nCrisis equals opportunity\n\nIn fact, Lubin is remarkably philosophical and sanguine about all the regulatory, game theory and technological challenges facing Ethereum. For example, he concedes centralization of staking on platforms like Lido could become a concern, but because progressive decentralization is baked into the nature of the ecosystem, it won’t be a problem for long.\n\n“Things don’t start very decentralized,” he says. “These are still pretty new innovations, and our ecosystem is pretty exacting. If you want to be in the Ethereum ecosystem proper, then you’re not going to want to try to dominate something, you’re not going to want to operate centralized for very long. The ecosystem will identify that as problematic and come up with solutions for it, which is great.”\n\nIn Lubin’s world view, problems are just short-term issues you deal with as part of the process of making the project better.\n\n“I see things as processes. I hope we run into lots of complications in the near term, and all the way through, because every complication just points out how we can build a more robust platform and a more decentralized platform. Yes, hopefully, we’ll run into lots of difficult problems.”\n\n“Lots of smart people have good solutions that are being built.”\n\n\n\nAlso read: Ethereum is eating the world — ‘You only need one internet’\n\nThe future of Ethereum\n\nThe big question is, where does he see Ethereum heading? Does he believe the world’s entire financial system could end up running on Ethereum using ZK-Rollups?\n\nLubin says the founding conception of Ethereum was that it would become a “world computer,” and he suggests that was still in the cards.\n\n“I think several of us thought early on that we were building the Star Trek computer essentially,” he says, explaining it handled pretty much anything and everything.\n\n“And so, I think that decentralized protocols will be the underlying trust foundation for lots of heterogeneous architectures. So, it’s possible that Ethereum will scale sufficiently so that we can have one trust foundation and then build lots of layer 2s and layer 3s and up.”\n\n“There have been many computer revolutions for the last 200 and something years and this is another one.”\n\n“So, the answer’s yes. And the answer will take time to unfold. It would be impossible to rearchitect the global economy or global financial system in a short period of time.”"}]