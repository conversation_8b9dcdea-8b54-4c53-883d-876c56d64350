[{"id": 6, "url": "https://news.google.com/rss/articles/CBMiY2h0dHBzOi8vY3J5cHRvc2xhdGUuY29tL2V0aGVyZXVtLWhvbGVza3ktdGVzdG5ldC1sYXVuY2gtZmxvcHMtcmVsYXVuY2gtaXMtZXhwZWN0ZWQtaW4tY29taW5nLXdlZWtzL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 17 Sep 2023 07:00:00 GMT", "title": "Ethereum Holesky testnet launch flops, relaunch is expected in coming weeks - CryptoSlate", "content": "Ethereum’s Holesky testnet experienced an error that will require another launch attempt, community members and developers said on Sept. 15.\n\nEthereum researcher <PERSON><PERSON><PERSON>, aka <PERSON><PERSON><PERSON><PERSON><PERSON>, wrote that one developer entered certain data in the wrong part of the network’s genesis files. He said:\n\n“Someone put 0x686f77206d7563682069732074686520666973683f (“how much is the fish?”) as extra-data in the EL Holesky genesis.json, and not in the CL genesis.ssz.”\n\nThis resulted in a misconfiguration that caused the testnet to fail to launch, according to <PERSON><PERSON><PERSON>. He added that other fork parameters are “rumored to mismatch” but did not provide further details of those issues.\n\n<PERSON><PERSON><PERSON> added that <PERSON><PERSON> did not experience issues related to network size, something that was considered before launch. In August, developers determined that Holesky could handle 1.4 million validators. In response, they introduced a large amount of Holesky ETH, amounting to 1.6 billion tokens.\n\nRelaunch expected in coming weeks\n\nThough developers will soon relaunch Holesky, the exact date of that event is still unclear. Nethermind, an Ethereum research and engineering group, said developers will relaunch <PERSON>sky in one week. That seemingly places the next attempt on Sept. 22.\n\nMeanwhile, Ethereum contributor and Sigma Prime member <PERSON> has suggested in a GitHub pull request that <PERSON><PERSON> should be relaunched on Sept. 28. That date is nearly two weeks later than the network’s first launch attempt.\n\nRegardless of delays, the issue does not affect Ethereum’s mainnet, which is responsible for transactions with real value. Beaconchain.eth acknowledged this, writing that the Holesky launch “didn’t go as expected, but that’s what testnets are here for.”\n\nAccording to Ethereum documentation, Holesky will supersede Ethereum’s Goerli testnet. Both testnets are intended for testing related to staking, validating, and core network upgrades. Another testnet called Sepolia, which is aimed at independent developers who want to test their own Ethereum-based apps, also exists.\n\nEach of these testnets relies on special testnet ETH that is freely available through faucets and which generally has no real market value."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vY3J5cHRvLm5ld3MvZXRoZXJldW0tdW52ZWlscy1uZXctdGVzdG5ldC1jYWxsZWQtaG9sZXNreS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 17 Sep 2023 07:00:00 GMT", "title": "Ethereum unveils new testnet called '<PERSON><PERSON>ky' - crypto.news", "content": "Ethereum’s developers launched a new testnet that’s being touted as a promising alternative to the aging Goerli network.\n\nDubbed “<PERSON><PERSON><PERSON>,” the testnet was designed to facilitate staking, infrastructure, and protocol development.\n\nEthereum preps for future\n\nEthereum, the blockchain powering the world’s second-largest crypto by market cap, is continually evolving to meet the demands of a rapidly growing blockchain ecosystem. To keep pace with the ambitious roadmap, Ethereum developers have introduced a new testnet called “Holešky.”\n\nAt the core of the Holešky testnet launch is its unprecedented initial supply of 1.6 billion Holešky testnet Ether (HETH). This substantial allocation is provided to validators on Holešky’s inaugural day to kick start network operations.\n\nIn contrast, it’s worth noting that this token amount is ten times more than what the main Ethereum network currently holds. Ethereum developer <PERSON> justified this allocation, stating that “devnets [are] regularly using 10B supply.” This move ensures that <PERSON>šky has ample resources to support the development and testing of new features.\n\nYou might also like: Ethereum developers on Goerli now have to pay for test ETH\n\nRetiring the Goerli testnet\n\nBefore <PERSON><PERSON><PERSON> emerged, the Goerli testnet was Ethereum’s primary platform for testing new staking, infrastructure, and protocol developments.\n\nConcerns began to surface in October when developers voiced their reservations about Goerli’s insufficient supply of Goerli ETH to meet testing requirements. Holešky’s generous HETH supply is expected to address this issue effectively.\n\nIn tandem with Holešky’s launch, Ethereum developers have been encouraging application developers to migrate from Goerli to Sepolia, a move that leaves only protocol developers on the old network. The Ethereum team has set its sights on deprecating Goerli in January 2024. Following deprecation, Goerli will receive another year of maintenance before being permanently shut down, as outlined in official documentation.\n\nThe advent of Holešky marks a critical juncture in Ethereum’s journey. The platform is poised to implement a slew of transformative features, including proto-danksharding, danksharding, and Verkle trees.\n\nThese innovations reduce transaction fees and make running a node more cost-effective. To ensure seamless integration into the mainnet, each feature will undergo rigorous testing on Holešky, underscoring the testnet’s pivotal role in Ethereum’s development pipeline.\n\nRelentless development\n\nEthereum’s commitment to innovation remains unwavering. Recent upgrades, including the Shanghai upgrade, have already introduced substantial scalability, security, and functionality improvements. Ethereum researcher Christine Kim affirms that Ethereum’s development community is far from complacent and is already plotting the next wave of enhancements. The blockchain’s execution layer (EL) and consensus layer (CL) are the focal points of these upcoming code changes.\n\nWhile Holešky gears up for pivotal infrastructure and protocol testing, Ethereum has also made strides in simplifying the process of launching tokens on its blockchain.\n\nEthereum developer Aram Mughalyan recently revealed a groundbreaking development. Using the OpenZeppelin Contract Wizard, Mughalyan demonstrated how anyone can create a new token on the Ethereum blockchain in under one minute. This streamlined process allows broader token innovation within the Ethereum ecosystem.\n\nYou might also like: Ethereum upgrade doubled energy efficiency rates, study reveals\n\nEther price analysis\n\nAccording to data from CoinGecko, the price of Ethereum (ETH) stands at $1,628; over the past seven days, the token has recorded a 0.07% increase. Ethereum’s market capitalization stands at more than $195 billion. On Sept. 11, ETH witnessed a dip to $1,530 before rebounding to its current valuation.\n\nThe introduction of the Holešky testnet and the simplification of token launches on Ethereum demonstrate the blockchain’s commitment to staying at the forefront of Web3 innovation. These developments pave the way for a more robust and accessible Ethereum ecosystem with exciting prospects.\n\nFollow Us on Google News"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMie2h0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjMvMDkvMTcvaXRzLWJlZW4tb25lLXllYXItc2luY2UtdGhlLWV0aGVyZXVtLW1lcmdlLWhlcmVzLWhvdy10aGUtbmV0d29yay1pcy1kb2luZy1vbi1pdHMtZ29hbHMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 17 Sep 2023 07:00:00 GMT", "title": "It's been one year since the Ethereum merge. Here's how the network is doing on its goals. - CNBC", "content": "Despite ether's recent price action, the network has been thriving since the \"merge\" – and could just be getting started. A year ago Ethereum underwent a major technical transition that was meant to improve its security in an energy efficient way that many hoped would lead to higher price s and, separately, would open a new yield-generating opportunity for investors. The price of ether is down over the past six months, but it's still up 8% over the past year and nearly 35% year-to-date, according to Coin Metrics. With the crypto market as a whole suffering from low liquidity and volatility amid regulatory uncertainty, the incremental regulatory and technical developments in Bitcoin and Ethereum haven't been enough to push prices to new highs after the banking crisis-fueled rally earlier this year. Still, market participants are seeing and feeling the success of the Ethereum merge. \"ETH's transition to proof-of-stake created a more composable, cheaper and less energy-intensive network that currently yields stakers about 4%,\" said <PERSON>, head of digital assets research at VanEck. \"But with T-bills yielding 5% and the U.S. regulatory onslaught ongoing, investor and developer interest has been insufficient to pump the token much in recent months.\" \"Ethereum daily gas fees are languishing near 2023 lows and daily trading activity at 2-year lows,\" he added. \"We look forward to Ethereum protocol upgrade EIP-4844 in Q4, which should enable much cheaper transactions on Ethereum-compatible layer 2s and potentially catalyze wider adoption of stablecoin and loyalty applications.\" Here's how Ethereum is doing on some of its post-merge goals: Yields Ether's staking reward reference rate was 5.854% per annum on Sept. 15, 2022, according to Ethereum data provider Beaconchain. It spiked to about 8% after the fall of FTX and in May following JPMorgan's takeover of First Republic. Yields were last at 3.9%. The yield changes based on participation levels in staking and Ethereum's process of validating transactions. It is expected to fall as more stakers come onto the network. (For more on staking check out our guide here .) More validators The amount of staked ether has been rising steadily over the past year with investors keen to earn extra yield on their assets. While the merge gave investors the opportunity to earn yield – by locking up their funds – this year's Shanghai, also known as Shapella, upgrade gave them the ability to unstake their funds. The Shanghai upgrade was widely seen as an event that would bring more liquidity to Ethereum. \"While the market has been really stagnant across digital assets, staking has been one of the few areas that have seen constant growth in this period,\" said Andrew Ballinger, head of staking solutions at Canadian investment firm 3iQ. \"About a week after [Shanghai] happened in April, we started to see assets coming into the network to be staked outpacing assets being withdrawn,\" he added. \"That really demonstrates the immediate interest from people who were sitting on the sidelines, who hadn't been staking until this point to start doing so.\" The percentage of ether supply being staked has risen from 11.99% on Sept. 15, 2022, to 20.46% on Aug. 30 of this year. Burning fees and decreasing supply Ethereum has a mechanism programmed in to regulate the network's notoriously high transaction fees, called gas fees, by \"burning\" them. Everyday new ether is emitted into the market and if demand doesn't match the supply level, the price goes down. After the merge, investors expected there to be less supply in the market, and if demand stayed constant, that the price would rise. Since the merge, the supply of ether has fallen 0.248%, according to data provider Ultrasound Money . About 681,449 ETH have been issued while 981,427 have been burned. \"The ETH supply is now kind of dependent on the usage – the more usage, the more ETH that's burned,\" said Maria Shen, a general partner at Electric Capital. \"Right before the merge, the supply of ETH peaked, and now we are on a flat, deflationary supply curve.\" \"Whenever someone does a transaction, they pay a fee,\" she explained. \"Part of that fee is now being burned, and whenever you do that, the supply of Ethereum shrinks. So you have effectively a deflationary supply of ETH\" – meaning the supply over time will begin to decline as opposed to increase, which is historically what it's always done – \"as long as people keep using the network.\" The next upgrade The merge was just the first step in a series of upgrades that are planned to make improvements on Ethereum. The blockchain is the most popular among developers who are keen to build a new world of applications on this technology. But, in recent years, it has been hampered by its famously slow transaction times and egregious transaction fees. Those specific problems gave rise to alternatives like Solana and Cardano. If Ethereum can speed up transactions and solve the fee structure, it could become more competitive, Shen said. \"Those no longer become differentiating factors,\" she said. \"So those ecosystems will be forced to figure out other differentiating factors. \"A lot of them do scale in different ways with different types of trade offs, so that's one consideration: what are the trade offs that users are willing to make?\" she added. \"It's not just enough for something to be fast, you need something to be able to do it on the chain.\""}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMie2h0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjMvMDkvMTcvaXRzLWJlZW4tb25lLXllYXItc2luY2UtdGhlLWV0aGVyZXVtLW1lcmdlLWhlcmVzLWhvdy10aGUtbmV0d29yay1pcy1kb2luZy1vbi1pdHMtZ29hbHMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 17 Sep 2023 07:00:00 GMT", "title": "It's been one year since the Ethereum merge. Here's how the network is doing on its goals. - CNBC", "content": "Despite ether's recent price action, the network has been thriving since the \"merge\" – and could just be getting started. A year ago Ethereum underwent a major technical transition that was meant to improve its security in an energy efficient way that many hoped would lead to higher price s and, separately, would open a new yield-generating opportunity for investors. The price of ether is down over the past six months, but it's still up 8% over the past year and nearly 35% year-to-date, according to Coin Metrics. With the crypto market as a whole suffering from low liquidity and volatility amid regulatory uncertainty, the incremental regulatory and technical developments in Bitcoin and Ethereum haven't been enough to push prices to new highs after the banking crisis-fueled rally earlier this year. Still, market participants are seeing and feeling the success of the Ethereum merge. \"ETH's transition to proof-of-stake created a more composable, cheaper and less energy-intensive network that currently yields stakers about 4%,\" said <PERSON>, head of digital assets research at VanEck. \"But with T-bills yielding 5% and the U.S. regulatory onslaught ongoing, investor and developer interest has been insufficient to pump the token much in recent months.\" \"Ethereum daily gas fees are languishing near 2023 lows and daily trading activity at 2-year lows,\" he added. \"We look forward to Ethereum protocol upgrade EIP-4844 in Q4, which should enable much cheaper transactions on Ethereum-compatible layer 2s and potentially catalyze wider adoption of stablecoin and loyalty applications.\" Here's how Ethereum is doing on some of its post-merge goals: Yields Ether's staking reward reference rate was 5.854% per annum on Sept. 15, 2022, according to Ethereum data provider Beaconchain. It spiked to about 8% after the fall of FTX and in May following JPMorgan's takeover of First Republic. Yields were last at 3.9%. The yield changes based on participation levels in staking and Ethereum's process of validating transactions. It is expected to fall as more stakers come onto the network. (For more on staking check out our guide here .) More validators The amount of staked ether has been rising steadily over the past year with investors keen to earn extra yield on their assets. While the merge gave investors the opportunity to earn yield – by locking up their funds – this year's Shanghai, also known as Shapella, upgrade gave them the ability to unstake their funds. The Shanghai upgrade was widely seen as an event that would bring more liquidity to Ethereum. \"While the market has been really stagnant across digital assets, staking has been one of the few areas that have seen constant growth in this period,\" said Andrew Ballinger, head of staking solutions at Canadian investment firm 3iQ. \"About a week after [Shanghai] happened in April, we started to see assets coming into the network to be staked outpacing assets being withdrawn,\" he added. \"That really demonstrates the immediate interest from people who were sitting on the sidelines, who hadn't been staking until this point to start doing so.\" The percentage of ether supply being staked has risen from 11.99% on Sept. 15, 2022, to 20.46% on Aug. 30 of this year. Burning fees and decreasing supply Ethereum has a mechanism programmed in to regulate the network's notoriously high transaction fees, called gas fees, by \"burning\" them. Everyday new ether is emitted into the market and if demand doesn't match the supply level, the price goes down. After the merge, investors expected there to be less supply in the market, and if demand stayed constant, that the price would rise. Since the merge, the supply of ether has fallen 0.248%, according to data provider Ultrasound Money . About 681,449 ETH have been issued while 981,427 have been burned. \"The ETH supply is now kind of dependent on the usage – the more usage, the more ETH that's burned,\" said Maria Shen, a general partner at Electric Capital. \"Right before the merge, the supply of ETH peaked, and now we are on a flat, deflationary supply curve.\" \"Whenever someone does a transaction, they pay a fee,\" she explained. \"Part of that fee is now being burned, and whenever you do that, the supply of Ethereum shrinks. So you have effectively a deflationary supply of ETH\" – meaning the supply over time will begin to decline as opposed to increase, which is historically what it's always done – \"as long as people keep using the network.\" The next upgrade The merge was just the first step in a series of upgrades that are planned to make improvements on Ethereum. The blockchain is the most popular among developers who are keen to build a new world of applications on this technology. But, in recent years, it has been hampered by its famously slow transaction times and egregious transaction fees. Those specific problems gave rise to alternatives like Solana and Cardano. If Ethereum can speed up transactions and solve the fee structure, it could become more competitive, Shen said. \"Those no longer become differentiating factors,\" she said. \"So those ecosystems will be forced to figure out other differentiating factors. \"A lot of them do scale in different ways with different types of trade offs, so that's one consideration: what are the trade offs that users are willing to make?\" she added. \"It's not just enough for something to be fast, you need something to be able to do it on the chain.\""}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vdS50b2RheS9ndWlkZXMvZXRoZXJldW0tZ29lcmxpLXRlc3RuZXQtanVzdC1yZXBsYWNlZC1ieS1ob2xlc2t5LXdoYXQteW91LW5lZWQtdG8ta25vd9IBYWh0dHBzOi8vdS50b2RheS9ndWlkZXMvZXRoZXJldW0tZ29lcmxpLXRlc3RuZXQtanVzdC1yZXBsYWNlZC1ieS1ob2xlc2t5LXdoYXQteW91LW5lZWQtdG8ta25vdz9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 17 Sep 2023 07:00:00 GMT", "title": "Ethereum Goerli Testnet Just Replaced by <PERSON><PERSON>: What You Need to Know - U.Today", "content": "Advertisement\n\nEthereum (ETH), the second largest blockchain and the most mainstream smart contracts platform, has accomplished a major milestone. The history of its most important testnet, Goerli, has come to an end.\n\nHere's why Ethereans decided to sunset Goerli in 2023 and what is special about <PERSON><PERSON>, Goerli's hotly anticipated successor.\n\nEthereum replaces Goerli testnet by <PERSON><PERSON>: Basics\n\nToday, Sept. 15, 2023, on the first anniversary of The Merge activation in mainnet, Ethereum (ETH) has ceased supporting its testnet, Goerli. Developers need to migrate toward Holesky, the first PoS-by-design testnet.\n\nHolesky (or Holešky, Holešovice) is an Ethereum (ETH) test network (testnet) activated on Sept. 15, 2023.\n\n<PERSON><PERSON> replaces <PERSON><PERSON><PERSON> as a staking, infrastructure and protocol-developer testnet.\n\nFor dApp stress tests, smart contract experiments and other EVM-related procedures, developers should refer to Sepolia testnet.\n\n<PERSON><PERSON> is the first-ever Ethereum (ETH) testnet launched on the top of the proof-of-stake (PoS) consensus.\n\n<PERSON><PERSON> launches to address scalability issues of Goerli: the shortage of goETH testnet tokens, the low number of validators and so on.\n\n<PERSON><PERSON> will have 1.46 million validators, which is almost twice as much as Ethereum (ETH) mainnet has.\n\nNew testnet support will be abandoned by 2028.\n\nAs such, with <PERSON><PERSON>, Ethereum (ETH) developers will have a new sandbox environment in which to experiment with various features of the protocol.\n\nWhat is testnet?\n\nIn cryptocurrencies, a test network or testnet is an experimental distributed network that emulates the processes that happen on a cryptocurrency network, but with no real value transferred. Just as with main networks (mainnets), accounts can send tokens to each other, while validators confirm them by adding new blocks to the chain.\n\nUsers of a testnet get \"play\" tokens from faucets, a free-to-use mechanism created by Ethereum (ETH) enthusiasts. For instance, some faucets distribute free testnet Ethers for sharing Twitter accounts, connecting to Discord and so on.\n\nSuch networks are necessary for stress testing dApps without the risks of losing money. Every modern DeFi or wallet undergoes a testnet phase before mainnet deployment.\n\nRopsten, Rinkeby, Goerli, Sepolia: Crucial Ethereum (ETH) testnets\n\nEthereum (ETH), the second largest blockchain and the first programmable network (with smart contracts), used a couple of testnets during its eight-year history. Launched in 2016, Ropsten replaced Morden, the first-ever testnet for proof-of-work (PoW) Ethereum. Ropsten was a testbed of all major Ethereum (ETH) dApps we are using and the first testnet to be \"merged\" when Ethereum (ETH) tested the PoW-to-PoS transition.\n\nIn 2017, Ethereans launched Rinkeby, a testnet on the top of the Clique proof-of-authority (PoA) consensus mechanism, which is a modified version of proof of stake (Pos). It only interacted with the Go Ethereum (Geth) version of client software.\n\nAt the ETH Berlin Conference in 2018, Ethereum (ETH) developers unveiled Goerli, a multiclient Ethereum (ETH) testnet. It means that it could work with the versions of Ethereum (ETH) software written in various programming languages.\n\nFinally, in 2021, Ethereum Sepolia testnet was launched with an unlimited number of testnet tokens.\n\nIntroducing Holesky, very special Ethereum (ETH) testnet\n\nActivated today, Ethereum's Holesky is an attempt by core developers to address all the issues that somehow affected previous testnets and their usage by teams.\n\nHolesky: Highlights\n\nHolesky is the first long-standing public Ethereum testnet that is \"merged from genesis\"; this means that Holesky never had a proof-of-work (PoW) version. Not unlike its predecessors, Holesky draws its name from the Nádraží Holešovice subway station in Prague.\n\nHolesky works together with Sepolia; while the first testnet is designed to be a technical platform for experiments with staking designs, the general infrastructure of Ethereum (ETH) and protocol-level development ideas, Sepolia is a platform for testing smart contracts, decentralized applications and all EVM functionalities. Like all mainstream crypto testnets, Holesky will have its own set of validators, block explorer and ecosystem of faucets.\n\nHolesky: What was wrong with Goerli?\n\nHolesky goes live to replace Goerli, a major Ethereum (ETH) testnet since 2018. Holesky is designed to help Ethereum (ETH) testers avoid the disadvantages of earlier testnets. For instance, Ethereum Goerli users faced a shortage of GoETH test tokens and were even forced to purchase them for money, which is in contradiction of Ethereum (ETH) testnet rules. Holesky is unlikely to face similar issues.\n\nHolesky public testnet genesis file has been generated, and merged into main branch of https://t.co/JWKySOSvtU.\n\nLaunch date Sept/15, 2023, 14:00 UTC\n\nChain ID-17000 (zip code of Holešovice).\n\nThe genesis will have 1,460,000 validators. Our biggest public network to date. — Barnabas Busa (@BarnabasBusa) August 22, 2023\n\nAlso, it will have a previously unimaginable number of validators: 1.46 million entities will protect network integrity. This is more than Ethereum’s mainnet (700,000) and Goerli (512,000) have in all. This, in turn, will allow developers to mimic \"real-world\" Ethereum (ETH) operations closely.\n\nPer the creators of Holesky, they allocated over two billion HoleskyETH by the launch in order to make this quantity sufficient for all developers.\n\nHolesky: Specifications\n\nHolesky goes live with a set of specifications just like any other EVM ecosystem blockchain. Here's what future Holesky users should consider before getting connected to the network."}]