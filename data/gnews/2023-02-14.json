[{"id": 31, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ibHVyLWxhdW5jaGVzLWxvbmctYXdhaXRlZC10b2tlbi0xNjQwMDM1MTAuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 14 Feb 2023 08:00:00 GMT", "title": "Blur Launches Long-Awaited <PERSON>ken Airdrop for Ethereum NFT Traders - Yahoo Finance", "content": "Upstart NFT marketplace Blur, the largest rival to market leader OpenSea in recent months, is set to begin its delayed airdrop of BLUR tokens today to reward Ethereum NFT traders.\n\nBlur launched its marketplace last October with the promise of token rewards for traders, and has granted users “care packages” that represent coming token allotments. Users can finally open up their care packages today and claim the Ethereum-based tokens, with an estimated start around 1:30pm ET following a late morning delay.\n\nThe marketplace has awarded the token allotments in three waves to date. The first wave was offered up to eligible Ethereum NFT traders who used a competing marketplace in the six months prior to <PERSON>r’s own launch. The second wave was for Blur users who listed their NFTs for sale on the marketplace through November, while the final wave is for traders who bid on NFTs through Blur.\n\nIT'S TIME FOR $BLUR Care Packages can be opened on Feb 14 at 12PM EST, 1AM HKG, 6PM CET. Make sure the launch announcement comes from our official @BLUR_io account tomorrow and double check all URLs before claiming. pic.twitter.com/tSbOPLqYTW — Blur (@blur_io) February 13, 2023\n\nBlur initially planned to drop the governance token to eligible users in January, but then delayed the airdrop to today. “We’re trying new things,” the marketplace tweeted on January 19, “and the extra two weeks will allow us to deliver a launch that hasn’t been done before.”\n\nBilling itself the “marketplace for pro traders,” Blur raised $11 million in a seed round led by Paradigm and announced in March 2022. Like LooksRare and other marketplaces that have emerged following the rise of the NFT market, Blur is trying to build up an audience by offering potentially valuable token rewards to traders.\n\nStory continues\n\nNFT Sales Jump 38% in January as Bored Apes Drive Trading Surge\n\nAnticipation for the pending token drop has apparently fueled Blur’s rise over the last few months, with the marketplace sometimes topping OpenSea in terms of NFT trading volume.\n\nHowever, the hype around token rewards has also prompted suspicion over how much “wash trading” is taking place on the platform as users potentially manipulate trades to boost rewards. That’s what happened with LooksRare in early 2022, as users traded NFTs back and forth between their own wallets at artificially inflated prices to manipulate the rewards model.\n\nBlur hasn’t yielded billions of dollars’ worth of suspicious-looking trades, however, unlike LooksRare did last year. But data shows that Blur has far fewer active traders and transactions than OpenSea over the past week, despite posting more overall trading volume.\n\nData from analytics platform Dune suggests that about 13% of Blur trades are classified as suspected wash trading, compared to about 2% for OpenSea.\n\nWe’ll see whether Blur’s surging momentum in recent months continues after users stop trading with airdrop incentives in mind. Notable crypto exchanges like Coinbase and Huobi have already announced plans to support the BLUR token once it starts trading today.\n\nEditor's note: This article was updated after publication to reflect the changed airdrop timing."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9mZXdlci1oYWxmLWV0aGVyZXVtLWJsb2Nrcy1vdmVyLTIyMjY1MjQ1Mi5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 14 Feb 2023 08:00:00 GMT", "title": "Fewer Than Half of New Ethereum Blocks Over the Past 24 Hours Are OFAC Compliant - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nAfter the U.S. government blacklisted Tornado Cash transactions for U.S. people in August, there was a swift response by validators on the Ethereum blockchain to exclude sanctioned transactions. For many in the crypto industry, such moves were decried as censorship – directly counter to the vision of a decentralized network free from government interference.\n\nNow, however, the trend is going the other way, and more blocks with Tornado Cash are going through even if they’re not compliant with U.S. Treasury Dept. sanctions.\n\nAccording to an MEV watchdog site, just 49% of blocks that made it onto the Ethereum blockchain over the past 24 hours were OFAC-compliant, meaning the blocks excluded transactions that have been sanctioned by the U.S. Treasury Department’s Office of Foreign Assets Control.\n\nThis is a significant decrease in censorship from Ethereum’s past. Ethereum hit an all-time high for censorship on Nov. 21, when 79% of blocks relayed on Ethereum excluded OFAC-sanctioned transactions. The last time that Ethereum’s censored blocks were below 50% was Oct.16.\n\nPut another way, that means that more than half of the blocks that made it onto the Ethereum blockchain over the last 24 hours are non-OFAC compliant.\n\nSo what has reversed Ethereum’s censorship course?\n\nPost-Merge OFAC Compliant Blocks (mevwatch.info)\n\nMEV-Boost contributed to Ethereum’s censorship problem\n\nSince Ethereum changed its consensus mechanism to proof-of-stake in September (known as the Merge), most of the blocks that make it onto the blockchain go through a middleware component known as MEV-Boost, which is a software that allows validators to request pre-made blocks from builders.\n\nMEV-Boost came into existence to help validators earn MEV, or Maximal Extractible Value, which are profits derived from including or reordering transactions within a block.\n\nThe software was built by Flashbots, an Ethereum research and development team, in order to democratize MEV among validators and skirt issues of centralization.\n\nStory continues\n\nFlashbots has succeeded in having MEV-Boost used across the ecosystem, as 96% of validators today have relayed blocks via the middleware. The firm also runs its own relayer, which is used by 68% of validators.\n\nBut a new set of issues, mainly censorship, arose with MEV-Boost after OFAC blacklisted Tornado Cash transactions for U.S. people in August.\n\nAs a result of the sanctioning, relayers using MEV-Boost deliver to their connected validators only pre-built blocks without Tornado Cash transactions included. In other words, MEV-Boost was censoring blocks by default.\n\nRead more: Is Ethereum’s Censorship Problem Taking a Turn?\n\nHas the censorship course reversed?\n\nThe push for validators to connect to other relayers that are not Flashbots’ has been at the center of reversing Ethereum’s censorship course.\n\nToday, 68% of blocks relayed have been done via Flashbots. As recently as December, that number was at 74%. Before Flashbots began censoring Tornado Cash transactions following the Merge, it open-sourced the code so that others could develop their own non-censoring relays.\n\nThere are now seven relayers that are noncensoring. Those include two of BloXroute’s relayers (max profit and ethical), Ultrasound, Agnostic Gnosis, Manifold, Relayoor and Aestus.\n\nRelayers and total market share (mevboost.pics)\n\nMartin Köppelmann, a censorship-resistance advocate and the co-founder of Gnosis Chain, which also runs a noncensoring relayer, shared in a tweet that the decline in censorship is partially thanks to BloXroute, Ultrasound and Gnosis since they make up most of the uncensored block space.\n\nWhile this is good news this very much depends currently on just 3 entities: @bloXrouteLabs, @ultrasoundmoney, and @GnosisDAO (those 3 relays make up for 47% of uncensored block space)\n\nSo we still need more systemic solutions 👇https://t.co/xo3zTKqiMu — Martin Köppelmann 🇺🇦 (@koeppelmann) February 14, 2023\n\nKöppelmann told CoinDesk that he believes this “is good news and I am happy Gnosis is contributing, but I also believe Ethereum censorship resistance should not depend on so few entities.”\n\nRead more: Is Ethereum’s Censorship Problem Taking a Turn?"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9mZXdlci1oYWxmLWV0aGVyZXVtLWJsb2Nrcy1vdmVyLTIyMjY1MjQ1Mi5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 14 Feb 2023 08:00:00 GMT", "title": "Fewer Than Half of New Ethereum Blocks Over the Past 24 Hours Are OFAC Compliant - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nAfter the U.S. government blacklisted Tornado Cash transactions for U.S. people in August, there was a swift response by validators on the Ethereum blockchain to exclude sanctioned transactions. For many in the crypto industry, such moves were decried as censorship – directly counter to the vision of a decentralized network free from government interference.\n\nNow, however, the trend is going the other way, and more blocks with Tornado Cash are going through even if they’re not compliant with U.S. Treasury Dept. sanctions.\n\nAccording to an MEV watchdog site, just 49% of blocks that made it onto the Ethereum blockchain over the past 24 hours were OFAC-compliant, meaning the blocks excluded transactions that have been sanctioned by the U.S. Treasury Department’s Office of Foreign Assets Control.\n\nThis is a significant decrease in censorship from Ethereum’s past. Ethereum hit an all-time high for censorship on Nov. 21, when 79% of blocks relayed on Ethereum excluded OFAC-sanctioned transactions. The last time that Ethereum’s censored blocks were below 50% was Oct.16.\n\nPut another way, that means that more than half of the blocks that made it onto the Ethereum blockchain over the last 24 hours are non-OFAC compliant.\n\nSo what has reversed Ethereum’s censorship course?\n\nPost-Merge OFAC Compliant Blocks (mevwatch.info)\n\nMEV-Boost contributed to Ethereum’s censorship problem\n\nSince Ethereum changed its consensus mechanism to proof-of-stake in September (known as the Merge), most of the blocks that make it onto the blockchain go through a middleware component known as MEV-Boost, which is a software that allows validators to request pre-made blocks from builders.\n\nMEV-Boost came into existence to help validators earn MEV, or Maximal Extractible Value, which are profits derived from including or reordering transactions within a block.\n\nThe software was built by Flashbots, an Ethereum research and development team, in order to democratize MEV among validators and skirt issues of centralization.\n\nStory continues\n\nFlashbots has succeeded in having MEV-Boost used across the ecosystem, as 96% of validators today have relayed blocks via the middleware. The firm also runs its own relayer, which is used by 68% of validators.\n\nBut a new set of issues, mainly censorship, arose with MEV-Boost after OFAC blacklisted Tornado Cash transactions for U.S. people in August.\n\nAs a result of the sanctioning, relayers using MEV-Boost deliver to their connected validators only pre-built blocks without Tornado Cash transactions included. In other words, MEV-Boost was censoring blocks by default.\n\nRead more: Is Ethereum’s Censorship Problem Taking a Turn?\n\nHas the censorship course reversed?\n\nThe push for validators to connect to other relayers that are not Flashbots’ has been at the center of reversing Ethereum’s censorship course.\n\nToday, 68% of blocks relayed have been done via Flashbots. As recently as December, that number was at 74%. Before Flashbots began censoring Tornado Cash transactions following the Merge, it open-sourced the code so that others could develop their own non-censoring relays.\n\nThere are now seven relayers that are noncensoring. Those include two of BloXroute’s relayers (max profit and ethical), Ultrasound, Agnostic Gnosis, Manifold, Relayoor and Aestus.\n\nRelayers and total market share (mevboost.pics)\n\nMartin Köppelmann, a censorship-resistance advocate and the co-founder of Gnosis Chain, which also runs a noncensoring relayer, shared in a tweet that the decline in censorship is partially thanks to BloXroute, Ultrasound and Gnosis since they make up most of the uncensored block space.\n\nWhile this is good news this very much depends currently on just 3 entities: @bloXrouteLabs, @ultrasoundmoney, and @GnosisDAO (those 3 relays make up for 47% of uncensored block space)\n\nSo we still need more systemic solutions 👇https://t.co/xo3zTKqiMu — Martin Köppelmann 🇺🇦 (@koeppelmann) February 14, 2023\n\nKöppelmann told CoinDesk that he believes this “is good news and I am happy Gnosis is contributing, but I also believe Ethereum censorship resistance should not depend on so few entities.”\n\nRead more: Is Ethereum’s Censorship Problem Taking a Turn?"}, {"id": 18, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ib3JlZC1hcGUtb3duZXItYnVybnMtMTY5ay0yMTMyMTc1MzMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 14 Feb 2023 08:00:00 GMT", "title": "Bored Ape Owner Burns $169K NFT to Move It From Ethereum to Bitcoin - Yahoo Finance", "content": "A valuable digital collectible was permanently removed from circulation over the weekend, as its owner aimed to symbolically shift the asset’s underlying blockchain from Ethereum to Bitcoin.\n\nThe collectible was Bored Ape Yacht Club (BAYC) #1626, an Ape from the most valuable project in the NFT space. Its most recent sale on OpenSea took place last November when it sold for 108 Ethereum—that's nearly $432,000 at the time of the transaction, or around $169,000 at today's prices.\n\nNFTs are digital assets that are provably unique and signify the ownership of an item, often digital art. The ownership of BAYC #1626 is linked to a digital token that is recorded on the Ethereum network, where it could also be traded—that is, up until recently.\n\nDigital tokens can be permanently removed from circulation through a process called burning, which involves sending an asset to a location where it cannot be retrieved. <PERSON> claimed to have burned BAYC #1626 over the weekend, preventing it from being sold ever again, at least on Ethereum’s network.\n\n​​“Essentially throwing a Lamborghini into a trash compactor–It's kind of fun,<PERSON> <PERSON> told Decrypt. “Whether putting bloated JPEGs on Bitcoin’s base chain is smart or not is a whole 'nother discussion, but I think it's going to be a lot of fun seeing how it plays out.<PERSON>\n\n<PERSON> believes his Ape now exists on Bitcoin. That’s because the location to which BAYC #1626 was burned is linked to an Inscription made through Ordinals. Ordinals, created by <PERSON>, is a project that allows content like videos and images to be assigned to individual satoshis—the smallest unit that Bitcoin can be divided into—where they permanently exist on Bitcoin’s network as Inscriptions.\n\nEven though the number of Inscriptions on Bitcoin is approaching 100,000, there are few notable marketplaces for people to trade them, and a significant number of buyers and sellers are linked together currently through Ordinal’s discord server.\n\nStory continues\n\nOrdinals Gain Momentum With 76,000 NFTs Minted to Bitcoin\n\nThe burn took place using a newly developed feature for Ordinals named Teleburn, which creates a unique location with each new Inscription to which digital assets can be burned. The feature allows users to assign an existing asset from another network to a Bitcoin Inscription while removing it from circulation, effectively transferring the token between chains in the eyes of those who created the new feature.\n\n“The idea is that you are one-way, permanently burning an asset on another chain and pointing it to the ordinal that lives on the Bitcoin chain,” said Rob Hamilton, who collaborated with Rodarmor to create the new Ordinals feature.\n\nHamilton approached Rodarmor about developing the Teleburn function last Saturday at Bitcoin Park, a coworking space in Nashville, Tennessee that caters to the Bitcoin community. The two decided to work together after Hamilton pointed out that Williams wanted to burn his Ape, who had responded to one of Rodarmor’s Tweets.\n\nHelp me. — Jason A. Williams (@GoingParabolic) February 11, 2023\n\n“Let's go write some code right now,” Rodarmor said, according to Hamilton, adding that the prospect of burning an Ape got Rodarmor “really excited.”\n\nRodarmar did not respond to requests for comment from Decrypt. However, the two can be seen developing Ordinal’s Teleburn function in a recent Tweet, according to Hamilton.\n\nBurning BAYC #1626 wasn’t the first time that Ordinal’s Teleburn function was used. Rodarmor first tested the new feature out on an ENS domain he owned, rodarmor.eth. Then the pair oversaw Williams as he burned the ape.\n\nThe term Teleburn was coined by Rodarmor, as a combination of the words teleport and burn, said Hamilton. Rodarmor referenced the function’s hasty development with Hamilton in a recent tweet.\n\nHamilton believes the Teleburn function will catch on as a way for people to bridge their digital collectibles, noting that Rodarmor plans to extend Ordinal’s Teleburn support to assets on other chains aside from Ethereum, such as Tezos and Solana.\n\n“This has now set the standard of representing an asset across the chain,“ said Hamilton. “It's going to be the way to actually have skin in the game,” pointing out that assets burned are permanently gone from circulation.\n\nA chimp off the old block?\n\nAs news of the Bored Ape burn spread on Twitter Monday, Yuga Labs co-founder Greg Solano weighed in on the matter, stating the Inscription linked to BAYC #1626 is an unlicensed reproduction of the original NFT because Williams no longer maintains its possession on Ethereum’s network.\n\n“If you transfer your ape to an address you no longer control (even if it's the 'burn' address), you have effectively given up your license,” he stated.\n\nSolano also pushed back against the notion that BAYC #1626 is “gone from [Ethereum] forever,” as mentioned in the post’s initial Tweet, because it still exists on-chain, even if people can no longer access it.\n\nIt's not \"gone from ETH forever.\" It's basically the same as any other transfer: If you transfer your ape to an address you no longer control (even if it's the 'burn' address), you have effectively given up your license.\n\nAnd no, before someone asks, that doesn't mean that anyone… https://t.co/E52b7ZjWbu — Garga.eth (Greg Solano) (@CryptoGarga) February 13, 2023\n\nA spokesperson from Yuga Labs confirmed to Decrypt the company believes the Inscription of BAYC #1626 is an illegitimate Ape.\n\n\"Only NFTs minted from Ethereum contract: ****************************************** are legitimate BAYC NFTs,\" a Yuga spokesperson said, referencing the smart contract that yielded the collection's 10,000 Apes.\n\nWhether the new Ordinals function could supplant Ethereum’s popularity for hosting digital art is doubtful, according to the CEO of Web3 development platform Hiro Alex Miller. Hiro builds tools for Stacks, a network built on top of Bitcoin that aims to bring smart contract functionality to the underlying network.\n\n“As much as I am very pro-Bitcoin and think there’s going to be a massive ecosystem there, it's not going to replace Ethereum NFTs tomorrow, or probably at all,” Hiro told Decrypt. “I think it's gonna be interesting to see where this goes.”\n\nMiller said the Teleburn function provides a solution for keeping track of a digital art’s provenance—the record of prior ownership—across blockchains, adding that it’s challenging to establish which digital collectible is the real one if it exists in circulation on multiple chains.\n\nThe Hiro CEO also compared how data is stored on Bitcoin using Ordinals versus Ethereum NFTs. While it’s possible to store assets such as images on-chain using Ethereum, most NFTs point to an asset that is stored off-chain, whether that’s also an image or another type of file. With Inscriptions, the data is stored directly and permanently on Bitcoin’s blockchain.\n\nRegardless of whether the Inscription of BAYC #1626 is an unofficial version of its Ethereum-based predecessor, Miller thinks the collectible will “carry a lot of value” because of its first-of-a-kind nature.\n\n“He basically turned it into something that is now an ultra-rare [Ape],” Miller said, referencing the categories by which NFTs are often ascribed value based on the rarity of their attributes. “The market is going to value it as its own unique piece of art.”"}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ib3JlZC1hcGUtb3duZXItYnVybnMtMTY5ay0yMTMyMTc1MzMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 14 Feb 2023 08:00:00 GMT", "title": "Bored Ape Owner Burns $169K NFT to Move It From Ethereum to Bitcoin - Yahoo Finance", "content": "A valuable digital collectible was permanently removed from circulation over the weekend, as its owner aimed to symbolically shift the asset’s underlying blockchain from Ethereum to Bitcoin.\n\nThe collectible was Bored Ape Yacht Club (BAYC) #1626, an Ape from the most valuable project in the NFT space. Its most recent sale on OpenSea took place last November when it sold for 108 Ethereum—that's nearly $432,000 at the time of the transaction, or around $169,000 at today's prices.\n\nNFTs are digital assets that are provably unique and signify the ownership of an item, often digital art. The ownership of BAYC #1626 is linked to a digital token that is recorded on the Ethereum network, where it could also be traded—that is, up until recently.\n\nDigital tokens can be permanently removed from circulation through a process called burning, which involves sending an asset to a location where it cannot be retrieved. <PERSON> claimed to have burned BAYC #1626 over the weekend, preventing it from being sold ever again, at least on Ethereum’s network.\n\n​​“Essentially throwing a Lamborghini into a trash compactor–It's kind of fun,<PERSON> <PERSON> told Decrypt. “Whether putting bloated JPEGs on Bitcoin’s base chain is smart or not is a whole 'nother discussion, but I think it's going to be a lot of fun seeing how it plays out.<PERSON>\n\n<PERSON> believes his Ape now exists on Bitcoin. That’s because the location to which BAYC #1626 was burned is linked to an Inscription made through Ordinals. Ordinals, created by <PERSON>, is a project that allows content like videos and images to be assigned to individual satoshis—the smallest unit that Bitcoin can be divided into—where they permanently exist on Bitcoin’s network as Inscriptions.\n\nEven though the number of Inscriptions on Bitcoin is approaching 100,000, there are few notable marketplaces for people to trade them, and a significant number of buyers and sellers are linked together currently through Ordinal’s discord server.\n\nStory continues\n\nOrdinals Gain Momentum With 76,000 NFTs Minted to Bitcoin\n\nThe burn took place using a newly developed feature for Ordinals named Teleburn, which creates a unique location with each new Inscription to which digital assets can be burned. The feature allows users to assign an existing asset from another network to a Bitcoin Inscription while removing it from circulation, effectively transferring the token between chains in the eyes of those who created the new feature.\n\n“The idea is that you are one-way, permanently burning an asset on another chain and pointing it to the ordinal that lives on the Bitcoin chain,” said Rob Hamilton, who collaborated with Rodarmor to create the new Ordinals feature.\n\nHamilton approached Rodarmor about developing the Teleburn function last Saturday at Bitcoin Park, a coworking space in Nashville, Tennessee that caters to the Bitcoin community. The two decided to work together after Hamilton pointed out that Williams wanted to burn his Ape, who had responded to one of Rodarmor’s Tweets.\n\nHelp me. — Jason A. Williams (@GoingParabolic) February 11, 2023\n\n“Let's go write some code right now,” Rodarmor said, according to Hamilton, adding that the prospect of burning an Ape got Rodarmor “really excited.”\n\nRodarmar did not respond to requests for comment from Decrypt. However, the two can be seen developing Ordinal’s Teleburn function in a recent Tweet, according to Hamilton.\n\nBurning BAYC #1626 wasn’t the first time that Ordinal’s Teleburn function was used. Rodarmor first tested the new feature out on an ENS domain he owned, rodarmor.eth. Then the pair oversaw Williams as he burned the ape.\n\nThe term Teleburn was coined by Rodarmor, as a combination of the words teleport and burn, said Hamilton. Rodarmor referenced the function’s hasty development with Hamilton in a recent tweet.\n\nHamilton believes the Teleburn function will catch on as a way for people to bridge their digital collectibles, noting that Rodarmor plans to extend Ordinal’s Teleburn support to assets on other chains aside from Ethereum, such as Tezos and Solana.\n\n“This has now set the standard of representing an asset across the chain,“ said Hamilton. “It's going to be the way to actually have skin in the game,” pointing out that assets burned are permanently gone from circulation.\n\nA chimp off the old block?\n\nAs news of the Bored Ape burn spread on Twitter Monday, Yuga Labs co-founder Greg Solano weighed in on the matter, stating the Inscription linked to BAYC #1626 is an unlicensed reproduction of the original NFT because Williams no longer maintains its possession on Ethereum’s network.\n\n“If you transfer your ape to an address you no longer control (even if it's the 'burn' address), you have effectively given up your license,” he stated.\n\nSolano also pushed back against the notion that BAYC #1626 is “gone from [Ethereum] forever,” as mentioned in the post’s initial Tweet, because it still exists on-chain, even if people can no longer access it.\n\nIt's not \"gone from ETH forever.\" It's basically the same as any other transfer: If you transfer your ape to an address you no longer control (even if it's the 'burn' address), you have effectively given up your license.\n\nAnd no, before someone asks, that doesn't mean that anyone… https://t.co/E52b7ZjWbu — Garga.eth (Greg Solano) (@CryptoGarga) February 13, 2023\n\nA spokesperson from Yuga Labs confirmed to Decrypt the company believes the Inscription of BAYC #1626 is an illegitimate Ape.\n\n\"Only NFTs minted from Ethereum contract: ****************************************** are legitimate BAYC NFTs,\" a Yuga spokesperson said, referencing the smart contract that yielded the collection's 10,000 Apes.\n\nWhether the new Ordinals function could supplant Ethereum’s popularity for hosting digital art is doubtful, according to the CEO of Web3 development platform Hiro Alex Miller. Hiro builds tools for Stacks, a network built on top of Bitcoin that aims to bring smart contract functionality to the underlying network.\n\n“As much as I am very pro-Bitcoin and think there’s going to be a massive ecosystem there, it's not going to replace Ethereum NFTs tomorrow, or probably at all,” Hiro told Decrypt. “I think it's gonna be interesting to see where this goes.”\n\nMiller said the Teleburn function provides a solution for keeping track of a digital art’s provenance—the record of prior ownership—across blockchains, adding that it’s challenging to establish which digital collectible is the real one if it exists in circulation on multiple chains.\n\nThe Hiro CEO also compared how data is stored on Bitcoin using Ordinals versus Ethereum NFTs. While it’s possible to store assets such as images on-chain using Ethereum, most NFTs point to an asset that is stored off-chain, whether that’s also an image or another type of file. With Inscriptions, the data is stored directly and permanently on Bitcoin’s blockchain.\n\nRegardless of whether the Inscription of BAYC #1626 is an unofficial version of its Ethereum-based predecessor, Miller thinks the collectible will “carry a lot of value” because of its first-of-a-kind nature.\n\n“He basically turned it into something that is now an ultra-rare [Ape],” Miller said, referencing the categories by which NFTs are often ascribed value based on the rarity of their attributes. “The market is going to value it as its own unique piece of art.”"}]