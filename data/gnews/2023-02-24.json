[{"id": 5, "url": "https://news.google.com/rss/articles/CBMihAFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wMi8yNC9zY2FsaW5nLXNvbHV0aW9uLWFyYml0cnVtLWhpdHMtbmV3LW1pbGVzdG9uZS1vdmVydGFrZXMtZXRoZXJldW0tZXRoLWluLXRlcm1zLW9mLWRhaWx5LXRyYW5zYWN0aW9ucy_SAYgBaHR0cHM6Ly9kYWlseWhvZGwuY29tLzIwMjMvMDIvMjQvc2NhbGluZy1zb2x1dGlvbi1hcmJpdHJ1bS1oaXRzLW5ldy1taWxlc3RvbmUtb3ZlcnRha2VzLWV0aGVyZXVtLWV0aC1pbi10ZXJtcy1vZi1kYWlseS10cmFuc2FjdGlvbnMvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Scaling Solution Arbitrum Hits New Milestone, Overtakes Ethereum (ETH) in Daily Transactions - The Daily Hodl", "content": "The roll-up scaling solution Arbitrum overtook Ethereum (ETH) in terms of daily transactions for the first time ever this week.\n\nData from blockchain research tool Dune Analytics indicates Arbitrum hit more than 1.1 million daily transactions on Tuesday, higher than Ethereum’s 1.084 million.\n\nThe Arbitrum team celebrated the milestone on Twitter.\n\n“For the first time ever, Arbitrum One processed more transactions than Ethereum This is a huge milestone achieved by our team and Arbinauts. We’ve come a long way as a community and we’re grateful to have you along with us! Our mission to scale Ethereum continues!”\n\nArbitrum doesn’t have a token yet. The project has seen its total value locked (TVL) skyrocket from zero in August 2021 to $1.92 billion at time of writing, according to the decentralized finance data tracker DeFi Llama. It’s currently the fourth-ranked chain in terms of TVL, behind Ethereum with $29.11 billion, Tron (TRX) with $5.29 billion, and Binance Smart Chain (BSC) with $4.95 billion.\n\nThe TVL of a blockchain represents the total capital held within its smart contracts, and it is calculated by multiplying the amount of collateral locked into the network by the current value of the assets.\n\nArbitrum has also witnessed its active address count explode recently, shooting up from around 60,000 at the start of the month to nearly 118,000 by February 20th, according to Dune.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nGenerated Image: Midjourney"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vZGFpbHljb2luLmNvbS9jb2luYmFzZS1sYXVuY2hlcy1ldGhlcmV1bS1sYXllci0yLW5ldHdvcmstYmFzZS1wb3dlcmVkLWJ5LW9wdGltaXNtL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Coinbase Launches Ethereum Layer-2 Network Base Powered by Optimism - DailyCoin", "content": "Base is a fork of Optimism, one of the most active Ethereum Layer-2 blockchains.\n\nCoinbase said it had thought about launching its own chain twice but decided not to.\n\nBase will not have a token of its own.\n\nCoinbase, one of the largest centralized cryptocurrency exchanges in the world, has surprised the market with a new product launch on Thursday.\n\nCoinbase introduced Base, an Ethereum Layer-2 network powered by Optimism, one of the most popular Layer-2 blockchains. The new Coinbase product is open source but is still somewhat centralized, though Coinbase said Base would be decentralized progressively.\n\nCoinbase stressed that Base would not have a token of its own.\n\n1/ Today, Coinbase is launching @BuildOnBase, an Ethereum layer 2 that will help improve scalability and usability of crypto, so we can bring economic freedom to 1B+ people. — <PERSON> (@brian_armstrong) February 23, 2023\n\n<PERSON>, the engineer responsible for protocols at Coinbase, said that Base would be home to Coinbase’s onchain products and serve as an open platform for anyone to build.\n\n“Our goal with Base is to make onchain the next online and onboard one billion users into the cryptoeconomy. In pursuit of this goal, Base will serve as both a home for Coinbase’s onchain products and an open ecosystem where anyone can build.”\n\nWhile Coinbase had thought about launching its own chain twice in the past, it decided not to do it. However, the company said yes to Base because it believes it will accelerate the development of Coinbase’s onchain products, bring the exchange’s customers into the cryptoeconomy, and increase its investment in core crypto infrastructure.\n\nBase is currently in testnet. The team behind the blockchain will share more information about its roadmap in the weeks ahead.\n\nOn the Flipside\n\nCoinbase provided no information on when Base will launch.\n\nSince Coinbase is a public and regulated company, the launch of Base may attract regulatory pressure and scrutiny for Coinbase.\n\nWhy You Should Care\n\nCoinbase is one of the most involved centralized crypto companies in the world. The company’s decision to fork Optimism and launch a Layer-2 network to scale Ethereum is a huge milestone for overall Ethereum scaling and crypto. Users can use Base to access and use Ethereum with cheap transactions.\n\nLearn more about Coinbase’s financial results for Q4 2022:\n\nCoinbase Posts Another Quarterly Loss Amid Regulator Crackdown"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiOWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3JlZGRpdC1hbGV4aXMtb2hhbmlhbi1ldGhlcmV1bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Reddit Co-founder <PERSON><PERSON> Bo<PERSON>ther in 2014 Presale - Blockworks", "content": "Reddit co-founder <PERSON> was tipped off to the impending launch of Ethereum’s native token, ether, in a meeting with crypto exchange Coinbase back in 2014.\n\nIn a recent interview, <PERSON><PERSON><PERSON> said he spent $15,000 to buy 50,000 ETH (average price: $3.33) in 2014, which is now worth $82.4 million.\n\n<PERSON><PERSON> was purchasable for bitcoin in 2014 during the project’s presale, but the Ethereum blockchain only went live on July 30, 2015. This meant the first ether holders had to wait until the blockchain’s launch to move or utilize their tokens.\n\n“In hindsight, I didn’t invest nearly as much as I should have,” <PERSON><PERSON><PERSON> told Forbes. Ether was last trading around $1,650, and is up 38% in the year-to-date period, according to Blockworks Research, and 49,500% above <PERSON><PERSON><PERSON>’s initial purchase price.\n\n<PERSON><PERSON><PERSON>’s openness to decentralized and uncensorable currency comes from knowledge about his family’s traumatic history, where some of his relatives were killed during the Armenian genocide of 1915. Their inherited heirloom rugs were seized by Turkish soldiers.\n\nThe idea of persecution, especially by a state, “makes the idea of a store of value that is not controlled by any single state very attractive,” he said.\n\nThat observation makes up one of Oh<PERSON>n’s core investment strategies. The tech entrepreneur and venture capitalist has made a total of 164 investments, according to Crunchbase data.\n\nAmong those, 29 are blockchain-related. And In February last year, his VC firm Seven Seven Six raised $500 million to focus on more such startups.\n\nOhanian also made an early investment in Coinbase, from which he reportedly gained $50 million. Part of those proceeds went into buying a 17-carat diamond engagement ring for his tennis superstar wife Serena Williams.\n\nBitcoin led Reddit co-founder to ether\n\nOhanian was already taken by bitcoin’s complexity as a technically unseizable financial instrument.\n\nSo, Ethereum was considered an equally good investment opportunity. He recognized the underlying blockchain’s potential for developing additional unseizable assets, such as non-fungible tokens (NFTs) that now have far-reaching use cases in several different industries.\n\nOhanian reportedly personally owns about 700 NFTs stored on a marketplace, some of which give him early access to one of his VC investments, Islands.\n\nAdditionally, he holds NFTs giving him ownership rights to real estate property in Georgia and to the Ethereum Naming Service.\n\nIn August 2021, about a year after stepping down from the Reddit board, Ohanian spent ether which would be worth $550,000 today on seven NFTs that he believed resembled his wife, including CryptoPunks.\n\nUpdated Feb. 24, 2022 at 9:53 am ET: Corrected reference to Ohanian’s family history.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiZ2h0dHBzOi8vY3J5cHRvLm5ld3MvZmxhcmUtbmV0d29yay1kZW1vbnN0cmF0ZXMtaG93LXVzZXJzLWNhbi1idXktZXRoZXJldW0tYmFzZWQtbmZ0cy11c2luZy14cnAtb3ItZG9nZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Flare Network demonstrates how users can buy Ethereum-based NFTs using XRP or DOGE - crypto.news", "content": "Flare Network, a mainnet seeking to connect Offchain sources and other layer-1s, recently demonstrated that its users can trustlessly purchase Ethereum non-fungible tokens (NFTs) using coins from other networks.\n\nTesting Flare Network’s interoperability function\n\nIn a press release, Flare Network showed that one could buy ERC-721 NFTs on its network using Dogecoin (DOGE) and XRP, the native token of the XRP Ledger. These coins are not inherently compatible with Ethereum and are yet to be integrated with the Flare Network.\n\nThis demonstration was done on Flare Network’s Songbird. In this testnet environment, developers can deploy dApps before launching them on the interoperable, scalable mainnet. This purchase was executed through Flare Network’s State Connector and Flare Time Series Oracle (FTSO). The State Connector links the Flare Network with smart contracts and Offchain sources. At the same time, the FTSO is a decentralized oracle that verifiably delivers on-chain smart contracts with reliably proven price and data feeds, including those of NFTs, from external sources.\n\nCommenting, <PERSON>, the Flare CEO and co-founder, said:\n\n“This demo highlights Flare’s ability to provide more secure, decentralized data on-chain to power new functionality and potential use cases for the industry. The NFT demo is one example of the web3 utility Flare can unlock for legacy tokens, enabling them to be used trustlessly in DApps on the network. We are excited to see what other applications engineers can develop, harnessing the capabilities of Flare’s native interoperability protocols.”\n\nBy illustrating this interoperability functionality currently in beta, Flare Network assured the broader community that it is possible to acquire NFTs launched on its rails using diverse tokens and coins, including DOGE and XRP. These coins are some of the world’s most liquid and are currently perched in the top 10 by market capitalization.\n\nExpanding non-EVM coins’ use cases\n\nBecause of interoperability, the Flare Network opens up new use cases for supported tokens. DOGE, for example, can now be used to acquire valuable NFTs, not just for trading purposes or speculation.\n\nOver the years, DOGE has been rising to new valuations with backing from Elon Musk, the owner of Twitter and one of the world’s richest people.\n\nMeanwhile, XRP, for example, has retained its value despite the ongoing court case versus the United States Securities and Exchange Commission (SEC). The XRPL also supports smart contracting, a net positive for XRP and the crypto ecosystem.\n\nIn January, the Flare Network partnered with FYEO, a blockchain security specialist. The firm will audit the platform’s code bases and support safer smart contract development.\n\nDisclosure: This content is provided by a third party. crypto.news does not endorse any product mentioned on this page. Users must do their own research before taking any actions related to the company.\n\nFollow Us on Google News"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ldGhlcmV1bS0yLTAtc3Rha2luZy1vbi1leGNoYW5nZS12cy1jcmVhdGluZy15b3VyLW5vZGUtd2hhdC15b3UtbmVlZC10by1rbm93L9IBamh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ldGhlcmV1bS0yLTAtc3Rha2luZy1vbi1leGNoYW5nZS12cy1jcmVhdGluZy15b3VyLW5vZGUtd2hhdC15b3UtbmVlZC10by1rbm93Lz9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Ethereum 2.0 Staking on Exchange vs. Creating Your Node: What You Need to Know - CryptoPotato", "content": "The Ethereum Network recently saw the official launch of the Beacon Chain, which is a stepping stone to the long-awaited Ethereum 2.0 upgrade, aiming to improve network speed, efficiency, and scalability. It’s a move by the Ethereum foundation to abandon the Proof of Work (PoW) consensus algorithm for the energy-efficient Proof of Stake (PoS) consensus algorithm. On top of that, we have to change terminology from having miners to having validators.\n\nEthereum 2.0 hopes to improve transaction processing speed to new heights, satisfying the demand of DeFi users.\n\nThe most significant change to facilitate improved transaction handling is the use of sharding technology. The term sharding comes from database terminology where we break up a single database into smaller parts that can be processed more quickly. In other words, not every node has to be a supercomputer capable of storing and searching terabytes of data. Even a small node can participate in the network and store a particular shard.\n\nOf course, every shard is stored on multiple nodes to guarantee redundancy in case many nodes crash. We don’t want to lose a valuable piece of blockchain data because it happens that all nodes containing a particular piece of data fail.\n\nNow, enough about ETH2.0 improvements. This upgrade gives crypto enthusiasts and investors a new opportunity to become part of the Ethereum Network through staking while earning ETH passively.\n\nBut what are your options? First of all, how does Ethereum 2.0 staking work?\n\nEthereum 2.0 Staking: Do You Have 32 ETH?\n\nStaking is mandatory to secure a PoS-based blockchain. Ethereum requires every validator to stake a minimum of 32 ETH or more to run a validator node.\n\nValidators who have staked 32 ETH or more with the Ethereum Network can validate transactions. For doing so, they will receive rewards that come from gas fees. At this point, it’s not clear yet what the expected APR will be for Ethereum staking. They added a rough estimate for the first year of Ethereum staking.\n\nAt this time, the exact size of an annual reward for Ethereum stakers is still unknown. However, according to the project roadmap, this value will vary between 1.56% and 18.1% and will be inversely proportional to the total number of validators: When the network increases, the rewards will contract.\n\nEven though Ethereum promises an attractive reward ratio, there are obvious risks attached. The Ethereum 2.0 staking page puts an important disclaimer.\n\nAlthough you can earn rewards for doing work that benefits the network, you can lose ETH for malicious actions, going offline, and failing to validate.\n\nHowever, you can pool your resources by joining a staking pool. In this case, there’s no need to own the full amount of 32 Ether. If you only have one or two Ether, you can still join a staking pool.\n\nThe following section will give you a detailed breakdown of the pros and cons of using an exchange-based staking pool over running your validator node, staking 32 Ether.\n\nExchange-Based Staking: The Pros and Cons\n\nPros\n\nHands-Off Approach\n\nYou don’t have to take care of the commitment of running and maintaining an Ethereum 2.0 node for years. Many things can happen in three years.\n\nSome exchanges might allow you to withdraw your stake after a fixed period or even allow flexible staking. This functionality depends on the popularity of the feature.\n\nYou Don’t Need 32 ETH\n\nAn obvious benefit is that you can participate in the process without committing the entire 32 ETH required to become a validator. This allows users with smaller investments to earn passively on their ETH without any further commitments.\n\nYou’re also not going to be concerned with any technicalities because everything you would need to do on most exchanges is stake your ETH without any further actions.\n\nCons\n\nLimited Control\n\nYou don’t have any control over how the exchange maintains the validator node(s). Anyone can make mistakes, even an exchange. It’s impossible to revert a slashing event. So, when an exchange-based node gets slashed, all exchange-based stakers are hurt.\n\nHowever, the slashing event will likely be less harsh as the exchange can spread the slash over all of its pooled stakers. Perhaps, they can tap into a reserve pool to cover the loss.\n\nThe Risk of a Halt\n\nAdditionally, an inherent risk of keeping your funds locked in an exchange is that of a potential hack or an attack by regulators, for example. If something happens to that exchange, your funds are also at risk.\n\nPotential Fees\n\nSome exchanges charge fees to maintain a validator node. You may wonder why? Exchanges charge fees to cover their costs to maintain nodes. This added cost can remove some of your earnings from staking. Always check the details when picking an exchange.\n\nYet, most exchanges choose to distribute the full earnings to their users. Binance distributes 100% of on-chain staking income among its users. Kraken Eth2.0 staking, however, doesn’t mention any costs.\n\nYou can take a look at some of the most popular exchanges that support Ethereum 2.0 in our list here. Now, let’s have a look at the pros and cons of running a validator node on your own.\n\nRunning a Validator Node: Pros and Cons\n\nWhile it might be more interesting to run your validator node to collect passive income from validating transactions, the risks are measurably higher.\n\nPros\n\nLet’s also take a look at the pros.\n\nMore control over your funds\n\nYou use your Ethereum wallet to stake the 32 ETH. In other words, the common phrase “Your wallet, your keys” applies here. When using an exchange-based staking pool, the exchange will still send your ETH to the Depositor contract that handles staking. It means that the exchange doesn’t hold the ETH in their wallets, as explained above.\n\nEarning full rewards\n\nYou earn the full rewards for your stake while running a validator node. Remember that you’ll have to spend some of your funds on maintenance, such as covering cloud costs when running a node with a cloud provider.\n\nCons\n\nHere are the disadvantages of running your validator node.\n\nNot everyone has the required capital of 32 ETH.\n\nTechnical Understanding is Necessary\n\nSome technical know-how is required to run but also maintain a validator node. If done incorrectly, such as failing to validate transactions or going offline, you can lose part of your stake. This event is called slashing, a well-known concept among the Cosmos and Polkadot community. This mechanism incentivizes validators to act honestly and maintain their nodes correctly.\n\nIndefinite Lockup Period\n\nYour staked Ether will be locked up for an indefinite period. It’s not possible to use this locked ETH in any of the existing DeFi protocols. The sole purpose of your stake is to secure the Ethereum Network.\n\nHere, it’s important to note that your ETH will also be locked indefinitely if you choose to use an exchange to stake. However, most exchanges mint a synthetic token in a 1:1 peg with the ETH you stake. This token is then listed for trading. You will be receiving your rewards based on the number of synthetic tokens you own, but if you decide that you don’t want to go on with staking, you can sell it and use the capital for something else.\n\nConclusion: Which Option is Best?\n\nAs with many areas of cryptocurrency, a core decision is whether to give up your control over assets. In the end, exchange-based staking still sends your funds to the Depositor contract. However, you put your trust with an exchange to maintain a validator node correctly.\n\nRemember that both staking options are subject to Ethereum’s volatility. It’s impossible to withdraw funds if Ethereum’s price experiences extreme volatility. Therefore, it’s a big commitment to stake with the Ethereum Network.\n\nHowever, the underlying goal is fascinating. With the right amount of stakers, Ethereum 2.0 has the potential to reach 100,000 transactions per second. And you can become part of the network to realize this ambitious goal of becoming a “world computer.”\n\nYet, Ethereum 2.0 staking offers better rates than centralized finance. On top of that, if you choose the exchange-based staking option, you will also get a synthetic asset. This opens up new ways to experiment with DeFi.\n\nIn short, if you don’t have the technical know-how or don’t have 32 ETH to commit long-term, it’s probably better to opt for an exchange-based staking option."}]