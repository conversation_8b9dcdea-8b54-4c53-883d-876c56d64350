[{"id": 4, "url": "https://news.google.com/rss/articles/CBMif2h0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9jb25zZW5zdXMtbWFnYXppbmUvMjAyMy8wMy8wMy9ldGhkZW52ZXItbG9va3MtY3JpbmdleS10by15b3UtYmVjYXVzZS1ldGhlcmV1bS1oYXMtYW4tYWN0dWFsLWNvbW11bml0eS_SAYMBaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2NvbnNlbnN1cy1tYWdhemluZS8yMDIzLzAzLzAzL2V0aGRlbnZlci1sb29rcy1jcmluZ2V5LXRvLXlvdS1iZWNhdXNlLWV0aGVyZXVtLWhhcy1hbi1hY3R1YWwtY29tbXVuaXR5L2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 03 Mar 2023 08:00:00 GMT", "title": "ETHDenver Looks <PERSON> to You Because Ethereum Has an Actual Community - CoinDesk", "content": "ETHDenver <PERSON> <PERSON><PERSON> to You Because Ethereum Has an Actual Community\n\nLike any good punk or hip hop show, this year's Colorado event for coders will feature events that will \"scare away normies,\" CoinDesk Chief Insights Columnist <PERSON> writes."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMidWh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9oZXJlcy10aGUtbW9zdC1pbW1uaW5ldC1zdXBwb3J0LWZvci1ldGhlcmV1bS1mb2xsb3dpbmctdGhlLTUtZGFpbHktY3Jhc2gtZXRoLXByaWNlLWFuYWx5c2lzL9IBeWh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9oZXJlcy10aGUtbW9zdC1pbW1uaW5ldC1zdXBwb3J0LWZvci1ldGhlcmV1bS1mb2xsb3dpbmctdGhlLTUtZGFpbHktY3Jhc2gtZXRoLXByaWNlLWFuYWx5c2lzLz9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 03 Mar 2023 08:00:00 GMT", "title": "Here’s the Most Immninet Support for Ethereum Following the 5% Daily Crash (ETH Price Analysis) - CryptoPotato", "content": "Ethereum’s price has been rejected from a significant resistance level following the turmoil surrounding Silvergate. However, there are still multiple support levels available to hold the price for now.\n\nTechnical Analysis\n\nBy: Edris\n\nThe Daily Chart\n\nOn the daily timeframe, the price has been struggling to break above the $1800 level. The higher boundary of the large symmetrical triangle pattern and a bearish rejection has led to the breakdown of the 50-day moving average located around the $1600 mark.\n\nThe 200-day moving average, located around $1400, seems like the next probable support zone, followed by $1300, which could be tested if a deeper pullback occurs.\n\nThe 4-Hour Chart\n\nOn the 4-hour chart, the price has been impulsively rejected from the $1650 level and is likely to test the $1500 support level soon. If this support level holds, ETH could once more target the $1650 level and the higher boundary of the triangle pattern.\n\nOn the other hand, a breakdown of the $1500 level would likely result in a deeper drop toward the $1350 area, as no significant support level exists between these 2 levels. The RSI indicator is also showing values below 50%, which points to the bears’ dominance.\n\nHowever, the RSI is approaching the oversold zone, which could lead to a consolidation or bullish pullback in the short term.\n\nOnchain Analysis\n\nBy Shayan\n\nThe following chart demonstrates the Estimated Leverage Ratio metric alongside ETH’s price. The metric measures the average amount of leverage used by the futures market participants. Typically, increasing values indicate that more investors are taking high-leverage risk in the derivatives market.\n\nThe Estimated Leverage Ratio has slightly increased during the last few weeks, showing that the futures market might become overheated due to the recent bullish price moves.\n\nThe metric hasn’t reached concerning levels yet, but if it continues spiking, participants should be wary due to the possibility of a short or perhaps even a long-squeeze event. In this case, a sudden impulsive move will occur in Ethereum’s price.\n\nIn conclusion, the metric’s behavior should be monitored in the upcoming weeks to prevent further losses."}, {"id": 18, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9mbGFzaGJvdHMtbWFrZXMtcHJpdmFjeS1lbmhhbmNlZC1ibG9jay0xNTU1NTM3MzcuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 03 Mar 2023 08:00:00 GMT", "title": "Flashbots Makes Privacy-Enhanced Block Builder Open Source on Ethereum Testnet Sepolia - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nFlashbots, an Ethereum research and development firm, has made open source a new block builder operating in a trusted environment, in this case a private mempool, on Ethereum’s Sepolia testnet, according to a press release shared exclusively with CoinDesk.\n\nThe research that Flashbots details will allow blocks to be built without revealing data on users’ transactions, a step toward enhancing privacy.\n\nFlashbots has not given an immediate timeline when it will move out of the testnet.\n\nBlock builders are specialized third-party participants who construct blocks based on optimizing transactions. Block builders offer those blocks to relays, such as the relay Flash<PERSON><PERSON> runs for MEV-Boost, in order to extract extra revenue made from reordering or including certain transactions.\n\nA mempool is like a waiting room, where pending transactions are sorted and stored before they are added to create a new block.\n\nRead more: What Is MEV, aka Maximal Extractable Value?\n\n<PERSON><PERSON><PERSON> told CoinDesk the move aims to enable privacy features on Ethereum. The code was made open source in an effort to decentralize block building on the Ethereum protocol.\n\n“​​Implementing block building inside encrypted enclaves brings us one step closer toward transaction confidentiality and decentralization of the block building role,” <PERSON><PERSON><PERSON> wrote in a blog post.\n\nExcited to announce that <PERSON><PERSON><PERSON> is successfully running a block builder inside an SGX enclave!\n\nThis brings us a step closer toward transaction confidentiality and decentralization of the block building role.\n\nTons of open learnings, code and tooling! ⚡️https://t.co/rrlLq6jxLW — <PERSON>ger ⚡🤖 (@metachris) March 3, 2023\n\nThis is not the first time that Flashbots has made its code open source. In August, when Tornado Cash was sanctioned by the U.S. Treasury’s Office of Foreign Asset Control, Flashbots conveyed that its relay would also be censoring Tornado Cash transactions. As a result, Flashbots raced to open its code so others could decide whether to censor or not censor.\n\nOne reason open-source software is part of the Flashbots agenda is so others in the block-building space can “build off the shared knowledge and test other solutions,” the Flashbots team told CoinDesk. “And, in reality, try to break it!”\n\nRead more: Flashbots Proposes New Class of ‘Matchmakers’ to Share MEV Gains With Ethereum Users"}, {"id": 23, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9mbGFzaGJvdHMtbWFrZXMtcHJpdmFjeS1lbmhhbmNlZC1ibG9jay0xNTU1NTM3MzcuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 03 Mar 2023 08:00:00 GMT", "title": "Flashbots Makes Privacy-Enhanced Block Builder Open Source on Ethereum Testnet Sepolia - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nFlashbots, an Ethereum research and development firm, has made open source a new block builder operating in a trusted environment, in this case a private mempool, on Ethereum’s Sepolia testnet, according to a press release shared exclusively with CoinDesk.\n\nThe research that Flashbots details will allow blocks to be built without revealing data on users’ transactions, a step toward enhancing privacy.\n\nFlashbots has not given an immediate timeline when it will move out of the testnet.\n\nBlock builders are specialized third-party participants who construct blocks based on optimizing transactions. Block builders offer those blocks to relays, such as the relay Flash<PERSON><PERSON> runs for MEV-Boost, in order to extract extra revenue made from reordering or including certain transactions.\n\nA mempool is like a waiting room, where pending transactions are sorted and stored before they are added to create a new block.\n\nRead more: What Is MEV, aka Maximal Extractable Value?\n\n<PERSON><PERSON><PERSON> told CoinDesk the move aims to enable privacy features on Ethereum. The code was made open source in an effort to decentralize block building on the Ethereum protocol.\n\n“​​Implementing block building inside encrypted enclaves brings us one step closer toward transaction confidentiality and decentralization of the block building role,” <PERSON><PERSON><PERSON> wrote in a blog post.\n\nExcited to announce that <PERSON><PERSON><PERSON> is successfully running a block builder inside an SGX enclave!\n\nThis brings us a step closer toward transaction confidentiality and decentralization of the block building role.\n\nTons of open learnings, code and tooling! ⚡️https://t.co/rrlLq6jxLW — <PERSON>ger ⚡🤖 (@metachris) March 3, 2023\n\nThis is not the first time that Flashbots has made its code open source. In August, when Tornado Cash was sanctioned by the U.S. Treasury’s Office of Foreign Asset Control, Flashbots conveyed that its relay would also be censoring Tornado Cash transactions. As a result, Flashbots raced to open its code so others could decide whether to censor or not censor.\n\nOne reason open-source software is part of the Flashbots agenda is so others in the block-building space can “build off the shared knowledge and test other solutions,” the Flashbots team told CoinDesk. “And, in reality, try to break it!”\n\nRead more: Flashbots Proposes New Class of ‘Matchmakers’ to Share MEV Gains With Ethereum Users"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vd3d3LmZvb2wuY29tL2ludmVzdGluZy8yMDIzLzAzLzAzL3doeS1iaXRjb2luLWV0aGVyZXVtLWFuZC1zaGliYS1pbnUtYXJlLWZhbGxpbmctdG9kL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 03 Mar 2023 08:00:00 GMT", "title": "Why Bitcoin, Ethereum, and Shiba Inu Are Falling Today - The Motley Fool", "content": "What happened\n\nMany cryptocurrencies continued to slide today as investors digest the fallout from the crypto bank Silvergate Capital (SI -2.94%) and evaluate its ability to stay in business.\n\nThe price of the world's largest cryptocurrency, Bitcoin (BTC 0.58%), is trading 4.3% lower than late yesterday afternoon as of 10 a.m. ET today. The price of the world's second-largest cryptocurrency, Ethereum (ETH 0.33%), is roughly 4.1% lower, while the price of the meme token Shiba Inu (SHIB 0.09%) is down 5.8%.\n\nSo what\n\nAfter the market closed Wednesday, Silvergate submitted a filing to the Securities and Exchange Commission (SEC), stating that it would need to delay the filing of its annual report as it analyzed the impact of several events on its business.\n\nSilvergate operates a real-time payments network for U.S. dollars that crypto exchanges and institutional traders use to better facilitate crypto transactions. The network is very helpful for the crypto ecosystem because cryptocurrencies trade around the clock and the U.S. banking system operates on a lag.\n\nBut the scandal-laden bankruptcy of FTX, which had been a large client of Silvergate, prompted a bank run, and Silvergate saw nearly 70% of its digital-asset-related deposits leave the bank in the fourth quarter of 2022. The bank had to sell a big chunk of its bond portfolio, while it traded at a loss, to cover the deposit outflows, wiping out a big chunk of shareholder equity. Furthermore, many investors believed Silvergate could face some severe regulatory action due to its relationship with FTX, and media outlets have reported that the U.S. Department of Justice is looking into the bank.\n\nIn its filing with the SEC, Silvergate said it continued to sell bonds at a loss in January and February and that its capital levels could now be below regulatory requirements as a result. Furthermore, Silvergate is also currently assessing the impact of regulatory inquiries and investigations into the bank. All of this has led the bank to assess its \"ability to continue as a going concern.\"\n\nYesterday, some of Silvergate's major clients, such as Coinbase (COIN 0.78%), announced they would stop using the bank, and Silvergate's stock fell a whopping 58% and is down nearly an additional 11% today, trading around $5 per share. Although most cryptocurrencies fell yesterday, they held up decently given the news. Today, however, investors seem more worried.\n\n\"The bearish turn could certainly be a delayed reaction to Silvergate's ongoing issues,\" said Clara Medalie, director of research at Kaiko, according to CNBC. \"Many large exchanges and market makers partner with Silvergate for fast transactions between entities, and any halt in activity could have an impact on global crypto liquidity.\"\n\n\"In addition, there are now wider industry concerns that U.S. regulators are trying to cut off further banking relationships between crypto firms and FDIC-insured banks,\" Markus Thielen, the head of research at digital asset platform Matrixport, told Cointelegraph.\n\nNow what\n\nFederal banking regulators have issued multiple statements warning banks about the risks associated with certain crypto activities, and another large crypto bank, Signature Bank (SBNY), has also scaled back its crypto activities.\n\nThe news is of industrywide interest because services provided by Silvergate help exchanges, traders, and others easily change dollars to crypto and vice versa.\n\nI still think the largest cryptocurrencies, like Bitcoin and Ethereum, will be here for a long time and that people will continue to be able to swap them for fiat currencies. However, I have no interest in meme tokens like Shiba Inu right now."}]