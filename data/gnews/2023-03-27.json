[{"id": 16, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tc2VuZHMtaGlkZGVuLW1lc3NhZ2UtMTgwMTQyNjM3Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Mar 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Buterin Sends Hidden Message in First Polygon zkEVM Transaction - Yahoo Finance", "content": "Polygon launched the long-awaited mainnet for its new Polygon zkEVM network today, and to mark the occasion, the core developers of the sidechain invited Ethereum creator <PERSON><PERSON> to send out the very first transaction—and he included a hidden message.\n\n<PERSON><PERSON><PERSON> appeared on a livestreamed event to mark the occasion, and while building the transaction, he included the following message converted into hex code: “A few million constraints for man, unconstrained scalability for mankind.”\n\nFurthermore, <PERSON><PERSON><PERSON> repeatedly set the Ethereum network transaction gas limit to 69,042, potentially referencing one meme number (69) and nearly another one (420) as well. Polygon Labs Head of Growth Sanket Shah tweeted that the gas limit number made the Ethereum creator a “man of culture.”\n\n.@VitalikButerin did the symbolic first ever transaction on @0xPolygon #zkEVM and it has a message on it \"Millions of constraints for man, unconstrained scalability for mankind\" Goose bumps!! https://t.co/HLNiVyRLie pic.twitter.com/Ue0C1CuDTd — Sandeep Nailwal | sandeep. polygon 💜 (@sandeepnailwal) March 27, 2023\n\n“And just like that, Vitalik Buterin has completed the first transaction on the newly deployed Polygon zkEVM chain,” Polygon Labs President <PERSON> tweeted this morning. “This is a big moment for scaling decentralized protocols to handle our growth.”\n\nPolygon zkEVM is billed as a next-generation blockchain network that enables faster and cheaper transactions that Ethereum’s own mainnet can handle.\n\n.@VitalikButerin sent the transaction on Polygon zkEVM He is a man of culture. Ensure that the gas limit is 69042 pic.twitter.com/Eh0ebi7giA — sanket.polygon | polygon zkEVM on 27th March (@sourcex44) March 27, 2023\n\nUnlike previous layer-2 and sidechain networks, a zkEVM pairs zero-knowledge rollups—which bundle together loads of transactions and execute those proofs onto Ethereum—with full compatibility with the Ethereum Virtual Machine. That means that it’s a native complement to Ethereum and that smart contracts, which contain the code that powers decentralized protocols and software, can be easily moved over to the zkEVM network.\n\nStory continues\n\nPolygon Launches Final Mainnet for zkEVM Solution to Scale Ethereum\n\n“zkEVM can be recognized as the ‘holy grail’ of blockchain scaling,” Polygon’s co-founder Mihailo Bjelic told Decrypt ahead of today’s launch.\n\nIt’s not the only zkEVM network out there, however: others are in development, and rival zkSync launched its own mainnet last week ahead of Polygon’s previously-announced move."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vZGVjcnlwdC5jby8xMjQ2MjMvZHlkeC1yZXZlYWxzLWxhdW5jaC1kYXRlLW1vdmUtZXRoZXJldW0tdG8tY29zbW9z0gFQaHR0cHM6Ly9kZWNyeXB0LmNvLzEyNDYyMy9keWR4LXJldmVhbHMtbGF1bmNoLWRhdGUtbW92ZS1ldGhlcmV1bS10by1jb3Ntb3M_YW1wPTE?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Mar 2023 07:00:00 GMT", "title": "dYdX Reveals Launch Date for Move From Ethereum to Cosmos - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\ndYdX announced the launch of its V4 private testnet this Tuesday, taking a significant step that will see the decentralized derivatives exchange (DEX) leave Ethereum.\n\nBy the end of September, the platform will be fully running on Cosmos.\n\nThe network allows developer teams to spin up their own native blockchains using the Cosmos Software Development Kit (SDK) according to their own preferences. Though distinct, each independent Cosmos-based blockchain can interact with one another.\n\nAD\n\nAD\n\nThe private testnet for dYdX is set to launch this Tuesday and run for two to three weeks. After that, a public testnet will be launched by the end of July.\n\nThe platform cited the lack of scalability on Ethereum as the main reason for the move.\n\n\"We reached a point where Ethereum couldn't process the transactions fast enough,\" dYdX’s marketing lead <PERSON> told Decrypt at this year’s Paris Blockchain Week.\n\nThis is the second project to announce its migration from Ethereum to Cosmos. SushiSwap is also making the same move after the project acquired the Cosmos-based trading platform Vortex Protocol last month.\n\nThe teams explored various options, including Solana and layer-2 solutions. \"We came to the conclusion that Cosmos was the better option because we can customize the blockchain to our needs,” said <PERSON>. “Now we can handle transactions at a faster pace.\"\n\nAD\n\nAD\n\ndYdX and centralized exchanges\n\nLaunched in 2017 by <PERSON>, who previously worked at Coinbase and Uber as a software engineer, dYdX currently has roughly $341.5 million in total value locked (TVL), per DeFi Llama.\n\nTVL is a metric used to measure how much money is sloshing around in a given DeFi protocol. Lido Finance, a liquid staking protocol, currently has the largest TVL at a whopping $10.4 billion.\n\nThough decentralized exchanges like Uniswap, Curve, and dYdX are steadily growing, they still only account for a small share of transactions compared to their centralized counterparts. Trading volume over the past day, Uniswap has facilitated more than $642 million in orders. Binance has processed more than $4.28 billion in trades over the same period.\n\nContrary to expectations at the time, the collapse of FTX last November did not significantly change this proportion.\n\n\"After FTX, we saw a 20-30% increase in trading volume, but only for a short time,\" David Gogel, Vice President of the dYdX Foundation, told Decrypt. \"The companies that benefited the most from the FTX case are other centralized exchanges. People don't really know about self-custody, and there's still a lot of work to do to educate them. It's a complicated journey.\""}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9udmlkaWEtc2F5cy1jcnlwdG8tYWRkcy1ub3RoaW5nLTE4NTcxMzU1Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Mar 2023 07:00:00 GMT", "title": "Nvidia Says Crypto Adds Nothing to Society, Despite Profiting From Mining - Yahoo Finance", "content": "Computer hardware manufacturer Nvidia has profited off of the cryptocurrency mining industry in recent years—but now CTO <PERSON> has reportedly said that crypto does not “bring anything useful for society,” according to The Guardian.\n\nInstead, <PERSON><PERSON> hopes that NVIDIA products are used for artificial intelligence (AI) development rather than crypto mining.\n\n“All this crypto stuff, it needed parallel processing, and [Nvidia] is the best so people just programmed it to use for this purpose,” <PERSON><PERSON> said. “They bought a lot of stuff, and then eventually it collapsed, because it doesn’t bring anything useful for society. AI does.”\n\nNvidia Hackers Are Selling Ethereum GPU Mining Workaround\n\nWhile <PERSON><PERSON> argues that the crypto industry has “collapsed,” Bitcoin and Ethereum, the two largest cryptocurrencies by market cap, have regained value in recent months. Bitcoin is up 17% in the past month, and Ethereum is up 7% per CoinGecko data.\n\nThe emerging Web3 gaming industry has also seen substantial venture capital funding in recent months. But <PERSON><PERSON> simply doesn’t believe that crypto has merit.\n\n“I never believed that [crypto] is something that will do something good for humanity,” he added. “You know, people do crazy things, but they buy your stuff, you sell them stuff. But you don’t redirect the company to support whatever it is.”\n\n<PERSON><PERSON> did not immediately respond to <PERSON>rypt’s request for comment.\n\nNvidia has a historically mixed relationship with crypto. Before Ethereum’s move to a more energy-efficient proof-of-stake model in September 2022, there was a high demand for powerful graphics cards (GPUs) as Ethereum miners used them to mine token rewards via the original proof-of-work model.\n\nCombined with a pandemic-related chip shortage, the cost of GPUs skyrocketed and it became near-impossible for gamers to buy the latest generation of graphics cards.\n\nIn an effort to deter crypto miners, Nvidia first tried to force hashrate limitations on its products—for which hackers found a workaround. Later, the company released special products designed for crypto miners, such as the Nvidia Cmp Hx, a chip which NVIDIA’s website states is “designed for professional mining operations.”\n\nStory continues\n\nSEC Fines Nvidia $5.5M for Not Disclosing Crypto Mining Impact on Gaming Business\n\nDespite Kagan’s dislike for crypto, Nvidia has profited off of the industry for years. The firm’s PC original equipment manufacturer (OEM) revenue jumped 200% in 2017, with Nvidia writing that the spike was “due primarily to strong demand for GPU products targeted for use in cryptocurrency mining.”\n\nLast year, Nvidia paid the U.S. Securities and Exchange Commission (SEC) $5.5 million in fines for failing to disclose that its gaming GPUs also saw success because crypto miners were buying up those products en masse as well."}, {"id": 24, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9udmlkaWEtc2F5cy1jcnlwdG8tYWRkcy1ub3RoaW5nLTE4NTcxMzU1Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Mar 2023 07:00:00 GMT", "title": "Nvidia Says Crypto Adds Nothing to Society, Despite Profiting From Mining - Yahoo Finance", "content": "Computer hardware manufacturer Nvidia has profited off of the cryptocurrency mining industry in recent years—but now CTO <PERSON> has reportedly said that crypto does not “bring anything useful for society,” according to The Guardian.\n\nInstead, <PERSON><PERSON> hopes that NVIDIA products are used for artificial intelligence (AI) development rather than crypto mining.\n\n“All this crypto stuff, it needed parallel processing, and [Nvidia] is the best so people just programmed it to use for this purpose,” <PERSON><PERSON> said. “They bought a lot of stuff, and then eventually it collapsed, because it doesn’t bring anything useful for society. AI does.”\n\nNvidia Hackers Are Selling Ethereum GPU Mining Workaround\n\nWhile <PERSON><PERSON> argues that the crypto industry has “collapsed,” Bitcoin and Ethereum, the two largest cryptocurrencies by market cap, have regained value in recent months. Bitcoin is up 17% in the past month, and Ethereum is up 7% per CoinGecko data.\n\nThe emerging Web3 gaming industry has also seen substantial venture capital funding in recent months. But <PERSON><PERSON> simply doesn’t believe that crypto has merit.\n\n“I never believed that [crypto] is something that will do something good for humanity,” he added. “You know, people do crazy things, but they buy your stuff, you sell them stuff. But you don’t redirect the company to support whatever it is.”\n\n<PERSON><PERSON> did not immediately respond to <PERSON>rypt’s request for comment.\n\nNvidia has a historically mixed relationship with crypto. Before Ethereum’s move to a more energy-efficient proof-of-stake model in September 2022, there was a high demand for powerful graphics cards (GPUs) as Ethereum miners used them to mine token rewards via the original proof-of-work model.\n\nCombined with a pandemic-related chip shortage, the cost of GPUs skyrocketed and it became near-impossible for gamers to buy the latest generation of graphics cards.\n\nIn an effort to deter crypto miners, Nvidia first tried to force hashrate limitations on its products—for which hackers found a workaround. Later, the company released special products designed for crypto miners, such as the Nvidia Cmp Hx, a chip which NVIDIA’s website states is “designed for professional mining operations.”\n\nStory continues\n\nSEC Fines Nvidia $5.5M for Not Disclosing Crypto Mining Impact on Gaming Business\n\nDespite Kagan’s dislike for crypto, Nvidia has profited off of the industry for years. The firm’s PC original equipment manufacturer (OEM) revenue jumped 200% in 2017, with Nvidia writing that the spike was “due primarily to strong demand for GPU products targeted for use in cryptocurrency mining.”\n\nLast year, Nvidia paid the U.S. Securities and Exchange Commission (SEC) $5.5 million in fines for failing to disclose that its gaming GPUs also saw success because crypto miners were buying up those products en masse as well."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiOWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3BvbHlnb24temtldm0tbGl2ZS1vbi1ldGhlcmV1bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Mar 2023 07:00:00 GMT", "title": "Polygon's zkEVM Goes Live on Ethereum Mainnet - Blockworks", "content": "The options for layer-2 scaling are expanding as Polygon launches its long-awaited zkEVM technology on Ethereum mainnet today.\n\nThe zero-knowledge Ethereum Virtual Machine is a technology that can execute smart contract transactions in a way that can prove the information is executed correctly without revealing the information itself on Ethereum’s mainnet, thereby bundling more transactions in less mainnet blockspace.\n\n<PERSON><PERSON>, the co-founder and chief operating officer of Polygon, told Blockworks this type of technology is considered the “holy grail of scaling.”\n\n“With the power of mathematics…you can compute something off-chain on layer-2, then verify everything back on Ethereum in a very succinct way in a very short amount of time,” <PERSON><PERSON><PERSON> said.\n\nPolygon’s launch on Ethereum comes just days after Matter Labs zkSync Era opened to the public on Ethereum mainnet.\n\nSimilar to zkSync’s offering, the Polygon zkEVM’s code is open-source, but under an AGPL v3 license.\n\n“That means that not only current repositories, but all future modifications and distributions as well, will remain open source, ensuring the code cannot be used for proprietary purposes,” <PERSON><PERSON><PERSON> said.\n\n<PERSON><PERSON>wal notes that Polygon zkEVM is EVM equivalent, whereas zkSync’s offering is EVM compatible.\n\nAccording to the framework laid out by Ethereum co-founder <PERSON><PERSON> in August 2022, zkSync Era is classified as a “Type-4 (high-level-language equivalent),” and there are advantages and disadvantages of various approaches to bringing the EVM to a zero-knowledge rollup. The merits of various tradeoffs are an ongoing topic of debate.\n\nLike zkSync Era, and the current crop of optimistic rollups, the Polygon zkEVM is not yet decentralized, but eventually, Nailwal hopes that Polygon’s new offering will take that step.\n\n“Everything is in the hands of the community,” he said.\n\nSecurity comes first\n\nIn addition to three internal audits, Nailwal notes that Polygon’s zkEVM has also gone through two rounds of external security audits from blockchain cybersecurity companies Hexens and Spearbit.\n\nOngoing, continuous audits are expected for the next three to four months following mainnet launch and a multi-million dollar bounty program will also be in place, he said.\n\nThe emphasis on security was also central to zkSync Era’s mainnet release, but Polygon said that Matter Labs had shared audits that cover their smart contracts and sequencer, not their prover.\n\n“This raises serious questions about security, at least until a full audit is released. The prover for Polygon zkEVM, meanwhile, has been audited, and is open-source,” Brendan Farmer, Polygon Zero co-founder, told Blockworks.\n\nA spokesperson for zkSync told Blockworks that the prover had been audited by Halborn Security, and pointed to the blockchain’s documentation, which notes that a recently completed review has not yet been published.\n\nEven conscientious security measures can leave gaps, however, and Nailwal notes that he does not wish users to bring millions of dollars to the zkEVM immediately.\n\n“Zk is new technology…so we [want] people [to be] as cautious as possible,” he said.\n\nDrawing on the example of the recent Euler Finance attacks, Nailwal cautioned that interacting with smart contracts is not without risk.\n\n“Euler, after running for two years, a bug was found — this can happen any day, on any smart contract-based application.” he said.\n\nZkEVMs are sure to be compared to their optimistic rollup counterparts — the most successful to-date being Optimism and Arbitrum.\n\n“Arbitrum took one and a half years before people actually started bringing in money,” Nailwal said. “So we don’t expect people to bring a lot of money [initially].”\n\nUpdated March 28, 2023 at 2:58 pm ET: After initial publication, Polygon announced the zkEVM prover would also be available under the AGPL v3 license, along with the rest of the codebase.\n\nDon’t miss the next big story – join our free daily newsletter."}]