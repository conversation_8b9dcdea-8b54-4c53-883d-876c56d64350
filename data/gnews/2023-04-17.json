[{"id": 1, "url": "https://news.google.com/rss/articles/CBMihgFodHRwczovL3d3dy5jb2luZGVzay5jb20vbWFya2V0cy8yMDIzLzA0LzE3L2V4Y2hhbmdlcy1yZWNlaXZlLTM3NW0taW5mbHV4LW9mLWV0aGVyLXNpbmNlLXNoYW5naGFpLXVwZ3JhZGUtYXMtcHJpY2UtaGl0cy0xMS1tb250aC1oaWdoL9IBigFodHRwczovL3d3dy5jb2luZGVzay5jb20vbWFya2V0cy8yMDIzLzA0LzE3L2V4Y2hhbmdlcy1yZWNlaXZlLTM3NW0taW5mbHV4LW9mLWV0aGVyLXNpbmNlLXNoYW5naGFpLXVwZ3JhZGUtYXMtcHJpY2UtaGl0cy0xMS1tb250aC1oaWdoL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Apr 2023 07:00:00 GMT", "title": "Ethereum Shanghai Upgrade Leads to Huge Influx of ETH at Exchanges - CoinDesk", "content": "Coinbase was one of the first exchanges that let users immediately unlock and withdraw their staked ETH via its platform. Binance, the world’s largest crypto exchange by volume, will follow suit on April 19, which “could result in more sell pressure for ETH,” <PERSON><PERSON> wrote."}, {"id": 13, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9jb25zZW5zdXMtbWFnYXppbmUvMjAyMy8wNC8xNy9ldGhlcmV1bS16a2V2bXMtc2Nyb2xsLW5ldHdvcmstc2NhbGFiaWxpdHkv0gFmaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2NvbnNlbnN1cy1tYWdhemluZS8yMDIzLzA0LzE3L2V0aGVyZXVtLXprZXZtcy1zY3JvbGwtbmV0d29yay1zY2FsYWJpbGl0eS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Apr 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Aims to Be the Turtle That Wins the Ethereum Scaling Race - CoinDesk", "content": "To make these matters harder (worse?) it's all being done in the open. Every Scroll pull request, comment, line of code and so forth is playing out in real time on its Github repository. That means anyone with an internet connection – anywhere in the world – can track the progress of Scroll’s codebase. It’s a little flourish of transparency that <PERSON> said is “a step above” the commonplace practice (even among open-source projects) of building code behind closed doors and then throwing them open at some point in time."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA0LzE3L2Zvcm1lci1nb2xkbWFuLXNhY2hzLWV4ZWN1dGl2ZS1kZXRhaWxzLWJpZy1jcnlwdG8tb3Bwb3J0dW5pdHktaW4tZXRoZXJldW0tZXRoLW1lZ2EtdHJlbmQv0gF-aHR0cHM6Ly9kYWlseWhvZGwuY29tLzIwMjMvMDQvMTcvZm9ybWVyLWdvbGRtYW4tc2FjaHMtZXhlY3V0aXZlLWRldGFpbHMtYmlnLWNyeXB0by1vcHBvcnR1bml0eS1pbi1ldGhlcmV1bS1ldGgtbWVnYS10cmVuZC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Apr 2023 07:00:00 GMT", "title": "Former Goldman Sachs Executive Details Big Crypto Opportunity in Ethereum (ETH) ‘Mega Trend’ - The Daily Hodl", "content": "Former Goldman Sachs executive <PERSON> says that there’s one big overlooked opportunity in the Ethereum (ETH) ecosystem.\n\nThe macro guru tells his nearly one million Twitter followers that CryptoPunks, one of the biggest non-fungible token (NFT) projects in existence, are starting to look appealing.\n\nThe Real Vision founder shares the floor price chart of CryptoPunks depicting declining prices, which he says could be an opportunity for bulls.\n\n“Punks are starting to get very interesting… They have held up in dollar terms at around $100,000 but in ETH. The tax season plus Blur farming has led to near-distressed prices.\n\nAs ETH rises over time, excess gains get recycled into trophy assets, with a lag. Same as traditional art when stock markets have been strong.\n\nLow prices seem to be an opportunity if you think crypto rises a lot over the next few years, which I believe.”\n\nWhile some have trouble grasping how seemingly silly images could be so valuable, <PERSON><PERSON> says that ETH holders often like to take their profits and put them into NFTs to diversify and send social signals to the rest of the space.\n\nIn a recent interview with NFT artist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> says NFTs are changing the game and allowing creators and artists to act as a new avenue for mass blockchain adoption.\n\n“[Artists and creators] have engaged millions of people in blockchain. To be honest, artists are the people who really lead movements, whether it’s musical artists or physical artists themselves…\n\nThis nexus between physical and digital, those two worlds are merging. So that’s one learning. The other learning is the metaverse [and] how it becomes the future digital representation of everything. How you can build brands from the ground up based around culture… To give their artists their control back, I think that’s probably another mega trend that we can see in the future.”\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/A. Solano"}, {"id": 15, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vZGFpbHljb2luLmNvbS9nb2toc2h0ZWluLXByZWRpY3RzLWV0aGVyZXVtLWZsaXAtYml0Y29pbi_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Apr 2023 07:00:00 GMT", "title": "<PERSON><PERSON><PERSON><PERSON> Predicts Ethereum Will Flip Bitcoin. How Likely Is It? - DailyCoin", "content": "Entrepreneur <PERSON> claims ETH will eventually top BTC.\n\nBitcoin hovers around the $30,000 price range with 5.5% weekly gains.\n\nEthereum broke through $2,100 with 12.5% weekly gains.\n\nCrypto entrepreneur <PERSON> revealed to his Twitter audience that he expects Ethereum (ETH) to flip Bitcoin (BTC) some time in the future. Not being threatened by getting roasted by his Bitcoin Maximalist friends, the Half-Baked Hero received some surprising answers from crypto aficionados.\n\n$ETH will eventually flip $BTC. I don’t know for how long, but it will. — <PERSON> (@davidgokhshtein) April 16, 2023\n\nCrypto Twitter Does the Math\n\nAs per usual, <PERSON>’s bald statements didn’t go unnoticed. “It already did, brother,” asserts <PERSON>, a crypto enthusiast who argues that adding all the ERC-20 tokens in the TOP 50 by global market capitalization does the trick. “It has been surpassed for a while now,” <PERSON><PERSON> claims, while another crypto trader <PERSON><PERSON><PERSON> remarks “Already has. Look at all the ERC-20 tokens with good liquidity.”\n\nLet’s look deeper at these claims: the leading digital asset Bitcoin has a global market capitalization of $578,508,586,135. On the other hand, the leading altcoin and NFT favorite blockchain Ethereum sits at $249,842,988,311. Bitcoin’s market dominance is 43.9%, while Ethereum’s is 19%.\n\nOn top of that, the global cryptocurrency market rank has many ERC-20 tokens, which are based on Ethereum, including Shiba Inu (SHIB), Uniswap (UNI), top stablecoins Tether (USDT) and USD Coin (USDC), along with the scaling solutions that come from forked versions of Ethereum’s code, like Polygon (MATIC).\n\nEthereum Kneels Before Bitcoin?\n\nAdding up all of the Ethereum-related altcoin with at least $5 billion, the global market cap of the TOP 20, stopping at decentralized crypto exchange Uniswap (UNI), equals to $402.5bn at the time of publication, which refutes these claims for the time being.\n\nHowever, it’s important to note that popular altcoins with large market capitalization like BNB (BNB), XRP (XRP), and TRX (TRX) have been excluded from the count since these altcoins originally started as ERC-20 tokens but later developed their own blockchains.\n\nUltimately, the swift growth of Ethereum-based tokens is evident in the numbers, but it’s yet to top Bitcoin. The ongoing squabble on Twitter between Ethereum enthusiasts and Bitcoin Maximalists gave birth to many heated discussions and a flurry of sarcastic memes.\n\nBTC and ETH Maxis are fighting it out on Twitter\n\nOn the Flipside\n\nMany crypto enthusiasts in the Twitter thread argue that Bitcoin will remain “the mother of all crypto,” comparing Bitcoin’s long-term value to gold.\n\nMany also argue that “Bitcoin would have to crash big time” for that to happen.\n\nWhy You Should Care\n\nThe two largest digital assets are enjoying solid bull runs, as BTC and ETH both recently achieved yearly highs.\n\nExplore trending crypto stories on DailyCoin:\n\nGokhshtein Shocked: Dogecoin Surges 7.7% After Twitter Move\n\nTestnets in Crypto: How To Use Test Networks to Earn Cryptocurrency"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ob3ctdG8tdXNlLWdhbmFjaGUtZm9yLWJsb2NrY2hhaW4tcHJvamVjdC1kZXZlbG9wbWVudNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Apr 2023 07:00:00 GMT", "title": "How to use Ganache for blockchain project development - Cointelegraph", "content": "Programming languages like Solidity, which is used to build smart contracts on the Ethereum blockchain network, are frequently used in blockchain development. Ganache helps developers test and debug their applications before deploying them on a live blockchain network. Also, developers must be well-versed in blockchain technology, including its underlying architecture and concepts like consensus algorithms, cryptography and decentralized governance.\n\nThis article will discuss what Ganache is, and how you can use it in decentralized application (DApp) development.\n\nWhat is Ganache in blockchain technology?\n\nGanache is a software tool developers widely use to create a local blockchain network for testing and development purposes. Developers may effectively test different scenarios and troubleshoot their blockchain apps by simulating a blockchain network on their local PC with Ganache. Ganache supports the quick development of distributed applications using Ethereum and Filecoin.\n\nThe tool is first installed on the developer’s computer, and a new workspace must be created before using Ganache for blockchain project development. Developers can link their blockchain project to Ganache once the workspace has been built, enabling them to test and debug their application on the simulated blockchain network.\n\nGanache provides a range of useful features, including the creation of new accounts, the ability to send transactions and the capability to debug smart contracts. By effectively locating and fixing bugs in their smart contract code, developers can use Ganache as a debugging tool to speed up the development process. The debugger feature allows developers to comb through their code line-by-line and see the values of variables at each step, making it easier to find and fix bugs.\n\nTwo versions of Ganache are available: a user interface (UI) and a command line interface (CLI). Thanks to the user-friendly Ganache UI, developers can quickly communicate with the local blockchain. In addition to offering real-time data on accounts, balances, transactions and events, it also has tools for testing and debugging smart contracts. In addition, the interface includes a built-in block explorer tool that lets users examine the specifics of each block and transaction.\n\nOn the other hand, developers can communicate with the local blockchain via the terminal using the Ganache CLI. It is a more versatile and compact choice for people who prefer using command-line tools. Developers may automate testing and deployment operations by integrating the CLI with other development tools and scripts.\n\nRegardless, the essential functionality of the Ganache UI and CLI is the same, and developers can choose the version that best suits their tastes and workflow.\n\nIs Ganache blockchain free?\n\nYes, Ganache is a free, open-source blockchain development tool. The personal blockchain network can be launched and managed using Ganache’s user-friendly interface. To make it simple for developers to test their smart contracts in a secure environment, it also produces private keys for the accounts generated in the network.\n\nRelated: The importance of open-source in computer science and software development\n\nIn the Ethereum development community, Ganache is a popular tool for creating, evaluating and deploying smart contracts. It is ideal for developers to incorporate it into their workflows because it is interoperable with other Ethereum development tools like the Truffle Suite framework. Truffle Suite is an Ethereum development framework for building, testing and deploying smart contracts on the blockchain.\n\nAre Truffle and Ganache the same blockchain?\n\nTruffle and Ganache are not the same blockchains, but they are closely related tools used in blockchain development. Truffle can be used with various blockchain networks, but as a local development network, it is most often used with Ganache.\n\nBefore releasing their smart contracts to a live network, developers can use Truffle to design, compile and test them on the Ganache network. This makes it possible to design and test software fast and affordably, and iterate on and modify the code of smart contracts.\n\nHow to install and use Ganache\n\nGanache is an essential tool for blockchain developers, as it allows them to test and debug their applications on a simulated blockchain network before deploying them on a live network. Here’s a step-by-step guide on how to install and use Ganache for personal Ethereum blockchain development:\n\nStep 1: Download and install Ganache\n\nDownload the application for your operating system from the official Ganache website. Run the installation file after downloading it, then install the application on your computer by adhering to the on-screen prompts. Ganache is available for Windows, Mac and Linux operating systems in all its versions.\n\nStep 2: Create a new workspace\n\nTo create a new workspace, open the Ganache application and select “New Workspace.” Users can set up the network parameters for their unique Ethereum blockchain in the workspace settings, including the number of accounts, the gas limit and the starting balance of each account.\n\nAn Ethereum workspace is a set of settings and user accounts that establish the parameters for a customized Ethereum blockchain network built using Ganache. Developers may quickly set up a private Ethereum network for testing and development purposes using workspaces.\n\nStep 3: Start the personal Ethereum blockchain network\n\nAfter configuring the network settings, click “Start” to begin your own private Ethereum blockchain network. For each of the accounts you set up in the workspace settings, Ganache will generate a set of private keys. Then, copy the remote procedure call (RPC) server address from the top of the screen, as you’ll need this to connect your development tool.\n\nUsing the RPC communication protocol, client software can invoke a server-side process from a distance. As a result, it is feasible to activate a procedure or function in another address space or process without the programmer worrying about the specifics of the underlying network transport or communication protocols. It enables programs to communicate with other systems on a network.\n\nStep 4: Connect your development tool to the Ganache network\n\nIt is necessary to link one’s development tool, such as Truffle Suite, to the Ganache network to deploy and test smart contracts on the private Ethereum blockchain. To do so, follow these steps:\n\nOpen your development tool and find the settings or configuration menu.\n\nSearch for a provider or network selection option, then type the RPC server address you copied from Ganache.\n\nTo ensure your development tool uses the new network, save your modifications and restart it.\n\nStep 5: Test and deploy smart contracts\n\nAfter configuring the network, users can deploy and test their smart contracts on the private Ethereum blockchain. Using the Truffle command line interface, they can compile and deploy their contracts to the Ganache network. Once the contracts are deployed, the Truffle CLI can interact with them and test their functionality.\n\nIt allows developers to interact with their smart contracts and the underlying blockchain network using various commands. Using the Truffle CLI, developers can automate the building and deployment of smart contracts, making it easier to develop and deploy DApps.\n\nWhen a smart contract is deployed to the mainnet, it must be submitted to the network, and a fee in cryptocurrency is paid to cover the cost of running the contract on the blockchain. When a contract is deployed, it becomes unchangeable and immutable. To guarantee that the smart contract works as intended and is secure, testing it properly before deployment is crucial.\n\nAn example of a simple contract deployment using Truffle CLI\n\nStep 1: Go to the directory where one wishes to build a project by opening the terminal or command prompt.\n\nStep 2: To start a new Truffle project, enter the following command:\n\n“Truffle init” is a command that initializes a new Truffle project with a basic directory structure and configuration files.\n\nStep 3: Under the contracts directory, add a new Solidity contract file. Here’s an example of a simple contract that stores a string:\n\nThe above code is a smart contract written in the Solidity programming language. One declared variable, a public string variable called “myString,” is present in the contract named “MyContract.” Everybody on the blockchain can access the string variable, which is initialized to “Hello, world!”\n\nWith a tool like Ganache, this contract can be set up on a private blockchain or an Ethereum network. Once installed, it can be used to interact with transactions sent to its blockchain address.\n\nStep 4: A contract can be compiled by running the following command:\n\n“Truffle compile” is a command that compiles the contract code and generates an application binary interface (ABI) and bytecode. The ABI serves as the interface between smart contracts and applications, while bytecode is a smart contract’s compiled version that may be run on the Ethereum Virtual Machine (EVM).\n\nStep 5: Run the following command to deploy the contract to a local blockchain network like Ganache:\n\n“Truffle migrate” is a command used to deploy the contract to the local network and create a new migration script in the “migrations” directory.\n\nStep 6: Run the following command to interact with the deployed contract using the Truffle console:\n\n“Truffle console” opens up a console with the web3.js library and contract artifacts loaded, allowing interaction with a blockchain network.\n\nStep 7: By establishing an instance of their contract and calling its functions once they are on the console, users can communicate with their contract. For instance, the following commands can be used to retrieve the value of myString:\n\nThe value of a string variable (myString) is then retrieved from the deployed instance of a smart contract (MyContract) using the above code. The output “Hello, world!” is printed to the console using “console.log(result).”\n\nAdvantages of using Ganache\n\nUsing Ganache as a blockchain development tool has several benefits. One of the key advantages is that it gives users access to a private Ethereum blockchain network with an intuitive UI for testing and development. As a result, programmers can test their smart contracts in a safe and private setting before using them on a live network. By offering a local network, developers can also avoid the high costs and prolonged transaction times linked to public networks.\n\nFor testing and development, Ganache also produces private keys for the accounts formed in the network, adding another level of protection. Moreover, creating, testing and deploying smart contracts on the blockchain is made simpler due to Ganache’s compatibility with the Truffle Suite framework.\n\nThe creation of DApps, such as blockchain-based games, and the testing of smart contracts for blockchain-based supply chain management systems are examples of how Ganache can be used.\n\nChallenges of using Ganache for blockchain development\n\nWhile Ganache is a powerful tool for blockchain development, there are still some challenges that developers may encounter. The fact that Ganache is a local development network and not directly connected to the Ethereum mainnet presents one of the main difficulties. Because of this, there may be differences in how smart contracts behave when deployed to a live network between the Ganache network and the mainnet, which may cause unforeseen problems.\n\nThe fact that Ganache might not always reflect the same conditions as a live network presents another difficulty with using it. Ganache, for instance, lets developers establish their own gas rates, which might not match those on a real network. When implementing smart contracts on a live network, this can cause problems because the gas price might not be enough to complete the transaction.\n\nFinally, issues with Ganache’s interoperability with other Ethereum development tools may arise. Although Ganache and the Truffle Suite framework are pretty compatible, there can be problems if developers use other programs or libraries that are not made to function with Ganache."}]