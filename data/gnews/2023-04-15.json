[{"id": 36, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vd3d3LmNyeXB0b3RpbWVzLmlvLzIwMjMvMDQvMTUvY2Fub25zLWNhZGFicmEtam9pbnMtbmZ0LWNyYXplLXdpdGgtZXRoZXJldW0tcGhvdG8tbWFya2V0cGxhY2Uv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 15 Apr 2023 07:00:00 GMT", "title": "Canon's Cadabra Joins NFT Craze with Ethereum Photo Marketplace - Crypto Times", "content": "As the photography business increases its footprint in the Web3 space, Canon USA is preparing to launch a brand-new NFT marketplace, “Cadabra,” focused on photography. Following a successful NFT drop in 2022, this is Canon’s first significant entry into the Web3 market.\n\nVarious kinds of tokenized photographs will be available on the Cadabra marketplace. Participating artists can sell tangible prints of their work, giving them new sources of income. Canon will fill orders for these prints, and the market will accept payments made with credit/debit cards and cryptocurrencies.\n\nThe Cadabra marketplace will offer initial drops of photo collections and a secondary resale market. Additionally, it makes priceless and one-of-a-kind digital artworks available to collectors.\n\nCanon will debut the marketplace later in 2023 after previewing it at the NFT NYC conference this week. The Cadabra marketplace will compete with Other photographic NFT marketplaces like Quantum Art and Sloika.\n\nBoth <PERSON>Drift<PERSON> (“Where My Vans Go?”) and <PERSON> (“Twin Flames”) have produced significant photo NFT collections which can help them get featured on Cadabra.\n\nThe photographers that Canon will showcase on the site are still a mystery. However, the corporation has promised various genres, such as landscapes, sports, animals, and lifestyle. The growth of NFTs has offered collectors access to rare and valuable digital artworks and new money streams for artists."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiOGh0dHBzOi8vdS50b2RheS9ldGhlcmV1bS1ldGgtcHJpY2UtYW5hbHlzaXMtZm9yLWFwcmlsLTE10gE8aHR0cHM6Ly91LnRvZGF5L2V0aGVyZXVtLWV0aC1wcmljZS1hbmFseXNpcy1mb3ItYXByaWwtMTU_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 15 Apr 2023 07:00:00 GMT", "title": "Ethereum (ETH) Price Analysis for April 15 - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nThe market is mainly trading sideways at the beginning of the weekend.\n\nAdvertisement\n\nETH/USD\n\nThe rate of Ethereum (ETH) has almost not changed since yesterday.\n\nOn the local chart, Ethereum (ETH) is trading closer to the resistance than to the support, which means that bulls are more powerful than bears at the moment.\n\nCurrently, the daily closure plays an important role. It the bar closes above the $2,100 mark, there is a chance to see a breakout followed by a blast to $2,130.\n\nOn the bigger time frame, Ethereum (ETH) is trading between the recently formed levels. In this case, consolidation in the narrow range of $2,075-$2,130 is the more likely scenario for the next days.\n\nOn the weekly chart, the rate of Ethereum (ETH) is about to fix above the level at $2,015. If it happens and the bar closes with no long wicks, there is a high chance to see a further rise to the $2,200-$2,300 zone.\n\nEthereum is trading at $2,099 at press time."}, {"id": 20, "url": "https://news.google.com/rss/articles/CBMigQFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wNC8xNS9jcnlwdG8tdHJhZGVyLXNheXMtZXRoZXJldW0tYWx0Y29pbi10aGF0cy1leHBsb2RlZC0xOTAtdGhpcy15ZWFyLWFib3V0LXRvLWhpdC1uZXctYWxsLXRpbWUtaGlnaC_SAYUBaHR0cHM6Ly9kYWlseWhvZGwuY29tLzIwMjMvMDQvMTUvY3J5cHRvLXRyYWRlci1zYXlzLWV0aGVyZXVtLWFsdGNvaW4tdGhhdHMtZXhwbG9kZWQtMTkwLXRoaXMteWVhci1hYm91dC10by1oaXQtbmV3LWFsbC10aW1lLWhpZ2gvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 15 Apr 2023 07:00:00 GMT", "title": "Crypto Trader Says Ethereum Altcoin That’s Exploded 190% This Year Is About To Hit New All-Time High - The Daily Hodl", "content": "A widely followed crypto analyst says that an Ethereum-based (ETH) altcoin looks set to hit new all-time highs after rising 190% in 2023.\n\nPseudonymous trader <PERSON> tells his 222,300 Twitter followers that layer-2 scaling solution Optimism (OP) will likely end up making all-time highs after breaking out from weeks of consolidation inside an ascending triangle pattern.\n\n“Beautiful ascending triangle breakout on OP after weeks of sideways/accumulation.\n\nAlso like how all the moves down from the highs are corrective three-wave moves.\n\nI think this one ends up making all-time highs.”\n\nOP is trading for $2.64 at time of writing, an 8.16% gain on the day and about a 190% increase since the start of the year.\n\nThe crypto strategist then turns his sights on the peer-to-peer payments network Litecoin (LTC).\n\n<PERSON>tracter says that he missed out on the opportunity to capitalize on the latest Bitcoin (BTC) rally because he made an error and sold during a period of low volatility, but now he’s looking to accumulate Litecoin as he believes the altcoin is flashing a bullish signal against the crypto king (LTC/BTC).\n\n“Missed this most recent BTC impulse as I got shook out during low [volatility] Easter break (rookie mistake). Now looking to chase alts with good BTC pair setups, and I think LTC is one of them. I think LTC/BTC is very close to a reversal and higher low on daily.”\n\nThe LTC/BTC pair is currently trading at 0.003148 ($95.64).\n\n<PERSON> Contracter then turns his attention toward the leading smart contract platform Ethereum. According to the trader, the fear surrounding the platform’s new Shanghai update, which now allows users to withdraw their staked ETH, is not warranted. He also believes the top altcoin is flashing vibes of Bitcoin’s price action in 2019 when the crypto king surged by about 300% in a few months.\n\n“Still think people are completely overreacting about the ETH Shanghai unlock and all I’m getting is 2019 vibes.”\n\nETH is trading for $2,095 at time of writing, a nearly 1.5% decrease during the last 24 hours.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nGenerated Image: Midjourney\n\nShutterstock/Sensvector"}, {"id": 38, "url": "https://news.google.com/rss/articles/CBMiaGh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ibG9ja3NlYy1sYXVuY2hlcy1jb2xsYWJvcmF0aXZlLXRlc3RpbmctdG9vbGtpdC1mb3ItcHJpdmF0ZS1mb3JrZWQtY2hhaW5z0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 15 Apr 2023 07:00:00 GMT", "title": "BlockSec launches collaborative testing toolkit for private forked chains - Cointelegraph", "content": "Blockchain security tech firm BlockSec has launched a new toolkit that enables collaborative testing on private chains “forked from arbitrary (transaction) positions” and block numbers on the Ethereum mainnet.\n\nThe developer and security researcher-focused toolkit is dubbed the “Phalcon Fork” and launched on April 14.\n\nWe're thrilled to introduce Phalcon Fork, a cutting-edge toolkit for Web3 developers & security researchers! It enables collaborative testing with private mainnet states, creating private chains forked from any mainnet position. #Web3 #DeFi pic.twitter.com/TlK169GeLP — BlockSec (@BlockSecTeam) April 14, 2023\n\nPhalcon Fork aims to provide greater control over work being conducted on testnets, such as transaction testing, analysis and debugging.\n\nIn the user manual, BlockSec touts that this extra control comes from being able to easily “fork arbitrary (transaction) positions and block numbers,” and retain certain “services and states” from the Ethereum mainnet.\n\n“Compared with traditional solutions like Goerli [...], Phalcon Fork has the following advantages: retain services and states from the mainnet, facilitating rapid integration and debugging with other DeFi contracts. [And maintain] full control over block information (e.g., Timestamp, BaseFee, MixDigest),” the Phalcon Fork user manual reads.\n\nWith the toolkit, users can also utilize features such as snapshots, enabling them to save certain blockchain positions and revert back to them at will during their testing processes. The snapshots essentially record the transactions being executed and deployed by the user at a given time.\n\n“The snapshot feature is particularly useful in the following two scenarios: When a user wants to run multiple times of a testing script, he/she just needs to revert to the original snapshot and rerun the script. [Or] When a user wants to save some states and return to them later, he/she can create a snapshot and then revert to this snapshot later,” the manual reads.\n\nPhalcon Fork also has an integrated faucet so users can acquire free fork network Ether (ETH) to conduct transactions on the private chains.\n\nTo directly interact with the chains and execute transactions, Phalcon Fork provides a remote procedure call node called Fork RPC, which can be integrated with Ethereum Virtual Machine-compatible development frameworks such as Hardhat, Foundry and Remix or added to MetaMask.\n\nAs it stands, users can only fork from the Ethereum mainnet; however, future support for additional blockchains, such as the BNB Smart Chain and Arbitrum, has been teased.\n\nTeased blockchain support additions. Source: Twitter\n\nApril has been a significant month for Ethereum developers, given that the highly anticipated Shapella hard fork went live on the Ethereum mainnet without a hitch on April 12. A major feature of the upgrade enables Ethereum validators to withdraw staked ETH from the Beacon Chain.\n\nRelated: Less than 1% of staked ETH estimated to be sold after Shapella: Finance Redefined\n\nThe move has been met with positive price action from Ether (ETH), with the asset gaining roughly 12% since April 12, to sit at $2,092 at the time of writing.\n\nSeven-day ETH price chart. Source: CoinGecko\n\nMagazine: ZK-rollups are ‘the endgame’ for scaling blockchains, Polygon Miden founder"}, {"id": 41, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vZGFpbHljb2luLmNvbS90ZXN0bmV0cy1jcnlwdG8tdGVzdC1uZXR3b3Jrcy10by1lYXJuLWNyeXB0b2N1cnJlbmN5L9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 15 Apr 2023 07:00:00 GMT", "title": "Testnets in Crypto: How To Use Test Networks to Earn Cryptocurrency - DailyCoin", "content": "Testnets are the unsung heroes of the crypto world. Every time a shiny new blockchain, cryptocurrency, or dApp goes live, it’s almost guaranteed that it has already existed for months on a test network.\n\nLike a shadowy vigilante, testnets expose hidden vulnerabilities and protect the cryptocurrency ecosystem. What many people don’t know about testnets is that there is a litany of benefits to using and exploring them, even if you’re not a developer.\n\nHow is something designed to be valueless so valuable?\n\nWhat are blockchain testnets? Why should everyone use them, and how can I get started?\n\nWhat are Testnets?\n\nA testnet is a valueless, low-risk sandbox environment similar to a main network. Blockchain engineers and smart contract developers use testnets to trial the functionality of their protocols and decentralized applications before deploying them on a mainnet or live blockchain.\n\nBecause a testnet is a separate ledger from its main network, all coins and tokens have no value. Regardless of the market price of Bitcoin (BTC), testnet bitcoin should always be worth $0. As a result, all transaction fees on a testnet are essentially free.\n\nThis is important for DeFi developers on networks like Ethereum (ETH), infamous for expensive gas fees. The Ethereum testnet enables developers to send thousands of transactions for free, which would normally cost tens of thousands of dollars.\n\nWhat Is the Purpose of Test Networks?\n\nThe main purpose of a testnet is to trail contracts and applications in a free environment. This helps developers ensure their applications run smoothly but also helps expose vulnerabilities and potential security issues.\n\nCybersecurity attacks in the crypto space are commonplace, with the on-chain world of DeFi and NFTs being particularly hazardous. White-hat hackers and security auditors use blockchain testnets to find potential faults before contracts get deployed on the mainnet, where an exploit could cost developers, and users like you and me, millions of dollars.\n\nThe cost of failure of a main network like Ethereum or XRP could be devastating. Hence, developers need a secure, valueless environment to make mistakes and break things safely before the public uses them.\n\nDo Testnet Coins Have Any Value?\n\nRemember when I said earlier that testnet coins have no value? The truth is, it’s not so black and white.\n\nDevelopers still need testnet coins, like Goerli Ethereum (gETH), to pay ‘transaction fees’ on the testnet. That means they still have some demand from developers testing decentralized applications.\n\nTestnet coins are collected from faucets, or free sites that ‘drip’ small amounts of test cryptocurrency into a testnet wallet. During periods of high demand, these faucets actually empty their supply of testnet coins, making them a hot commodity on the crypto market. For a brief moment, gETH was trading at over $1.\n\nIn February, a bridge opened between the Goerli Ethereum Testnet and the Ethereum Mainnet. Developers needing gETH can buy testnet Ethereum if the faucets are empty or unavailable.\n\nYou can’t trade gETH on a crypto exchange like Binance or Coinbase, but decentralized exchanges like UniSwap aren’t bound by the same restrictions as their centralized alternatives.\n\nThis was great news for developers and testnet faucet farmers who’ve been collecting gETH for free over time. In fact, one savvy player could even play a Cryptopunk NFT worth over $100,000 with gETH they accumulated for free.\n\nUltimately, testnet coins are intended to be free for everyone and have zero value on the market. That doesn’t mean that there is no demand and that testnet crypto is worthless.\n\nWhy You Should Use a Testnet, Even If You’re Not a Blockchain Developer\n\nEven if buying a Cryptopunk for free isn’t enough of an incentive, the non-developers out there might be thinking:\n\n‘That’s all very interesting, but what does that mean for me, a humble crypto enthusiast?’\n\nThere are plenty of benefits to using a testnet, even if you aren’t building dApps or designing blockchains. Many emerging crypto projects ask community members to trial products on a testnet in exchange for rewards, like token airdrops.\n\nFor example, the Aptos Incentivized Testnet gave anywhere between 150-300 APT tokens to selected users who operated a node validator or minted an NFT on their testnet. These tasks didn’t take long to complete, and participants could claim an airdrop worth over $5,000 at Aptos’ all-time high price.\n\nHow Do I Access A Testnet?\n\nThe good news is that accessing a blockchain testnet is easy. Metamask is a great open-source testnet wallet for any EVM (Ethereum Virtual Machine) network, like Polygon.\n\nAll you need to do is visit a trusted site like Chainlist and connect your wallet. Then, check the box to ‘Include Testnets’ and search for your chosen network, and the site will automatically configure your testnet account for you.\n\nWhich Blockchain Networks Have a Testnet?\n\nAlmost every blockchain in the cryptocurrency industry uses a testnet of some description. That includes leading Layer-1 networks like Solana and Cardano.\n\nSome networks, like Ethereum, have multiple different test networks for different areas of development. You must make sure you’re connecting to the right network for your specific testing requirements, or else you might run into some errors.\n\nOn the Flipside\n\nBlockchain testnets are an obscure area of the crypto industry. As a result, you may find the occasional scammer who will try and confuse you into sending your real crypto for valueless testnet coins.\n\nAs always, take extra care when exploring the possibilities of a blockchain testnet and never give anyone your crypto or seed phrase.\n\nWhy You Should Care\n\nEven if you’re not a developer, using a blockchain testnet can make you eligible for generous airdrops and give you a better understanding of how certain blockchains and decentralized applications work for free.\n\nFAQs"}]