[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiUGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtbmZ0LXNhbGVzLW92ZXJ0YWtlLWV0aGVyZXVtLTA2NTQ1Nzk5Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Solana NFT Sales Overtake Ethereum for the First Time Ever in December - Yahoo Finance", "content": "Solana NFT Sales Overtake Ethereum for the First Time Ever in December\n\nSolana's NFT sales volume surpassed that of Ethereum in December 2023, marking the first time this has occurred. According to data from CryptoSlam, Solana's NFT sales reached approximately $366.5 million, while Ethereum's sales totaled $353.2 million.\n\nWhen excluding wash trades, Solana's sales remained strong at $366.5 million, nearly matching its all-time high of $373.5 million set in October 2021. In contrast, Ethereum's sales were relatively flat, with December's total being comparable to November's $350 million.\n\nFurthermore, Solana saw an increased number of unique traders, with around 218,000 unique sellers and 279,000 buyers. That is nearly double the number of unique traders compared to Ethereum.\n\nThe surge in Solana's NFT sales may be attributed to the significant rise in the value of its native token, SOL, which has more than quadrupled in price over the past three months. This has also led to the resurgence of the Solana ecosystem, coupled with a memecoin frenzy led by Bonk (BONK).\n\nThe influx of users and transactions on Solana may be driven by broader momentum around the network and the hype surrounding specific projects offering potential airdrops and other perks. Projects like Tensorians and Mad Lads generated substantial trading volume in December, contributing to Solana's overall success.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiUGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtbmZ0LXNhbGVzLW92ZXJ0YWtlLWV0aGVyZXVtLTA2NTQ1Nzk5Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Solana NFT Sales Overtake Ethereum for the First Time Ever in December - Yahoo Finance", "content": "Solana NFT Sales Overtake Ethereum for the First Time Ever in December\n\nSolana's NFT sales volume surpassed that of Ethereum in December 2023, marking the first time this has occurred. According to data from CryptoSlam, Solana's NFT sales reached approximately $366.5 million, while Ethereum's sales totaled $353.2 million.\n\nWhen excluding wash trades, Solana's sales remained strong at $366.5 million, nearly matching its all-time high of $373.5 million set in October 2021. In contrast, Ethereum's sales were relatively flat, with December's total being comparable to November's $350 million.\n\nFurthermore, Solana saw an increased number of unique traders, with around 218,000 unique sellers and 279,000 buyers. That is nearly double the number of unique traders compared to Ethereum.\n\nThe surge in Solana's NFT sales may be attributed to the significant rise in the value of its native token, SOL, which has more than quadrupled in price over the past three months. This has also led to the resurgence of the Solana ecosystem, coupled with a memecoin frenzy led by Bonk (BONK).\n\nThe influx of users and transactions on Solana may be driven by broader momentum around the network and the hype surrounding specific projects offering potential airdrops and other perks. Projects like Tensorians and Mad Lads generated substantial trading volume in December, contributing to Solana's overall success.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 90, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9lbnMtdG9rZW4tc3Bpa2VzLTcwLXZpdGFsaWstMDYyMDAxOTI5Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "ENS Token Spikes by 70% After Vitalik Buterin's Post Calling it “Super Important” - Yahoo Finance", "content": "ENS Token Spikes by 70% After <PERSON><PERSON>erin’s Post Calling it “Super Important”\n\nThe governance token of the Ethereum Name Service (ENS), ENS, surged by as much as 72% on January 3 after Ethereum co-founder <PERSON><PERSON> mentioned it in a post on X. <PERSON><PERSON><PERSON> wrote that ENS is \"super important\" and emphasized the need for the service to remain accessible and affordable to all users of the Ethereum network, especially those on layer-2 networks.\n\n<PERSON><PERSON><PERSON> said:\n\n\"All L2s should be working on (trustless, merkle-proof-based) CCIP resolvers, so that we can have ENS subdomains registerable, updateable and readable directly on L2s.\"\n\n<PERSON><PERSON><PERSON>'s comments highlight the importance of ENS in the Ethereum ecosystem, as it provides a user-friendly way to interact with the network. ENS allows users to purchase a \".eth\" domain name, which can replace the complex string of letters and numbers that typically comprise a user's wallet address.\n\nShortly after <PERSON><PERSON><PERSON>'s tweet, the price of ENS surged from a yearly low of $8.50 to reach an eight-month high of $14.69, according to CoinMarketCap data. The price has since leveled out and is currently trading at around $13. The ENS token has been on a downward trend since reaching an all-time high of $74.25 in November 2021.\n\nENS is an essential part of the Ethereum ecosystem, and <PERSON><PERSON><PERSON>'s support is a major boost for the project. His comments underscore the importance of making ENS accessible and affordable to all users, particularly those on layer-2 networks.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vd3d3LmJ1c2luZXNzaW5zaWRlci5jb20vcGVyc29uYWwtZmluYW5jZS93aGF0LWlzLWNyeXB0b2N1cnJlbmN50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Cryptocurrency Explained: Definition & Examples of Crypto - Business Insider", "content": "Our experts answer readers' investing questions and write unbiased product reviews (here's how we assess investing products). Paid non-client promotion: In some cases, we receive a commission from our partners. Our opinions are always our own.\n\nCryptocurrencies are digital assets that are created and run on a blockchain.\n\nBitcoin and ether are two popular cryptocurrencies, but there are many others.\n\nInvesting in cryptocurrency can be extremely risky, and the underlying technology is very new.\n\nNEW LOOK Sign up to get the inside scoop on today’s biggest stories in markets, tech, and business — delivered daily. Read preview Thanks for signing up! Access your favorite topics in a personalized feed while you're on the go. download the app Email address Sign up By clicking “Sign Up”, you accept our Terms of Service and Privacy Policy . You can opt-out at any time.\n\nAdvertisement\n\nIt's important for investors to understand how cryptocurrencies work, who creates and controls them, and why you might want to buy cryptocurrencies.\n\nWhile there may be opportunities to build wealth, there are a lot of risks involved with crypto investing, and you need to be mindful of scams.\n\nWhat is cryptocurrency?\n\nCryptocurrency is a type of decentralized digital currency that investors can buy and sell along the blockchain. Unlike banknotes or minted coins that have a tangible physical form, cryptocurrencies can only be accessed using computers and other electronic devices.\n\nAdvertisement\n\nA decentralized currency is a currency not issued by a government or financial institution. In fact, no single person, company, or government controls a crypto's blockchain. Instead, it's run by a decentralized network of computers worldwide. Anyone with advanced technology skills and coding experience can create a cryptocurrency.\n\nThe lack of a central authority can also make cryptocurrencies more secure. \"It's hack-proof because there's no one central point of failure,\" explains <PERSON>, executive vice president at Publicis Sapient.\n\nHow do cryptocurrencies work?\n\nWhile there are thousands of cryptocurrencies, many with unique traits, they all tend to work in similar ways. It's hard to avoid some jargon when discussing cryptos, but the concepts can be relatively easy to understand.\n\nBlockchain technology\n\nA cryptocurrency's blockchain is a digital record of all the transactions involving that crypto. Copies of the blockchain are stored and maintained by computers around the world. They're often compared to general ledgers, part of traditional double-entry bookkeeping systems where each transaction leads to debit and credit in different sections of the books.\n\nAdvertisement\n\n\"It works like a general ledger — it's that simple,\" says Donovan. Perhaps you start with two coins and send one to someone. \"On the blockchain, it would say I'm sending you one coin, and I now have one coin, and you have one coin.\"\n\nEach grouping of transactions is turned into a block and chained to the existing ledger. Once a block is added it can't be reversed or altered — which is why people describe blockchains as \"immutable.\"\n\nSome cryptos have their own blockchain. For example, there are Bitcoin and Ethereum blockchains. But there are also cryptos that are built on top of an existing blockchain rather than starting from zero.\n\nEther is the cryptocurrency native to the Ethereum blockchain, but is also available for trading on other exchanges like Coinbase, Binance.US, and Robinhood.\n\nPublic transactions under pseudonymous\n\nCryptocurrencies have another defining feature. The blockchains are public ledgers, which means anyone can see and review the transactions that occurred. However, they can also provide a degree of anonymity.\n\nAdvertisement\n\n\"You have a private key, which is how you initiate transactions, and a public key, which is how someone identifies you in the market,\" says Donovan.\n\nA blockchain's transactions are tied to a crypto wallet's public key, but nobody necessarily knows who controls that wallet. This is why cryptos are often described as pseudonymous — the public key is a person's pseudonym.\n\nComing to consensus Cryptocurrencies commonly use one of two mechanisms to create a system of trust and determine which transactions are valid and added to their blockchain:\n\n\n\nProof of work . This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power.\n\n. This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power. Proof of stake. This is a newer and less energy-intensive mechanism. \"Proof of stake is they validate transactions on the blockchain by people putting value on the line,\" explains Parisi. \"They stake some of the currency they own to make sure they only validate true transactions.\"\n\nTypes of cryptocurrencies\n\nAccording to CoinMarketCap, there were more than 25,149 different cryptocurrencies with a global market value of about $1.16 trillion as of May 30, 2023. Some of the most popular cryptocurrencies include:\n\nBitcoin\n\nDogecoin\n\nEther\n\nLitecoin\n\nTether\n\nBinance coin\n\nDai\n\nTRON\n\nCronos\n\nUSD coin\n\nBitcoin cash\n\nAdvertisement\n\nBitcoin, the first cryptocurrency, was launched in 2009 as an alternative type of decentralized and digital money. Since then, people have also created cryptocurrencies that serve other functions or are designed for specific types of transactions.\n\n\"Cryptocurrencies can have many different uses,\" says Parisi. \"Some are used in gaming environments to earn rewards in a game, while others facilitate payments. Some are designed for cross-border remittances … some are designed for micro payments.\"\n\nFor example, stablecoins are a type of cryptocurrency that try to maintain a steady and fixed exchange rate with another asset, such as the US dollar. Governance tokens are another example of a specialized cryptocurrency. They give token holders voting power in a corresponding crypto project.\n\nInsider's Featured Crypto Apps Public Investing\n\nWealthfront Investing Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Editor's Rating 4.14/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Editor's Rating 4.34/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Learn More Start investing On Wealthfront's website\n\nWhat is digital currency?\n\nDigital currency is a type of currency that can only be accessed in an electronic form, such as through a computer or mobile phone. This money has no physical equivalent, unlike tangible forms of currency like banknotes or minted coins. But just like physical money, digital currencies can be used to purchase goods and services.\n\nAdvertisement\n\nHowever, you'll be limited to online platforms and communities, such as investing platforms, gaming sites, and gambling portals. Some of the most popular forms of digital currency include cryptocurrencies, central bank digital currencies (CBDC), and stablecoins.\n\n\"There's a strive toward decentralization,\" says Nisa Amoils, a managing partner at A100xx Ventures. \"Digital currencies like cryptocurrencies continue to be a worthwhile investment for many investors.\"\n\nDigital currencies come in two forms:\n\nCentralized currency: Currencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public.\n\nCurrencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public. Decentralized currency: Currencies not issued by governments or financial institutions. Instead, decentralized currencies operate through peer-to-peer financial networks to eliminate the middleman (aka banks) and allow lending, trading, and borrowing directly with merchants.\n\nAdvertisement\n\nDigital currencies like crypto are often appealing to investors who are wary of government-issued funds and are that are seeking alternatives.\n\n\"Some people who had been excluded from the traditional financial system, or have had their currencies devalued, are seeking an opportunity to participate in the markets, and this is a retail-driven phenomenon first,\" says Amoils. \"There's this crisis of trust, and people want wealth creation for themselves. And so that spurred this whole kind of trading speculative movement.\"\n\nHow to invest in cryptocurrency\n\nYou can start investing in cryptocurrencies through existing crypto exchanges and investing platforms. Some of the best cryptocurrency exchanges (such as Kraken and Coinbase) offer assets like staking rewards, goal-planning features, low fees, and more.\n\nSome of the best investment apps that offer cryptocurrencies (such as Robinhood Investing) include a range of investment types, low fees, market access, and more.\n\nAdvertisement\n\nYou can create your own crypto\n\nAnyone with coding skills and/or advanced technical knowledge can create their own cryptocurrencies — although this is not always an easy feat and isn't recommended for beginners. The three ways to create crypto are:\n\nBuilding a new blockchain: The most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more.\n\nThe most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more. Modifying a blockchain: If you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge.\n\nIf you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge. Building upon a blockchain: The simplest way to make your own coins or tokens is by expanding upon an already existing blockchain. But keep in mind that the success of your cryptocurrency will be reliant on the success of the original blockchain. Some blockchains that allow this are Binance and Ethereum.\n\nAre cryptocurrencies secure?\n\nThe blockchain technology behind cryptocurrencies can help ensure that the coins and systems remain secure. \"What's never been refuted is the value of blockchain,\" says Donovan. \"The way the ledger system is set up and every transaction is recorded. And the fact that it's immutable.\"\n\nHowever, that doesn't mean you don't need to worry about security. The crypto world is rife with scams. Of course, that's also true of traditional financial systems and currencies. Someone asking you to pay with a gift card or wire transfer is a red flag that you're dealing with a scammer. But several factors could make crypto scams especially worrisome.\n\nAdvertisement\n\nFor example, cryptocurrency transactions can't be reversed. There's also less regulation of cryptocurrencies and platforms than of traditional financial services in the US. Plus, some people may feel pressure to act quickly and send or invest their money because they're worried about missing out on an opportunity.\n\n\"One way to avoid a scam is to invest in more well-established cryptocurrencies,\" says Parisi. \"You still may be subject to scams or fraud in terms of how you hold it, send it, or receive it.\" But you can have some certainty that the cryptocurrency itself isn't a scam.\n\nAre cryptocurrencies a good investment?\n\nCryptocurrencies may present a good investment opportunity, and there are many ways to invest in the crypto world.\n\nYou could buy a coin (or coins) and hold onto them, hoping they'll increase in value. Or you could use your coins in a decentralized finance (DeFi) platform to earn interest through staking or lending. You also might take a more traditional route, such as an exchange-traded fund (ETF) that is tied to cryptocurrencies. There could even be opportunities to invest in projects or supporting industries rather than in the cryptocurrencies themselves.\n\nAdvertisement\n\n\"From an investment perspective, crypto is rapidly evolving,\" says Parisi. \"You shouldn't put an amount of assets you're not willing to lose. It should be, relatively speaking, a small portion of your portfolio.\"\n\nBefore making any investment, consider the potential pros and cons:\n\nPros Cons Easy to invest\n\nDiversify your portfolio\n\nThere is a lot of opportunity\n\nFaster and cost-effective transactions\n\nDecentralized currency\n\nSecurity and transparency through the blockchain Cryptocurrencies can be very volatile\n\nSome crypto projects may fail\n\nThe investment may be a scam\n\nEnvironmental impact due to excessive power consumption through ASIC computers\n\nLacking refund and cancellation policies\n\nShould you invest in crypto?\n\nWhile cryptocurrency investing is a hotly debated topic, it's worth understanding what's going on so you can make an informed decision. If you decide to get started, you could fully jump in or just dip your toe.\n\n\"Learn about crypto by opening up wallets, accounts, trading currencies, and learning more about the use cases,\" says Parisi. \"But do it in a reasonable way. We're still in the early days, and regulation of crypto is still evolving.\"\n\nAdvertisement\n\nDonovan suggests opening an account with a regulated and publicly traded company like Coinbase. But, he says, \"It's really about being smart and using the system to take baby steps.\"\n\nCrypto FAQs\n\nWhat is Bitcoin? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Bitcoin is a cryptocurrency, an electronic version of money that verifies transactions using cryptography (the science of encoding and decoding information). As Bitcoin educator, developer, and entrepreneur Jimmy Song says, Bitcoin is \"decentralized, digital, and scarce money.\" Bitcoin is decentralized because this code is run by thousands of computers (i.e., 'nodes') spread across the globe, digital because it exists as a set of code that determines how it operates, and scarce because its code caps its overall volume to 21 million bitcoins. When you use bitcoin to buy something, it records the transaction on a blockchain, which is essentially a ledger or database whose entries can't be modified or erased.\n\nWhat is Ethereum? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Ethereum is an open-source, decentralized computing platform network. The Ethereum network works like the Bitcoin network in that it's built on blockchain technology, essentially a digital public ledger where financial agreements can be verified and stored entirely by software — without the intervention of a third party.\n\nWhat are privacy coins? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Privacy coins are cryptocurrencies that obscure transactions on their blockchain to maintain the anonymity of users and their activity. Participants in a transaction will know the amount transacted and the parties involved. However, the same information will be unobtainable to any outside observer. The anonymity that privacy coins provide offers a potentially appealing outlet for money laundering or other criminal transactions. As such, privacy coins are a point of contention in the ongoing debate around cryptocurrency privacy and regulation.\n\nWhat is a crypto wallet? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. A crypto wallet is a software program or physical device that allows you to store your crypto and allow for the sending and receiving of crypto transactions. A crypto wallet consists of two key pairs: private keys and public keys. A public key is derived from the private key and serves as the address used to send crypto to the wallet. The important part of a wallet — and the part where new users often find themselves getting into trouble — is the private key. A private key is like the key to a safe deposit box. Anyone who has access to the private key of a wallet can take control of the balance held there. But unlike a safe deposit box, crypto users who hold their own private keys and make transactions using non-custodial wallets (i.e., a wallet not hosted by an exchange or other third-party) become their own bank.\n\nWhat is hash rate? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Hash rate is a measure of the total computational power being used by a proof-of-work cryptocurrency network to process transactions in a blockchain. It can also be a measure of how fast a cryptocurrency miner's machines complete these computations. Miners use computers to run computations on complex mathematical puzzles based on transaction data. These systems generate millions or trillions of guesses per second as to what the solutions to these puzzles could be. These are hashes, alphanumeric codes randomized to identify a single, unique piece of data.\n\nWhat is yield farming? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Yield farming is a means of earning interest on your cryptocurrency, similar to how you'd earn interest on any money in your savings account. And similarly to depositing money in a bank, yield farming involves locking up your cryptocurrency, called \"staking,\" for a period of time in exchange for interest or other rewards, such as more cryptocurrency.\n\nWhat is crypto staking? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Crypto staking is similar to depositing money in a bank, in that an investor locks up their assets, and in exchange, earns rewards, or \"interest.\" \"Staking is a term used to refer to the delegating of a certain number of tokens to the governance model of the blockchain and thus locking them out of circulation for a specified length of time,\" says Nicole DeCicco, the owner and founder of CryptoConsultz, a cryptocurrency consultancy in the Portland, Oregon area. A particular network's protocol locks up an investor's holdings — similar to depositing money in a bank, and agreeing not to withdraw it for a set time period, which benefits the network in a couple of ways, according to DeCicco. First, this can increase the value of a token by limiting the supply. Second, the tokens can be used to govern the blockchain if the network uses a proof-of-stake (PoS) system. A PoS system — as opposed to a proof-of-work (PoW) one, which incorporates \"mining\" — can be fairly complicated, especially for crypto newcomers."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vd3d3LmJ1c2luZXNzaW5zaWRlci5jb20vcGVyc29uYWwtZmluYW5jZS93aGF0LWlzLWNyeXB0b2N1cnJlbmN50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Cryptocurrency Explained: Definition & Examples of Crypto - Business Insider", "content": "Our experts answer readers' investing questions and write unbiased product reviews (here's how we assess investing products). Paid non-client promotion: In some cases, we receive a commission from our partners. Our opinions are always our own.\n\nCryptocurrencies are digital assets that are created and run on a blockchain.\n\nBitcoin and ether are two popular cryptocurrencies, but there are many others.\n\nInvesting in cryptocurrency can be extremely risky, and the underlying technology is very new.\n\nNEW LOOK Sign up to get the inside scoop on today’s biggest stories in markets, tech, and business — delivered daily. Read preview Thanks for signing up! Access your favorite topics in a personalized feed while you're on the go. download the app Email address Sign up By clicking “Sign Up”, you accept our Terms of Service and Privacy Policy . You can opt-out at any time.\n\nAdvertisement\n\nIt's important for investors to understand how cryptocurrencies work, who creates and controls them, and why you might want to buy cryptocurrencies.\n\nWhile there may be opportunities to build wealth, there are a lot of risks involved with crypto investing, and you need to be mindful of scams.\n\nWhat is cryptocurrency?\n\nCryptocurrency is a type of decentralized digital currency that investors can buy and sell along the blockchain. Unlike banknotes or minted coins that have a tangible physical form, cryptocurrencies can only be accessed using computers and other electronic devices.\n\nAdvertisement\n\nA decentralized currency is a currency not issued by a government or financial institution. In fact, no single person, company, or government controls a crypto's blockchain. Instead, it's run by a decentralized network of computers worldwide. Anyone with advanced technology skills and coding experience can create a cryptocurrency.\n\nThe lack of a central authority can also make cryptocurrencies more secure. \"It's hack-proof because there's no one central point of failure,\" explains <PERSON>, executive vice president at Publicis Sapient.\n\nHow do cryptocurrencies work?\n\nWhile there are thousands of cryptocurrencies, many with unique traits, they all tend to work in similar ways. It's hard to avoid some jargon when discussing cryptos, but the concepts can be relatively easy to understand.\n\nBlockchain technology\n\nA cryptocurrency's blockchain is a digital record of all the transactions involving that crypto. Copies of the blockchain are stored and maintained by computers around the world. They're often compared to general ledgers, part of traditional double-entry bookkeeping systems where each transaction leads to debit and credit in different sections of the books.\n\nAdvertisement\n\n\"It works like a general ledger — it's that simple,\" says Donovan. Perhaps you start with two coins and send one to someone. \"On the blockchain, it would say I'm sending you one coin, and I now have one coin, and you have one coin.\"\n\nEach grouping of transactions is turned into a block and chained to the existing ledger. Once a block is added it can't be reversed or altered — which is why people describe blockchains as \"immutable.\"\n\nSome cryptos have their own blockchain. For example, there are Bitcoin and Ethereum blockchains. But there are also cryptos that are built on top of an existing blockchain rather than starting from zero.\n\nEther is the cryptocurrency native to the Ethereum blockchain, but is also available for trading on other exchanges like Coinbase, Binance.US, and Robinhood.\n\nPublic transactions under pseudonymous\n\nCryptocurrencies have another defining feature. The blockchains are public ledgers, which means anyone can see and review the transactions that occurred. However, they can also provide a degree of anonymity.\n\nAdvertisement\n\n\"You have a private key, which is how you initiate transactions, and a public key, which is how someone identifies you in the market,\" says Donovan.\n\nA blockchain's transactions are tied to a crypto wallet's public key, but nobody necessarily knows who controls that wallet. This is why cryptos are often described as pseudonymous — the public key is a person's pseudonym.\n\nComing to consensus Cryptocurrencies commonly use one of two mechanisms to create a system of trust and determine which transactions are valid and added to their blockchain:\n\n\n\nProof of work . This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power.\n\n. This relies on people around the world, known as miners, competing to be first to solve complex cryptographic puzzles and add the next block to the blockchain. The winners are paid after the other members of the network confirm that the required amount of computing power was used to find the solution. \"The way you make sure all the participants are validating the transactions is the hard work, effort, and money they're spending solving the problem,\" says Donna Parisi, global head of financial services and FinTech at Shearman & Sterling. However, proof-of-work systems require a lot of energy to power. Proof of stake. This is a newer and less energy-intensive mechanism. \"Proof of stake is they validate transactions on the blockchain by people putting value on the line,\" explains Parisi. \"They stake some of the currency they own to make sure they only validate true transactions.\"\n\nTypes of cryptocurrencies\n\nAccording to CoinMarketCap, there were more than 25,149 different cryptocurrencies with a global market value of about $1.16 trillion as of May 30, 2023. Some of the most popular cryptocurrencies include:\n\nBitcoin\n\nDogecoin\n\nEther\n\nLitecoin\n\nTether\n\nBinance coin\n\nDai\n\nTRON\n\nCronos\n\nUSD coin\n\nBitcoin cash\n\nAdvertisement\n\nBitcoin, the first cryptocurrency, was launched in 2009 as an alternative type of decentralized and digital money. Since then, people have also created cryptocurrencies that serve other functions or are designed for specific types of transactions.\n\n\"Cryptocurrencies can have many different uses,\" says Parisi. \"Some are used in gaming environments to earn rewards in a game, while others facilitate payments. Some are designed for cross-border remittances … some are designed for micro payments.\"\n\nFor example, stablecoins are a type of cryptocurrency that try to maintain a steady and fixed exchange rate with another asset, such as the US dollar. Governance tokens are another example of a specialized cryptocurrency. They give token holders voting power in a corresponding crypto project.\n\nInsider's Featured Crypto Apps Public Investing\n\nWealthfront Investing Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Editor's Rating 4.14/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Editor's Rating 4.34/5 A five pointed star A five pointed star A five pointed star A five pointed star A five pointed star Learn More Start investing On Wealthfront's website\n\nWhat is digital currency?\n\nDigital currency is a type of currency that can only be accessed in an electronic form, such as through a computer or mobile phone. This money has no physical equivalent, unlike tangible forms of currency like banknotes or minted coins. But just like physical money, digital currencies can be used to purchase goods and services.\n\nAdvertisement\n\nHowever, you'll be limited to online platforms and communities, such as investing platforms, gaming sites, and gambling portals. Some of the most popular forms of digital currency include cryptocurrencies, central bank digital currencies (CBDC), and stablecoins.\n\n\"There's a strive toward decentralization,\" says Nisa Amoils, a managing partner at A100xx Ventures. \"Digital currencies like cryptocurrencies continue to be a worthwhile investment for many investors.\"\n\nDigital currencies come in two forms:\n\nCentralized currency: Currencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public.\n\nCurrencies issued by governments or financial institutions as part of the commercial banking system that are available to the general public. Decentralized currency: Currencies not issued by governments or financial institutions. Instead, decentralized currencies operate through peer-to-peer financial networks to eliminate the middleman (aka banks) and allow lending, trading, and borrowing directly with merchants.\n\nAdvertisement\n\nDigital currencies like crypto are often appealing to investors who are wary of government-issued funds and are that are seeking alternatives.\n\n\"Some people who had been excluded from the traditional financial system, or have had their currencies devalued, are seeking an opportunity to participate in the markets, and this is a retail-driven phenomenon first,\" says Amoils. \"There's this crisis of trust, and people want wealth creation for themselves. And so that spurred this whole kind of trading speculative movement.\"\n\nHow to invest in cryptocurrency\n\nYou can start investing in cryptocurrencies through existing crypto exchanges and investing platforms. Some of the best cryptocurrency exchanges (such as Kraken and Coinbase) offer assets like staking rewards, goal-planning features, low fees, and more.\n\nSome of the best investment apps that offer cryptocurrencies (such as Robinhood Investing) include a range of investment types, low fees, market access, and more.\n\nAdvertisement\n\nYou can create your own crypto\n\nAnyone with coding skills and/or advanced technical knowledge can create their own cryptocurrencies — although this is not always an easy feat and isn't recommended for beginners. The three ways to create crypto are:\n\nBuilding a new blockchain: The most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more.\n\nThe most advanced way to create crypto, but offers the most flexibility of nodes, architecture, tokenomics, and more. Modifying a blockchain: If you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge.\n\nIf you can't create your own blockchain, you can modify an existing blockchain's open-source code to your liking. Still, this method requires expansive technical knowledge. Building upon a blockchain: The simplest way to make your own coins or tokens is by expanding upon an already existing blockchain. But keep in mind that the success of your cryptocurrency will be reliant on the success of the original blockchain. Some blockchains that allow this are Binance and Ethereum.\n\nAre cryptocurrencies secure?\n\nThe blockchain technology behind cryptocurrencies can help ensure that the coins and systems remain secure. \"What's never been refuted is the value of blockchain,\" says Donovan. \"The way the ledger system is set up and every transaction is recorded. And the fact that it's immutable.\"\n\nHowever, that doesn't mean you don't need to worry about security. The crypto world is rife with scams. Of course, that's also true of traditional financial systems and currencies. Someone asking you to pay with a gift card or wire transfer is a red flag that you're dealing with a scammer. But several factors could make crypto scams especially worrisome.\n\nAdvertisement\n\nFor example, cryptocurrency transactions can't be reversed. There's also less regulation of cryptocurrencies and platforms than of traditional financial services in the US. Plus, some people may feel pressure to act quickly and send or invest their money because they're worried about missing out on an opportunity.\n\n\"One way to avoid a scam is to invest in more well-established cryptocurrencies,\" says Parisi. \"You still may be subject to scams or fraud in terms of how you hold it, send it, or receive it.\" But you can have some certainty that the cryptocurrency itself isn't a scam.\n\nAre cryptocurrencies a good investment?\n\nCryptocurrencies may present a good investment opportunity, and there are many ways to invest in the crypto world.\n\nYou could buy a coin (or coins) and hold onto them, hoping they'll increase in value. Or you could use your coins in a decentralized finance (DeFi) platform to earn interest through staking or lending. You also might take a more traditional route, such as an exchange-traded fund (ETF) that is tied to cryptocurrencies. There could even be opportunities to invest in projects or supporting industries rather than in the cryptocurrencies themselves.\n\nAdvertisement\n\n\"From an investment perspective, crypto is rapidly evolving,\" says Parisi. \"You shouldn't put an amount of assets you're not willing to lose. It should be, relatively speaking, a small portion of your portfolio.\"\n\nBefore making any investment, consider the potential pros and cons:\n\nPros Cons Easy to invest\n\nDiversify your portfolio\n\nThere is a lot of opportunity\n\nFaster and cost-effective transactions\n\nDecentralized currency\n\nSecurity and transparency through the blockchain Cryptocurrencies can be very volatile\n\nSome crypto projects may fail\n\nThe investment may be a scam\n\nEnvironmental impact due to excessive power consumption through ASIC computers\n\nLacking refund and cancellation policies\n\nShould you invest in crypto?\n\nWhile cryptocurrency investing is a hotly debated topic, it's worth understanding what's going on so you can make an informed decision. If you decide to get started, you could fully jump in or just dip your toe.\n\n\"Learn about crypto by opening up wallets, accounts, trading currencies, and learning more about the use cases,\" says Parisi. \"But do it in a reasonable way. We're still in the early days, and regulation of crypto is still evolving.\"\n\nAdvertisement\n\nDonovan suggests opening an account with a regulated and publicly traded company like Coinbase. But, he says, \"It's really about being smart and using the system to take baby steps.\"\n\nCrypto FAQs\n\nWhat is Bitcoin? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Bitcoin is a cryptocurrency, an electronic version of money that verifies transactions using cryptography (the science of encoding and decoding information). As Bitcoin educator, developer, and entrepreneur Jimmy Song says, Bitcoin is \"decentralized, digital, and scarce money.\" Bitcoin is decentralized because this code is run by thousands of computers (i.e., 'nodes') spread across the globe, digital because it exists as a set of code that determines how it operates, and scarce because its code caps its overall volume to 21 million bitcoins. When you use bitcoin to buy something, it records the transaction on a blockchain, which is essentially a ledger or database whose entries can't be modified or erased.\n\nWhat is Ethereum? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Ethereum is an open-source, decentralized computing platform network. The Ethereum network works like the Bitcoin network in that it's built on blockchain technology, essentially a digital public ledger where financial agreements can be verified and stored entirely by software — without the intervention of a third party.\n\nWhat are privacy coins? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Privacy coins are cryptocurrencies that obscure transactions on their blockchain to maintain the anonymity of users and their activity. Participants in a transaction will know the amount transacted and the parties involved. However, the same information will be unobtainable to any outside observer. The anonymity that privacy coins provide offers a potentially appealing outlet for money laundering or other criminal transactions. As such, privacy coins are a point of contention in the ongoing debate around cryptocurrency privacy and regulation.\n\nWhat is a crypto wallet? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. A crypto wallet is a software program or physical device that allows you to store your crypto and allow for the sending and receiving of crypto transactions. A crypto wallet consists of two key pairs: private keys and public keys. A public key is derived from the private key and serves as the address used to send crypto to the wallet. The important part of a wallet — and the part where new users often find themselves getting into trouble — is the private key. A private key is like the key to a safe deposit box. Anyone who has access to the private key of a wallet can take control of the balance held there. But unlike a safe deposit box, crypto users who hold their own private keys and make transactions using non-custodial wallets (i.e., a wallet not hosted by an exchange or other third-party) become their own bank.\n\nWhat is hash rate? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Hash rate is a measure of the total computational power being used by a proof-of-work cryptocurrency network to process transactions in a blockchain. It can also be a measure of how fast a cryptocurrency miner's machines complete these computations. Miners use computers to run computations on complex mathematical puzzles based on transaction data. These systems generate millions or trillions of guesses per second as to what the solutions to these puzzles could be. These are hashes, alphanumeric codes randomized to identify a single, unique piece of data.\n\nWhat is yield farming? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Yield farming is a means of earning interest on your cryptocurrency, similar to how you'd earn interest on any money in your savings account. And similarly to depositing money in a bank, yield farming involves locking up your cryptocurrency, called \"staking,\" for a period of time in exchange for interest or other rewards, such as more cryptocurrency.\n\nWhat is crypto staking? Chevron icon It indicates an expandable section or menu, or sometimes previous / next navigation options. Crypto staking is similar to depositing money in a bank, in that an investor locks up their assets, and in exchange, earns rewards, or \"interest.\" \"Staking is a term used to refer to the delegating of a certain number of tokens to the governance model of the blockchain and thus locking them out of circulation for a specified length of time,\" says Nicole DeCicco, the owner and founder of CryptoConsultz, a cryptocurrency consultancy in the Portland, Oregon area. A particular network's protocol locks up an investor's holdings — similar to depositing money in a bank, and agreeing not to withdraw it for a set time period, which benefits the network in a couple of ways, according to DeCicco. First, this can increase the value of a token by limiting the supply. Second, the tokens can be used to govern the blockchain if the network uses a proof-of-stake (PoS) system. A PoS system — as opposed to a proof-of-work (PoW) one, which incorporates \"mining\" — can be fairly complicated, especially for crypto newcomers."}]