[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tc2hhcmVzLWV0aGVyZXVtLXJvYWRtYXAtMDUyODQzMzk4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 01 Jan 2024 08:00:00 GMT", "title": "Vitalik Buterin Shares Ethereum Roadmap For 2024, Focuses on Six Key Components - Yahoo Finance", "content": "Vitalik Buterin Shares Ethereum Roadmap For 2024, Focuses on Six Key Components\n\nEthereum co-founder <PERSON><PERSON> has shared the Ethereum roadmap for 2024, outlining the project's continued focus on six main components.\n\n<PERSON><PERSON><PERSON> posted on X (formerly Twitter) an updated roadmap with annotations and diagrams, elaborating on each of the six elements: the Merge, the Surge, the Scourge, the Verge, the Purge and the Splurge.\n\n<PERSON><PERSON><PERSON> noted that as Ethereum's technical direction becomes clearer, there are only a handful of minor adjustments compared to the 2023 roadmap. The Merge, which integrated the Ethereum mainnet with the proof-of-stake (PoS) blockchain the Beacon Chain in September 2022, remains a key focus area. <PERSON><PERSON><PERSON> emphasized the importance of maintaining a simple and resilient PoS consensus.\n\nFollowing the Merge, the most significant observation on Ethereum was the transition from a power-intensive proof-of-work (PoW) consensus mechanism to PoS, leading to a major reduction in the network's overall energy consumption.\n\n<PERSON><PERSON><PERSON> also highlighted the developments in Ethereum's single-slot finality (SSF), which aims to ensure that changes to a blockchain block are irreversible without burning at least 33% of the total staked Ether (ETH).\n\n<PERSON><PERSON><PERSON> stated:\n\n\"The role of single slot finality (SSF) in post-Merge PoS improvement is solidifying. It's becoming clear that SSF is the easiest path to resolving a lot of the Ethereum PoS design's current weaknesses.\"\n\nButerin's roadmap also emphasizes the importance of rollups, zero-knowledge proofs, account abstraction, and second-generation privacy solutions, which align with the original cypherpunk principles of Ethereum.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tc2hhcmVzLWV0aGVyZXVtLXJvYWRtYXAtMDUyODQzMzk4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 01 Jan 2024 08:00:00 GMT", "title": "Vitalik Buterin Shares Ethereum Roadmap For 2024, Focuses on Six Key Components - Yahoo Finance", "content": "Vitalik Buterin Shares Ethereum Roadmap For 2024, Focuses on Six Key Components\n\nEthereum co-founder <PERSON><PERSON> has shared the Ethereum roadmap for 2024, outlining the project's continued focus on six main components.\n\n<PERSON><PERSON><PERSON> posted on X (formerly Twitter) an updated roadmap with annotations and diagrams, elaborating on each of the six elements: the Merge, the Surge, the Scourge, the Verge, the Purge and the Splurge.\n\n<PERSON><PERSON><PERSON> noted that as Ethereum's technical direction becomes clearer, there are only a handful of minor adjustments compared to the 2023 roadmap. The Merge, which integrated the Ethereum mainnet with the proof-of-stake (PoS) blockchain the Beacon Chain in September 2022, remains a key focus area. <PERSON><PERSON><PERSON> emphasized the importance of maintaining a simple and resilient PoS consensus.\n\nFollowing the Merge, the most significant observation on Ethereum was the transition from a power-intensive proof-of-work (PoW) consensus mechanism to PoS, leading to a major reduction in the network's overall energy consumption.\n\n<PERSON><PERSON><PERSON> also highlighted the developments in Ethereum's single-slot finality (SSF), which aims to ensure that changes to a blockchain block are irreversible without burning at least 33% of the total staked Ether (ETH).\n\n<PERSON><PERSON><PERSON> stated:\n\n\"The role of single slot finality (SSF) in post-Merge PoS improvement is solidifying. It's becoming clear that SSF is the easiest path to resolving a lot of the Ethereum PoS design's current weaknesses.\"\n\nButerin's roadmap also emphasizes the importance of rollups, zero-knowledge proofs, account abstraction, and second-generation privacy solutions, which align with the original cypherpunk principles of Ethereum.\n\nLet us know what you loved about this article, what could be improved, or share any other feedback by filling out this short form."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMie2h0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvYml0Y29pbi1jcm9zc2VzLSUyNDQ1ay1vbi1maXJzdC1kYXktb2YtMjAyNC1ldGhlcmV1bS1kb2dlY29pbi1zcGlrZS1hcy13ZWxsJTNBLWFuYWx5c3Qtc2F5c9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 01 Jan 2024 08:00:00 GMT", "title": "Bitcoin Crosses $45K On First Day Of 2024, Ethereum, Dogecoin Spike As Well: Analyst Says It's 'A Great Year' - Nasdaq", "content": "Bitcoin crossed the psychologically important $45,000 mark on the first day of 2024. The apex cryptocurrency saw an impressive 5.7% gain over 24 hours at the time of publishing. Ethereum and Dogecoin also saw gains of over 3% respectively in the same time frame.\n\nKing Crypto's move is significant since it is the first time since April 2022 that BTC has eclipsed the $45,000 mark.\n\nCryptocurrency Gains +/- Price (Recorded at 9.21 p.m. EST) Bitcoin (CRYPTO: BTC) +5.77% $44,992.09 Ethereum (CRYPTO: ETH) +3.54% $2,378.39 Dogecoin (CRYPTO: DOGE) +2.93% $0.093\n\nThe buzz surrounding the notification of spot Bitcoin ETFs fueled the rally as it did at the end of 2023. Reuters reported over the New Year weekend that asset managers, who are vying for approval of the ETFs, could be notified on Tuesday or Wednesday that their applications are approved with a possible launch date of Jan. 10.\n\nSee Also: ‘<PERSON>ecoin Killer’ Shiba Inu To Go Head-To-Head With Ethereum, And <PERSON><PERSON><PERSON>in Will Reach ‘New All-Time High’, Predicts Crypto Analyst\n\nThere are a total of 14 asset managers who are hoping that the SEC approval will come through, according to the report.\n\nIt should be noted that the cryptocurrency world is currently swirling with bold predictions for Bitcoin's trajectory. Financial guru <PERSON> anticipates the largest cryptocurrency to touch the $60,000 mark by 2024. <PERSON>i Trenchev has an even loftier $100,000 prediction in store for Bitcoin this year.\n\nOther Risk Assets:\n\nWhile cryptocurrency markets were decidedly in the green at the beginning of 2024, stock futures remained flat at the time of publishing. Vital Knowledge founder Adam Crisafulli noted in a recent appearance on CNBC that the markets were due for a 1-3% pullback in order to consolidate. He said such a move would be \"very healthy\" but noted that the rally could continue.\n\nA pullback would be healthy, but the market has further to run: Vital Knowledge's Adam Crisafulli https://t.co/PcIt0XY5U8\n\n— Vital Knowledge Media (@knowledge_vital) December 30, 2023\n\nTop Gainer (24-Hours)\n\nCryptocurrency Gains +/- Price (Recorded at 9.21 p.m. EST) Sei (SEI) +31.9% $0.77 Mina (MINA) +19.51% $1.62 The Graph (GRT) +18.46% $0.23\n\nAnalyst Takes:\n\nGiving his take on 2024, cryptocurrency trader Michaël van de Poppe called it a \"great year.\" He said spot ETF approval, halving and the beginning of the Altcoin bull market, and finally approval of an Ethereum spot ETF all await investors.\n\nThe year 2024:– #Bitcoin Spot ETF Approval– #Bitcoin Halving– The start of the #Altcoin bull market– #Ethereum Spot ETF Approval– Regulatory frameworks applied to stimulate institutional interest and adoption.A great year!\n\n— Michaël van de Poppe (@CryptoMichNL) January 1, 2024\n\nBitcoin maximalist Max Keiser reacted to Bitcoin hitting $45,000 by saying \"I told you so!\" while sharing a meme video featuring El Salvador President Nayib Bukele \"playing\" the drums in celebration.\n\n\"I told you so!\" #Bitcoin $45,000 pic.twitter.com/PNDhr4BdqD\n\n— Max Keiser (@maxkeiser) January 2, 2024\n\nAlpha Impact's co-founder Hayden Hughes said there is a fear of missing out on Bitcoin among some traders in the U.S. and Europe ahead of the approval of spot EFT. He said they have started \"buying on January 1, first thing New Year's morning,\" reported Bloomberg.\n\nPhoto by Igor Faun on Shutterstock\n\nRead Next: The Million Dollar Club: These Famous People Said That Bitcoin Will Reach $1M In 2023\n\n© 2024 Benzinga.com. Benzinga does not provide investment advice. All rights reserved.\n\nProfit with More New & Research. Gain access to a streaming platform with all the information you need to invest better today. Click here to start your 14 Day Trial of Benzinga Professional\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 35, "url": "https://news.google.com/rss/articles/CBMie2h0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvYml0Y29pbi1jcm9zc2VzLSUyNDQ1ay1vbi1maXJzdC1kYXktb2YtMjAyNC1ldGhlcmV1bS1kb2dlY29pbi1zcGlrZS1hcy13ZWxsJTNBLWFuYWx5c3Qtc2F5c9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 01 Jan 2024 08:00:00 GMT", "title": "Bitcoin Crosses $45K On First Day Of 2024, Ethereum, Dogecoin Spike As Well: Analyst Says It's 'A Great Year' - Nasdaq", "content": "Bitcoin crossed the psychologically important $45,000 mark on the first day of 2024. The apex cryptocurrency saw an impressive 5.7% gain over 24 hours at the time of publishing. Ethereum and Dogecoin also saw gains of over 3% respectively in the same time frame.\n\nKing Crypto's move is significant since it is the first time since April 2022 that BTC has eclipsed the $45,000 mark.\n\nCryptocurrency Gains +/- Price (Recorded at 9.21 p.m. EST) Bitcoin (CRYPTO: BTC) +5.77% $44,992.09 Ethereum (CRYPTO: ETH) +3.54% $2,378.39 Dogecoin (CRYPTO: DOGE) +2.93% $0.093\n\nThe buzz surrounding the notification of spot Bitcoin ETFs fueled the rally as it did at the end of 2023. Reuters reported over the New Year weekend that asset managers, who are vying for approval of the ETFs, could be notified on Tuesday or Wednesday that their applications are approved with a possible launch date of Jan. 10.\n\nSee Also: ‘<PERSON>ecoin Killer’ Shiba Inu To Go Head-To-Head With Ethereum, And <PERSON><PERSON><PERSON>in Will Reach ‘New All-Time High’, Predicts Crypto Analyst\n\nThere are a total of 14 asset managers who are hoping that the SEC approval will come through, according to the report.\n\nIt should be noted that the cryptocurrency world is currently swirling with bold predictions for Bitcoin's trajectory. Financial guru <PERSON> anticipates the largest cryptocurrency to touch the $60,000 mark by 2024. <PERSON>i Trenchev has an even loftier $100,000 prediction in store for Bitcoin this year.\n\nOther Risk Assets:\n\nWhile cryptocurrency markets were decidedly in the green at the beginning of 2024, stock futures remained flat at the time of publishing. Vital Knowledge founder Adam Crisafulli noted in a recent appearance on CNBC that the markets were due for a 1-3% pullback in order to consolidate. He said such a move would be \"very healthy\" but noted that the rally could continue.\n\nA pullback would be healthy, but the market has further to run: Vital Knowledge's Adam Crisafulli https://t.co/PcIt0XY5U8\n\n— Vital Knowledge Media (@knowledge_vital) December 30, 2023\n\nTop Gainer (24-Hours)\n\nCryptocurrency Gains +/- Price (Recorded at 9.21 p.m. EST) Sei (SEI) +31.9% $0.77 Mina (MINA) +19.51% $1.62 The Graph (GRT) +18.46% $0.23\n\nAnalyst Takes:\n\nGiving his take on 2024, cryptocurrency trader Michaël van de Poppe called it a \"great year.\" He said spot ETF approval, halving and the beginning of the Altcoin bull market, and finally approval of an Ethereum spot ETF all await investors.\n\nThe year 2024:– #Bitcoin Spot ETF Approval– #Bitcoin Halving– The start of the #Altcoin bull market– #Ethereum Spot ETF Approval– Regulatory frameworks applied to stimulate institutional interest and adoption.A great year!\n\n— Michaël van de Poppe (@CryptoMichNL) January 1, 2024\n\nBitcoin maximalist Max Keiser reacted to Bitcoin hitting $45,000 by saying \"I told you so!\" while sharing a meme video featuring El Salvador President Nayib Bukele \"playing\" the drums in celebration.\n\n\"I told you so!\" #Bitcoin $45,000 pic.twitter.com/PNDhr4BdqD\n\n— Max Keiser (@maxkeiser) January 2, 2024\n\nAlpha Impact's co-founder Hayden Hughes said there is a fear of missing out on Bitcoin among some traders in the U.S. and Europe ahead of the approval of spot EFT. He said they have started \"buying on January 1, first thing New Year's morning,\" reported Bloomberg.\n\nPhoto by Igor Faun on Shutterstock\n\nRead Next: The Million Dollar Club: These Famous People Said That Bitcoin Will Reach $1M In 2023\n\n© 2024 Benzinga.com. Benzinga does not provide investment advice. All rights reserved.\n\nProfit with More New & Research. Gain access to a streaming platform with all the information you need to invest better today. Click here to start your 14 Day Trial of Benzinga Professional\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiPmh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLWltcHJvdmVtZW50LXByb3Bvc2Fscy0yMDI00gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 01 Jan 2024 08:00:00 GMT", "title": "Ethereum Improvement Proposals to watch in 2024 - Blockworks", "content": "The latest Ethereum All Core Developers meeting finally put some tentative dates on the upgrade schedule for the next mainnet hard fork, Dencun.\n\nWith the caveat that it’s only “if no major issues arise,” Ethereum devs are eyeing the following dates for forking Ethereum’s public testnets:\n\n<PERSON>erli: Jan. 17\n\nSepolia: Jan. 30\n\n<PERSON><PERSON>: Feb. 7\n\nThis will be the last time that <PERSON><PERSON><PERSON> is included in the testing regimen, as the network is slated for deprecation.\n\nThey also discussed what comes next — the as-yet-un-nicknamed Prague/Electra upgrade. The Ethereum community is considering whether to focus on a large core feature — which could take a year of work — or structure the upgrade around multiple smaller improvements, which could be feasible for late 2024.\n\nA decision will come in the new year, but for now, here are some of the improvements to watch in 2024 in the wake of Dencun:\n\nEIP-4844 (Proto-Danksharding)\n\nThis is the big kahuna among Dencun EIPs, which were the focus of many news stories in 2023.\n\nRead more: Ethereum’s next upgrade to focus on blobs\n\nThe upgrade “will reduce the cost of data availability across all layer-2s,” StarkWare co-founder <PERSON> told Blockworks. “So that’s something that Starknet is very much anticipating so that users can have lower costs.”\n\nIt’s at the “forefront” of what <PERSON>, chief technology officer of Web3 wallet developer Su<PERSON>, calls “a year of groundbreaking improvements for Ethereum.”\n\n“[EIP-4844 is] a transformational enhancement” that will slash rollups gas fees “by up to 100x,” Henning told Blockworks.\n\nRead more: Core devs rule out Dencun fork this year\n\nAccount abstraction comes into its own\n\nAlso at the top of Henning’s mind are improvements taking advantage of account abstraction: ERC-4337 and its extension, ERC-6900.\n\nERCs are a subset of EIPs focusing specifically on token standards within the Ethereum ecosystem. They define rules for token implementations to ensure interoperability. Unlike some EIPs that modify the core protocol, ERCs typically don’t require a hard fork.\n\nERC-4337 went live in March and the concept of account abstraction “will play a pivotal role as the most significant changes for the end-user,” Henning said.\n\n“Account abstraction is set to revolutionize the way we perceive and interact with wallets, making gasless transactions the standard and secure social logins the new norm, fundamentally reshaping the Ethereum user experience,” he said.\n\nTraditionally, Ethereum has two types of accounts: externally owned accounts (EOAs) controlled by private keys and contract accounts controlled by their code. Account abstraction blurs this distinction, allowing users to create accounts that behave more like smart contracts.\n\nIt can enhance both user experience and security, and allows for more complex account logic, such as multisig wallets or social recovery of lost keys.\n\nERC-6900, introduced the concept of “delegated transactions.” This standard, which also did not require changes to Ethereum’s mainnet consensus, allows users to delegate the ability to send transactions on their behalf, for instance, to make one approval for a batch of actions to save time and hassle.\n\nEIP-1153 (transient storage opcodes)\n\nThis proposal, part of Dencun, aims to introduce a new mechanism for handling temporary or transient storage during smart contract execution.\n\nTraditional storage operations on Ethereum are permanent and consume gas. This can be inefficient for temporary data that doesn’t need to persist for longer than one transaction.\n\nEIP-1153 is an opcode (operational code) that would allow smart contracts to use transient storage — storage that would be wiped clean at the end of transaction execution.\n\nThe Uniswap team lobbied for 1153 to be included, and wanted it already in Shapella, but they couldn’t rally enough support to reach consensus among the core developers. The upgrade is expected to play a significant role in enhancing the capabilities and efficiency of Uniswap’s upcoming v4 protocol.\n\nRead more: What has Uniswap Labs cooked up for v4?\n\nBy enabling temporary storage, EIP-1153 can reduce the gas costs associated with storing data during contract execution and provide developers more flexibility in designing smart contracts.\n\nAnd by reducing the burden on permanent storage and minimizing state bloat, EIP-1153 can contribute to the overall scalability of the Ethereum network.\n\nEIP-4788 (Beacon block root commits)\n\nImagine Ethereum as a vast library with two main sections: the Ethereum Virtual Machine (EVM) section, which is like the reading room where people come to read books (execute smart contracts), and the Beacon Chain section, which is like the library’s catalog system, keeping track of all the books and their locations (consensus and coordination of the Ethereum network).\n\nBefore EIP-4788, these two sections functioned somewhat independently. The EVM section doesn’t have direct access to the up-to-date catalog; it has to rely on indirect methods to understand what’s happening in the Beacon Chain section.\n\nEIP-4788 proposes to put a “Beacon Block Root” (a summary or hash tree root of the parent block) into each EVM block.\n\nIt’s like moving from an outdated card filing system in a library — inefficient and sometimes inaccurate — to a system with a real-time, accurate and direct link to the main library database.\n\nIn this modern library, every time a new book is added, moved or removed (the Beacon Chain updates), the readers (EVM) have immediate and accurate information. Readers can trust they’re getting the most current information and the library operations (like executing smart contracts) are more aligned with the overall catalog system (the state of the consensus layer).\n\nAll this happens in a trust-minimized manner, eliminating the need for external oracles to provide this data, and thereby reducing potential points of failure or manipulation.\n\nThis change is particularly beneficial for liquid staking protocols such as Lido, smart contract-based bridges and restaking solutions, as it allows these protocols to access crucial data like validator balances and states directly from the consensus layer, enhancing their security and operational efficiency.\n\nEIP-4788 essentially introduces a protocol-level oracle, relaying Ethereum’s consensus state throughout the mainnet.\n\nMisha Komarov, founder of Nil Foundation, which is deploying a zkOracle for Lido, called it “definitely helpful.”\n\n“They need the consensus layer state root within their application logic (right now it is being proven by Casper FFG proof done via zkLLVM to the execution layer within the zkOracle design,” he told Blockworks.\n\nRead more: New Ethereum rollup takes a zero-knowledge approach to sharding\n\nEIP-5656 (MCOPY opcode)\n\nThe EVM operates using a set of opcodes that dictate various operations.\n\nEIP-5656 introduces a new opcode called MCOPY, which is proposed to optimize the process of copying data in memory during the execution of smart contracts.\n\nIn the current EVM architecture, copying large data segments can be inefficient and costly when using existing opcodes. MCOPY offers a more efficient way, one which is expected to reduce the gas fees associated with these operations, while improving performance.\n\nFaster memory operations mean quicker execution of contracts, and developers would have more tools at their disposal to optimize their smart contracts — particularly when dealing with large data structures or complex operations that involve memory manipulation.\n\nEIP-6780 (restrict SELFDESTRUCT)\n\nIn Ethereum, the SELFDESTRUCT opcode allows a smart contract to delete itself from the blockchain.\n\nWhen executed, it removes the contract’s code and storage from the state and sends the contract’s remaining ether to a specified address.\n\nHowever, this feature has led to several issues, including complexity in state management and potential security vulnerabilities.\n\nBy restricting SELFDESTRUCT, Ethereum can better manage its state size, leading to a more stable and predictable blockchain.\n\nThis is crucial for the network’s long-term scalability and maintenance, as it will simplify future Ethereum upgrades.\n\nDon’t miss the next big story – join our free daily newsletter."}]