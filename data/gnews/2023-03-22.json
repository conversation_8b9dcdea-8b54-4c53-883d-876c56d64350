[{"id": 10, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzAzLzIyLzYtZXRoZXJldW0tYWx0Y29pbnMtdGFyZ2V0ZWQtYnktc2VjLWFoZWFkLW9mLWNvaW5iYXNlLXdlbGxzLW5vdGljZS_SAWhodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wMy8yMi82LWV0aGVyZXVtLWFsdGNvaW5zLXRhcmdldGVkLWJ5LXNlYy1haGVhZC1vZi1jb2luYmFzZS13ZWxscy1ub3RpY2UvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "6 Ethereum Altcoins Targeted By SEC Ahead of Coinbase Wells Notice - The Daily Hodl", "content": "The U.S. Securities and Exchange Commission has specifically targeted several Ethereum-based altcoins on Coinbase before sending today’s Wells Notice to the leading crypto exchange.\n\nBack in July, in its insider trading case against a former Coinbase employee, the SEC called out nine crypto assets that it believes are securities – and six of those assets remain on the exchange today.\n\nThe coins in question are Amp (AMP), LCX (LCX), Power Ledger (POWR), Rally (RLY), XYO Network (XYO) and DerivaDAO (DDX).\n\nAt the time, Coinbase strongly rejected the claim that the above crypto assets are securities, citing its “rigorous” and “SEC-reviewed” process for determining which assets are listed on the exchange.\n\nToday, the SEC sent a Wells Notice to Coinbase, asserting the agency has made a preliminary determination that recommends the agency file an enforcement action against Coinbase.\n\nCoinbase says the notice targets an “undefined portion” of its listed digital assets, as well as the company’s staking service Coinbase Earn, its institutional arm Coinbase Prime, and its non-custodial Coinbase Wallet.\n\nThe exchange says it welcomes the opportunity to defend its products in court if need be, and notes it continues to operate as usual on all fronts.\n\nThe news comes amid a volatile day for Bitcoin and the crypto markets following a fresh quarter point rate hike from the Federal Reserve.\n\nBitcoin (BTC) is trading at $27,397 at time of publishing, down 2.7% in the last 24 hours.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/GrandeDuc"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiOGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3Rva2VuaXplZC1ob21lLW5ldHMtdGhvdXNhbmRz0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Atlanta Home Tokenized on Ethereum, Nets $214882 in Under 3 Minutes - Blockworks", "content": "With US single-family home values hovering in a middle-of-the-road range on a year-over-year basis, crypto companies are looking to carve out a slice of the US real estate pie — even as digital assets themselves remain deeply depressed over that same period.\n\nIn the latest instance, fintech and single family-focused sales company Roofstock has sold off a Georgia home to RealT via a tokenized Ethereum-based NFT, representatives for both companies told Blockworks on Tuesday. It marked the first collaboration between the two companies.\n\nThe first round of the transaction was facilitated by OpenSea, with RealT imposing “heavy restrictions” around the float of the initial fractionalization offering, according to <PERSON><PERSON>, the co-founder at RealT and one of two chief executives at the businesses. A second round will follow later.\n\nRead more: The Crypto Native’s Guide to Real Estate Investing\n\nIt’s not the first such instance of an NFT-enabled property sale for Roofstock, which has a Web3-focused division that aims to make inroads into the property sector.\n\nThe process is similar to Roofstock’s previous tokenized real estate sale of a home in Alabama last month.\n\n“RealT acquires properties sourced by Roofstock into C-Corps which then sell tokenized shares to investors,” the spokesperson said in an additional statement. The idea is to use the tokenized equities originated in the US to provide blockchain-based exposure to investors in other countries.\n\nA spokesperson disclosed metrics behind the fractionalization of the NFT by RealT:\n\n670 unique token holders snapped up a total of 722 orders\n\nThe blockchain transaction process took two minutes and 46 seconds\n\nThe average ETH-based purchase equated to about $298.\n\nThe partnership relies upon creating a separate Wyoming LLC tied to each single-family asset the company has available for sale in its inventory, which acts as a shell for a corresponding wallet holding the buyer’s NFT on Ethereum “that has [its] token ID associated with the smart contract,” said Sanjay Raghavan, vice president of Web3 initiatives at Roofstock.\n\n“The property in question can then be bought and sold on various NFT marketplaces, resulting in instantaneous sale and settlement,” the statement said.\n\nEach buyer holds a “soulbound” NFT, according to Raghavan, a non-transferrable token, meaning that the underlying blockchain mechanics are permissioned in nature.\n\nEach property NFT, as a result, “gets marked with a KYC flag,” Raghavan said, and the prospective owner “can go to OpenSea or any other marketplace where the asset is listed and purchase the NFT.”\n\nEther collected through RealT’s tokenization offering this week partially divides ownership of the property acquired for 218,000 USDC on OpenSea.\n\nThe process facilitating the sale of the Atlanta-area home — which has two bedrooms, 2.5 bathrooms and a total of 1,524 square-feet — is being billed as the beginning of a partnership between the two companies, which is kicking off at a time when US regulators are keeping a close eye on crypto. And when murmurs of a real estate bubble waiting to burst have been percolating.\n\nBoth executives said they’re putting in place measures to ensure compliance with the relevant US authorities as the outlook for digital asset oversight remains largely mixed.\n\n“Blackrock is saying tokenization is the future, and you can be sure the SEC is listening,” Jacobson said.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vZXhwbGFpbmVkL2V0aGVyZXVtLWFzLWEtZGVmbGF0aW9uYXJ5LWFzc2V0LWV4cGxhaW5lZNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Ethereum as a deflationary asset, explained - Cointelegraph", "content": "What is a deflationary cryptocurrency? Although cryptocurrencies are often promoted as investment opportunities, their primary purpose was originally to serve as an alternative form of currency. Considering this narrative, the rules of supply and demand apply to cryptocurrencies as to fiat currencies. An undergraduate economics student might say the basics of money, economy and market forces is balancing supply and demand. How much of an asset is in circulation versus the demand — how many people want that particular asset — helps decide its price. This equation between supply and demand underlies the fundamentals of all economies and also applies to cryptocurrencies. Deflationary cryptocurrency is one where the value of the crypto increases due to a reduction or stagnation in supply. This ensures that the coin’s market value is attractive for more people to invest in and can be used as a store of value. While deflationary cryptocurrencies look more attractive, not all are designed that way. Many well-known cryptocurrencies are not deflationary. In addition, there is often no supply limit to them. Some are disinflationary because inflation gradually reduces over time due to its tokenomics. Bitcoin (BTC), for instance, won’t be deflationary until all 21 million coins have been mined. Ether (ETH) was not deflationary until the “Merge” happened in September 2022. Related: Inflationary vs. deflationary cryptocurrencies, Explained\n\nHow does Ethereum fare against other deflationary tokens? Developers of tokens create deflationary mechanisms during the design of the economic model behind the token. The economic model — tokenomics — can be fundamental to how stakeholders add and accrue value in a Web3 ecosystem. The supply and demand dynamics of a token are decided at the level of development. Deflationary characteristics like burn mechanisms are decided as the economic model underlying the token is being developed. This can be a point-in-time process like with Bitcoin or an evolving mechanism like with Ethereum. When creating Bitcoin, Satoshi Nakamoto ensured there would only be a finite supply of 21 million. Once 21 million Bitcoin are mined, no new BTC can be created. This limited supply has helped the narrative that Bitcoin is a true store of value compared with fiat currencies that increase supply due to central bank monetary policies. In contrast, Ethereum had an inflationary supply at its inception. Ether supply was increasing at an annual rate of 4.5%. However, after the Ethereum Merge that saw it move from proof-of-work to proof-of-stake, it is now a non-inflationary asset due to its burn rate. The number of Ether burned in maintaining the network activity is more than the amount of Ether entering circulation. Implementing the EIP-1559 protocol has altered the economic nature of the Ethereum token by incorporating the burning of a fraction of the gas fees per transaction. As a result, some experts argue that Ethereum has become more deflationary than Bitcoin. As deflationary tokens are considered a better store of value, new tokens created for both protocol and application tiers may be designed to be deflationary.\n\nHas Ethereum’s transition to a deflationary token made it a more desirable asset? Investments in deflationary cryptocurrencies can yield growth and returns for investors. But being deflationary alone may not be a criterion to be identified as a better investment. Due to their supply cap, deflationary tokens are typically perceived as more valuable by holders and investors. This was also demonstrated by the rise of nonfungible tokens (NFTs), where the rarity of the NFTs often decided the prices. Limited supply driving prices higher was also true with the Ethereum Name Service (ENS), where some three-digit ENS names were sold for even more than 100 ETH. Ethereum may not necessarily be classified as a better asset after it became deflationary. Ethereum has a rich ecosystem that drives transactions on the chain, and as more Ether gets burned in the process, it causes deflation. An unused Ethereum blockchain wouldn’t be able to achieve this economic feat. The underlying chain fundamentals must remain strong for Ethereum to thrive as an investment. A chain with strong fundamentals typically has a developer ecosystem to create many applications that users widely adopt. As users flock to these applications, developers are encouraged to continue innovating. The resulting network effect would make Ethereum deflationary, making it a more attractive investment asset."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vZGVjcnlwdC5jby8xMjQxMDAvbWFraW5nLWNyeXB0by13YWxsZXRzLXNtYXJ0ZXItZXRoZXJldW3SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Making Ethereum Wallets Smarter Is the Next Challenge—and Visa Is Among Those Working on It - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nIt’s no secret that crypto wallets need a makeover, and fast. But as the pieces and players leading this makeover emerge, so too is a fight over what to call the effort.\n\nSome call it account abstraction, others call it EIP-4337.\n\n“We call it a smart wallet,\" <PERSON><PERSON> cofounder and CEO <PERSON><PERSON><PERSON> told Decrypt. \"It’s as simple as that.”\n\nAD\n\nAD\n\nArgent, along with Safe (formerly Gnosis Safe), is at the forefront of the account abstraction movement, making crypto wallets easier, and smarter, to use.\n\nThe story of account abstraction, or smart wallets, dates back as far as 2016 with Ethereum Improvement Proposal (EIP)-86, and there have been many other related EIPs along the way.\n\nIt's a movement that's attempting to change the current wallet standard, called Externally Owned Accounts (EOAs) like Metamask, to a smart contract-based one. Whenever you go about your crypto business, you currently need to sign your EOA wallet each and every time you execute a transaction. It's also risky, due to the unique demands of private key management.\n\nAccount abstraction provides far more flexibility for crypto wallets. Instead of individually signing 10 different transactions, for example, a smart contract wallet could batch all of these transactions into one click.\n\nThese new types of wallets could also mean adding features like recurring payments or, in the case of crypto, recovering lost private keys. <PERSON><PERSON> solved for this in 2018 with something called social recovery. <PERSON>et users would tap friends or family members to help get back their wallets back should they lose the keys.\n\nAD\n\nAD\n\nLike the smartphone, <PERSON>uisse and his team mean to bring crypto wallets out of the landline era and into the world of programmable money. Another analogy that the Argent chief uses is (ironically) when people first began moving the cash in their wallets into bank accounts.\n\n“Suddenly, there was a piece of software that could do stuff for us to make it easier to transfer or make it more secure,” he said.\n\nLike banking, smart wallets refer to just about anything that we can program our money and accounts to do without needing our intervention.\n\nVisa turns to smart wallets\n\nThe trend is also turning heads at payments giant Visa.\n\nIn a new deep dive report on self-custodial crypto wallets, Visa wrote that “Ethereum is designed for push payments” rather than the more intuitive and automated “pull payments.”\n\nThe former refers to manual payments made by the account holder and the latter to payments by which money leaves an account automatically, like auto-payments for student loans or paying your mortgage every month.\n\nSmart wallets would essentially usher in more of those programmable pull payments to crypto, and make it look a lot more like how we use money in the bank.\n\n“It's like blockchains took all these sensibilities away that we were fully expecting and using non-stop in the world. Blockchain sort of ripped off all that from the developer's toolbox,” StarkWare co-founder and CEO Uri Kolodny told Decrypt. “Account abstraction is saying, ‘guys, can we please have those very sensible tools back in our hands?’”\n\nAD\n\nAD\n\nKolodny’s team is behind StarkNet, a speedy layer-2 network built on Ethereum, and rolled out a wallet browser extension back in 2021 with Argent called Argent X.\n\nAs part of its tinkering in the smart wallet space, Visa also turned to the layer-2 solution to experiment with so-called delegable accounts.\n\n“The most beautiful thing about the Visa project is that we only became aware of it when they first posted their amazing research,” StarkWare co-founder and president Eli Ben-Sasson told Decrypt. “They looked around, and realized that they needed to start from a place both where they will have a global scale needed for their customer base and also has the account abstraction or, as we like to call it, smart wallets, out of the box.”\n\nA delegable account, per Visa’s research, would essentially integrate auto-payments into a crypto wallet and could come with a series of constraints, such as making the payments monthly or putting a limit on how much can be pulled from the wallet.\n\nBeyond the traditional use cases like those that Visa has in mind, smart wallets would also be supremely useful in the world of on-chain gaming.\n\n“If you play games, you probably wouldn't want to sign a transaction every five seconds, every time you are killing a player, winning new updates, and then paying for the transaction,” said Lesuisse. “With account abstraction, you can program what we call a ‘session key’ and say, ‘hey, I will let that game sign for me for the next hour.’”\n\nIt’s still early days, of course, and custodial wallets like Coinbase Wallet or the original crypto wallet MetaMask still dominate the space.\n\nBut as the idea of account abstraction (or simply smarter wallets) gains more attention among users as a viable alternative, the Web3 heavyweights could face some serious competition.\n\nAD\n\nAD"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL29wLWVkLXRoZS11bnNwb2tlbi1ldGhlcmV1bS1yZXZvbHV0aW9uLWFyZS1lb2FzLWJlY29taW5nLW9ic29sZXRlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Op-ed: The unspoken Ethereum revolution: Are EOAs becoming obsolete? - CryptoSlate", "content": "Under the radar, one of the most significant changes to the Ethereum ecosystem has been announced with little to no community reaction. Account abstraction is a core advancement in web3 account management, but the current roadmap brings a new goal — to remove Externally Owned Accounts (EOAs) entirely from the Ethereum ecosystem.\n\nThe term account abstraction refers to the process of abstracting away the complexity of a web3 account to create a more user-friendly experience for the end user. Initially, the goal was to generalize the web3 account model so that all accounts are treated similarly — regardless of whether they are EOAs or smart contract accounts. However, the Ethereum Foundation appears to have decided that there is no room for EOAs in the ecosystem’s future, favoring smart contract wallets as the default account model for users.\n\nEIP-4337 & Account Abstraction\n\nSecurity Fellow for the Ethereum Foundation, <PERSON><PERSON>, announced the launch of EIP-4337 while speaking at ETHDenver. The update to the Ethereum network upgrades the capabilities of smart contract wallets with elements of account abstraction, including decentralized bundlers, token fee payment, an alternative mempool, and other account abstraction features.\n\nEthereum co-founder <PERSON><PERSON> originally introduced the EIP in September 2021 when he shared a concept on the Ethereum message board with the following message:\n\n“An account abstraction proposal which completely avoids the need for consensus-layer protocol changes, instead relying on a separate mempool of UserOperation objects and miners running either custom code or a bundle marketplace.”\n\nHowever, one key aspect of EIP-4337 that has seemingly gone unnoticed by many is the move toward removing EOAs entirely. The documentation for the EIP on the Ethereum Foundation website states that a core motivation for the upgrade is to “completely remove any need at all for users to also have EOAs.”\n\n“Achieve the key goal of account abstraction: allow users to use smart contract wallets containing arbitrary verification logic instead of EOAs as their primary account. Completely remove any need at all for users to also have EOAs.”\n\nCryptoSlate reached out to several wallet providers, but none were willing to discuss the potential elimination of EOAs entirely, given the lack of timeframe from the Ethereum Foundation. As of press time, the Ethereum Foundation has not responded to attempts for comment.\n\nWhat is an EOA?\n\nAn EOA on Ethereum is a specific kind of account managed by a user who holds the private key, unlike a smart contract account. Essentially, an EOA serves as a user’s cryptographic identity on the Ethereum blockchain, enabling them to hold, send, and receive ETH, NFTs, or other tokens and interact with smart contracts.\n\nAn EOA is identified by a unique public address from its private key. Unlike a smart contract account, an EOA has no code or logic associated with it. However, it can still sign transactions to initiate transfers, deploy smart contracts, or interact with existing smart contracts on the Ethereum network.\n\nThe main distinction between an EOA and a smart contract account lies in their control. An EOA is managed by an external entity using a private key, while a smart contract account is controlled by the smart contract’s code and follows the rules specified within that code.\n\nDo we need EOAs?\n\nEOAs are the most tried and tested type of blockchain account. Popular software wallets such as MetaMask and hardware wallets like Ledger, Tezor, and SafePal are all founded in EOA accounts. Removing EOAs would dramatically impact such projects and require large-scale code updates.\n\nWhile the issue of onboarding new users into web3 — by requiring them to safely record and store a complex private key or long seed phrase — is a widely accepted problem, removing a core component of the Ethereum ecosystem poses a drastic solution to the problem.\n\nFurthermore, removing EOAs would bring countless potential issues that need to be addressed — including the loss of simplicity, increased complexity, higher transaction costs, compatibility issues, security concerns, EVM fragmentation, and even a potential decrease in adoption due to increased friction.\n\nI am not suggesting that all of the above issues are insurmountable. However, the path to removing EOAs will include problems that have yet to be conceived. Furthermore, as Ethereum sits at the heart of the web3 ecosystem, removing EOAs from the Ethereum network will likely lead to compatibility issues across the entire EVM landscape.\n\nProblems removing EOAs\n\nIn a bear market, it is easy to advocate for the use of smart contracts — which use, on average, more gas than EOAs due to the complex logic used in the execution of the code. As of press time, the cost of gas on Ethereum is 12 GWEI ($0.40), including the network base fee.\n\nThe chart below shows the change in the average gas price paid per transaction since the network was launched. Throughout the bull run 2021 – 2022, gas rose to a high of 305 GWEI and averaged around 120 GWEI, some ten times higher than it is now. Should EOAs be removed entirely, the cost of transacting on the Ethereum layer-1 blockchain network would almost certainly increase.\n\nHowever, the advancing progress of Ethereum scaling solutions — such as Polygon and dedicated industry-specific layer-2s like Immutable — will be even more vital to the network should transacting on the base layer become prohibitive.\n\nConcerning the other issues identified, the changing landscape of regulatory guidance also needs to be considered. Recently, the European Parliament passed an act on the Internet of Things (IoT) industry — requiring all smart contracts to contain a ‘kill switch’ and therefore include ‘proxy upgradeability.’ Article 30 of the legislation has the following requirement:\n\n“The deployment of smart contracts for others in the context of an agreement to make data available shall comply with the following essential requirements[…] Safe termination and interruption: ensure that a mechanism exists to terminate the continued execution of transactions: the smart contract shall include internal functions which can reset or instruct the contract to stop or interrupt the operation to avoid future (accidental) executions.”\n\nThis means that any smart contract wallet would have to contain a function that would allow the developer to remove the account — eliminating the self-sovereign nature of the account if this were implemented by anyone other than the account owner.\n\nFurthermore, if Ethereum moves away from EOAs entirely, any EVM chain would need to implement the same functionality — or risk losing compatibility with Ethereum Mainnet. The implementation across other chains would unlikely be synchronized, resulting in a fragmented ecosystem and potentially incompatible dApps.\n\nProjects that currently have full compatibility with multiple EVM chains could lose access to some networks during the transition.\n\nEOA Innovation\n\nSo why remove EOAs? The Ethereum Foundation appears to have given up the potential to innovate in the EOA space with the call to remove EOAs entirely. However, I advised a project called Intu in 2022 that is doing precisely this, and it is unlikely to be the only one. For total transparency, I was paid for my time advising the project, but I have no incentive for Intu to succeed other than believing in the team’s vision.\n\nThe point of this article is not to shill any solution or create FUD within the Ethereum ecosystem. Instead, I wish to raise awareness for this problem and facilitate debate and coordination within the space.\n\nI do not believe we should have statements from the Ethereum Foundation declaring the removal of EOAs without there first being a proper public process. Such a process would ensure that EOAs need to be removed, what the timeframe looks like, and how all potential security, compatibility, and usability issues would be solved prior to the transition.\n\nIt is also critical to emphasize that the removal of EOAs is not confirmed. Ethereum is a decentralized ecosystem without any centralized controlling party. However, the Ethereum Foundation holds a lot of sway within the developer community. Therefore, I believe it’s important to continue this conversation for the health of the ecosystem.\n\nI understand the Ethereum Foundation’s point of view. I simply wish for the conversation to be more openly held to ensure we move toward account abstraction with our eyes fully open. As Paul Saffo so wisely said, “Strong convictions, weakly held.”\n\nMentioned in this article"}]