[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiXGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL21hcmMtYW5kcmVlc3Nlbi1ldGhlcmV1bS1hbmQtd2ViMy1sZWFkLWJpdGNvaW4tb24tdGVjaC1pbm5vdmF0aW9u0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "<PERSON>: Ethereum and Web3 Lead Bitcoin on Tech Innovation - Blockworks", "content": "<PERSON>, best known for co-founding web browser Netscape and Silicon Valley venture capital fund a16z, is still invested in the crypto industry, even though his expectations around Bitcoin didn’t pan out.\n\nIn 2014, <PERSON><PERSON><PERSON> predicted that the world would be talking about Bitcoin the same way they did about the internet. The tech investor may have now changed his mind somewhat, but is still heavy on the industry as a whole.\n\nHe told Reason Magazine in an interview published Wednesday that, back then, he thought Bitcoin was a tech innovation that would develop to support several other applications just like the internet, but that it “basically stopped evolving.” He now has his sights set on Ethereum being at the core of transformation.\n\n“What happened was a bunch of other projects emerged and took that place, and the big one right now is Ethereum. I would either say Ethereum instead of Bitcoin, or I would just say crypto or Web3 instead of Bitcoin,” he said.\n\nAccording to the early Facebook investor, Web3 has all the features of the internet that people knew they wanted to have when the internet was originally built.\n\n“[Web3] is all the aspects of basically being able to do business and be able to have money and be able to do transactions and have trust,” he said. “We did not know how to use the internet to do that in the 90s. And now, with this technological breakthrough of the blockchain, we now know how to do that, we have the technological foundation to be able to do that.”\n\nA16Z launched a $4.5 billion crypto fund in 2022, which was touted as the largest-ever crypto-venture fund. The firm’s flagship crypto fund, launched in 2018, lost 40% in the first half of 2022 as they faced a battering. The VC’s portfolio counts projects like Alchemy, Aptos, Avalanche, Chia, Compound, Coinbase, Lido, Mysten Labs and Yuga Labs.\n\nAndreessen didn’t touch upon the bear market that lasted most of 2022, but said the potential of the industry is “extraordinarily high” and that there are “tonnes of really smart entrepreneurs” pursuing various opportunities.\n\n“A lot of those things have worked, some of those things haven’t worked yet, but I think that they’re going to work,” he said.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vY3J5cHRvLm5ld3MvY2hhcmxlcy1ob3NraW5zb24tZXRoZXJldW0tc3Rha2luZy1pcy1wcm9ibGVtYXRpYy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "<PERSON>: Ethereum staking is problematic - crypto.news", "content": "The Securities and Exchange Commission (SEC) is said to be considering a ban on crypto-staking services. In response, <PERSON> of Cardano voiced his opinion on the subject, training his eyes on Ethereum, whose staking model he said is problematic.\n\n<PERSON><PERSON><PERSON>: The locked stakes are a red flag\n\nAccording to <PERSON><PERSON><PERSON>, the fact that ethereum coins are locked in staking smart contracts that are completely inaccessible gives the impression that they are controlled goods.\n\n<PERSON><PERSON><PERSON> asserts that staking ethereum also presents many challenges. The temporary transfer of one’s assets to another party in exchange for compensation from that party resembles the sale of a regulated financial product. <PERSON> believes cutting and bonding could be more effective instead of this system.\n\nIn the response tweet to <PERSON>’s first article, he further explained that non-custodial liquid staking is analogous to the mining pools that the bitcoin community has been using for the last 13 years.\n\nEthereum staking is problematic. Temporarily giving up your assets to someone else to have them get a return looks a lot like regulated products. Slashing and bonds not so good. Non-custodial liquid staking on the other hand is like the mining pools we've used for 13 years — <PERSON> (@IOHK_Charles) February 9, 2023\n\nAccording to the head of <PERSON><PERSON>, his team tackled the issue of staking on the protocol to a model mostly governed by the community rather than a select group of users.\n\n<PERSON><PERSON><PERSON>, the doge founder, another renowned crypto personnel who remarked on <PERSON>’s thread, seemed somehow unsupportive of the ban and wondered why the government would let slip such a taxation opportunity bringing more weight and questions into the SEC’s alleged move.\n\nwhy don’t they want more easy tax money\n\n\n\nthe government is so weird — Shibetoshi Nakamoto (@BillyM2k) February 9, 2023\n\nWhat could this mean to the ecosystem?\n\nEven if the information is still considered a rumor, many involved in the business are noticeably getting ready for a real ban to be imposed by the market regulator.\n\nAccording to Hoskinson, the “basic misunderstanding about the real facts of operation and design” will cause the SEC to classify all staking products under the same umbrella even though they each have unique characteristics.\n\nHe added that although most staking protocols have opted to construct centralized rather than decentralized systems, it is a move that will ultimately be detrimental to the industry.\n\nThe SEC has been given the authority to regulate cryptocurrencies pending a change in the regulatory landscape in the United States. As a result, the commission has initiated several enforcement actions against many companies, including Ripple Labs Inc. and LBRY.\n\nFollow Us on Google News"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiMWh0dHBzOi8vdS50b2RheS9ldGhlcmV1bS10d28ta2V5LXVwZGF0ZXMtZm9yLTIwMjPSATVodHRwczovL3UudG9kYXkvZXRoZXJldW0tdHdvLWtleS11cGRhdGVzLWZvci0yMDIzP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "Ethereum: Two Key Updates for 2023 - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nAdvertisement\n\nThe Ethereum (ETH) network's Shanghai update is one of the most highly anticipated events on the crypto market. It promises to bring increased liquidity to the leading altcoin by unlocking ETH staked in the Beacon Chain. The expected release date for the Shanghai update is March 2023, marking the end of the Merge era.\n\nWhile the crypto community has some concerns about the potential selling pressure that could result from the update, it is important to note that the ETH release will occur gradually through a queue, potentially reducing the impact on the market.\n\nThe launch of the Beacon Chain enabled stakeholders to validate blocks and secure the ETH network while maintaining the predictability of security during The Merge, the biggest update on the cryptocurrency market in 2022.\n\nIn addition to the Q1 hard fork, the ETH network will also undergo two major upgrades later in 2023. These updates will further enhance the leading altcoin's position on the market.\n\nThe Surge\n\nEthereum creator <PERSON><PERSON> has stated that the primary focus of the upcoming update is to address the scalability challenges faced by the smart contract platform. The update will accomplish this by fragmenting the ETH database, enabling the network to scale while maintaining its decentralization.\n\nTo achieve this, the ETH network's information will be split horizontally, allowing it to distribute the load and reduce storage costs and hardware requirements. It is important to note that this update will not immediately result in a scalable Ethereum blockchain. Instead, EIP 4844 will create a separate blockchain for Layer 2, which will aid scalability solutions such as Arbitrum and Optimism in lowering their transaction fees and becoming truly competitive with other ETH competitors with lower transfer fees.\n\nEOF\n\nThe upcoming EOF (EVM Object Format) update will bring improvements to Ethereum's code execution environment, making the cryptocurrency platform more efficient and streamlining the process of creating smart contracts. EOF will also facilitate updates to the EVM and create a system to track resource usage of new contracts.\n\nThese upgrades bring Ethereum closer to its goal of becoming a large, decentralized computer that supports dApps with a user-friendly interface, low fees and a secure network for daily transactions.\n\nIt is important to note that updates in the cryptocurrency world are made through proposed improvements that are subject to change at any time."}, {"id": 39, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9vbmNoYWluLW1vbmtleS1uZnRzLWRvdWJsZS1wcmljZS0wMDAyMDU0MjMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "OnChain Monkey NFTs Double in Price After Creators Put Them on Bitcoin - Yahoo Finance", "content": "Buzz around Bitcoin-based NFTs—via the recently-launched Ordinals protocol—keeps growing by the day, with record-setting mints on Thursday and high-value sales over the past day. And one established Ethereum NFT collection is reaping the benefits of going multi-chain, seeing its prices surge after revealing Bitcoin-based counterparts.\n\nOnChain Monkey, a collection of 10,000 Ethereum NFT profile pictures (PFPs) minted in 2021, used Ordinals to “inscribe” all of its existing artwork on Bitcoin over the past day. Now the NFT holders on Ethereum can also say that their respective collectibles live on Bitcoin, as well.\n\nPrices for the Ethereum NFTs have almost tripled since the announcement, with the floor price—that is, the cheapest listed NFT on a marketplace—for the project jumping from 0.79 ETH at the start of the day (per NFT Price Floor) to a peak of 1.75 ETH before settling to about 1.5 ETH (nearly $2,500) as of this writing.\n\nOnChainMonkey is the first 10K collection to be inscribed on Bitcoin via Ordinals! Own an ETH OCM Genesis = Owning a BTC OCM Genesis All on-chain on Bitcoin on Feb 8, 2023, and all on-chain on Ethereum on Sept 11, 2021! 🔥 🧵/8 pic.twitter.com/AMuUlERJZM — OnChainMonkey (@OnChainMonkey) February 9, 2023\n\nAccording to data from CryptoSlam, the move has fueled a 12,200% increase in NFT trading volume for the Ethereum project over the past 24 hours compared to the previous span. The analytics platform reports about $1.1 million in sales over the past day for a project with lifetime secondary sales totaling almost $39 million.\n\nMetagood, the startup behind OnChain Monkey, said it put all 10,000 NFTs onto Bitcoin via the Ordinals protocol using a single transaction, much as it did for the original Ethereum collection back in 2021.\n\nStory continues\n\nThe Hottest Bitcoin NFTs Right Now Are CryptoPunks Clones\n\nIn a Twitter Spaces today, Metagood co-founder Danny Yang said that enabling trading was the next step for the team but suggested that other tooling needs to be created around Ordinals to facilitate that feature. He also noted that Metagood plans to build a bridge between Ethereum and Bitcoin to let NFT holders switch between the two versions.\n\n“They are the same on both chains,” an OnChain Monkey Discord moderator wrote earlier today. “Buy on ETH and you will have access to the BTC version when the tools catch up.”\n\nThe project’s Discord server is filling up with users who claim to have purchased one of the Ethereum NFTs following the Bitcoin announcement and are asking for details on how it will work.\n\nOnChain Monkey is part of Metagood’s push to use Web3 initiatives to fund programs that benefit communities. These include efforts to fund coral restoration and provide aid to Ukraine amid the Russian invasion. Metagood, co-founded by venture capitalist Bill Tai alongside Yang and Amanda Terry, raised $5 million in December.\n\nMetagood says that it minted the first 10,000 NFT project on Ordinals—a claim that the creators of Bitcoin Punks, a clone of popular Ethereum project CryptoPunks, have also made.\n\nBitcoin Punks began inscribing its NFTs via Ordinals before OnChain Monkey, but opted to put each Punk as a separate inscription—a process that stretched from Wednesday into Thursday. OnChain Monkey, on the other hand, committed its entire collection to Ordinals via a single transaction late Wednesday.\n\nEditor's note: This article was updated after publication to clarify when OnChain Monkey and Bitcoin Punks were inscribed via Ordinals."}, {"id": 47, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9vbmNoYWluLW1vbmtleS1uZnRzLWRvdWJsZS1wcmljZS0wMDAyMDU0MjMuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Feb 2023 08:00:00 GMT", "title": "OnChain Monkey NFTs Double in Price After Creators Put Them on Bitcoin - Yahoo Finance", "content": "Buzz around Bitcoin-based NFTs—via the recently-launched Ordinals protocol—keeps growing by the day, with record-setting mints on Thursday and high-value sales over the past day. And one established Ethereum NFT collection is reaping the benefits of going multi-chain, seeing its prices surge after revealing Bitcoin-based counterparts.\n\nOnChain Monkey, a collection of 10,000 Ethereum NFT profile pictures (PFPs) minted in 2021, used Ordinals to “inscribe” all of its existing artwork on Bitcoin over the past day. Now the NFT holders on Ethereum can also say that their respective collectibles live on Bitcoin, as well.\n\nPrices for the Ethereum NFTs have almost tripled since the announcement, with the floor price—that is, the cheapest listed NFT on a marketplace—for the project jumping from 0.79 ETH at the start of the day (per NFT Price Floor) to a peak of 1.75 ETH before settling to about 1.5 ETH (nearly $2,500) as of this writing.\n\nOnChainMonkey is the first 10K collection to be inscribed on Bitcoin via Ordinals! Own an ETH OCM Genesis = Owning a BTC OCM Genesis All on-chain on Bitcoin on Feb 8, 2023, and all on-chain on Ethereum on Sept 11, 2021! 🔥 🧵/8 pic.twitter.com/AMuUlERJZM — OnChainMonkey (@OnChainMonkey) February 9, 2023\n\nAccording to data from CryptoSlam, the move has fueled a 12,200% increase in NFT trading volume for the Ethereum project over the past 24 hours compared to the previous span. The analytics platform reports about $1.1 million in sales over the past day for a project with lifetime secondary sales totaling almost $39 million.\n\nMetagood, the startup behind OnChain Monkey, said it put all 10,000 NFTs onto Bitcoin via the Ordinals protocol using a single transaction, much as it did for the original Ethereum collection back in 2021.\n\nStory continues\n\nThe Hottest Bitcoin NFTs Right Now Are CryptoPunks Clones\n\nIn a Twitter Spaces today, Metagood co-founder Danny Yang said that enabling trading was the next step for the team but suggested that other tooling needs to be created around Ordinals to facilitate that feature. He also noted that Metagood plans to build a bridge between Ethereum and Bitcoin to let NFT holders switch between the two versions.\n\n“They are the same on both chains,” an OnChain Monkey Discord moderator wrote earlier today. “Buy on ETH and you will have access to the BTC version when the tools catch up.”\n\nThe project’s Discord server is filling up with users who claim to have purchased one of the Ethereum NFTs following the Bitcoin announcement and are asking for details on how it will work.\n\nOnChain Monkey is part of Metagood’s push to use Web3 initiatives to fund programs that benefit communities. These include efforts to fund coral restoration and provide aid to Ukraine amid the Russian invasion. Metagood, co-founded by venture capitalist Bill Tai alongside Yang and Amanda Terry, raised $5 million in December.\n\nMetagood says that it minted the first 10,000 NFT project on Ordinals—a claim that the creators of Bitcoin Punks, a clone of popular Ethereum project CryptoPunks, have also made.\n\nBitcoin Punks began inscribing its NFTs via Ordinals before OnChain Monkey, but opted to put each Punk as a separate inscription—a process that stretched from Wednesday into Thursday. OnChain Monkey, on the other hand, committed its entire collection to Ordinals via a single transaction late Wednesday.\n\nEditor's note: This article was updated after publication to clarify when OnChain Monkey and Bitcoin Punks were inscribed via Ordinals."}]