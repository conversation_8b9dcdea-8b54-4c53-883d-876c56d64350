[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLWRlbmN1bi11cGdyYWRlLXByb2dyZXNzZXPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 28 Sep 2023 07:00:00 GMT", "title": "Devnet, testnet, mainnet: Ethereum's next upgrade progresses — slowly - Blockworks", "content": "Say hello to <PERSON><PERSON><PERSON>, a new Ethereum testnet that successfully went live today after a configuration problem prevented the network from launching two weeks ago on the 1-year anniversary of the Merge.\n\n<PERSON><PERSON><PERSON> is intended to replace Ethereum’s <PERSON><PERSON><PERSON> testnet — Ethereum’s validator and staking-focused testnet — which will be retired in 2024.\n\nA minor bug reported on today’s all-core-developers call, affecting some Prysm Ethereum clients, was not enough to derail the launch, according to <PERSON><PERSON><PERSON>, a software engineer at the Ethereum Foundation. Validator participation has been climbing in the hours since, <PERSON><PERSON> said, expected to reach 90% by Friday.\n\nGoerli is still expected to be used for the Dencun upgrade, featuring EIP-4844, on its way to mainnet release.\n\nBut before that step, the upgrade has been running a gauntlet of developer networks — or devnets.\n\nCore developers discussed the imminent launch of Devnet-9, which will debut “Friday or early next week” according to project coordinator <PERSON>. That’s slightly delayed from the planned Sept. 27 start date projected on the previous developer call.\n\nThe Dencun upgrade initially aimed for a hard fork date in the fourth quarter of this year. However, due to the slower-than-expected progress of the Devnets, a 2023 hard fork now seems unlikely.\n\nLoading Tweet..\n\n<PERSON><PERSON> told his colleagues on the call, “we probably need a Devnet 10 too,” though it’s expected to be “a shorter-lived Devnet.”\n\nThe new goal is for the first testnet launch to occur prior to November’s Devconnect conference, taking place in Istanbul in mid-November.\n\nIf all goes smoothly, the mainnet could follow as early as January 2024.\n\n<PERSON>’t miss the next big story – join our free daily newsletter."}, {"id": 64, "url": "https://news.google.com/rss/articles/CBMivwFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtMjgtc2VwdGVtYmVyLTIwMjMvYXJ0aWNsZXNob3cvMTA0MDA4NzQzLmNtc9IBwwFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtMjgtc2VwdGVtYmVyLTIwMjMvYW1wX2FydGljbGVzaG93LzEwNDAwODc0My5jbXM?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 28 Sep 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin rises above $26,400; Ethereum holds $1,600 level - The Economic Times", "content": "\n\n\n\n\n\n\n\nThe crypto tokens were trading mixed in Thursday's trade. Bitcoin, Ethereum, Toncoin , and Solana were trading with gains, while XRP , BNB, Cardano, Dogecoin , and Litecoin were trading with cuts.Bitcoin (BTC) rose 0.7% to $26,419, whereas Ethereum (ETH) was above the 1,600 level. Meanwhile, the global cryptocurrency market cap was trading higher, around $1.06 trillion, rising 0.6% in the last 24 hours.BTC has managed to maintain its position above the $26,000 level, even as the S&P 500 experienced a significant drop to a three-month low and the US dollar index (DXY) reached a new year-to-date high. This relatively stable performance is viewed as a positive sign, indicating a lack of aggressive selling at lower price levels, said ZebPay Trade Desk.In today's trade, Solana, Toncoin, and Ethereum rose up to 3%, while XRP, Litecoin, and Cardano declined up to 1%.The total volume in DeFi is currently $2.72 billion, 9.4% of the total crypto market 24-hour volume. The volume of all stablecoins is now $27.16 billion, which is 94.09% of the total crypto market 24-hour volume.Bitcoin's dominance is currently 48.99%, according to CoinMarketCap. Meanwhile, BTC volume stood at approximately $12.49 billion, falling 71% in the last 24 hours.\"Bitcoin continues to consolidate above $26,000 even as its trade volumes are at a 5-year low. Long-term investors, including MicroStrategy, are buying at these prices which augurs well for the asset leading into October, traditionally a green month for BTC. Key resistance level at $27,500 is the next target to breach to give momentum to a rally,\" said Vikram Subburaj, CEO of Giottus Crypto Platform.Shubham Hudda, Senior Manager at CoinSwitch Markets Desk, said, \"Bitcoin, in the last 24 hours, showed a small pump, breaking over $26.5k for a brief period before facing resistance at its 50-day Exponential Moving Average (50EMA). The markets are calling for a $30k level for BTC soon. However, a support level at $26k has to be maintained to expect an upside movement in the short term.\"BTC's Exponential Moving Average for a 10-day EMA indicates 'Sell' at 26417 and a 200-day EMA indicates 'Sell' at 26946. The Simple Moving Average for 10-day SMA indicates 'Sell' at 26555 and 200-day SMA indicates 'Sell' at 27979.The Relative Strength Index (14) sits at 48, with a neutral outlook. The Stochastic %K (14, 3, 3) at 26 and the Average Directional Index (14) at 12 indicate a Neutral outlook.The MACD Level (12, 26), at -61, indicates 'Buy'. The Stochastic RSI Fast (3, 3, 14, 14) is 'Neutral' at 13 and William's Percentage is 'Neutral' at -74.(Disclaimer: Recommendations, suggestions, views and opinions given by the experts are their own. These do not represent the views of The Economic Times)"}, {"id": 66, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vd3d3LmJlbnppbmdhLmNvbS9tb25leS9ob3ctdG8tYnV5LXdyYXBwZWQtZXRoZXJldW0td2V0aNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 28 Sep 2023 07:00:00 GMT", "title": "How to Buy Wrapped Ethereum (WETH) - Benzinga", "content": "Want to jump straight to how to buy Wrapped Ethereum (WETH)? You can buy ETH on eToro, Webull or Robinhood and swap it for WETH.\n\nAre you looking to make an offer on the non-fungible token (NFT) marketplace OpenSea? Or do you want to stake WETH in a yield farming platform? To do so, you will first need to buy WETH, the wrapped version of Ether (WETH). Wrapped ETH (WETH) has a wider range of use cases than ETH.\n\nget started Limited Time Offer: Trade $100, Get $10 Buy $100 worth of crypto and get a $10 bonus! get started Disclosure: eToro supports the following currencies: BTC, ADA, DASH, DOGE, EOS, ETH, LTC, NEO, XLM, XTZ, TRX, ZEC. eToro USA LLC; Investments are subject to market risk, including the possible loss of principal. T&Cs apply. *The bonus is available to Benzinga readers in the US for open states only. Served by eToro USA LLC. Show More\n\nWhat is the Difference Between WETH and ETH?\n\nEther (ETH) is the native token of the Ethereum blockchain. If you are looking to use the ERC-20 standard of ETH, you may need to turn your ETH into WETH. This guide shows you how to buy Wrapped Ethereum (WETH).\n\nWhat is WETH?\n\nWrapped Ethereum (WETH) is an ERC-20 token pegged to Ethereum’s native token ETH. The ERC-20 token standard allows for the capability of smart contracts on the Ethereum blockchain. A smart contract is a computer program that enforces the terms of an agreement. ERC-20 tokens allow for all kinds of things. Since both are pegged to Ethereum, ETH and WETH have the same price. If you want to access a service paid for using an ERC-20 token, you need to wrap your ETH. Some platforms automatically swap it for you as a first step in the transaction, but in other cases, you need to do it yourself. WETH is the tradable version of ETH and is frequently used on decentralized finance (DeFi) and decentralized application (dApp) platforms.\n\nBrief History of WETH\n\nWETH launched in 2017 and was pegged to ETH. ETH is the second largest cryptocurrency in the world and may even contest Bitcoin for the top spot. You can check out ETH’s price history here.\n\nETH is unable to be exchanged directly with ERC-20 tokens that live on the Ethereum blockchain. To exchange ETH for these tokens you first wrap it, creating WETH. One of the main reasons WETH was created in the first place was to use dApps in the Ethereum ecosystem.\n\nHow to Buy WETH\n\nBefore you learn how to buy WETH, you need ETH.\n\nStep 1: Open an Online Account.\n\nStep 1: Open an Online Account\n\nThe first step in getting WETH is to open an account on a crypto exchange to purchase ETH. To open an account, you’ll need to provide things such as:\n\nYour full legal name\n\nYour address\n\nYour phone number and email address\n\nA government-issued ID (includes driver’s license, passport or military ID)\n\nA password of your choice\n\nDepending on the exchange, you may need to provide more personal info. It's a good idea to have the information ready to go when opening an account. Most exchanges allow you to open an account in as little as a few minutes; however, some exchanges may take longer to verify your ID. Once your identity has been verified, you’re ready to begin investing.\n\nPurchase ETH. Being the second largest cryptocurrency by market cap, many popular crypto trading platforms support Ethereum. Some of your best options include eToro, Webull and Robinhood Markets Inc. (NASDAQ: HOOD).\n\nStep 2: Buy or Download a Wallet (Optional).\n\nTo obtain WETH, you’ll need a wallet that supports the token. You can choose from two main types of crypto wallets: hardware wallets and software wallets. Software wallets are often easier and faster to use but are less secure than their hardware counterparts. Some of the best options for software wallets on the market right now are the Exodus wallet and MetaMask. Then, you will be able to obtain WETH by connecting your wallet to a decentralized exchange (DEX) like Uniswap and swapping your ETH for WETH in a liquidity pool.\n\nBest Hardware Wallet: Ledger\n\nLedger is one of the top hardware wallets on the market. It is easy to use and supports sending and receiving WETH. Once you have purchased a Ledger hardware wallet, you download its software onto your computer and create an account.\n\nYou’ll be assigned a wallet address (typically a long string of letters and numbers), and then you can send and receive thousands of cryptocurrencies through the wallet. Hardware wallets offer an extra level of security as opposed to software wallets, but they cost money, while software wallets are typically free. Additionally, Ledger provides the convenience of connecting to software wallets such as MetaMask to check your account balance while maintaining absolute custody of your wallet address.\n\nBest Software Wallet: MetaMask\n\nBest For: Ethereum wallet securely through MetaMask's website Get Started with MetaMask\n\nMetaMask is a free and widely used online wallet for Ethereum. It’s available as an extension on Google Chrome, Mozilla Firefox, Brave and Microsoft Edge browsers. It also has an app on iOS and Android phones.\n\nAllows you to swap ETH for WETH directly in the wallet.\n\nClick the wallet extension icon in upper right-hand of Google Chrome.\n\nClick Swap.\n\nSwap as much ETH for WETH as you'd like to.\n\nStep 3: Swap for WETH Through a DEX\n\nLaunch Uniswap.\n\nConnect your wallet with the “Connect Wallet” button in the upper right-hand corner.\n\nSelect ETH on the top and WETH on the bottom as shown below.\n\nEnter the desired amount of ETH you'd like to swap for WETH.\n\nPress Wrap, complete the transaction in the wallet window and in a few seconds your WETH should show up in your wallet\n\nNote: If you don't see your WETH after the swap goes through, you may need to add the token to MetaMask (or whichever wallet you are using). To do this all you need to do is scroll down in the Assets tab in the MetaMask window and click import tokens. You can either search for WETH there or copy and paste the token address from a site like CoinMarketCap.\n\nTrade, Sell or Convert Your ETH to WETH\n\nDeciding on whether to trade, sell or convert your crypto is based on your personal goals and preferences. If you are looking to take profits on WETH, a common strategy is to sell a portion of your WETH on the way up and keep a portion to hold long term to eliminate some risk. Most exchanges allow you to sell your crypto for cash or trade it for USDC, a stablecoin pegged to the U.S. dollar. If you are trying to accumulate more crypto long term, taking profits when crypto prices go up could allow you to accumulate more when prices go back down.\n\nBy following the steps above, you can convert your ETH to WETH using Uniswap. If you are looking to place an offer on OpenSea, you will need to convert your ETH to WETH. If you want to sell your WETH for cash, it's easiest to first convert it back to ETH.\n\nCurrent Crypto Prices\n\nSince reaching all-time highs in November 2021, crypto prices have dropped. In November 2021, Bitcoin saw its all-time high of over $69,000, and Ethereum saw its all-time high of over $4,800. However, in June of 2022, Bitcoin and Ethereum hit multi-year lows. Bitcoin went to $17,601.58, and Ethereum reached $883.15. In 2023, both tokens have been able to rally, putting Bitcoin around $25,000 and Ethereum around $1,700. Crypto prices seem to be holding on macro levels.\n\nIs WETH a Good Investment?\n\nWETH can be a good investment if you are looking to have tradable ETH to do things like make bids on OpenSea. However, since it is pegged to the price of ETH, its price will follow suit. To read more about if ETH is a good investment please visit here.\n\nClaim Now Get up to $200 in rewards with Coinbase! Coinbase users can earn up to $200 on average just by taking advantage of our rewards. Claim Now Disclosure: ²Sum of median estimated savings and rewards earned, per user in 2021 across multiple Coinbase programs (excluding sweepstakes). This amount includes fee waivers from Coinbase One (excluding the subscription cost), rewards from Coinbase Card, and staking rewards. ³Crypto rewards is an optional Coinbase offer. Upon purchase of USDC, you will be automatically opted in to rewards. If you’d like to opt out or learn more about rewards, you can click here. The rewards rate is subject to change and can vary by region. Customers will be able to see the latest applicable rates directly within their accounts Show More"}, {"id": 63, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vd3d3LmJlbnppbmdhLmNvbS9tb25leS9ob3ctdG8tYnV5LXdyYXBwZWQtZXRoZXJldW0td2V0aNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 28 Sep 2023 07:00:00 GMT", "title": "How to Buy Wrapped Ethereum (WETH) - Benzinga", "content": "Want to jump straight to how to buy Wrapped Ethereum (WETH)? You can buy ETH on eToro, Webull or Robinhood and swap it for WETH.\n\nAre you looking to make an offer on the non-fungible token (NFT) marketplace OpenSea? Or do you want to stake WETH in a yield farming platform? To do so, you will first need to buy WETH, the wrapped version of Ether (WETH). Wrapped ETH (WETH) has a wider range of use cases than ETH.\n\nget started Limited Time Offer: Trade $100, Get $10 Buy $100 worth of crypto and get a $10 bonus! get started Disclosure: eToro supports the following currencies: BTC, ADA, DASH, DOGE, EOS, ETH, LTC, NEO, XLM, XTZ, TRX, ZEC. eToro USA LLC; Investments are subject to market risk, including the possible loss of principal. T&Cs apply. *The bonus is available to Benzinga readers in the US for open states only. Served by eToro USA LLC. Show More\n\nWhat is the Difference Between WETH and ETH?\n\nEther (ETH) is the native token of the Ethereum blockchain. If you are looking to use the ERC-20 standard of ETH, you may need to turn your ETH into WETH. This guide shows you how to buy Wrapped Ethereum (WETH).\n\nWhat is WETH?\n\nWrapped Ethereum (WETH) is an ERC-20 token pegged to Ethereum’s native token ETH. The ERC-20 token standard allows for the capability of smart contracts on the Ethereum blockchain. A smart contract is a computer program that enforces the terms of an agreement. ERC-20 tokens allow for all kinds of things. Since both are pegged to Ethereum, ETH and WETH have the same price. If you want to access a service paid for using an ERC-20 token, you need to wrap your ETH. Some platforms automatically swap it for you as a first step in the transaction, but in other cases, you need to do it yourself. WETH is the tradable version of ETH and is frequently used on decentralized finance (DeFi) and decentralized application (dApp) platforms.\n\nBrief History of WETH\n\nWETH launched in 2017 and was pegged to ETH. ETH is the second largest cryptocurrency in the world and may even contest Bitcoin for the top spot. You can check out ETH’s price history here.\n\nETH is unable to be exchanged directly with ERC-20 tokens that live on the Ethereum blockchain. To exchange ETH for these tokens you first wrap it, creating WETH. One of the main reasons WETH was created in the first place was to use dApps in the Ethereum ecosystem.\n\nHow to Buy WETH\n\nBefore you learn how to buy WETH, you need ETH.\n\nStep 1: Open an Online Account.\n\nStep 1: Open an Online Account\n\nThe first step in getting WETH is to open an account on a crypto exchange to purchase ETH. To open an account, you’ll need to provide things such as:\n\nYour full legal name\n\nYour address\n\nYour phone number and email address\n\nA government-issued ID (includes driver’s license, passport or military ID)\n\nA password of your choice\n\nDepending on the exchange, you may need to provide more personal info. It's a good idea to have the information ready to go when opening an account. Most exchanges allow you to open an account in as little as a few minutes; however, some exchanges may take longer to verify your ID. Once your identity has been verified, you’re ready to begin investing.\n\nPurchase ETH. Being the second largest cryptocurrency by market cap, many popular crypto trading platforms support Ethereum. Some of your best options include eToro, Webull and Robinhood Markets Inc. (NASDAQ: HOOD).\n\nStep 2: Buy or Download a Wallet (Optional).\n\nTo obtain WETH, you’ll need a wallet that supports the token. You can choose from two main types of crypto wallets: hardware wallets and software wallets. Software wallets are often easier and faster to use but are less secure than their hardware counterparts. Some of the best options for software wallets on the market right now are the Exodus wallet and MetaMask. Then, you will be able to obtain WETH by connecting your wallet to a decentralized exchange (DEX) like Uniswap and swapping your ETH for WETH in a liquidity pool.\n\nBest Hardware Wallet: Ledger\n\nLedger is one of the top hardware wallets on the market. It is easy to use and supports sending and receiving WETH. Once you have purchased a Ledger hardware wallet, you download its software onto your computer and create an account.\n\nYou’ll be assigned a wallet address (typically a long string of letters and numbers), and then you can send and receive thousands of cryptocurrencies through the wallet. Hardware wallets offer an extra level of security as opposed to software wallets, but they cost money, while software wallets are typically free. Additionally, Ledger provides the convenience of connecting to software wallets such as MetaMask to check your account balance while maintaining absolute custody of your wallet address.\n\nBest Software Wallet: MetaMask\n\nBest For: Ethereum wallet securely through MetaMask's website Get Started with MetaMask\n\nMetaMask is a free and widely used online wallet for Ethereum. It’s available as an extension on Google Chrome, Mozilla Firefox, Brave and Microsoft Edge browsers. It also has an app on iOS and Android phones.\n\nAllows you to swap ETH for WETH directly in the wallet.\n\nClick the wallet extension icon in upper right-hand of Google Chrome.\n\nClick Swap.\n\nSwap as much ETH for WETH as you'd like to.\n\nStep 3: Swap for WETH Through a DEX\n\nLaunch Uniswap.\n\nConnect your wallet with the “Connect Wallet” button in the upper right-hand corner.\n\nSelect ETH on the top and WETH on the bottom as shown below.\n\nEnter the desired amount of ETH you'd like to swap for WETH.\n\nPress Wrap, complete the transaction in the wallet window and in a few seconds your WETH should show up in your wallet\n\nNote: If you don't see your WETH after the swap goes through, you may need to add the token to MetaMask (or whichever wallet you are using). To do this all you need to do is scroll down in the Assets tab in the MetaMask window and click import tokens. You can either search for WETH there or copy and paste the token address from a site like CoinMarketCap.\n\nTrade, Sell or Convert Your ETH to WETH\n\nDeciding on whether to trade, sell or convert your crypto is based on your personal goals and preferences. If you are looking to take profits on WETH, a common strategy is to sell a portion of your WETH on the way up and keep a portion to hold long term to eliminate some risk. Most exchanges allow you to sell your crypto for cash or trade it for USDC, a stablecoin pegged to the U.S. dollar. If you are trying to accumulate more crypto long term, taking profits when crypto prices go up could allow you to accumulate more when prices go back down.\n\nBy following the steps above, you can convert your ETH to WETH using Uniswap. If you are looking to place an offer on OpenSea, you will need to convert your ETH to WETH. If you want to sell your WETH for cash, it's easiest to first convert it back to ETH.\n\nCurrent Crypto Prices\n\nSince reaching all-time highs in November 2021, crypto prices have dropped. In November 2021, Bitcoin saw its all-time high of over $69,000, and Ethereum saw its all-time high of over $4,800. However, in June of 2022, Bitcoin and Ethereum hit multi-year lows. Bitcoin went to $17,601.58, and Ethereum reached $883.15. In 2023, both tokens have been able to rally, putting Bitcoin around $25,000 and Ethereum around $1,700. Crypto prices seem to be holding on macro levels.\n\nIs WETH a Good Investment?\n\nWETH can be a good investment if you are looking to have tradable ETH to do things like make bids on OpenSea. However, since it is pegged to the price of ETH, its price will follow suit. To read more about if ETH is a good investment please visit here.\n\nClaim Now Get up to $200 in rewards with Coinbase! Coinbase users can earn up to $200 on average just by taking advantage of our rewards. Claim Now Disclosure: ²Sum of median estimated savings and rewards earned, per user in 2021 across multiple Coinbase programs (excluding sweepstakes). This amount includes fee waivers from Coinbase One (excluding the subscription cost), rewards from Coinbase Card, and staking rewards. ³Crypto rewards is an optional Coinbase offer. Upon purchase of USDC, you will be automatically opted in to rewards. If you’d like to opt out or learn more about rewards, you can click here. The rewards rate is subject to change and can vary by region. Customers will be able to see the latest applicable rates directly within their accounts Show More"}, {"id": 47, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9laWdlbmxheWVyLXNyZWVyYW0ta2FubmFuLWhvdC1yaXNreS0xNTEwNDU4OTcuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 28 Sep 2023 07:00:00 GMT", "title": "EigenLayer's <PERSON><PERSON><PERSON> on the Hot (and Risky) Ethereum Trend of 'Restaking' - Yahoo Finance", "content": "<PERSON><PERSON><PERSON> was a professor at the University of Washington, Seattle, when he started working for its blockchain research lab in 2017.\n\nIt was there while working at the lab that he founded his company, EigenLabs, the organization behind EigenLayer – a blockchain protocol considered a pioneer in a just-now-arriving trend in the Ethereum ecosystem known as restaking. The idea is to repurpose ETH tokens staked on the Ethereum blockchain for double duty, using them to provide security for other applications.\n\nThis article is featured in the latest issue of The Protocol, our weekly newsletter exploring the tech behind crypto, one block at a time. Sign up here to get it in your inbox every Wednesday.\n\nEigenLayer has been in the news lately, as the restaking ecosystem starts to take shape. Some top Ethereum figures have also raised flags about the risks; Ethereum co-founder <PERSON><PERSON> at one point cautioned that the new feature could ultimately pose systemic risks to the main blockchain’s stability.\n\nWe spoke with <PERSON><PERSON><PERSON> last week, where we asked him questions about EigenLayer and restaking. Some highlights include:\n\nOn <PERSON><PERSON><PERSON>’s response to <PERSON><PERSON><PERSON>’s concerns about staking: “Anything that restaking can do, already liquid staking can do.”\n\nOn Ethereum’s plan to slow the rate of new validators coming on line, via the EIP-7514 proposal: “This is a super important thing for Ethereum to be conservative and not have an overflow.”\n\nOn whether Ethereum will ever reach its maximum capacity for shared security: “There's absolutely a limit.”\n\nQ: I saw <PERSON><PERSON>’s blog post about overloading the consensus layer, and how restaking, in his view, could pose systemic risks to Ethereum. I'm curious to hear your take on his take?\n\nKannan: One of the things I think he wants to kind of lay out is that, “Hey don't externalize, and don't create something that, assuming that if the protocol goes wrong, Ethereum is going to fork around it.”\n\nI think that is a pretty reasonable position from Ethereum, that you build protocols and the protocols have to internalize social consensus rather than externalize it to Ethereum.\n\nStory continues\n\nSo I read it as to not overload Ethereum social consensus, which is used only for forking the chain. And don't assume that you can build a protocol that, and because you're too big to fail, Ethereum can fork around that. So that's how I read it.\n\nAnd I think it's a pretty obvious statement in our view. But I think it has to be said, somebody has to say it, so it's good that Vitalik went out and said it.\n\nBecause what we don't want is for calls to deploy code that is not properly audited, doesn't have internal security controls, and then the Ethereum community has to now work hard to figure out how to retrieve it.\n\nI think a lot of people after reading the article have been talking a lot about restaking risks.\n\nI want to make it super clear: anything that restaking can do, already liquid staking can do, so I view restaking as a lesser risk than liquid staking.\n\nQ: Can you expand on that?\n\nKannan: Basically, you can take a liquid staking token and then deposit it into complex DeFi protocols, or you could just deposit it into validating a new layer 2, or a new oracle or any of these things.\n\nSo anything that restaking can do, liquid staking can already do. Because you know, you have the LSD [short for liquid staking derivative] token, and you can do anything with it. And one particular thing you could do with that is, of course, go and validate another network.\n\nSo I view restaking as just one particular use case of liquid staking, but actually reducing the risk of that one particular use case.\n\nQ: Why do you think restaking is having a moment in the news?\n\nKannan: I don't know. I'm glad people are talking about it. Of course, anything that adds new rewards to stakers is something interesting.\n\nI said anything that could be done with EigenLayer could be done with LSTs, but people didn't know what to do with these LSTs.\n\nThey were doing exactly the same thing that people are doing with ether, which is lending, borrowing, the same set of DeFi parameters.\n\nI think one thing that EigenLayer did is by creating this new category, that validation, if I can borrow the Ethereum trust network to do new things: I can build a new layer 1, I can build a new like oracle network, I can build a new data availability system, I can build any system on top of the Ethereum trust network, so it internalizes all the innovation back into Ethereum, or aggregates all the innovation back into Ethereum, rather than each innovation requiring a whole new system.\n\nSo I think that narrative is quite attractive.\n\nQ: I was just reading the news about EIP-7514, which is a short term solution for solving the overcrowdedness of validators, by limiting entries of new validators. How does that affect an EigenLayer?\n\nKannan: I think mostly, it means the same thing for EigenLayer that it means for liquid staking protocols, that there is going to be a smaller rate at which new validators can enter.\n\nThere's a long entry queue right now, and people don't want to wait that long.\n\nAnd making it slower is going to just make the new growth of LSTs slower. But I understand fully that this is a super important thing for Ethereum to be conservative and not have an overflow of validators that may not be able to be handled by the consensus layer.\n\nBut in the long term, if the total staking of Ethereum cannot grow, one of the things that happens is the total yield or the return that stakers are getting is bounded by the Ethereum staking, whereas in the presence of restaking there is a possibility for them to get some of these additional rewards. Other than that, it's pretty similar.\n\nQ: You were making the point that EigenDA is just like in-house AVS (actively validated service) – explain what it is:\n\nKannan: What we decided is, in order to keep this system of shared security, in order to keep EigenLayer as decentralized as possible, we want to make sure that there is a highly scalable data system at its backbone. And that's what EigenDA is, it's a highly scalable data availability system, built on the same ideas that underpin the Ethereum roadmap, particularly what is called danksharding.\n\nOur view is that building an Ethereum-adjacent data availability layer requires first principles thinking, whereas Celestia and Avail are built to be chains by themselves.\n\nIf you're building a data availability system adjacent to Ethereum, you'd want Ethereum validators to participate. So that's just one part of the story. Of course, EigenLayer enables that.\n\nBut then you go beyond that, and then you see, “Oh, it's not just you want to get the Ethereum nodes to participate.”\n\nEthereum already has consensus built in, and Ethereum gives you the ordering of the various transactions. So you should build the data availability system, which doesn't need its own ordering.\n\nWhereas all the existing other protocols like Celestia and Avail, are basically chains that have to do their own ordering; we built a system which doesn't have internal ordering; all ordering is done on Ethereum.\n\nQ: Liquid restaking tokens - once your liquid staking tokens are locked on EigenLayer, they become illiquid?\n\nKannan: That’s correct, the problem that the liquid restaking tokens are trying to solve is, can I just have a restaked position, and then still keep it liquid. So you can take that receipt token of liquid restaking and then transfer it.\n\nWe are not building this kind of liquid restaking but other people are building liquid restaking on top of them.\n\nQ: I think your comment was, you want to use the Ethereum shared security for as many things as possible. I'm curious, now that there's also people building on the back of what y'all are doing, is there a natural limit to how much that you know, Ethereum can support?\n\nKannan: This is a similar kind of question that one could ask already at the application layer of Ethereum: How many applications on Ethereum are smart contracts and how many smart contracts can be built on top of Ethereum?\n\nSo it's the same thing with EigenLayer because people staking and running new applications, but now they do it much more flexibly and programmably with these aliases on top of EigenLayer, all contribute back to Ethereum. Their ETH staking increases rewards, ETH itself potentially increases in value because of all these additional use cases.\n\nSo over time, this can start to accommodate more and more.\n\nBut there's absolutely a limit."}]