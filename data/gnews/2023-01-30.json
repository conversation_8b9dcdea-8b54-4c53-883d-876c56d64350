[{"id": 6, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9tYXJrZXRzLzIwMjMvMDEvMzAvZGVmbGF0aW9uYXJ5LWV0aGVyLWlzLXVuZGVycGVyZm9ybWluZy1iaXRjb2luLWhlcmUtYXJlLTMtcmVhc29ucy13aHkv0gF1aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL21hcmtldHMvMjAyMy8wMS8zMC9kZWZsYXRpb25hcnktZXRoZXItaXMtdW5kZXJwZXJmb3JtaW5nLWJpdGNvaW4taGVyZS1hcmUtMy1yZWFzb25zLXdoeS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Jan 2023 08:00:00 GMT", "title": "Deflationary Ether Is Underperforming Bitcoin, Here Are 3 Reasons Why - CoinDesk", "content": "\"For most macro investors, BTC is the obvious on-ramp into the crypto market – the most liquid, the widest range of on-ramps. We see this in the strong increase in BTC spot volumes (while ETH spot volumes have not risen much), surge in the open interest on CME and stronger positioning in the options market,\" <PERSON>, author of the popular \"Crypto Is Macro Now\" newsletter, said."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMic2h0dHBzOi8vY3J5cHRvc2xhdGUuY29tLzYwLW9mLWludmVzdG9ycy1iZWxpZXZlLWV0aC1oYXMtYmV0dGVyLWdyb3d0aC1wb3RlbnRpYWwtdGhhbi1idGMtY29pbnNoYXJlcy1zdXJ2ZXktcmV2ZWFscy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Jan 2023 08:00:00 GMT", "title": "60% of investors believe ETH has better growth potential than BTC – CoinShares survey reveals - CryptoSlate", "content": "Around 60% of investors believe that Ethereum (ETH) has a more compelling growth outlook, according to a survey by CoinShares.\n\nAs opposed to the 60% siding with ETH, only 30% of the respondents said Bitcoin (BTC) had the most compelling growth outlook, according to the CoinShares survey.\n\nThe survey included 43 investors who managed a total of $390 billion worth of assets. Among the participants, those who identified as Wealth Managers (25%) and Family Office (25%) accounted for half of the group. Another 22% and 17% identified as Hedge Fund and Institutional, respectively.\n\nYear-to-year changes\n\nIt can be seen that a bulk of investors shifted to ETH from BTC when comparing the latest results with results from 2022.\n\nThe blue columns on the chart below represent the latest results, while the red marks show the results from last year’s survey.\n\nOnly 40% of the respondents said ETH had more compelling growth potential, while only a little less than 40% chose BTC in the 2022 survey. In one year, investors who opted for ETH spiked to 60%, while the ones who voted for BTC fell to 30%.\n\nEven though investors distanced themselves from BTC, this year’s results show an increase in the number of investors who invested in it. 30% of the participants own BTC, which marks an increase from 24% in 2022, according to CoinShares.\n\nDigital assets in portfolios\n\nThe latest numbers indicated that digital assets accounted for 1.1% of portfolios, which marks a significant increase from last year’s 0.7%.\n\nHedge Funds especially have considerably increased their investments in digital assets, CoinShares data revealed. In the meantime, institutional investors reduced their digital assets to below 1%.\n\nMentioned in this article"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiiwFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wMS8zMC9pcy1zb2xhbmEtZm9sbG93aW5nLWV0aGVyZXVtcy1lYXJseS1kYXlzLXRvcC10cmFkZXItY29tcGFyZXMtc29sLWFuZC1ldGgtdXBkYXRlcy1zdGFuY2Utb24tY3J5cHRvLW1hcmtldHMv0gGPAWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzAxLzMwL2lzLXNvbGFuYS1mb2xsb3dpbmctZXRoZXJldW1zLWVhcmx5LWRheXMtdG9wLXRyYWRlci1jb21wYXJlcy1zb2wtYW5kLWV0aC11cGRhdGVzLXN0YW5jZS1vbi1jcnlwdG8tbWFya2V0cy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Jan 2023 08:00:00 GMT", "title": "Is Solana Following Ethereum’s Early Days? Top Trader Compares SOL and ETH, Updates <PERSON><PERSON> on Crypto Markets - The Daily Hodl", "content": "A trader who continues to build a large following with his timely altcoin calls says that one of Ethereum’s biggest competitors may be following ETH‘s early stages of growth.\n\nPseudonymous trader <PERSON><PERSON> shares a chart with his 159,000 Twitter followers showing uncanny similarities between the price action of Solana (SOL) today and Ethereum’s in 2018.\n\n“Early ETH vs SOL.”\n\nThe trader’s chart shows both assets bouncing off a support level mutiple times, then collapsing through it before making a lower low and then a rebound.\n\nFrom its 2018 low, Ethereum ultimately rallied 5,777% to its all-time high, from $83 to $4,878. With Solana’s recent low of $8, a similar move for SOL would suggest a rally to roughly to $462 before the next bear market.\n\nLooking at the rest of crypto markets, <PERSON><PERSON> says that the “January effect,” referring to cypto’s tendency to rally at the beginning of the year, is likely over. He predicts a correction in the near term, potentially triggered by poor earnings reports or other macroeconomic announcements.\n\n“The January effect is done. Historically big months like this follow with some mean reversion, and what better way to start the next month with a gamut of events: FOMC and earnings for Amazon, Apple, Google, Meta, Exxon, Pfizer, Merck…\n\nIf I had to guess.\n\nTake it back to 20k area, and chop people up left and right before continuing.”\n\nThe trader says BTC lacks a lot of liquidity between $23,000 and about $21,000 which could mean price falls rather quickly until the next major level.\n\n“I don’t really care to do anything aggressive while Bitcoin is at the top of the range.\n\nFlipping 25k might change my mind.\n\nThere are some really imbalanced areas that price likes to revisit and could cut through like butter.\n\nEyes on anchored yearly vwap (volume weighted average price) and Jan 23rd low.”\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/Shacil/WhiteBarbie"}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiXmh0dHBzOi8vY3J5cHRvLm5ld3MvZW5zLWRvbWFpbnMtbWltaWNraW5nLW1ham9yLWV4Y2hhbmdlcy13YWxsZXRzLWluZGljYXRlLWEtbmV3LXR5cGUtb2Ytc2NhbS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Jan 2023 08:00:00 GMT", "title": "ENS domains mimicking major exchanges' wallets indicate a new type of scam - crypto.news", "content": "Today crypto.news has been investigating the activity involving the wallets of defunct cryptocurrency exchange FTX. While examining the blockchain data, we found an apparent attempt at nefarious activity involving the Ethereum Name Service (ENS).\n\nWhat is an ENS domain?\n\nThe ENS is a decentralized domain name service built on the ethereum blockchain. It provides a more human-readable format for ethereum addresses, allowing users to send and receive funds at addresses like “myname.eth” instead of the traditional long string of numbers and letters. ENS aims to make interacting with the ethereum network more accessible and more user-friendly by providing a mapping between names and ethereum addresses.\n\nThe service is decentralized and operates on a blockchain, making it resistant to censorship and control by a single entity. ENS is an important infrastructure component of the Ethereum ecosystem, facilitating the widespread adoption of decentralized applications and services built on the Ethereum network. The service does not only support ethereum addresses but also other blockchains and centralized services such as websites and instant messaging profiles.\n\nHow ENS addresses can impersonate wallets\n\nA single address registered 50 different ENS domains — most of those, if not all, are the complete hexadecimal addresses of highly active addresses with the addition of “.eth” at the end. One example is FTX address ****************************************** which is present among this peculiar collection as “******************************************.eth.”\n\nOne likely reason why those ENS domains were registered is in the hope of intercepting payments meant for the addresses that those use as domain names. Many wallets support ENS domains as addresses to which users send assets. It implies that a user sending assets to one such address would only need to misclick once to transfer tokens to the ENS domain mimicking the wallet.\n\nAlleged scammers create ENS domains mimicking crypto exchanges\n\nA quick review of the addresses with the help of blockchain analysis service Arkham Intelligence reveals that they tend to be internal addresses of cryptocurrency exchanges. We can find a Coinbase address, an FTX address, a Binance address, and many other high-profile on-chain addresses — usually multiple per entity.\n\nThe mentioned address bought a total of 50 ENS domains in March 2022 for well over $3,000 — all in compliance with the scheme described above. However, the address failed to mislead any network participant into sending funds to the wrong recipient. It has been involved in 102 transactions in total: two for funding its ENS domain shopping and 100 for acquiring and receiving the domains in question.\n\nA visualization of the on-chain activity: funding address (top left), compulsive ENS shopper (bottom left), ENS smart contract (top right.)\n\nThere is one possible reason why no funds were mistakenly sent to the owner of those ENS domains. The addresses involved tended to be ones meant for internal use. Due to this choice, the transactions that could have been misled were sent by those working in the crypto industry. Most likely, they were using a very specific wallet infrastructure that only sent to whitelisted addresses and probably did not support ENS in the first place.\n\nDespite this, there is still plenty of time for somebody to make a mistake considering that those ENS domains will only expire in about four years.\n\nThe findings follow a recent report that Reddit users on the r/CryptoCurrency subreddit raised alarm bells regarding a potential Shiba Inu (SHIB) airdrop scam.\n\nFollow Us on Google News"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vd3d3LmNyeXB0b3RpbWVzLmlvLzIwMjMvMDEvMzAvZXRoZXJldW0tMi0wLW5ldy1ibG9ja2NoYWluLXZlcnNpb24v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Jan 2023 08:00:00 GMT", "title": "Ethereum 2.0: What Exactly is this New Blockchain Version? - Crypto Times", "content": "Ethereum — the second most popular blockchain after Bitcoin — has continually deduced methods to update its network to become better than its predecessor and major rival.\n\nAnd after a long wait, the upgraded version launched in mid-September 2022 called “Ethereum Merge”. Originally referred to as Ethereum 2.0, the merge is an upgraded version of the Ethereum network that now uses proof-of-stake consensus.\n\nThe upgrade will allow Ethereum to scale its transaction throughput, enable new applications to drive greater utility on-chain, and further reduce cost. Let’s understand how this revolutionary Merge upgrade will gear up to be future-proof to follow mass adoption.\n\nWhat is Ethereum 2.0?\n\nEthereum 2.0 is simply an upgrade to Ethereum’s network. It is a multi-phased project that intends to make the blockchain network faster, cheaper, more accessible, more secure, and more effective.\n\nThis upgrade heralded the switch of Ethereum’s consensus mechanism from the Proof-of-Work (PoW) model to the Proof-of-Stake (PoS) model, and brought about “Sharding.” Sharding is reportedly the last phase of the Ethereum 2.0 transition which should be finalized by 2023.\n\nThe transition from Ethereum 1.0 to 2.0 was always in the works, and not a random event. To be honest, you hardly find any unplanned incident in the crypto space. Even the rising platform Xbitcoincapex.app (which recently made headlines in several crypto media websites) admits that no events go unprepared for in the ecosystem.\n\nIt commenced in December 2020 with the launch of the Beacon Chain, which introduced staking and PoS to the Ethereum network. Using the proof-of-stake (PoS) model over the proof-of-work (PoW) model was a major game changer for the network.\n\nNormally, with the PoW model — which was the primary consensus method used by all networks, as started by Bitcoin — miners use their sophisticated computing power to decipher complicated equations and validate blocks, which are then added to the blockchain. Each block had to be proven unique to prohibit duplication of transactions. This process rendered the whole PoW process expensive, in terms of financial resources and time to undergo.\n\nIn addition, blockchain mining utilizes a lot of electricity which isn’t environmentally friendly. Then there’s the issue of miners who are inferiorly stacked against their competitors, as they don’t have enough resources to compete to solve these equations. There is, however, the option of miners collaborating with other miners in a ‘mining pool’ to solve these equations. But, the reward is shared amongst the miners.\n\nWith the PoS model, users ‘validate’ transactions (just like PoW mine) by ‘staking’ crypto into the network. The more funds the validators stake, the higher their rewards for participating.\n\nIn this method, the bar for entry is low as long as you have the funds to participate, (and there are other ways to get the 32 ETH needed to stake), unlike PoW which involves having expensive hardware and utilizing copious amounts of electricity.\n\nThis level of network accessibility (of Ethereum 2.0) enables better scalability, as more users are connected to the network and validate blockchain transactions. Ultimately, this leads to a higher level of security and decentralization due to multiple peoples’ participation.\n\nUsing the PoS model also prevents a 51% attack, something which is prevalent in PoW models. It simply means when a user commands control of 51% of nodes and validates transactions wrongly. Since it is practically impossible to HODL 51% of tokens on the Ethereum network, the PoS model is more secure.\n\nGenerally speaking, upgrading to Ethereum 2.0 will help secure the network and allow participants, collectors, and cryptocurrency owners to stake their ETH tokens and earn rewards.\n\nWhen did Ethereum 2.0 Start?\n\nWork on Ethereum 2.0 had begun early, as previously stated. It can be summarized into four phases:\n\nPhase 0\n\nIt commenced in 2020 with what was called “Beacon Chain” — which introduced the PoS model to the Ethereum blockchain — enabling users to stake their ETH and become validators. Beacon Chain didn’t disrupt the Ethereum 1.0 blockchain but existed alongside Ethereum’s mainnet.\n\nThe first Phase provided three major technological implementations to the Ethereum ecosystem: the Beacon Chain, the PoS consensus mechanism, and validator nodes.\n\nPhase 1\n\nThe next phase was purported to launch in mid-2021 but it was delayed to early 2022, with the Ethereum developers mentioning unfinished work. In Phase 1, there was meant to be a merge of the Mainnet into the Beacon Chain, therefore switching the consensus mechanism from PoW to PoS. Miners were expected to take their holdings and stake them to evolve into validators.\n\nIn addition, the Ethereum developers intended to introduce “Sharding” in this Phase. This is because it has a significant effect in ensuring the Ethereum network can handle an increased number of transactions.\n\nRather than settling all operations on just one blockchain, “Shard” chains disperse these operations across 64 new chains. This enables parallel processing to decrease the latency that originates from linear processing utilizing a single blockchain.\n\nInvariably, this would lead to an increase in the transaction speed and reduce the load on the network as validators could run their Shards. Also, processing any transactions would become easier from a hardware purview, as there would be reduced data to be stored on a particular machine.\n\nPhase 2\n\nUltimately, Phase 2 came with the introduction of Ethereum Web Assembly (eWASM). It was designed to make the Ethereum 1.0 network more efficient, a subset of the proposed Ethereum smart contract execution layer.\n\nEthereum 1.0 was equipped with an Ethereum Virtual Machine (EVM). This software interacts with dApps, evaluates gas fees for every transaction, and computes the network after each block is added to the blockchain. A fundamental constituent of the Ethereum blockchain, the EVM wears many hats. For one, it executes and terminates smart contracts, and detects if a dApp is deterministic or if a smart contract is isolated and is used by users globally.\n\nAs a result of numerous transactions occurring at once on the Ethereum blockchain, the EVM has become slow. Unfortunately, it is difficult to upgrade since it was written in complicated code, i.e Solidity. So the most likely option was to replace it.\n\nThat’s where eWASM comes in. It was designed to replace the EVM in the Ethereum hierarchy. It speeds up the network process by computing codes fast and it is effectively compatible with various coding languages like C and C++. All of these are poised to make eWASM more accessible for the Ethereum blockchain, which in turn makes the network faster and more efficient.\n\nPhase 3\n\nThis phase — a miscellaneous stage — was reserved for any extra upgrade that might have been needed before Ethereum 2.0 was launched completely. For example, creating extra Shard chains to add to the original 64 chains, heightened privacy on the network, or improving the overall technology on the blockchain.\n\nIt was estimated that each phase of Ethereum 2.0 would require about six to eight months to come to total fruition. Admittedly, the long-awaited Ethereum merge finally came, in mid-September 2022. The Ethereum Foundation however remarked that the complete transition to Ethereum 2.0 would occur in 2023, as the network was highly dynamic and ever-evolving.\n\nWhy Shift to Ethereum 2.0?\n\nOver time, the Ethereum network was faced with some constraints that were affecting its efficiencies like scalability, accessibility, and security.\n\nUsers, at times, had to pay over 100% in gas fees and extra costs just to undergo minimal transactions. Plus, the network struggled to approve transactions rapidly too.\n\nAll of these necessitated the need to improve its blockchain network to achieve an expansive level of adoption from investors. This is because Ethereum finds use in dApps — which are beneficial in governance, supply chain, finance, education, etc.\n\nTherefore, Ethereum developers — led by the creator, Vitalik Buterin — established a proposed upgrade (Ethereum 2.0) and worked towards it. It is simply the transition of Ethereum’s consensus mechanism from a proof-of-work (PoW) model to a proof-of-stake (PoS) model.\n\nThis was in hopes to reduce the blockchain’s energy consumption by 99%, and make the network faster, more scalable, and more secure to utilize.\n\nWhat Are the Benefits of Ethereum 2.0?\n\nPreviously, Ethereum was held back by some technical limitations, but all these changed with the advent of Ethereum 2.0. These are the benefits:\n\nLess Computation Power, More Eco-friendly\n\nAs opposed to the proof-of-work (PoW) model which involves using sophisticated power to solve complex equations, the proof-of-stake (PoS) model — which Ethereum 2.0 utilizes — doesn’t require such. This in turn makes it to be more eco-friendly as a single PoW transaction is reportedly said to consume as much energy as an average U.S. household in a week. Moreover, the hardware is said to generate electronic waste also.\n\nIncreased Scalability and Efficiency\n\nEthereum 1.0 computed about 25 to 30 transactions per second, but Ethereum 2.0 is said to handle 100,000 transactions per second. This increased Scalability is possible due to the Sharding technique.\n\nSharding enables Ethereum to store and access data, plus “Shard chains” would be utilized in transactions which makes it reportedly 64 times faster than the previous blockchain version.\n\nMaking ETH a more Deflationary Asset\n\nDue to the merge, issuing Ether as block rewards would greatly reduce. There are reportedly about 13,000 ETH mined daily. The number would reduce to 1,600 ETH daily as a result of the Merge. All of which would ensure less ETH is in circulation, thereby reducing its inflationary quality.\n\nHigher Security Level\n\nEthereum 2.0 needs a minimum of 16,384 validators, enabling a decentralized network and thus securing the blockchain, by reducing the attack surface area. This prevents a 51% attack from occurring on the network.\n\nMore Network Participation\n\nWith lower hardware requirements, more people can participate in Ethereum 2.0. By using Sharding, validators don’t need to store data by themselves. They can instead employ data techniques to confirm that the data has been made usable by the network. Moreover, more users can join in the staking process — provided they have 32 ETH to lock in — thereby increasing network participation.\n\nReduced Costs\n\nWith the PoW model, there was a high cost of hardware equipment, gas fees, and electricity bills. All of these are not present in Ethereum 2.0, as the PoS model doesn’t require as much hardware (with Sharding) and doesn’t have to pay miners gas fees, as the validators who stake ETH secure the network indirectly.\n\nWhat Are the Risks of Ethereum 2.0?\n\nThe Ethereum merge is the most significant upgrade to a blockchain network and it doesn’t come without its risks:\n\nPossible Scams\n\nSome crypto-related organizations pretend as though ETH (Ether) was what was updated to Ethereum 2.0, by calling it “ETH 2.” This is in a bid to get users to swap their ETH for the so-called ETH 2 and scam them.\n\nFeasible drop in ETH Value\n\nIf things go awry after the merge, it is possible that the value of ETH would drop, as well as other cryptos that are built on the Ethereum blockchain.\n\nWhat is the Future of Ethereum?\n\nThe crux of an updated Ethereum network (Ethereum 2.0) is to enable it to process more transactions speedily while being secure and accessible.\n\nWith the transition to a PoS model from the PoW model and the advent of Sharding, expectations — from industry experts and enthusiasts alike — are high. It is expected that the merge would have an all-encompassing impact on the DeFi space. Time would tell what would happen though."}]