[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vZGVjcnlwdC5jby8xNDg1MDkvZXRoZXJldW0taGl0cy0yMDAwLWZvci1maXJzdC10aW1lLXNpbmNlLW1heS1mb2xsb3dpbmcteHJwLXJ1bGluZ9IBYGh0dHBzOi8vZGVjcnlwdC5jby8xNDg1MDkvZXRoZXJldW0taGl0cy0yMDAwLWZvci1maXJzdC10aW1lLXNpbmNlLW1heS1mb2xsb3dpbmcteHJwLXJ1bGluZz9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Jul 2023 07:00:00 GMT", "title": "Ethereum Hits $2,000 for First Time Since May Following XRP Ruling - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nThe broader cryptocurrency market is showing green almost across the board today following a U.S. federal judge’s ruling that XRP should not be considered a security in certain contexts. That momentum has propelled Ethereum, the second-largest cryptocurrency by market cap, over $2,000 for the first time in over two months.\n\nEthereum traded as high as $2,009 this afternoon, per data from CoinGecko, but has dipped slightly to a current price of $1,985 as of this writing. That’s a 5% rise over the past 24 hours, and a 14% gain over the last month.\n\nAD\n\nAD\n\nThe last time that Ethereum traded above $2,000 was on May 5, according to CoinGecko. Ethereum briefly popped above $2,100 in April, but other than those two brief time periods, ETH has remained under the $2,000 threshold over the last 11 months.\n\nEven with today’s climb, Ethereum remains down 59% from its peak price point of $4,878 set at the peak of the cryptocurrency market in November 2021.\n\nEthereum is far from the only cryptocurrency or token to benefit from the momentum generated by the XRP ruling, which has propelled XRP’s own price by 81% over the last 24 hours.\n\nAltcoins like Solana, Polygon, and Cardano have each seen double-digit gains today, with Cardano and Polygon both up about 20% apiece. Bitcoin, by comparison, is only up about 2% today as of this writing, but briefly hit $31,444—its highest price in more than a year.\n\nBroadly, the entire cryptocurrency market is up about 5% over the past 24 hours, per CoinGecko."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLWFjdGl2aXR5LW9uLWNoYWluLW1ldHJpY3PSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Jul 2023 07:00:00 GMT", "title": "Ethereum activity on the rise as on-chain metrics print fresh highs - Blockworks", "content": "The total value of staked ether (ETH) continues apace, reflecting positive signs of growth and stability for the world’s second-largest digital asset.\n\nFollowing Ethereum’s critical PoS upgrade in April, the amount of ETH that has been deposited to the ETH 2.0 contract now stands above 25.6 million, per Glassnode data.\n\nThat figure, worth some $48 billion, represents a rough 70% increase since the new year began, from around 15 million ETH ($28 billion) to current levels. Total staked ETH has effectively doubled when viewed over a year-long horizon.\n\nYield on staked ether is currently around 4.5%, paid in ETH.\n\nAt the same time, the total balance of ETH sitting on exchanges fell to its lowest level in five years, dropping from around 22.5 million ETH at the start of 2023 to 15.3 million ETH — a 32% decline.\n\nAccording to analysis from CryptoQuant, exchange outflows, as seen in the total number of ETH disappearing from exchanges, indicate a decline in sell-side pressure across the spot market.\n\nParticipants continue to bank on the value of the Ethereum network, with those holding the underlying becoming more hesitant to relinquish their assets as they anticipate future demand.\n\nThe increase in staking is coupled with gas prices — a measure of transaction activity — that have generally exceeded the rate at which ether becomes deflationary, and the total supply is expected to contract this year by somewhere between -0.1 and -0.3%.\n\n“As scarcity increases and demand grows, assets generally rise in price,” <PERSON>, co-founder at AltTab Capital told Blockworks via email.\n\nThe number of externally controlled accounts, or private keys, holding a non-zero amount of ETH also shot up to record highs on Tuesday, rising just above 100 million for the first time.\n\nWhile not perfect, the metric analyzing an approximate number of active users on the network further illustrates a rising demand for the asset as institutions mull the implications of wider adoption.\n\n“We’ve seen a very large uptick in interest from institutional capital and when the smart money is getting into an asset class, it’s because they too see the potential for strong growth,” Moritz said.\n\nMacauley Peterson contributed reporting.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS12dWxuZXJhYmxlLTE4MjUv0gFIaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vYW5hbHlzaXMvZXRoL2V0aGVyZXVtLXByaWNlLXZ1bG5lcmFibGUtMTgyNS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Jul 2023 07:00:00 GMT", "title": "Ethereum Price Topside Bias Vulnerable If ETH Drops Below $1,825 - NewsBTC", "content": "Ethereum price failed again to clear the $1,900 resistance against the US Dollar. ETH is declining and might revisit the $1,825 support zone.\n\nEthereum topped near the $1,900 resistance and declined.\n\nThe price is trading below $1,880 and the 100-hourly Simple Moving Average.\n\nThere was a break below a key bullish trend line with support near $1,875 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could extend its decline toward $1,845 or even $1,825.\n\nEthereum Price Restarts Decline\n\nEthereum’s price made another to clear the key resistance at $1,900. However, ETH failed to gain pace above the $1,900 resistance and reacted to the downside, similar to Bitcoin.\n\nA high was formed near $1,901 and the price declined below $1,880. There was a break below a key bullish trend line with support near $1,875 on the hourly chart of ETH/USD. The pair traded below the 76.4% Fib retracement level of the upward move from the $1,862 swing low to the $1,901 high.\n\nEther is now trading below $1,870 and the 100-hourly Simple Moving Average. It seems like the bears are in control and they might aim for a move below the $1,862 low.\n\nSource: ETHUSD on TradingView.com\n\nOn the upside, immediate resistance is near the $1,880 level and the 100-hourly Simple Moving Average. The first major resistance is near the $1,890 zone. The main resistance is still near $1,900. A close above the $1,900 resistance could start a decent increase. The next major resistance is near the $1,955 level. Any more gains could send Ether toward the $2,000 resistance.\n\nMore Losses in ETH?\n\nIf Ethereum fails to clear the $1,880 resistance, it could continue to move down. Initial support on the downside is near the $1,860 level or the last swing low at $1,862.\n\nThe first major support is near the $1,845 level, below which the price might test the 1.618 Fib extension level of the upward move from the $1,862 swing low to the $1,901 high at $1,838. The next major support is near the $1,825 level. Any more losses could send Ether toward the $1,770 support level in the near term.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is gaining momentum in the bearish zone.\n\nHourly RSI – The RSI for ETH/USD is now below the 50 level.\n\nMajor Support Level – $1,825\n\nMajor Resistance Level – $1,900\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS1zY2FsaW5nLXByb3RvY29scy1kcml2ZS16ZXJvLWtub3dsZWRnZS1wcm9vZi11c2UtMjAyM9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Jul 2023 07:00:00 GMT", "title": "Ethereum scaling protocols drive zero-knowledge proof use in 2023 - Cointelegraph", "content": "Ethereum scaling protocols dominate the use of zero-knowledge rollups (ZK-rollups), with major launches, new research and healthy competition key points in a sector report published by ZKValidator.\n\nThe node infrastructure operator’s “State of ZK Q2” report reflects on significant events across the ZK ecosystem, with notable launches of ZK-powered layer 2’s highlighting the use of the technology for scaling in comparison with other market segments.\n\nZK-proofs use cryptography to allow one party to prove to another party that certain information is correct without revealing any data. They present a solution to trust and privacy in digital environments, and have been pivotal in scaling layer-1 blockchain protocols.\n\nA poll put to the 18,000 followers of the Zero Knowledge Podcast provided a sample for a community gauge on the most important applications of ZK-proofs. Of the respondents, 42% selected “ZK for Scaling” as the segment that would experience the highest growth in the coming 12 months.\n\nZK use cases. Source: State of ZK Report Q2 2023.\n\nZK for privacy and identity followed at 23.8% and 22.2%, respectively, while zero-knowledge machine learning was the least selected segment.\n\nThe poll reflects the real-world applications of ZK-proofs, with several major Ethereum scaling protocols hitting mainnet in the first half of 2023.\n\nThis includes Polygon’s announcement of its forthcoming “2.0” multichain system, which will use ZK-proofs to transfer assets between networks and zkSync Era’s ZK Stack, which will allow developers to build ZK-rollups and proprietary layer-3 “hyperchains.“\n\nConsenSys also began onboarding partners to its mainnet Linea network on July 11. Linea is another prominent layer 2 that allows developers to build or migrate decentralized applications for Ethereum.\n\nRelated: Are ZK-proofs the answer to Bitcoin’s Ordinal and BRC-20 problem?\n\nThe sector has also attracted investment across several segments, including new zk-based layer 2’s and zkEthereum Virtual Machines (zkEVMs), as well as ZK-proofs for off-chain computation.\n\nEthereum scaling firm StarkWare, which helped pioneer zk technology, and the Ethereum co-founder Vitalik Buterin, invested in Kakarot — a zkEVM running on StarkWare’s technology that aims to build layer-3 applications.\n\nThe report also highlights a $115 million raise by Worldcoin to continue the development of its zk-powered digital identity application and ecosystem. Swiss nonprofit Anoma Foundation also features in the report after its latest $25 million raise to continue building its third-generation blockchain architecture for decentralized applications.\n\nCollect this article as an NFT to preserve this moment in history and show your support for independent journalism in the crypto space.\n\nMagazine: Here’s how Ethereum’s ZK-rollups can become interoperable"}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiO2h0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvaG93LXRvLWJyaWRnZS1wb2x5Z29uLXRvLWV0aGVyZXVt0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Jul 2023 07:00:00 GMT", "title": "How to Bridge Polygon to Ethereum? - Watcher <PERSON>", "content": "How to Bridge Polygon to Ethereum: A Comprehensive Guide\n\nIn the ever-evolving world of blockchain and cryptocurrency, interoperability is crucial for seamless asset transfers and data sharing between different networks.\n\nThe Polygon Bridge has emerged as a top solution for achieving interoperability between blockchain networks, specifically for bridging tokens from Polygon to the Ethereum ecosystem.\n\nThis comprehensive guide will walk you through the process of bridging tokens, step by step.\n\nAlso read: MATIC Surges 8% As Polygon 2.0 Inches Closer\n\nUnderstanding the Importance of Interoperability\n\nSource: binance.com\n\nIn the rapidly evolving blockchain ecosystem, interoperability plays a vital role in connecting different blockchain networks and enabling seamless data and asset transfers.\n\nFurthermore, without a secure, cost-effective, and reliable data-sharing process, the full potential of blockchain technology cannot be realized.\n\nAdditionally, interoperability bridges this gap, allowing users to move assets between different blockchain networks.\n\nThe Role of the Polygon Bridge\n\nOne of the leading solutions for achieving interoperability between blockchain networks is the Polygon Bridge. The Polygon Bridge enables users to bridge tokens from Polygon, a framework for Ethereum-supported blockchain networks, to the Ethereum mainnet.\n\nAdditionally, by leveraging the Polygon Bridge, users can tap into the vast Ethereum ecosystem, accessing a wide range of decentralized applications (dApps) and services.\n\nWhat is the Polygon Bridge?\n\nConnecting Blockchain Networks\n\nThe Polygon Bridge acts as a bridge between the Polygon network and the Ethereum mainnet, facilitating the seamless transfer of tokens between the two networks.\n\nFurthermore, as the number of blockchain networks continues to grow, the challenge of sharing data and tokens across different networks becomes more apparent.\n\nThe Polygon Bridge addresses this challenge by providing a secure and efficient way to connect blockchain networks.\n\nIntroducing Polygon Bridge V2\n\nThe Polygon Bridge V2 is built on the Proof of Stake (PoS) chain, offering improved functionality and faster token transfers.\n\nAdditionally, using a compatible crypto wallet, it allows users to transfer their assets from Polygon to Ethereum.\n\nBy leveraging the Polygon Bridge V2, users can unlock the full potential of the Ethereum ecosystem, including access to scalable dApps and decentralized finance (DeFi) platforms.\n\nThe Benefits of Using the Polygon Bridge\n\nSource: 101blockchains.com\n\nSeamless Asset Transfers\n\nThe Polygon Bridge enables users to seamlessly transfer their tokens from Polygon to Ethereum.\n\nBy bridging tokens, users gain increased liquidity as they can access a larger liquidity pool on the Ethereum network. This increased liquidity helps reduce price volatility and provides more trading opportunities.\n\nEnhancing the Ethereum Ecosystem\n\nBridging tokens from Polygon to Ethereum allows users to access a wider range of DeFi applications and services.\n\nFurthermore, the Ethereum ecosystem offers various services, including lending, staking, and trading protocols, which may not be available on the Polygon network.\n\nBy using bridging tokens, users can tap into the full potential of the Ethereum ecosystem and explore new opportunities.\n\nAlso read: Polygon Rallies 9.2% After Appointing New CEO\n\nSupporting Scalable dApps\n\nPolygon is known for its scalability solutions, which enable faster and cheaper transactions compared to the Ethereum network.\n\nBy bridging tokens from Polygon to Ethereum, users can leverage these scaling solutions while still benefiting from the security and decentralization of the Ethereum network.\n\nThis allows developers to build and deploy scalable dApps without compromising on security or performance.\n\n4. How to Use the Matic PoS Bridge\n\nSource: 101blockchains.com\n\nConnecting Your Wallet\n\nTo begin the token bridging process, head over to the Matic Bridge website (https://wallet.matic.network/bridge/) and connect your compatible wallet.\n\nThe Matic Bridge supports various wallets, including Metamask, WalletConnect, and WalletLink. Connect your preferred wallet and ensure it contains the tokens you want to bridge.\n\nSwitching the Token Transfer Direction\n\nBy default, the Matic Bridge interface shows the transfer direction as Ethereum to Polygon.\n\nTo bridge tokens from Polygon to Ethereum, click on the Switch button to change the transfer direction.\n\nChoosing the Transfer Mode\n\nThe Matic PoS Bridge offers two transfer modes: Plasma Bridge and PoS Bridge. The Plasma Bridge takes approximately 7 days to complete the transfer, while the PoS Bridge only takes 3 hours. Select the PoS Bridge for faster token transfers.\n\nSelecting the Token and Amount\n\nChoose the token you want to bridge from the dropdown menu. If your desired token is not listed, paste its contract address.\n\nEnter the number of tokens you want to bridge or click the MAX button to bridge all tokens in your wallet.\n\nInitiating the Transfer\n\nAfter selecting the token and amount, click the Transfer button to initiate the token bridging process.\n\nReview the transaction details, including the sending and recipient wallets, gas fees, and the token being bridged.\n\nOnce you have confirmed the details, approve the transaction and wait for a few minutes for the transfer to complete.\n\nOnce the transfer is successful, your tokens will be bridged from Polygon to Ethereum.\n\nGas Fees and Transaction Costs\n\nSource – Forbes\n\nUnderstanding Gas Fees\n\nGas fees are the costs associated with executing transactions on the blockchain.\n\nWhen bridging tokens from Polygon to Ethereum, you must consider the gas fees involved.\n\nGas fees vary depending on network congestion and demand. It is important to choose an appropriate gas price to ensure the timely processing of your transaction.\n\nGas Estimators for the Polygon Network\n\nTo estimate the gas fees for your transaction on the Polygon network, you can use tools like Blocknative’s gas estimator or Polygonscan.\n\nThese tools provide real-time gas price information, allowing you to make informed decisions about gas fees. It is recommended to check the current gas price before initiating a transaction to ensure you choose an optimal gas price.\n\nTips for Minimizing Gas Fees\n\nTo minimize gas fees when bridging tokens from Polygon to Ethereum, consider the following tips:\n\nUse a low-gas-price strategy: You can specify a lower gas price for your transaction, which may result in a longer processing time but lower fees. Compare bridging service fees: Different bridging services may charge different fees for token transfers. Compare the fees charged by different services to find the most cost-effective option. Consider reputation and security: While minimizing fees is important, it is equally crucial to consider the reputation and security of the bridging service. Choose a reputable service that prioritizes security and maintains a reliable track record.\n\nThe Cheapest Way to Bridge Polygon to Ethereum\n\nLow-Gas-Price Strategy\n\nOne of the cheapest ways to bridge tokens from Polygon to Ethereum is by using a low-gas-price strategy. By setting a lower gas price for your transaction, you can reduce the fees associated with the token transfer.\n\nHowever, it’s important to note that choosing a lower gas price may result in longer processing times. Consider the urgency of your transfer and the current network congestion when selecting a gas price.\n\nComparing Bridging Services\n\nTo find the most cost-effective option for bridging tokens, compare the fees of different bridging services. Some services may offer lower fees than others, allowing you to minimize the overall cost of the token transfer.\n\nHowever, consider other factors such as reputation, security, and reliability when choosing a bridging service.\n\nSource: Outlook India\n\nConsidering Reputation and Security\n\nWhile cost is an important factor, prioritizing the bridging service’s reputation and security is crucial.\n\nLook for services with a proven track record, positive user reviews, and transparent operations.\n\nAdditionally, consider the security measures implemented by the service to protect your assets during the token bridging process. Choosing a reliable and secure bridging service ensures the safety of your tokens throughout the transfer.\n\nExploring Alternative Bridge Options\n\nOther Bridging Platforms\n\nIn addition to the Matic PoS Bridge, other bridging platforms are available for bridging tokens from Polygon to Ethereum.\n\nThese platforms offer similar functionalities, allowing users to transfer their tokens between the two networks.\n\nResearch and explore different bridge options to find the one that best suits your needs regarding cost, speed, and security.\n\nEvaluating Features and Capabilities\n\nWhen considering alternative bridge options, evaluate their features and capabilities. Look for platforms that offer seamless token transfers, competitive fees, and reliable customer support.\n\nConsider the specific tokens each platform supports and ensure that your desired token is compatible.\n\nBy thoroughly evaluating the features and capabilities of different bridging platforms, you can make an informed decision and choose the one that meets your requirements.\n\nThe Importance of Blockchain Interoperability\n\nFacilitating Data Sharing\n\nBlockchain interoperability is crucial for enabling seamless data sharing between different blockchain networks.\n\nBy bridging tokens from Polygon to Ethereum, users can transfer assets and share data across networks, fostering collaboration and innovation.\n\nInteroperability breaks down silos and allows for the efficient exchange of information, creating a more connected and inclusive blockchain ecosystem.\n\nEnabling Flexibility and Accessibility\n\nSource: TechMende\n\nBlockchain interoperability offers flexibility and accessibility to users. By bridging tokens, users can access a wider range of services, applications, and decentralized finance platforms available on different blockchain networks.\n\nThis increased accessibility opens up new opportunities and allows users to leverage different networks’ unique features and benefits.\n\nDriving Innovation and Collaboration\n\nInteroperability fuels innovation and collaboration within the blockchain ecosystem. By connecting different networks, developers can combine the strengths and capabilities of multiple blockchains, creating new and innovative solutions.\n\nInteroperability encourages collaboration between different projects, fostering the development of cross-chain applications and services.\n\nThis collaborative environment drives the growth and advancement of the blockchain ecosystem as a whole.\n\nSecurity Considerations\n\nProtecting Your Assets\n\nWhen bridging tokens from Polygon to Ethereum, prioritizing your assets’ security is essential. Choose reputable and reliable bridging services that have implemented robust security measures.\n\nEnsure that the bridging process is transparent and auditable, minimizing the risk of asset loss or theft. Additionally, consider using hardware wallets or secure wallet solutions to store your tokens securely during the bridging process.\n\nVerifying Bridging Services\n\nBefore choosing a bridging service, conduct thorough research and verify the credibility and reputation of the service provider. Look for reviews, testimonials, and user feedback to assess the reliability and security of the service.\n\nAdditionally, check for any security audits or certifications obtained by the bridging service. By verifying the credibility of the bridging service, you can ensure the safety of your assets throughout the token bridging process.\n\nConclusion: How to Bridge Polygon to Ethereum?\n\nIn conclusion, Bridging tokens from Polygon to Ethereum using the Polygon Bridge opens up a world of opportunities for users.\n\nLeveraging the Polygon Bridge allows users to tap into the vast Ethereum ecosystem, access scalable dApps, and explore decentralized finance platforms.\n\nWith careful consideration of gas fees, security measures, and bridging service reputation, users can bridge tokens seamlessly and securely.\n\nEmbrace interoperability and unlock the full potential of the blockchain ecosystem with the Polygon Bridge.\n\nRemember, bridging tokens are a powerful tool that allows you to access a wider range of services and applications. Stay informed, stay secure, and embrace the possibilities offered by blockchain interoperability."}]