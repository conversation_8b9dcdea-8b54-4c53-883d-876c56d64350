[{"id": 19, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS92aWRlby9jb25zZW5zdXMtMjAyMy1ldGhlcmV1bXMtbmV4dC1mcm9udGllci_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 27 Apr 2023 07:00:00 GMT", "title": "Consensus 2023: Ethereum's Next Frontier | Video - CoinDesk", "content": "Learn more about Consensus 2024, CoinDesk’s longest-running and most influential event that brings together all sides of crypto, blockchain and Web3. Head to coindesk.consensus.com to register and buy your pass now."}, {"id": 17, "url": "https://news.google.com/rss/articles/CBMihQFodHRwczovL3d3dy5jb2luZGVzay5jb20vbWFya2V0cy8yMDIzLzA0LzI3L2NvaW5kZXNrLWluZGljZXMtY3J5cHRvLWFzc2V0LW1hbmFnZXItY29pbmZ1bmQtbGF1bmNoLWFuLWV0aGVyZXVtLXN0YWtpbmctYmVuY2htYXJrLXJhdGUv0gGJAWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9tYXJrZXRzLzIwMjMvMDQvMjcvY29pbmRlc2staW5kaWNlcy1jcnlwdG8tYXNzZXQtbWFuYWdlci1jb2luZnVuZC1sYXVuY2gtYW4tZXRoZXJldW0tc3Rha2luZy1iZW5jaG1hcmstcmF0ZS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 27 Apr 2023 07:00:00 GMT", "title": "CoinDesk Indices, Crypto Asset Manager CoinFund Launch an Ethereum Staking Benchmark Rate - CoinDesk", "content": "<PERSON>, head of digital assets at fund manager <PERSON>, said in the release that his firm is \"very excited about the launch of CESR\" because it opens new pathways for the industry, particularly for TradFi firms intrigued by crypto."}, {"id": 26, "url": "https://news.google.com/rss/articles/CBMiYWh0dHBzOi8vY29pbmdhcGUuY29tL3BvbHlnb24tYnJpZGdlLWZvci1wb2x5Z29uLXprZXZtLWdvZXMtbGl2ZS1hbGxvd3Mtd2l0aGRyYXdhbHMtZnJvbS1ldGhlcmV1bS_SAWVodHRwczovL2NvaW5nYXBlLmNvbS9wb2x5Z29uLWJyaWRnZS1mb3ItcG9seWdvbi16a2V2bS1nb2VzLWxpdmUtYWxsb3dzLXdpdGhkcmF3YWxzLWZyb20tZXRoZXJldW0vYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 27 Apr 2023 07:00:00 GMT", "title": "Polygon Bridge for zkEVM Allows User to Withdraw from Ethereum - CoinGape", "content": "On Wednesday, April 26, Polygon announced that the Polygon PoS Bridge is now available for Polygon zkEVM, whose beta version launched last month. The Polygon zkEVM network offers scalability solutions to decentralized applications (dApps) running on the Ethereum blockchain network.\n\nThe Polygon Bridge for Polygon zkEVM will have the same user experience as available in the Polygon PoS mainnet. However, it comes with a number of UX improvements that incorporate the feedback received from the community.\n\nadvertisement\n\nThese UX improvements include transaction history, a visible progress bar, and a recent transaction panel with completion status.\n\nThe concept of ‘Bridges’ in the crypto space is quite popular as they facilitate the transfer of assets across different blockchain platforms. However, bridges transferred using zk-technology are much better than the regular ones as they are entirely governed by smart contracts, one on the Polygon zkEVM and the other on Ethereum.\n\nPolygon Bridge – Withdrawing Funds from Ethereum\n\nThe launch of this Polygon Bridge for Polygon zkEVM will allow users to withdraw funds from the Ethereum mainnet within just 30-60 minutes of initiating the withdrawal. The Polygon Bridge shall be supporting some of the users’ favorite assets such as ERC-20 tokens and the extended features of ERC-777 tokens.\n\nAlso, users of the Polygon Bridge won’t have to undergo the bridging process. Soon as the user triggers a bridging transaction, the bridged token will be automatically mapped. Most of the other platform require users to map their tokens before bridging. This could also take hours or even days. Thus, the Polygon Bridge cuts through these hurdles offering users a seamless experience.\n\nThe Polygon zkEVM launched last month as mainnet beta is open source, permissionless, and public. Thus, any can build atop it and transact their assets. Some teams have already started building atop the Polygon zkEVM. This includes premier dApps such as Balancer and Lens, and other gaming projects."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vZmluYm9sZC5jb20vZmluYW5jZS1leHBlcnRzLXNldC1ldGhlcmV1bS1wcmljZS1mb3ItdGhlLWVuZC1vZi0yMDIzL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 27 Apr 2023 07:00:00 GMT", "title": "Finance experts set Ethereum price for the end of 2023 - Finbold - Finance in Bold", "content": "The price of Ethereum (ETH) has been a hot topic in the crypto community as of late, to the point where numerous experts were consulted and requested to share their views.\n\nIn particular, finance platform Finder has asked 32 fintech and crypto experts to give their end-of-year price predictions for Ethereum, and the results reveal positive expectations for the asset.\n\nAccording to the results shared with Finbold, the experts suggest that Ethereum is set to rise as the year progresses, although it will not come close to its all-time high ($4,891). The coin reached this height on November 16, 2021, just before the crypto winter started pushing crypto prices down.\n\nPolled finance professionals believe that ETH could reach its peak in 2023 at around $2,758. However, after that the price is expected to start dropping again in the second half of 2023, sinking to $2,342 by the end of the year.\n\nEthereum price prediction. Source: Finder.com\n\nNotably, some of the surveyed experts included <PERSON>, a senior lecturer from the University of Brighton; <PERSON><PERSON>, the director of FinTech Capability at the Swinburne University of Technology, as well as <PERSON>, the co-founder and CEO of StandardDAO.\n\nThe panelists’ predictions were made into a chart that can be viewed here.\n\nPost-Shapella worries\n\nMoreover, commenting on their prediction, a number of panelists also expressed concerns regarding post-Shapella Ethereum.\n\nData shows that around 21% of them believe that ETH is facing a risk of centralization, while nearly one-third of panelists (29%) think that the upgrade will eventually cause a massive sell-off. Meanwhile, half of the panelists believe that the update will result in greater scrutiny by the regulators.\n\nPost-Shapella Ethereum poll. Source: Finder.com\n\nAt the time of writing, April 27th, the price of ETH sits at $1,874.89 after declining by 4.64% in the last 7 days.\n\nEthereum price chart; Last 7 days. Source: Finbold.com\n\nAI provides its own prediction of the Ethereum price\n\nWith AI being another hot topic in fintech lately, some experts leverage this technology to predict Ethereum’s future price. AI has been used for automated trading for some time now, as it can quickly identify chart patterns and trends.\n\nIn this line, Finbold used projections made by CoinPriceForecast which revealed that Ethereum will potentially manage to climb above the $2k mark by mid-2023 and surge to $2,146 by the end of the year.\n\nIt is worth noting that the AI prediction is less optimistic than the one provided by the polled experts, which projects the ETH price to be higher than that by year-end, even after it drops from the expected $2,758.\n\nHowever, if AI is correct, ETH would still see a 31% price surge by the end of the year compared to its price today.\n\nDisclaimer: The content on this site should not be considered investment advice. Investing is speculative. When investing, your capital is at risk."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiOGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLWNhbmN1bi11cGdyYWRlLWJsb2Jz0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 27 Apr 2023 07:00:00 GMT", "title": "Ethereum's Next Upgrade To Focus on Blobs - Blockworks", "content": "Ethereum’s developer community has had time to recover from the Shanghai-Capella (Shapella) upgrade a little over two weeks ago. Now, they have turned their attention to planning what will ship next.\n\nThe consensus on Thursday’s bi-weekly all-core devs call, was that the upgrade, known as Cancun-Deneb — shortened to “Dencun” — will focus on a feature vital to the success of the network’s rollup-centric roadmap: proto-danksharding, the nickname for Ethereum Improvement Proposal (EIP) 4844.\n\nProto-danksharding introduces a new transaction type — the “blob-carrying transaction” — a temporary data storage mechanism that is expected to reduce transaction costs for users on layer-2 rollups, by orders of magnitude.\n\n<PERSON><PERSON><PERSON>, team lead at the Ethereum foundation, summed up the overarching view in the Zoom room: “I kind of feel that 4844 should be the thing that everybody focuses on, and the rest is ‘nice to have’.”\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> was the chief proponent of making 4844 a priority back in January, when he suggested it should take precedence over the withdrawal of staked ether in Shapella, but was overruled by his peers.\n\nThis time, he’s all but certain to get his wish. There was no disagreement that <PERSON><PERSON><PERSON> — the execution layer part of the upgrade that was the focus of the call — will have proto-danksharding front and center.\n\nBut what goes into Cancun besides 4844?\n\nDeactivate SELFDESTRUCT\n\nIt may sound like an urgent task for your movie hero of choice, but in Ethereum-speak it’s an “attempt at getting rid of the SELFDESTRUCT opcode in order to pave the road for statelessness.”\n\nThe concept of a stateless Ethereum goes back to co-founder Vitalik Buterin’s October 2017 post. “State” refers to all the key information at a point in time for the blockchain: all Ethereum accounts, their balances, all deployed smart contracts and the storage associated with these.\n\nA future upgrade or set of upgrades will shift responsibility for storing the blockchain’s full state to more specialized “archival nodes,” allowing for a much larger set of full nodes to store much less. That would make it even easier for anyone to run a node, furthering the goal of network decentralization.\n\nFor now, this precursor EIP-6780 was agreed to merit inclusion in Cancun by all client teams.\n\nCall moderator and project coordinator Tim Beiko concluded that “clearly the self-destruct [proposal] is the second most mentioned EIP beyond 4844.”\n\nHe noted that an external auditor has been hired to ascertain, using on-chain data, whether currently deployed smart contracts could be broken by the change. The results are about a month away, Beiko said, yet there was no objection to making plans for the destruction of SELFDESTRUCT now and, as pseudonymous Geth developer lightclient said, “revisit it after the report comes.”\n\nTransient storage\n\nOne EIP that was eighty-sixed from Shapella, despite being ready to go, was EIP-1153.\n\nEIP-1153 proposes new, cost-effective ways to temporarily store data in Ethereum smart contracts, improving communication between different parts of a transaction. This update would support various applications, such as enhanced security features and streamlined token approvals.\n\n“I don’t see much effort to push it out, so I don’t see a point for it to be delayed,” Lukasz Rozmej from the Nethermind client team said on the call.\n\nJustin Florentine from the Besu client team said, while he would stop short of calling 1153 “a must-have,” he remained in very strong support.\n\nEIP-1153, or “transient storage” is popular enough to have its own “fan page,” and was pushed hard by the Uniswap team in late 2022, in a manner that rubbed some Ethereum developers the wrong way.\n\nLoading Tweet..\n\nBut, in the intervening months, the proposal has truly become uncontroversial, and Erigon’s Andrew Ashikhmin tipped the consensus in favor of its inclusion when he singled it out for endorsement on the call as “a small EIP that people are mostly in favor of.”\n\nEOF remains a ways off\n\nOne upgrade that was also originally a part of Shanghai, known as EVM Object Format (EOF), was broadly lauded by the assembled core developers, but once again considered a poor match for the 4844-centered Cancun plan — essentially reaffirming the consensus from late March.\n\nThe EVM Object Format (EOF) is an extensible and versioned container format for the Ethereum Virtual Machine (EVM) that introduces a one-time validation during contract deployment. It aims to improve code and data separation, making it easier to introduce new features or deprecate old ones.\n\nLong-time Ethereum core developer Greg Colvin articulated the importance of getting EOF implemented from his perspective.\n\n“We’ve been discussing this for 7 years,” Colvin said.\n\nOther developers were of the opinion that EOF was “too big to be the second place in a fork,” as Ansgar Dietrichs, a researcher at the Ethereum Foundation, put it. He suggested it should be the focus of the next major upgrade, called Prague.\n\nDanno Ferrin, principal software engineer at Swirlds Labs concurred. “As much as it hurts, I think it’s the right decision,” he told his colleagues on the call.\n\nBeiko analogized the pair of major upgrades — 4844 and EOF — to the prior two — the Merge and withdrawals — telling Colvin, “I think the strongest commitment we can make is to have it be the main thing for the next fork, but even that is not something we can 100% commit to today.”\n\nEOF remains a “quick follow” candidate, meaning developers would likely make it a priority within six months after Cancun.\n\n“It’s just time,” Colvin said. “I’ve got work I’ve been wanting to do on the [Ethereum Virtual Machine] for 7 years that I can’t do until this is in.”\n\nDon’t miss the next big story – join our free daily newsletter."}]