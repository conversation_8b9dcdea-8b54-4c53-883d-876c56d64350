[{"id": 18, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vdS50b2RheS94cnAtb3V0cGVyZm9ybXMtc2hpYmEtaW51LWV0aGVyZXVtLWFuZC1jYXJkYW5vLXdpdGgtMTAtcHJpY2Utc3Bpa2XSAVZodHRwczovL3UudG9kYXkveHJwLW91dHBlcmZvcm1zLXNoaWJhLWludS1ldGhlcmV1bS1hbmQtY2FyZGFuby13aXRoLTEwLXByaWNlLXNwaWtlP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 20 Aug 2023 07:00:00 GMT", "title": "XRP Outperforms Shiba Inu, Ethereum and Cardano with 10% Price Spike - U.Today", "content": "XRP, the digital asset affiliated with Ripple, witnessed a significant uptick, surging nearly 10% earlier today. As of recent metrics, XRP has settled at a 4.5% gain, still significantly outshining its rivals, including Bitcoin, Ethereum, and Cardano.\n\nAdvertisement\n\nBitcoin, for instance, barely moved with a 0.2% increase, while Ethereum slightly ascended by 0.4%. In contrast, Cardano, a notable contender in the crypto market, saw its price dip by 0.4%.\n\nHowever, when we zoom out to look at the seven-day performance, the narrative changes a bit. Over a week, XRP suffered a notable decline, shedding 14% of its value. In comparison, Ethereum's value decreased by 9.3% and BNB's by 9.9% over the week. Cardano's seven-day performance also indicated a drop but at a lesser rate of 7.5%.\n\nAs reported by U.Today, XRP recently lost roughly 40% in a single month, underperforming other top cryptocurrencies and erasing its gains that were added because of the favorable Ripple ruling.\n\nThe SEC is fighting back\n\nDespite its impressive short-term performance, XRP remains clouded by uncertainties surrounding the U.S. Securities and Exchange Commission (SEC) appeal against Ripple.\n\nThe appeal, recently submitted by the SEC, challenges a July court order. This order had decided that Ripple's sales and offers of XRP on cryptocurrency trading platforms didn't lead investors to anticipate profits dependent on others' efforts. Furthermore, it had ruled that XRP distributions as payment for services don't constitute an \"investment of money.\"\n\nThis appeal's central issues revolve around the classification of XRP's offers and sales. One court has already indicated a disagreement with the initial Ripple decision, but some prominent voices within the XRP community have downplayed the significance of such a move.\n\nThe court previously recognized that institutional sales fit the Howey criteria. However, distinctions have been drawn regarding the categorization of coins sold directly to institutional investors compared to retail investors in secondary markets."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8venljcnlwdG8uY29tL2NoYXJsZXMtaG9za2luc29uLXVwcy10aGUtYW50ZS1hcy1ldGhlcmV1bS1pcy1sYWJlbGxlZC1hLWNhcmRhbm8tYWRhLWNvcHljYXQv0gFmaHR0cHM6Ly96eWNyeXB0by5jb20vY2hhcmxlcy1ob3NraW5zb24tdXBzLXRoZS1hbnRlLWFzLWV0aGVyZXVtLWlzLWxhYmVsbGVkLWEtY2FyZGFuby1hZGEtY29weWNhdC8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 20 Aug 2023 07:00:00 GMT", "title": "<PERSON> Ups the Ante as Ethereum Is Labelled A Cardano (ADA) Copycat - ZyCrypto", "content": "Advertisement\n\n<PERSON>, the founder of Cardano and a co-founder of Ethereum, has a history of teasing Ethereum and its co-founder <PERSON><PERSON>, but now he has stepped things up a notch, it seems. A discussion broke out about which ecosystem is better after a question was posted on ‘X’ if Cardano would ever be an Ethereum competitor.\n\nOne user said that it was telling that Ethereum had to transition to a 2.0 version of what Cardano already is today. Another argued that Cardano followed Ethereum’s roadmap but was behind by years.\n\nAnd then came along an ‘X’ user who said he predicted <PERSON><PERSON><PERSON> would stop talking about Hydra and look towards things that Ethereum developers are doing, essentially copying from them. He added that <PERSON><PERSON><PERSON> would soon say that “Our plan was always that if it works on Ethereum, we can just copy it, and actually, rollups and data availability have always been the future of Cardano too”.\n\n<PERSON><PERSON><PERSON> on Fire\n\nCharles <PERSON> swallowed the bait and said he felt sorry for people obsessed with their blockchain, saying, “We are living rent-free in the maxi minds. I pity them”.\n\nHe reminded the original poster that Hydra, one of the recent improvements to the popular blockchain, is live and evolving rapidly. Additionally, <PERSON><PERSON><PERSON> stated that Mithril is now operational, marking the initial move toward an enhanced ADA and light client approach. He explained that innovative transaction methods have been developed, including tiered pricing and Babel fees. Adding that, the concept of a roll-up strategy has been in place since the inception of Midnight four years ago.\n\nAdvertisement\n\nHe did not only defend Cardano but started to throw punches at Ethereum too, saying: “Meanwhile Ethereum has a dumpster fire of a consensus layer, has a terrible programming model that they can’t change, and are getting eaten alive by their own layer 2 ecosystem.”\n\nNo matter whose side you take, it is clear that Cardano is unfinished, and there is no dispute about that. Cardano’s development runs in stages and is now in the second last stage. While the current stage focuses on scaling and resource use, the final stage will see a fully decentralized community-governed ecosystem. Hoskinson thinks by then, Cardano will serve as a blueprint for other blockchains and be “substantially more decentralized than all cryptocurrencies on the market”."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vdS50b2RheS9wcmUtbWluZS1ldGhlcmV1bS13aGFsZS13YWtlcy11cC1hZnRlci04LXllYXJzLW9mLXNpbGVuY2XSAU1odHRwczovL3UudG9kYXkvcHJlLW1pbmUtZXRoZXJldW0td2hhbGUtd2FrZXMtdXAtYWZ0ZXItOC15ZWFycy1vZi1zaWxlbmNlP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 20 Aug 2023 07:00:00 GMT", "title": "Pre-Mine Ethereum Whale Wakes Up After 8 Years of Silence - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nIn an unexpected development, Whale Alert, a renowned blockchain tracking platform, has unveiled that a long-dormant pre-mine Ethereum address, which lay asleep for a staggering 8.1 years, has been reactivated. The address, previously containing 191 Ethereum tokens with a valuation of approximately $317,724, has sprung to life, sending ripples of intrigue across the crypto space.\n\nAdvertisement\n\n💤 A dormant pre-mine address containing 191 #ETH (317,724 USD) has just been activated after 8.1 years!https://t.co/VJ7RgTzxz3 — Whale Alert (@whale_alert) August 19, 2023\n\nPre-mining is the practice of mining and distributing cryptocurrencies prior to their official launch. The resurgence of this sleeping whale address marks a rare event, one that last occurred nearly three weeks ago at the close of July. A case in point, an Ethereum ICO participant, who had maintained silence for eight years, orchestrated a substantial transfer of 641 ETH, currently valued at around $1.07 million.\n\nMajor bear alert\n\nThe crypto community remains divided over the significance of these reawakenings. The prevailing sentiment suggests that the resurgence may portend bearish market trends. This belief has garnered support from recent historical trends. For instance, following the previous instance of a similar whale reactivation in July, the ETH price plummeted by a staggering 20.42%.\n\nETH to USD by CoinMarketCap\n\nThe skeptics' position finds further validation in the months spanning from May to mid-July, during which weekly awakenings of such dormant wallets precipitated a 17.3% decline in the ETH price.\n\nAs the awakened whale navigates the current market landscape, the community should brace for potential fluctuations. All eyes are now fixated on the awakening whale and the cryptic tides it might usher in.\n\nIn this ever-evolving narrative of crypto trends, the reanimation of a pre-mine Ethereum whale after nearly a decade serves as a poignant reminder of the intricate interplay between early investors, market sentiment and the pulse of digital assets."}, {"id": 20, "url": "https://news.google.com/rss/articles/CBMilQFodHRwczovL2NyeXB0b25ld3MuY29tL25ld3MvZXRoZXJldW0tbGF5ZXItMi1hcmJpdHJ1bS1zdXN0YWlucy1ncm93dGgtYW5kLXVzZXItaW5mbHV4LWZvbGxvd2luZy1hcmItYWlyZHJvcC10b3RhbC1hc3NldHMtcmVhY2gtNTc3LWJpbGxpb24tcmVwb3J0Lmh0bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 20 Aug 2023 07:00:00 GMT", "title": "Ethereum Layer-2 Arbitrum Sustains Growth and User Influx Following ARB Airdrop, Total Assets Reach $5.77 Billion ... - Cryptonews", "content": "Ethereum Layer-2 Arbitrum Sustains Growth and User Influx Following ARB Airdrop, Total Assets Reach $5.77 Billion: Report\n\nSource: Arbitrum\n\nPer <PERSON><PERSON>’s Arbitrum Quarterly Report for Q2 2023, the Arbitrum (ARB) airdrop on March 23, 2023, led to a peak in users and transactions. Following the ARB airdrop, daily transactions and user numbers have consistently exceeded the chain’s past averages.\n\nArbitrum, an Ethereum rollup hosting asset valued at $5.77 billion, sustained robust growth in the previous quarter. Throughout Q2, Arbitrum maintained its momentum by enhancing its infrastructure and advancing its ecosystem.\n\nAfter the ARB airdrop incentives concluded and the airdrop resulted in a “sell-the-news” reaction for the native token and its associated ecosystem projects, noteworthy activity levels persisted, evident in transaction and user counts surpassing pre-airdrop figures.\n\nThe influx of new users underscores Arbitrum’s enduring strength and flourishing ecosystem.\n\nA group of “super airdrop hunters” who amassed ARB tokens using multiple Ethereum addresses contributed significantly to the selling pressure.\n\nThe ARB token experienced significant selling during the airdrop, causing its price to plummet from $10.29 to $1 in just a few hours.\n\n<PERSON>sen’s report stated that the stability of Arbitrum after its Airdrop “points towards more organic activity” as new users have been increasing, even “surpassing Ethereum on certain days in Q2.”\n\nIn the second quarter of 2023, daily transactions and gas prices on Arbitrum One, one of the critical components of Arbitrum utilizing Arbitrum Rollup technology to enhance transaction throughput while maintaining security, remained steady, averaging 800k transactions.\n\nSource: Nansen\n\nThe quarter saw a consistent rise in transaction count compared to the previous quarter, indicating heightened activity since the airdrop. Daily active addresses on Arbitrum remained stable at approximately 200k throughout the quarter.\n\nConversely, there has been a gradual increase in the number of new wallets initiating transactions on Arbitrum since the beginning of the year. On specific days in Q2, this count surpassed Ethereum’s new wallet activity.\n\nArbitrum (ARB) Airdrop Spurs Price Surge and Ecosystem Growth Since March\n\nThe announcement of the Arbitrum (ARB) airdrop on March 16 led to a significant price increase in native tokens of projects within the Arbitrum ecosystem, including GMX (GMX), Magic (MAGIC), Gains Network (GNS), and Radiant Network (RDNT).\n\nThis surge was directly tied to the ARB airdrop, fueling growth in the Arbitrum ecosystem.\n\nThe price surge occurred mainly between the airdrop announcement and its execution on March 23.\n\nAs noted in a report, “Once the airdrop launched alongside the $ARB token introduction, ecosystem token prices began to decrease, following a typical ‘sell the news’ pattern.”\n\nThe Arbitrum airdrop allocated 1.1% of the total 12.75 billion ARB supply to the ecosystem’s DAOs. This allocation was crucial for a positive outlook on the Arbitrum ecosystem, as DAOs could leverage ARB incentives to encourage usage.\n\nHowever, the airdrop amounts for individual projects in the Arbitrum ecosystem were relatively smaller. Only GMX and MAGIC received allocations exceeding $10 million.\n\nMost projects received less than $500,000 worth of ARB tokens, which needed to be improved to incentivize liquidity across a large user base.\n\nWith the speculation surrounding the ARB airdrop subsiding, the growing number of new wallets implies increased organic engagement.\n\nDaily active users, transaction count, and on-chain value have all settled at levels higher than pre-airdrop figures.\n\nThe upward trends in gas fee expenditure and the establishment of new wallets further signify the growing utilization of the network."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiQmh0dHBzOi8vZGFpbHljb2luLmNvbS9ldm0tY2hhaW5zLXdoYXQtaXMtZXRoZXJldW0tdmlydHVhbC1tYWNoaW5lL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 20 Aug 2023 07:00:00 GMT", "title": "EVM Chains: What Is the Ethereum Virtual Machine? - DailyCoin", "content": "EVM Chains are the bread and butter of the crypto space. In select cryptocurrency camps, the Ethereum Virtual Machine (EVM) is considered a non-negotiable feature that emerging blockchains need to support to stay relevant and attract users.\n\nIf you sort the industry’s blockchains by TVL (Total Value Locked), 9 of the top 10 networks are EVM-compatible, with Solana (SOL) being the only exception to the rule.\n\nWhat exactly is the Ethereum Virtual Machine, and why is it so important?\n\nWhat Is the EVM (Ethereum Virtual Machine)?\n\nThe Ethereum Virtual Machine is a software environment that executes code and contracts on EVM networks. An easier way to think of it is to imagine a computer’s operating system. Regardless of whether you use an HP or Lenovo laptop, it’s easy to jump between the two because you’re ultimately communicating with the device through a Windows operating system.\n\nLet’s apply this concept to the world of blockchain technology. The Ethereum Virtual Machine means that whether you’re using Ethereum (ETH) or Polygon (MATIC), things look and feel more or less the same. For example, you don’t need to memorize the Arbitrum (ARB) whitepaper to use crypto wallets like MetaMask or a dex like Uniswap on the Arbitrum network.\n\nIf you’re confident using one EVM Chain, you’re confident using all of them.\n\nHow Does the Ethereum Virtual Machine Work?\n\nWhen you look closely at the EVM, you’ll find a complex system that runs tasks consistently. The Ethereum Virtual Machine is deterministic, meaning if you give it a specific task, it will always give the same result, no matter where it’s done or who does it.\n\nThis deterministic nature is crucial for the consensus mechanism of the EVM-based networks, ensuring all nodes harmoniously agree on the state of the blockchain.\n\nThe EVM operates on a unique set of instructions, allowing for the creation, deployment, and execution of smart contracts. These contracts, written primarily in a programming language called Solidity, are then compiled into bytecode. This bytecode is what the EVM reads, interprets, and executes.\n\nWhen you initiate a smart contract transaction, the EVM springs into action. It processes the transaction, calculates the necessary gas fees, and updates the blockchain’s state.\n\nAnother intriguing facet of the EVM is its Turing completeness, meaning it can perform any calculation that any other programmable computer can, provided it’s given enough time and memory.\n\nHow does this benefit the Ethereum blockchain? Well, it means that the EVM can execute any algorithm or program, granting Ethereum its iconic flexibility. This versatility helps developers create innovative smart contracts and Web 3 dApps (decentralized applications) on the Ethereum mainnet and other EVM chains.\n\nWhy Is EVM Compatibility so Important?\n\nIn an industry fraught with interoperability issues and complex bridging processes, EVM chains help to smooth the onboarding process and give users a sense of trust and familiarity with new networks.\n\nEVM compatibility means that a blockchain can run the EVM and execute Ethereum smart contracts. This makes it easy for blockchain developers to port over existing contracts and ERC-20 tokens to a cross-chain environment and deploy crucial dApps, like decentralized exchanges or NFT marketplaces.\n\nWith the rise of multiple blockchains, each with its unique strengths like faster transactions and lower transaction fees, the ability to communicate and interact between them is of paramount importance. EVM-compatible blockchains integrate more easily than non-EVM chains, enabling assets and data to flow between different chains.\n\nOn top of that, operating an EVM helps emerging chains leverage Ethereum’s existing tools and infrastructure. Being a pioneer in the smart contract space, Ethereum has a rich ecosystem of DApps. EVM compatibility allows other blockchains to tap into this established ecosystem, benefiting from tried-and-tested tools and services without reinventing the wheel.\n\nLooking at competing Layer-1 blockchains that don’t naturally support EVM compatibility, like Cardano (ADA), we see first-hand how difficult it is to onboard new users, especially in DeFi.\n\nDespite Cardano having powerful tech in its own right, the network struggles to attract a user base as large as EVM chains like the BSC, Polygon, and Arbitrum. Cardano developers need to be proficient in the network’s dedicated programming language, Haskell, while all EVM chains use the same languages, like Solidity and Vyper.\n\nWhat’s the Difference Between EVM-Equivalence and EVM-Compatibility?\n\nJust when you thought you’d understood everything, there’s another layer of definitions. In today’s rapidly evolving industry, some blockchain ecosystems insist that simply being EVM-compatible is no longer enough. Teams like Polygon aim for EVM equivalence within the Polygon Proof-of-Stake sidechain and zkEVM.\n\nIs there any difference between compatibility and equivalence?\n\nEVM-Compatibility\n\nEVM-Compatibility represents a blockchain’s ability to run the EVM and execute Ethereum-based smart contracts. An EVM-compatible blockchain can seamlessly integrate with Ethereum’s tools, protocols, and standards.\n\nDevelopers can deploy the same smart contracts across multiple EVM-compatible blockchains without major code modifications.\n\nEVM-Equivalence\n\nOn the other hand, EVM-Equivalence is a more profound alignment with the Ethereum ecosystem. An EVM-equivalent blockchain not only supports Ethereum’s smart contracts but also mirrors Ethereum’s state, account structures, and consensus mechanisms.\n\nIt’s like a twin of the Ethereum network, sharing its DNA but existing as a separate entity. EVM-equivalent blockchains can synchronize with Ethereum’s state and offer a near-identical environment for dApps and contracts.\n\nWhile EVM-Compatibility offers a bridge to the Ethereum ecosystem, allowing for smooth functionality and integration, EVM-Equivalence is like walking in Ethereum’s shoes, mirroring its every step.\n\nExamples Of EVM-Compatible Blockchains\n\nNow that we better understand what EVM Chains are let’s dive into a quick list of the top EVM-compatible blockchains in the crypto industry.\n\nBinance Smart Chain (BSC) – With more daily users than any other network, BNB Chain is easily the most popular EVM chain. It introduced thousands of people to DeFi for the first time and was the first major low-cost alternative to Ethereum.\n\n– With more daily users than any other network, BNB Chain is easily the most popular EVM chain. It introduced thousands of people to DeFi for the first time and was the first major low-cost alternative to Ethereum. Polygon – Polygon is a multi-chain scaling solution for Ethereum-compatible blockchains building various EVM-equivalent networks.\n\n– Polygon is a multi-chain scaling solution for Ethereum-compatible blockchains building various EVM-equivalent networks. Avalanche (AVAX) – Another popular Layer-1 network, Avalanche is a decentralized platform tailored for custom blockchain networks and subnets.\n\nArbitrum – Undoubtedly Ethereum’s most popular Layer-2 scaling solution Arbitrum boasts higher transaction throughput and lower gas fees than the Ethereum mainnet.\n\n– Undoubtedly Ethereum’s most popular Layer-2 scaling solution Arbitrum boasts higher transaction throughput and lower gas fees than the Ethereum mainnet. Optimism (OP) – The pioneers behind ‘rollup’ technology, Optimism was one of the first Ethereum Layer-2s. On top of being EVM-compatible, Optimism has one of the most committed approaches to decentralization among EVM chains.\n\n– The pioneers behind ‘rollup’ technology, Optimism was one of the first Ethereum Layer-2s. On top of being EVM-compatible, Optimism has one of the most committed approaches to decentralization among EVM chains. Fantom (FTM) – Unlike its rivals in this list, Fantom isn’t technically a blockchain. The Fantom network is a directed acyclic graph (DAG) designed for scalability.\n\n– Unlike its rivals in this list, Fantom isn’t technically a blockchain. The Fantom network is a directed acyclic graph (DAG) designed for scalability. Celo (CELO) – Originally designed as a simple payment network, Celo has blossomed into a fully-fledged and functional EVM chain with plenty of dApps and tools for the budding DeFi enthusiast.\n\nEVM Chain Pros and Cons\n\nAfter the invention of Bitcoin (BTC) itself, the Ethereum Virtual Machine (EVM) is arguably the biggest revolution in the blockchain landscape, serving as the backbone for a myriad of decentralized applications and platforms. Of course, it’s not without its faults.\n\nPros\n\nInteroperability – EVM-compatible chains can seamlessly interact with Ethereum-based code, allowing for smooth data and asset transfers between blockchains.\n\n– EVM-compatible chains can seamlessly interact with Ethereum-based code, allowing for smooth data and asset transfers between blockchains. Rich Developer Ecosystem – If you can build dApps on Ethereum, you can build dApps on any EVM chain. This means native Ethereum developers can spread their knowledge throughout the wider blockchain industry.\n\n– If you can build dApps on Ethereum, you can build dApps on any EVM chain. This means native Ethereum developers can spread their knowledge throughout the wider blockchain industry. Standardization – EVM chains provide a standardized environment, ensuring smart contracts behave consistently across different networks.\n\n– EVM chains provide a standardized environment, ensuring smart contracts behave consistently across different networks. Security – The EVM’s isolated environment ensures that smart contracts are executed securely, protecting the network from potential vulnerabilities and external threats.\n\n– The EVM’s isolated environment ensures that smart contracts are executed securely, protecting the network from potential vulnerabilities and external threats. Flexibility – The Ethereum Virtual Machine’s Turing completeness allows developers to craft intricate and versatile smart contracts, catering to various use cases and industries.\n\nCons\n\nScalability Issues – Even on separate networks like Avalanche and BNB Chain, EVM chains are vulnerable to gas spikes and network congestion. Competing networks like Solana and XRP charge consistent transaction costs regardless of network strain.\n\n– Even on separate networks like Avalanche and BNB Chain, EVM chains are vulnerable to gas spikes and network congestion. Competing networks like Solana and XRP charge consistent transaction costs regardless of network strain. Complexity – While the EVM offers immense flexibility, it also introduces complexity. Developers need to be well-versed in specific programming languages like Solidity and aware of decentralized development’s nuances.\n\n– While the EVM offers immense flexibility, it also introduces complexity. Developers need to be well-versed in specific programming languages like Solidity and aware of decentralized development’s nuances. Resource Intensity – Running and maintaining EVM nodes is resource-intensive and requires significant computational power and storage.\n\n– Running and maintaining EVM nodes is resource-intensive and requires significant computational power and storage. Learning Curve – For newcomers, the Ethereum Virtual Machine can present a steep learning curve, marking it difficult to come to terms with.\n\nOn the Flipside\n\nWhile networks like Solana and Cardano don’t natively support EVM-based programs, independent teams have created EVM networks like NEON and Milkomeda that settle transactions on their respective Layer-1.\n\nWhy This Matters\n\nIn the current climate of the crypto industry, running an Ethereum Virtual Machine is essential if teams want to bring users to their blockchain. Without being EVM-compatible, the barriers to entry are too inconvenient.\n\nAs we can see, Cardano is a Top 10 cryptocurrency by market cap, but it has far fewer DeFi applications and TVL statistics than relatively unknown EVM-chains like Kava and Pulsechain.\n\nFAQs"}]