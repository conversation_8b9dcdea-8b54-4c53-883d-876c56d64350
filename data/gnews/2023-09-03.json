[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiO2h0dHBzOi8vdS50b2RheS9ldGhlcmV1bS1ldGgtcHJpY2UtYW5hbHlzaXMtZm9yLXNlcHRlbWJlci0z0gE_aHR0cHM6Ly91LnRvZGF5L2V0aGVyZXVtLWV0aC1wcmljZS1hbmFseXNpcy1mb3Itc2VwdGVtYmVyLTM_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 03 Sep 2023 07:00:00 GMT", "title": "Ethereum (ETH) Price Analysis for September 3 - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nSellers maintain their pressure, according to CoinStats.\n\nAdvertisement\n\nTop coins by CoinStats\n\nETH/USD\n\nThe price of Ethereum (ETH) has almost not changed since yesterday.\n\nImage by TradingView\n\nOn the hourly chart, the price of Ethereum (ETH) has bounced off the local support level at $1,631. If buyers can hold the mark above it, the upward move may continue to the resistance around $1,640 tomorrow.\n\nImage by TradingView\n\nOn the daily time frame, the rate of ETH is trading sideways, which is confirmed by low volatility. The volume keeps falling, which means that neither side is ready for a sharp move.\n\nAll in all, consolidation in the area of $1,620-$1,660 is the more likely scenario for the next few days.\n\nImage by TradingView\n\nOn the bigger chart, traders should focus on the bar closure in terms of the nearest level at $1,571. If the candle closes near it, the drop can continue to the vital level of $1,500.\n\nEthereum is trading at $1,571 at press time."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiUGh0dHBzOi8vY3J5cHRvLm5ld3MvZXRoZXJldW0tY28tZm91bmRlci12aXRhbGlrLWJ1dGVyaW4tZXhpdHMtbWFrZXJkYW8tcG9zaXRpb24v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 03 Sep 2023 07:00:00 GMT", "title": "Ethereum co-founder <PERSON><PERSON> exits MakerDAO position - crypto.news", "content": "<PERSON><PERSON>, the co-founder of the Ethereum (ETH) network, has sold his remaining stake of MakerDAO tokens.\n\nOnchain data from Etherscan, shared by Lookonchain, shows that <PERSON><PERSON><PERSON> sold 500 MKR tokens, which he had held for the last two years.\n\nThe sale, made through CoW Protocol, netted <PERSON><PERSON>n a total of 353 ETH, which is approximately $580,000 at current rates.\n\n<PERSON><PERSON><PERSON> initially invested in MakerDAO in April 2018, purchasing 1071 MKR tokens at $905 each. His recent sale implies a gain of about 27%. The Ethereum co-founder’s last interaction with his MKR tokens was in April 2021, when he donated 100 tokens to India’s Covid relief fund.\n\nHis action follows a Sept. 1 blog entry by <PERSON><PERSON>, MakerDAO’s co-founder and CEO. In the post, <PERSON> detailed ambitious plans for a new iteration of the project on a different blockchain, tentatively named NewChain.\n\nThe last phase of Endgame is the launch of a native blockchain for Maker with the codename NewChain\n\n\n\nIt will make the ecosystem more secure and efficient\n\n\n\nAfter some research, I believe the Solana codebase should be considered as the basis for NewChainhttps://t.co/KyGxBBGlVH — Rune (@RuneKek) September 1, 2023\n\nYou might also like: <PERSON><PERSON>erin throws shade at XRP centralization\n\nMakerDAO may move to Solana\n\nAccording to the MakerDAO frontman, the proposed blockchain could potentially be a fork of Solana (SOL), which is a significant development, especially considering MakerDAO’s current roots in Ethereum.\n\nIn the blog, Christensen explained that his choice to potentially explore Solana is driven by three key reasons. He cited the technical quality and enhancement of the Solana codebase, the ability to outlast hardships displayed by Solana following the collapse of FTX, and the success stories of previous Solana forks, such as the Pyth Network, which runs an adapted version of Solana as its backend.\n\nChristensen envisions a future where NewChain serves as a secure conduit between Ethereum and Solana. However, he did not limit his options to Solana. He also name-dropped Aptos, Sui, and Cosmos as alternatives.\n\nThe main benefits of Cosmos were its “large and high quality” pool of talent, as well as its wide range of independent developers, Christensen said.\n\nRead more: Solana Pay partners with Shopify to enable USDC payments\n\nFollow Us on Google News"}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMiV2h0dHBzOi8vY29pbmdhcGUuY29tL3NvbGFuYS1mb3VuZGVyLWNhdXRpb25zLWFnYWluc3QtZGV0cmltZW50YWwtcml2YWxyeS13aXRoLWV0aGVyZXVtL9IBW2h0dHBzOi8vY29pbmdhcGUuY29tL3NvbGFuYS1mb3VuZGVyLWNhdXRpb25zLWFnYWluc3QtZGV0cmltZW50YWwtcml2YWxyeS13aXRoLWV0aGVyZXVtL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 03 Sep 2023 07:00:00 GMT", "title": "Solana Founder Cautions Against Rivalry With Ethereum - CoinGape", "content": "Solana (SOL) founder <PERSON><PERSON><PERSON> has taken to the X social media platform, formerly known as Twitter to warn his community members against hurling hate statements and attacks on Ethereum (ETH). MakerDAO Co-founder and Chief Executive Officer <PERSON><PERSON> had to also take the same action.\n\nMakerDAO CEO on Solana Blockchain\n\nIt all started after <PERSON> announced plans to re-implement MakerDAO on a new blockchain identified as NewChain. According to the Decentralized Finance (DeFi) giant, this blockchain could be forked from the Solana (SOL) blockchain. In effect, this would mean that MakerDAO will be moving away from the Ethereum-based architecture which it currently utilizes.\n\nadvertisement\n\nExplaining further, the MakerDAO CEO claimed that the Solana codebase is the “most promising” to explore for NewChain due to its technical quality and optimization, resilience of the Solana ecosystem following the “FTX blowup,” and past examples of successful forks of the protocol, like the Pyth Network.\n\nIn response to this potential development, <PERSON><PERSON><PERSON> acknowledged that MakerDAO’s move is impressive, citing that it is a win for open source. At the same time, he warned that it should not be perceived as an endorsement of one blockchain over the other.\n\n“I really hope that people in the Solana community don’t use this as some cudgel to attack Ethereum,” <PERSON><PERSON><PERSON> said in a recent post. He even advocated for Ethereum noting that the protocol is “awesome.”\n\nEthereum is awesome. Solana wasn’t built in a vacuum, and tons and tons of things that make @solana special were built based on ethereum’s r&d. — toly 🇺🇸 (@aeyakovenko) September 3, 2023\n\nCrypto Leaders Advocate Collaborative Efforts\n\nUnlike the narrative that is being peddled by some community members, Christensen is confident that in the future, NewChain would serve as a bridge between Ethereum and Solana. He also shared that in the long run, this will provide “a useful boost to the network effect of the entire multichain economy.”\n\nHe went on to acknowledge Cosmos as a close alternative to the NewChain codebase.\n\nPaying close attention to Christensen’s tweet, one would see that he was clearly appreciating multiple blockchains including Ethereum, Solana and Cosmos.\n\n“All chains are increasingly interconnected and synergize to form a global multichain network economy.”\n\nHowever, enthusiastic community members seem to have taken him out of context and ultimately, it looks like crypto industry leaders are beginning to get more interested in collaborative technological advancements, rather than encourage rivalry."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vZGVjcnlwdC5jby8xNTQ4MDIvcnVuZS1jaHJpc3RlbnNlbi1tYWtlcmRhby1zb2xhbmEtZXRoZXJldW0tbmV3Y2hhaW7SAVJodHRwczovL2RlY3J5cHQuY28vMTU0ODAyL3J1bmUtY2hyaXN0ZW5zZW4tbWFrZXJkYW8tc29sYW5hLWV0aGVyZXVtLW5ld2NoYWluP2FtcD0x?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 03 Sep 2023 07:00:00 GMT", "title": "‘Ethereum Is Awesome', <PERSON><PERSON> Co-Founder <PERSON><PERSON> Planned MakerDAO Migration - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nMakerDAO’s controversial endgame inches closer.\n\nA multi-year Maker protocol overhaul, dubbed \"Endgame,\" is reaching its final phase, according to protocol co-founder <PERSON><PERSON>—setting the stage for the resurgence of a timeless rivalry.\n\n“The last phase of Endgame is the launch of a native blockchain for <PERSON> with the codename NewChai<PERSON>,” tweeted <PERSON> late Friday.\n\nAD\n\nAD\n\nThe crux of the controversy lies in the tweet’s final words: “After some research, I believe the Solana codebase should be considered as the basis for NewChain.”\n\nOutlining three main reasons, <PERSON><PERSON> explained that it is due to the technical quality of the blockchain, as well as its resilience after the FTX blow up, and proven examples of Solana’s codebase being forked for other successful projects.\n\nIf his preference comes to pass, it would mean that MakerDAO—which up until now has lived on Ethereum—will essentially migrate to a long-time rival.\n\n<PERSON>’s endorsement of Sol<PERSON> sparked backlash on Crypto Twitter, with many users questioning the decision.\n\n“For your information,” said one user, “your user base primarily has more allegiance to Ethereum,” adding that <PERSON>should probably give that more weight” than he is.\n\nAD\n\nAD\n\nEthereum and Solana communities have been at each other's necks for years, with the latter’s co-founder once appearing on CNBC to declare that “Ethereum is way too slow for peer-to-peer payments.”\n\nThings have changed in 2023, however.\n\n<PERSON>toly ‘Toly’ <PERSON>kov<PERSON>, <PERSON>ana’s co-founder, preemptively took to Twitter to stave off a possible attack from his own protocol's community.\n\nMaker considering @solana’s tech is a win for open source, and has nothing to do with solana’s mainnet or sol vs eth. I really hope that people in the @solana community don’t use this as some cudgel to attack ethereum. — toly 🇺🇸 (@aeyakovenko) September 3, 2023\n\n“Maker considering Solana’s tech is a win for open source,” he posted, adding that he “really hope[s] that people in the community don’t use this as some cudgel to attack Ethereum.”\n\nYakovenko then went a step further and tweeted “Ethereum is awesome,” and that a lot of Solana’s tech stack was inspired by Ethereum’s research and development. “Solana wasn’t built in a vacuum.”\n\nSolana’s co-founder pointed to proto-danksharding, an upcoming Ethereum upgrade that he considers “a good design for a settlement layer.”\n\nSome were quick to counter that he had \"gone soft” on Ethereum. Another wrote, \"The mere utterance of Solana next to Maker is fascinating in itself. Excited to see their next steps.\"\n\nAD\n\nAD\n\nToly’s views were echoed by Matias Barrios, security researcher for Kudelski Security and a Solana programs auditor, who said he is surprised by the reaction of the Solana community. He claimed that he had not “seen any mocking” of Ethereum.\n\nAs he told Decrypt, “Christensen’s backing is an important validation [for Solana] coming from one of the strongest projects in the ecosystem.”\n\nBarrios said he agrees with Yakovenko’s peace-keeping efforts, adding that “tribalism is the worst we can do in innovation,” and that “Ethereum and Solana are simply two different roads.”\n\nNot to be sidelined by the possible reignition of rivalry, MakerDAO’s Christensen also heeded Yakovenko’s words of warning.\n\n“Ethereum is great, Solana is great. Cosmos is great,” he posted on Saturday.\n\n“All chains are increasingly interconnected and synergize to form a global multi chain network economy,” he wrote, concluding that “Tribalist incels (of every flavor) are in the wrong decade—go back to Bitcoin and let the rest of us build.\""}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vZGFpbHljb2luLmNvbS93aGF0LWlzLW9wdGltaXNtLWNyeXB0by1ldGhlcmV1bS1wcmVtaWVyLWxheWVyLTIv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 03 Sep 2023 07:00:00 GMT", "title": "What Is Optimism Crypto? - DailyCoin", "content": "What is Optimism? Is it an innate sense of positivity; a belief that everything will turn out for the best?\n\nTo ordinary people, yes. That’s exactly what it is. For crypto enthusiasts, on the other hand, Optimism is something quite different.\n\nOptimism is an Ethereum Layer-2 scaling solution that aims to make Vitalik Buterin’s blockchain faster, affordable, and ultimately more accessible. As the Ethereum blockchain has grown in popularity and usage, it’s become clear that the network’s sluggish speeds and extortionate gas fees are ill-equipped for crypto mass adoption.\n\nWhat is Optimism (OP), and how does it work? In a crowded niche, what separates Optimism from the dozens of other Layer-2 blockchains like Arbitrum (ARB) or Polygon zkEVM?\n\nWhat is Optimism (OP)?\n\nOptimism, known by its native token symbol OP, is one of the Ethereum network’s leading Layer-2 scaling solutions. It resolves the litany of issues plaguing the Ethereum blockchain, namely scalability and high transaction fees.\n\nThe Optimism project is spearheaded by the Optimism Foundation, a passionate team of developers and decentralization enthusiasts dedicated to improving Ethereum (ETH) and ensuring its long-term growth and sustainability. The foundation’s work has given birth to one of the most promising Layer-2 solutions, with Optimism going from strength to strength since its launch.\n\nHow Does Optimism Work?\n\nCreatively named, Optimism is what crypto people call an Optimistic Rollup. In simple terms, Optimistic rollups are a method of batching groups of Layer-2 blockchain transaction data into one ‘rolled up’ transaction that can then be secured on the Ethereum mainnet, a Layer-1.\n\nProcessing transactions off-chain significantly reduces the load on the main network, meaning that Layer-2 users can enjoy all of Ethereum’s functionality without suffering its low speeds and crippling gas fees.\n\nA sequencer handles optimism block production. The sequencer is responsible for instant transaction confirmations and blockchain state updates, constructing and executing Layer-2 blocks, and submitting user transactions to Layer-1.\n\nWhy are they called Optimistic rollups, you ask? When it comes to blockchain security, Optimism operates on the principle of ‘fault proofs.’ This means that node operators operate on the ‘optimistic’ approach that the state of the blockchain is correct, secure, and doesn’t require proof of validity. It is removed and replaced if this state is challenged and found faulty.\n\nAs is always the case among Layer-2s, Optimism runs an EVM (Ethereum Virtual Machine). This makes it easy for Ethereum developers to redeploy their favorite EVM-based dApps and smart contracts directly on Optimism and gives users a sense of familiarity with the network.\n\nThe Optimism Token\n\nAt the heart of Optimism is the OP token, the native cryptocurrency token of the network. OP is an ERC-20 token that plays a crucial role in the governance and future development of the Optimism network, serving as the requirement for OP community members to join and contribute to decision-making processes. It may come as a surprise, but OP is not used to pay Optimism gas fees.\n\nOne of the largest crypto airdrops to date, the OP token was distributed to hundreds of thousands of Optimism wallets. Those lucky qualifying addresses were people who’d positively contributed to Optimism’s growth, whether they were DAO voters, multi-sig signers, or simple users who interacted with Optimism dApps.\n\nOP’s first airdrop shared over 200M OP tokens and marked the birth of Optimism’s governance model, known as the Optimism Collective. This governing body collective comprises two separate houses: The Token House and the Citizens’ House.\n\nComprised of OP token holders, The Token House handles protocol governance. Through decentralized governance proposals, the Token House manages everything from protocol upgrades to inflation adjustments, treasury appropriations, rights protection, and governance fund grants.\n\nOn the other hand, the Citizens’ House is an experimental attempt at a non-plutocratic governance model, which aims to draw power and influence away from crypto whales. The Citizen’s House manages retroactive public goods funding, which rewards builders and developers who create products and services that grow the Optimism ecosystem.\n\nOptimism Ecosystem\n\nAs you’d expect from a top Layer-2 network, the Optimism ecosystem is a vibrant and dynamic space with an excellent variety of dApps. Thanks to roll-up technology, Optimism is paving the way for a new era of Ethereum-based applications that are faster, cheaper, and more efficient than what’s available on the mainnet.\n\nDeFi\n\nDecentralized Finance, or DeFi, is undoubtedly one of the most transformative applications of blockchain technology. Optimism’s EVM compatibility and low fees have made it the perfect playground for creative DeFi applications.\n\nClassic decentralized exchanges like Uniswap and Sushiswap have both moved in and set up camp on Optimism, but they’re outclassed in TVL (Total Value Locked) and trading volume by Velodrome, Optimism’s native DEX.\n\nOther DeFi platforms on Optimism can offer financial services like lending, borrowing, and yield farming through popular platforms like AAVE and Optimism’s dedicated lending market, Sonne Finance. Synthetix (SNX), another popular application on the network, supports derivatives and synthetic asset trading.\n\nNFTs\n\nWhile Optimism DeFi is a force to be reckoned with, NFTs on the network have struggled to stay relevant. What’s more interesting is that this trend is consistent across all Layer-2 networks, not just Optimism.\n\nEven though Layer-2 networks, whether they’re optimistic rollups or zk-rollups (zero-knowledge), make it cheaper and easier to trade and mint NFTs, NFT culture still resides largely on Layer-1 networks like Ethereum and Solana (SOL).\n\nDespite iconic NFT marketplaces like Opensea integrating support for Optimism, NFT trading volume is almost non-existent.\n\nA quick look at the top collections page on Quix, Optimism’s top NFT marketplace, shows that the last week of trading volume barely tips over 0.1 ETH. Opensea doesn’t fare much better, with the world’s most recognizable NFT platform tipping the scales at around two ETH per week.\n\nThe Optimism Superchain\n\nTo expand Ethereum’s capabilities further than we’ve ever thought possible, Optimism has set out to build a horizontally scalable network of blockchains called the Optimism Superchain. With the vision of growing a wider network of standardized, open-source Layer-2 networks, the Superchain tackles Ethereum’s scalability crisis from a new angle.\n\nThe central engine of the Superchain is the OP Stack, an open-source development toolkit that unifies all Superchain networks and ensures they’re all beating to the same rhythm. It guarantees that all networks are built using the same security features and communication systems to maintain a cohesive ecosystem.\n\nMoreover, the Superchain introduces a new revenue model that rewards developers with fees from their respective chains and applications. Optimism’s Superchain has proven a popular choice for large-scale businesses, including Worldcoin (WLD) and Coinbase’s Base Chain.\n\nOptimism vs. Arbitrum\n\nTwo names reign supreme in the fierce and competitive race to become the ultimate Ethereum scaling solution. At face value, there’s not a huge difference between Optimism and Arbitrum. Both networks are optimistic rollups that improve Ethereum’s scalability.\n\nUnderneath the hood, the main difference is how they handle fraud proofing. While Optimism uses single-round fraud proofs, Arbitrum takes this one step further with multi-round proofs. The result? Optimism is a faster network, but it runs the risk of having higher gas fees than Arbitrum during periods of Ethereum congestion.\n\nDiving into the on-chain metrics, Arbitrum is king in the eye of DeFi users. According to DeFiLlama, Arbitrum has more than double Optimism’s TVL (Total Value Locked) and almost 3x as many protocols built on the network. But if you’re an Optimism fan, don’t lose heart.\n\nRecent trends indicate a change in the guard. Over the past few months, daily transactions and active addresses on Arbitrum have steadily declined, while the same metrics on Optimism show sustained growth.\n\nOptimism Pros and Cons\n\nShould we be feeling optimistic or pessimistic about Optimism? Let’s dive into the pros and cons.\n\nPros\n\nScalability – Optimism offers greater transaction speeds and throughput than the Ethereum mainnet, making it better suited to supporting a large user base.\n\n– Optimism offers greater transaction speeds and throughput than the Ethereum mainnet, making it better suited to supporting a large user base. Optimism Superchain – The Optimism Superchain provides a new approach to Ethereum scaling by expanding horizontally and vertically.\n\n– The Optimism Superchain provides a new approach to Ethereum scaling by expanding horizontally and vertically. Affordability – Along with improved scalability, Optimism boasts lower gas fees, making it cheaper to use and lowering the barrier to entry.\n\n– Along with improved scalability, Optimism boasts lower gas fees, making it cheaper to use and lowering the barrier to entry. Compatibility – Courtesy of the EVM, Optimism is designed to be fully compatible with the Ethereum ecosystem’s existing smart contracts.\n\n– Courtesy of the EVM, Optimism is designed to be fully compatible with the Ethereum ecosystem’s existing smart contracts. Security – Like most Layer-2s, Optimism leverages the security of the Ethereum network, providing a level of security that matches the mainnet.\n\n– Like most Layer-2s, Optimism leverages the security of the Ethereum network, providing a level of security that matches the mainnet. Decentralization & Community – With the introduction of the OP token and the Optimism Collective governance model, Optimism is decentralized and community-driven.\n\nCons\n\nWithdrawal Delay – Transferring assets from Optimism to the Ethereum mainnet often involves a seven-day withdrawal delay. This could be inconvenient for users who need to move their assets quickly.\n\n– Transferring assets from Optimism to the Ethereum mainnet often involves a seven-day withdrawal delay. This could be inconvenient for users who need to move their assets quickly. Early Stage Development – While Optimism has shown much promise, it’s important to remember that it’s still in the early stages of development.\n\n– While Optimism has shown much promise, it’s important to remember that it’s still in the early stages of development. Competition – Optimism faces stiff competition from other Layer 2 solutions, such as Arbitrum and zkSync. The network’s success depends not only on its own merits but also on how it stacks up against its rivals.\n\nOn the Flipside\n\nOptimism is just one Layer-2 provider among a sea of competitors. Dozens of Layer-2 networks are joining the race, some with greater resources and more modern infrastructure. While Optimism was one of the first Layer-2s to hit the market, there is a chance its competitors will outperform it.\n\nWhy This Matters\n\nEthereum is undeniably the largest Layer-1 blockchain in the industry. However, its scalability concerns make it unusable for most of the global population. Optimism resolves these problems while retaining a focus on decentralization that Ethereum can be proud of.\n\nFAQs"}]