[{"id": 12, "url": "https://news.google.com/rss/articles/CBMiUGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDkvMTMvaGVsbG8taG9sZXNreS1ldGhlcmV1bXMtbmV3ZXN0LXRlc3RuZXQv0gFUaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL3RlY2gvMjAyMy8wOS8xMy9oZWxsby1ob2xlc2t5LWV0aGVyZXVtcy1uZXdlc3QtdGVzdG5ldC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 13 Sep 2023 07:00:00 GMT", "title": "Hello Holesky, Ethereum’s Newest Testnet - CoinDesk", "content": "Goerli and Sepolia are operated by a smaller subset of “validators” than the main Ethereum chain, and some developers think these smaller validator sets pose a problem: “We don't want to hit a scaling issue that could happen first on mainnet,” <PERSON><PERSON><PERSON>, a devops engineer at the Ethereum Foundation, a non-profit focused on Ethereum research and development, told CoinDesk. “We want to catch [scaling issues] on testnet, which means we have to have a testnet that's bigger” than the main Ethereum chain."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3ZpdGFsaWstYnV0ZXJpbi1mdXR1cmUtYnVpbHQtZXRoZXJldW3SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 13 Sep 2023 07:00:00 GMT", "title": "Vitalik Buterin previews a future built on Ethereum - Blockworks", "content": "Vitalik Buterin doesn’t want Ethereum to rest on its laurels, but to keep pressing forward with new ideas and approaches to old problems.\n\nLooking around at the app landscape today, <PERSON><PERSON><PERSON> sees chutes of the future sprouting, but even the newest of the new is “not imaginative enough,” he told the Permissionless audience in Austin, Texas, during an interview with <PERSON>.\n\nDeFi “is cool,” NFTs are a new primitive but “an extension of something that has a history,” and using crypto for payments is good, but also familiar. These are “individual pieces that are designed to fit into an ecosystem that’s otherwise the same as before,” <PERSON><PERSON><PERSON> said.\n\nWhat he’s excited about is decentralized social, repeatedly name-dropping Farcaster, a Twitter-like protocol built on the OP mainnet with a companion Warpcast mobile app that is currently in invite-only alpha release.\n\nAlong with Lens, developed by Aave-founder <PERSON><PERSON> and running on Polygon’s proof-of-stake chain, Farcaster and similar social experiences are using crypto tools to compete with centralized platforms.\n\n“Let’s see how far we can push things in that direction,” <PERSON><PERSON><PERSON> said. “But where I see the longer term future here is it really can plug into all of the other stuff that we’ve been doing as a space.”\n\nBuilding Sybil-resistance for example, so that “the 894 likes are 894 people, and not 894 accounts that are all controlled by the Kremlin.”\n\nCrypto-based solutions have the potential to be more decentralized, privacy-preserving and secure than the current Web2 landscape, according to <PERSON><PERSON><PERSON>, citing building blocks such as ENS, various proof-of-humanity ideas, and POAPs — or a next generation version using zero-knowledge technology that will be even better — and Gitcoin Passport, as examples.\n\n“The big dream here is to like, really create an independent open tech stack,” Buterin said. Something to compete with the likes of Google and Twitter, without going down a closed, centralized system such as China’s tight integration of Wechat, Alipay and increasingly CBDC.\n\nButerin envisions a gradual on-ramping process for new Web3 users to create an Ethereum address, initially controlled by a familiar service like Gmail, but using account abstraction, giving people the ability to take ownership when they are ready to use more crypto-native approaches.\n\n“Basically, give people the opportunity to slide down the decentralization ladder and then, at the end of it, really properly be in this totally independent stack that actually works together — all the different pieces inside it,” he said.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMidWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA5LzEzL3Zpc2Etc2F5cy10aGlzLWV0aGVyZXVtLXJpdmFsLWhhcy11bmlxdWUtdGVjaG5vbG9naWNhbC1hZHZhbnRhZ2VzLW92ZXItb3RoZXItY2hhaW5zL9IBeWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA5LzEzL3Zpc2Etc2F5cy10aGlzLWV0aGVyZXVtLXJpdmFsLWhhcy11bmlxdWUtdGVjaG5vbG9naWNhbC1hZHZhbnRhZ2VzLW92ZXItb3RoZXItY2hhaW5zL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 13 Sep 2023 07:00:00 GMT", "title": "Visa Says This Ethereum Rival Has ‘Unique Technological Advantages’ Over Other Chains - The Daily Hodl", "content": "Credit card giant Visa says that one Ethereum (ETH) competitor has unique technological advantages over other blockchains as a payments platform.\n\nIn a new company blog post, the financial services titan says that smart contract platform Solana (SOL) has distinctive features that set it apart from its rivals, such as low and predictable transfer fees, high throughput rates and significant node support.\n\nAccording to Visa, Solana even has the upper hand against the king crypto Bitcoin (BTC) and ETH in some aspects.\n\n“Solana’s transaction fees are not only affordable, usually less than $0.001, but predictable. This level of low-cost predictability helps make it an attractive network to explore efficiencies and cost savings for existing payment operations.\n\nIn [the figure] below, Solana clearly stands out from a cost perspective compared to Bitcoin and Ethereum, whose fees can fluctuate unpredictably based on demand for transactions to execute on the network. A network with unpredictable transaction costs can be more difficult for payment companies to manage within their products and can lead to confusing consumer experiences.”\n\nVisa goes on to say that because of Solana’s efficiency, they have decided to expand their stablecoin settlement program to include Solana as a means of testing the blockchain’s capacity to meet the financial demands of modern corporations.\n\n“Solana’s unique technological advantages, including high throughput with parallel processing, low cost with localized fee markets and high resiliency with a significant number of nodes and multiple node clients, work together to create a scalable blockchain platform with a compelling value proposition for payments.\n\nThese are some of the reasons that we decided to expand our stablecoin settlement pilot to include transactions over the Solana network. As we pilot our stablecoin settlement functionality on Solana, we plan to test whether Solana has the ability to meet the demands of modern corporate treasury operations.”\n\nSolana is trading for $18.39 at time of writing, a 2.19% increase during the last 24 hours.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/Fortis Design/Sol Invictus"}, {"id": 13, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3JvbGx1cHMtc2NhbGUtZXZtLWV0aGVyZXVtLW1vbmFkLWxhYnPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 13 Sep 2023 07:00:00 GMT", "title": "Scaling Ethereum's virtual machine is a 'solvable problem,' says Monad Labs' Galler - Blockworks", "content": "The Ethereum scaling story tends to center around layer-2 rollups, but what about the EVM itself? Does the virtual machine suffer from limitations that are inherent to the execution layer of the system, or can developers overcome them?\n\nWhile rollups take the computation load off the Ethereum mainchain, Blockworks co-founder <PERSON> wondered how the computation that occurs directly on Ethereum could be more “performant.”\n\n“Do rollups solve all of our scalability challenges, or is there still work to be done on the execution layer?” he asked during the Permissionless II conference in Austin, Texas.\n\nMonad Labs developer relations engineer <PERSON> replied: “A lot of the current limitations of the EVM we know today are actually more of a side effect of how the backend computer of this EVM has been constructed up until now,” but are not inherent to the interface or the bytecode.\n\n“You can actually design parallelism and stuff into this,” he said, acknowledging that “there’s stuff you have to work around to maintain backwards compatibility.”\n\n“There’s like, 20 years of high performance computing research on how to do these things,” he said, “so, it’s a very solvable problem.”\n\nOffchain Labs senior software engineer <PERSON><PERSON> admitted that the current implementation of the EVM is “fairly slow for many reasons,” adding “there are many things we can do to power that up.<PERSON>\n\n<PERSON> noted that developers are already accustomed to being able to build DeFi primitives and other “really cool applications” with the EVM as it stands today, but wondered “how can we give them superpowers to build something greater than they have before?”\n\n“How can we empower them?” he asked. “Should we make the EVM faster, or can we introduce new constructs that allow them to do things that weren’t possible before?”\n\nCan’t cheat physics\n\nSomani expressed concerns with the possibility of an excess of layer-2 protocols. “The issue with the single threaded EVM…is that it results in a lot of rollups.”\n\n“There’s people suggesting there should be millions of rollups or something,” Somani continued. “That’s going to lead to a lot of issues with liquidity fragmentation and end-to-user experience.”\n\n“Sufficiently advanced virtual machines” have local fee markets, Somani said, “so that if there’s a lot of activity on one app, it doesn’t congest all those.”\n\n“So bringing that [virtual machine] to Ethereum can lead to this highly parallelized [layer-2] that doesn’t really need all these other robots to exist,” he said.\n\n“You can’t cheat physics,” Galler interjected. “Bandwidth is fundamentally what limits how fast a lot of these blockchains can run.”\n\nReflecting on scaling challenges, Jordan referred to the example of Arbitrum Stylus, noting that the product allows developers to write code in common programming languages like Rust and C++, compiling to a special instruction format called WebAssembly. “You can actually execute this side by side with EVM smart contracts.”\n\n“So we ended up realizing that the EVM is not the ceiling,” he said, “it’s actually the floor.”\n\n“You can do a lot more than you thought was possible while still maintaining the same guarantees.”\n\nScalability at the execution layer “has a long way to go,” Jordan added, “and we’re trying to get there.”\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiQ2h0dHBzOi8vYW1iY3J5cHRvLmNvbS93aHktZXRoZXJldW1zLTYtbW9udGgtbG93LW1heS1ub3QtYmUtdGhlLWVuZC_SAUdodHRwczovL2FtYmNyeXB0by5jb20vd2h5LWV0aGVyZXVtcy02LW1vbnRoLWxvdy1tYXktbm90LWJlLXRoZS1lbmQvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 13 Sep 2023 07:00:00 GMT", "title": "Why Ethereum’s 6-month low may not be the end - AMBCrypto News", "content": "ETH could drop below $1,500 as many old coins changed wallets.\n\nThe sentiment remained bearish, but ETH’s burn mechanism could rescue the altcoin in the long-term.\n\nEthereum’s [ETH] plunge to $1,540 has been accompanied by interesting changes that could push the altcoin into capitulation, Santiment revealed. The on-chain analytic platform, in its 12 September post on X (formerly Twitter), noted that a large number of ETH have been transferred from old wallets.\n\n? #Ethereum dropped to $1,540 for the first time since March 12th, and this coincided with large quantities of stagnant $ETH moving away from old wallets. A continued dip in mean $ age while prices drop is a capitulation sign, which foreshadows reversals. https://t.co/50jK2C7aLi pic.twitter.com/4RhtlVX3rr — Santiment (@santimentfeed) September 12, 2023\n\nOldies leave their former abode\n\nThe post focused on using the Mean Coin Age (MCA) to decipher the possibility. The MCA is the sum of a coin’s Unspent Transaction Output (UTXO) alive at the time of coin creation.\n\nAs a long-term indicator, a drop in the MCA implies a massive movement of UTXOs that have been immobile for a long period.\n\nRead Ethereum’s [ETH] Price Prediction 2023-2024\n\nAt the time of writing, ETH’s 90-day MCA had decreased to 41.07. This further affirmed Santiment’s position that a big fall could be close. Although the 90-day dormant circulation has now reduced, the surge to 634,000 on 11 September reinforced the notion that old coins were moving in droves.\n\nSo, the spike implies that it’s not just the two to five years dormant coins on Ethereum that are moving. Those who have remained stagnant for just three months joined the party.\n\nPreviously, AMBCrypto had explained why ETH’s price action could remain bearish. However, Santiment mentioned that after the rain comes sunshine, noting that relief could come to ETH after the projected price drop.\n\nIs light at the end of the tunnel?\n\nHowever, traders do not expect the recovery to be anytime soon, as shown by the funding rate. As of this writing, ETH’s funding rate was -0.003%. Since perpetual futures contracts can be held indefinitely, it becomes very necessary to have the funding rate.\n\nFunding rate is the amount of an asset paid between long and short-positioned traders with open contracts. When the funding rate is positive, it means that longs are paying too short to keep their position. In this case, traders’ sentiment is bullish.\n\nBut a negative funding rate means that shorts are paying longs a funding fee. Here, like it was with ETH, the sentiment is bearish. So, the broader bias was for ETH to drop much below $1,500.\n\nFor now, Ethereum may need to depend on other metrics besides its market-based indicators in the hope of recovery. One metric that comes to mind is the Ethereum burned supply.\n\nThis Ethereum burned supply represents the cumulative sum of ETH incinerated since the implementation of the EIP-1559. For context, the EIP-1559 was implemented during the London Hard Fork— the same period Ethereum began the burn mechanism.\n\nIn EIP-1559, the base for transactions is not sent to any miner/validator. Rather, it is burned as a means to reduce ETH’s supply and improve its value in the long term.\n\nIs your portfolio green? Check out the ETH Profit Calculator\n\nLately, ETH turned inflationary, which could be a cause for concern. However, the burned supply of Ethereum had also increased to 4.25 million (as displayed above).\n\nIf the number continues to increase, then ETH may get back its deflationary pressure, and in the long term, could be profitable for its price."}]