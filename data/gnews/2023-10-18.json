[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiO2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3JlZGRpdC1jb21tdW5pdHktcG9pbnRzLWV0aGVyZXVt0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Reddit to end Ethereum-based Community Points - Blockworks", "content": "Reddit has announced the end of its Community Points beta program, set to take place next month.\n\nReddit cited “scalability limitations” and an uncertain regulatory landscape as contributing factors to the program’s discontinuation, according to a Tuesday statement. Reddit did not immediately respond to a request for comment.\n\n“Though we saw some future opportunities for Community Points, there was no path to scale it broadly across the platform,” Reddit user u/cozy__sheets, who is part of the Community team, wrote.\n\nIt’s unclear which specific regulations Reddit is concerned about, though some users pointed to the possibility of it centering on tax complications or securities classifications.\n\nRead more: Global securities regulator calls out MEV in DeFi, highlighting ‘unlawful’ nature\n\nReddit introduced Community Points, built atop Ethereum, to incentivize participation within specific subreddit communities roughly three years ago.\n\nUsers earned tokens — referred to as “Moons” in the r/CryptoCurrency subreddit — for activities like posting and commenting. Others, such as “Donuts” and “<PERSON>s” tokens, were also earned across subreddits r/EthTrader and r/FortniteBR, respectively.\n\nThese tokens could then be used for a variety of features, such as purchasing subreddit-specific awards. They could also be bought and sold on decentralized exchanges Uniswap and Sushiswap.\n\nRead more: Uniswap Labs to institute 0.15% fee on select assets and frontends\n\nNext month, users will no longer see Points in their Reddit Vault — the platform’s digital wallet — nor will they be able to earn points in their respective communities, the statement reads.\n\nModerators of r/CryptoCurrency appeared caught off guard by the announcement. “We are very disappointed in Reddit’s decision today and want to clarify that we were not made aware of this decision until one hour ago,” they said in a statement.\n\nIn a stickied comment, the moderators clarified the “Moons” currently held by community members would remain intact. “Transfer functionality in the smart contract is not being shut off and Reddit is removing their control over the contract,” the moderators wrote.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiXmh0dHBzOi8vY3J5cHRvLm5ld3Mvdml0YWxpay1idXRlcmluLWRpc2N1c3Nlcy1ldGhlcmV1bXMtZnV0dXJlLWFuZC1pbm5vdmF0aXZlLXByb2plY3RzLWluLWFtYS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "<PERSON><PERSON> discusses Ethereum's future and innovative projects in AMA - crypto.news", "content": "In a recent interactive session, Ethereum’s co-founder, <PERSON><PERSON>, shed light on the platform’s technical advances and celebrated emerging decentralized projects within its ecosystem.\n\nIn a recent Ask Me Anything (AMA) session, Ethereum Co-founder <PERSON><PERSON> touched on several topics that offer a glimpse into the potential trajectory of the Ethereum ecosystem. Drawing insights from a report by Odaily news, the discourse ranged from technical advancements to acknowledgments of decentralized projects, weaving a narrative of Ethereum’s evolving landscape.\n\nForemost, <PERSON><PERSON><PERSON> delved into the intricacies of quantum resistance—a burgeoning concern in the blockchain community given the potential of quantum computing to challenge existing cryptographic systems. He discussed the role of account abstraction, specifically through Ethereum’s proposal ERC-4337.\n\nThis initiative, according to <PERSON><PERSON><PERSON>, can imbue users’ accounts with quantum-resistant properties, fortifying Ethereum against future threats posed by quantum computers. Such a development underscores the proactive approach the Ethereum community is taking to ensure the blockchain’s continued security and relevance.\n\nYou might also like: Ethereum co-founder <PERSON><PERSON> calls for privacy and decentralization\n\nBeyond the technical realm, <PERSON><PERSON><PERSON> also spotlighted some decentralized projects that exemplify innovation and impact within the Ethereum ecosystem. Polymarket, a decentralized prediction market platform, garnered Buterin’s praise for its strides in decentralized finance (DeFi).\n\nThe platform allows users to speculate on future events, illustrating the versatility of DeFi applications. PoolTogether, a no-loss lottery system built on Ethereum (ETH), similarly caught Buterin’s attention. This unique model lets users pool funds, earn interest and stand a chance to win prizes—a testament to the creative possibilities of blockchain technology.\n\nOn a more personal tangent, Buterin expressed his admiration for several cities around the globe. Singapore, Taipei, Vienna, Toronto and the New York State’s City of Ithaca made the list, each resonating with Buterin for their distinctive mix of technological innovation and cultural vibrancy.\n\nIn wrapping up, the insights from Buterin’s AMA serve as a compelling indicator of Ethereum’s forward momentum, with an emphasis on both technological foresight and the value of innovative decentralized projects.\n\nFollow Us on Google News"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvY3J5cHRvLWJ1bGwtc2NhcmFtdWNjaS1zZWVzLTN4LWdhaW5zLWZvci1iaXRjb2luLWV0aGVyZXVtLWFuZC1hbGdvcmFuZC1oZXJlcy13aHnSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Crypto Bull Sc<PERSON>cci Sees 3X Gains For Bitcoin, Ethereum, And Algorand — Here's Why - Nasdaq", "content": "SkyBridge Capital founder, <PERSON> has shed a bullish light on the future price trajectories of Bitcoin (CRYPTO: BTC), Ethereum (CRYPTO: ETH), and Algorand (CRYPTO: ALGO), suggesting hearty targets for these cryptocurrencies, detailing the intrinsic factors that could drive such a bullish trend.\n\nThis optimistic outlook comes just ahead of Benzinga's Future of Digital Assets conference on Nov. 14, which aims to delve into the evolving dynamics of digital assets. Discussions about these predictions by <PERSON><PERSON><PERSON><PERSON> could form a riveting part of the narrative as industry experts explore the potential future of cryptocurrencies.\n\n<PERSON><PERSON><PERSON><PERSON> postulates a bullish scenario for Bitcoin, attributing his confidence to the post-halving cycle which historically propels a surge in demand, in an interview with Altcoin Daily.\n\nHe states, “I believe six to 12 months after the halving cycle, there’s no reason why Bitcoin couldn’t be at $100,000. Just given what I know about demand and given what I know about the cycle and where the miners are, and so forth, plus, we may get some interest rate relief. That’s a 3x bump.”\n\nHis prediction reflects a tripling in Bitcoin’s value, which he analogizes to the technological advancements that have historically witnessed such price surges.\n\nWhile <PERSON><PERSON><PERSON><PERSON> admits to not being as intimately acquainted with Ethereum, he nonetheless holds a positive stance.\n\nAlso Read: Fed Governor <PERSON> Says Creation Of US CBDC 'Could Pose Significant Risks'\n\nHighlighting the technological improvements and the increasing utility that Ethereum offers, he said, “Could Ethereum be $4,500 by then? Why not? You know it’s a great utility. Speeds have improved. The layer-2s have helped it. Why not? Why couldn’t it be $4,500? These things are maturing.”\n\nHis optimism extends to Algorand, albeit with a tempered tone due to its current trading price. Despite the crypto’s present modest trading range of around $0.09 to $0.10, Scaramucci sees potential for growth.\n\nHe envisions a path to $0.30 per token as Algorand continues to develop and garner adoption, especially from corporations enamored by its technology.\n\nHe adds, “I’m bullish. I thought at $1 it could have easily gotten to $5... but could Algorand from here... could it be a $0.30 token as they continue their development and continue the adoption of what they’re doing and bringing in corporations that love the technology? I believe it can.”\n\nRead Next: Binance.US Changes Terms: No More Direct Withdrawals Of US Dollars From Platform\n\nJoin Benzinga's Fintech Deal Day & Awards on Nov. 13 and Future of Digital Assets on Nov. 14 in New York City to stay updated on trends like AI, regulations, SEC actions and institutional adoption in the crypto space. Secure early bird discounted tickets now!\n\nPhoto: Shutterstock\n\n© 2023 Benzinga.com. Benzinga does not provide investment advice. All rights reserved.\n\nGet insight into trading platforms. Compare the best online stock brokerages.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3Lm5hc2RhcS5jb20vYXJ0aWNsZXMvY3J5cHRvLWJ1bGwtc2NhcmFtdWNjaS1zZWVzLTN4LWdhaW5zLWZvci1iaXRjb2luLWV0aGVyZXVtLWFuZC1hbGdvcmFuZC1oZXJlcy13aHnSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Crypto Bull Sc<PERSON>cci Sees 3X Gains For Bitcoin, Ethereum, And Algorand — Here's Why - Nasdaq", "content": "SkyBridge Capital founder, <PERSON> has shed a bullish light on the future price trajectories of Bitcoin (CRYPTO: BTC), Ethereum (CRYPTO: ETH), and Algorand (CRYPTO: ALGO), suggesting hearty targets for these cryptocurrencies, detailing the intrinsic factors that could drive such a bullish trend.\n\nThis optimistic outlook comes just ahead of Benzinga's Future of Digital Assets conference on Nov. 14, which aims to delve into the evolving dynamics of digital assets. Discussions about these predictions by <PERSON><PERSON><PERSON><PERSON> could form a riveting part of the narrative as industry experts explore the potential future of cryptocurrencies.\n\n<PERSON><PERSON><PERSON><PERSON> postulates a bullish scenario for Bitcoin, attributing his confidence to the post-halving cycle which historically propels a surge in demand, in an interview with Altcoin Daily.\n\nHe states, “I believe six to 12 months after the halving cycle, there’s no reason why Bitcoin couldn’t be at $100,000. Just given what I know about demand and given what I know about the cycle and where the miners are, and so forth, plus, we may get some interest rate relief. That’s a 3x bump.”\n\nHis prediction reflects a tripling in Bitcoin’s value, which he analogizes to the technological advancements that have historically witnessed such price surges.\n\nWhile <PERSON><PERSON><PERSON><PERSON> admits to not being as intimately acquainted with Ethereum, he nonetheless holds a positive stance.\n\nAlso Read: Fed Governor <PERSON> Says Creation Of US CBDC 'Could Pose Significant Risks'\n\nHighlighting the technological improvements and the increasing utility that Ethereum offers, he said, “Could Ethereum be $4,500 by then? Why not? You know it’s a great utility. Speeds have improved. The layer-2s have helped it. Why not? Why couldn’t it be $4,500? These things are maturing.”\n\nHis optimism extends to Algorand, albeit with a tempered tone due to its current trading price. Despite the crypto’s present modest trading range of around $0.09 to $0.10, Scaramucci sees potential for growth.\n\nHe envisions a path to $0.30 per token as Algorand continues to develop and garner adoption, especially from corporations enamored by its technology.\n\nHe adds, “I’m bullish. I thought at $1 it could have easily gotten to $5... but could Algorand from here... could it be a $0.30 token as they continue their development and continue the adoption of what they’re doing and bringing in corporations that love the technology? I believe it can.”\n\nRead Next: Binance.US Changes Terms: No More Direct Withdrawals Of US Dollars From Platform\n\nJoin Benzinga's Fintech Deal Day & Awards on Nov. 13 and Future of Digital Assets on Nov. 14 in New York City to stay updated on trends like AI, regulations, SEC actions and institutional adoption in the crypto space. Secure early bird discounted tickets now!\n\nPhoto: Shutterstock\n\n© 2023 Benzinga.com. Benzinga does not provide investment advice. All rights reserved.\n\nGet insight into trading platforms. Compare the best online stock brokerages.\n\nThe views and opinions expressed herein are the views and opinions of the author and do not necessarily reflect those of Nasdaq, Inc."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiS2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbWFnYXppbmUvZXRoZXJldW0tcmVzdGFraW5nLWJsb2NrY2hhaW4tZGFuZ2Vyb3VzL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Ethereum restaking: Blockchain innovation or dangerous house of cards? - Cointelegraph", "content": "Ethereum restaking — proposed by middleware protocol <PERSON><PERSON>n<PERSON>ayer — is a controversial innovation over the past year that has some of the brightest minds worried about the potential ramifications.\n\n\n\nRestaking involves reusing staked or locked-up Ether tokens to earn fees and rewards. The restaked tokens can then help secure and validate other protocols.\n\nProponents believe restaking can squeeze additional security and rewards from already staked ETH and grow the crypto ecosystem in a healthier way based on Ethereum’s existing trust mechanisms. Restaking could serve as a security primitive for exporting Ethereum’s trust generated by its validators to other projects.\n\nYet Ethereum co-founder <PERSON><PERSON> and a number of key devs worry that restaking is a house of cards that will inevitably tumble. Some of those Ethereum devs have even proposed a fork to head off restaking platform EigenLayer.\n\nWhy the project’s founders promote “trust as a service” from Ethereum without the Ethereum founder and others’ willingness to participate is still to play out. Will the whole concept result in an Ethereum fork to protect the network from catastrophic failure?\n\nThe way eth Community turned on EigenLayer should be studied, it’s not for building, just side @BanklessHQ deals with canto n other grifters, we gonna keep forking yall ideas tho on other non zuzalu shit https://t.co/7Erh6qKVSE — ⟠yumatrades.eth⟠ – #6585 (😈,😇) (@yumatrades) May 22, 2023\n\nStaking and restaking\n\nStaking is a crypto-native concept. On Ethereum, it means putting up a security bond in ETH so that the validator (validators of new transactions who maintain the security of the blockchain) will behave honestly in verifying transactions rather than lose their staked tokens. Stakers are then paid rewards for locking up this ETH.\n\nIn essence, stakers lock up their tokens to commit to producing Ethereum blocks — an on-chain way of supporting development, regardless of fluctuations in highly volatile token prices.\n\nSo what is restaking?\n\nIn short, restaking works in that already staked Ethereum tokens can be rehypothecated (when a lender re-uses collateral posted from one loan to take out a new loan) to secure a wider variety of applications and accrue additional rewards.\n\nBut restakers also get penalized or slashed for non-performance of their staking tasks. (More on that below).\n\nSo restaking is a crypto primitive for generating economic security from Ethereum’s nine years of concerted developer activity and project track record.\n\n“It’s an extension protocol to extend what Ethereum can do, scaling out Ethereum stakers beyond Ethereum to other bridges and oracles that need to be secured,” EigenLayer founder Sreeram Kannan tells Magazine.\n\n\n\nHe says EigenLayer is commoditizing ETH staking to make it more general purpose, as, in crypto parlance, “staking is the root of trust.”\n\nKannan is an academic on leave from the University of Washington, and EigenLayer began as academic research into “exported trust” as a consensus protocol. Basically, he sought to piggyback the trust generated by Ethereum to other ecosystems.\n\nKannan essentially seeks to export the “trust” generated by Ethereum for other projects across the ecosystem and other chains. “In crypto, mechanisms for trust mean that investors need skin in the game. The pseudonymous world needs carrots and sticks whereby validators are distributed.” He calls it “permissionless innovation.”\n\nThe best each chain has to offer\n\nThe big idea for EigenLayer is to bridge blockchains and create super applications, taking the best each chain has to offer. Kannan says “every ecosystem is better in some dimension, but not all dimensions,” and EigenLayer enhancing decentralized tech stacks will actually benefit the industry.\n\nKannan said that what can be built with EigenLayer fits roughly into two categories.\n\n\n\nFirstly, EigenLayer allows for the construction of bridges from chain to chain, say Ethereum to Avalanche. EigenLayer acts as a marketplace for “decentralized trust,” connecting stakers seeking yields, projects built on EigenLayer offering risk-reward structures for yields, and operators acting as bridges between stakers and projects.\n\nSecondly, a set of smart contracts on Ethereum’s chain lets ETH stakers opt to run other software. EigenLayer could, for example, improve Ethereum transaction finality speeds. ETH stakers can now take the layer-1 blockchain Fantom chain (for better transaction finality times) and fork it on EigenLayer, thereby running a layer as a super fast finalization layer with an EigenLayer trust layer.\n\nBut it’s all still theoretical.\n\nThe idea of restaking makes sense theoretically, helping projects build off Ethereum’s security layer — but the problems worry many.\n\nIn theory, “it’s like the NATO security alliance; each country is still a sovereign country, but their mutual defense pact is secured by the sum of their military power,” Sunny Aggarwal, co-founder of Osmosis Labs and creator of a similar restaking system — Mesh, on Cosmos’ chain — told Magazine.\n\nIn practice, EigenLayer provides two ways to restake: whitelisted liquid staking derivatives can be restaked with EigenLayer or an EigenPod (a smart contract can be created to run a validator while restaking). But most restakers won’t run their own validator, so new networks can build projects without their own communities of validators.\n\nEigenLayer isn’t live yet, and it’s impact is still highly speculative, according to Anthony “0xSassal” Sassano, a full-time Ethereuem educator, founder of YouTube channel The Daily Gwei and an early investor in EigenLayer.\n\nTo date, there’s only a smart contract for staked ETH to bootstrap the EigenLayer network, and perhaps given EigenLayer’s hype, people are depositing their ETH into that network, expecting to farm an unconfirmed airdrop of native EigenLayer tokens.\n\nA force for good or evil?\n\nTo be successful, new consensus protocols need a balanced alignment of incentives. Trust is like a scale weighing competing interests. And trying to export Ethereum security layers to different blockchain ecosystems worries some. Many are still trying to understand if it’s a force for good or evil — or both.\n\n“There are two camps: those excited by broadening the use case of ETH staking, and then there are those that worry about potential attack vectors on Ethereum and potential negative consequences for Ethereum if something goes wrong with EigenLayer. My view is in the middle; I understand the concerns and the excitement.” Sassano says.\n\n“Inherently, all of this is complex; it depends which rabbit hole you want to go down. The simple answer is that Ethereum, as a network, currently has over 25 million ETH at stake — that’s tens of billions of dollars. So restaking is asking, what if we could harness that economic security for other purposes than just securing the Ethereum chain?”\n\nSassano continues: “That’s exactly what EigenLayer is trying to do, to generalize the security that Ethereum has with its stakers and expand that to other things like an oracle network or a data availability network. It’s inherently more technical and complex than that, but that’s the gist of it.”\n\nThere are two types of danger that restaking could pose: first for “restakers” and then for Ethereum itself.\n\nRestaking creates too much leverage\n\nRestaking is controversial as it is akin to leveraged investing through borrowing. Some argue that the danger here is that the hunger for “real yields” or actual revenue that emerged in crypto in 2022 leads to unsavory developments, like restaking.\n\nJae Sik Choi, portfolio manager at Greythorn Asset Management, told Magazine that securing networks through restaking could work, but restaking is akin to leverage:\n\n“Just like how Terra’s over-leveraged ‘safe’ collateralization of Luna was, there would always be a risk of participants over-leveraging into this new concept, and such a risk won’t be quantifiable until we see more data sets throughout the emergence of this new restaking narrative.”\n\nDan Bar, chief investment officer at Bitfwd Capital — a boutique crypto assets hedge fund — agreed that restaking amounts to leverage, telling Magazine: “While moderate schemes of restaking could be beneficial for capital efficiency purposes, any crypto assets manager and finance professional worth their salt knows too well how easily and quickly leverage can turn into a slew of synthetic toxic financial instruments that bring disasters into even the most healthy of ecosystems.”\n\n\n\nAnd maybe that’s the first major problem. Investors will only see restaking as quick, easily leveraged financial products. EigenLayer building an open-source, decentralized network security may fail to convince doubters.\n\nRisks to Ethereum itself\n\nOne fear is that slashing on EigenLayer will affect Ethereum itself.\n\n\n\nEthereum’s proof-of-stake trust system keeps everyone in check with slashing conditions — essentially non-performance penalties. Programmable slashing means restakers have additional computational responsibilities and face consequences for non-execution.\n\nEthereum co-founder Vitalik Buterin fears an overload of the chain’s consensus, basically, computational overloads, if the blockchain’s computational power is suddenly redirected elsewhere.\n\nKannan admits that Vitalik’s concerns are valid. “We don’t want to shard Ethereum’s trust layer, and we don’t want contagion of nefarious actors leveraging Ethereum’s trust system.”\n\n\n\nSassano also notes that the functionality of Ethereum proof-of-stake was designed to make sure that there won’t be a sudden influx or outflux of validators, which would affect the core properties of Ethereum’s consensus mechanism.\n\nThe issue is that EigenLayer will decide where to take ETH from, but they can’t slash a validator on Ethereum.\n\n“In Ethereum, there’s also a queue for validators to enter or exit each day. So let’s say, in an extreme example, 30% of all staked ETH begins staking with EigenLayer and say that all 30% gets ‘slashed’ by EigenLayer. While it depends on what the slashing condition was, let’s say all this ETH was lost because they tried to do something really bad. Even if all 30% had to be exited, there’s a limit on how much can exit per day. It would take literally years to exit 30% of ETH stake. So I understand people’s concerns, but at the same time, other things built on top cannot dictate what happens on Ethereum.”\n\nSo, restakers should have to play by Ethereum’s rules.\n\nYet Sassano’s biggest concern is around the calculus of ETH staking, which may one day become a question of whether stakers get more from staking on EigenLayer than Ethereum itself. This could erode the Ethereum staking model in time.\n\nHe is confident, though, that Ethereum’s tech offsets those systemic risks: “It’s not a critical risk to Ethereum if you are slashed on EigenLayer. You are not slashed on Ethereum. EigenLayer cannot cause you to be slashed on Ethereum because Ethereum has its own slashing conditions built into the protocol. And EigenLayer has its own separate slashing conditions built into its protocol as well.”\n\nAnything built on top of Ethereum introduces additional complexity and risk. Juan David Mendieta Villegas, co-founder and chairman at crypto market maker Keyrock, tells Magazine:\n\n“EigenLayer is an interesting development but creates additional attack vectors without providing explicit benefits to the Ethereum ecosystem itself. If we take a step back, it’s important to note that ETH staking has introduced a base benchmark yield for the industry, and that is a good development. You can almost think of it as a ‘risk-free’ rate. Any additional layers, such as liquid staking derivatives and re-staking mechanisms, of course, can carry more concerns such as concentration risk, security and smart contract.”\n\nBut Villegas wishes EigenLayer well. “Overall, we’re advocates of the innovations that are happening around staking and want to see multiple protocols win as this will assist in the decentralization and democratization of the network.”\n\nIn other words, he wishes for competitors to EigenLayer to create similar products.\n\nGreat conversation. The TLDR is that basically Justin, Vitalik, and Dankrad all agree that restaking is a giant existential threat to Ethereum if it gets big (if it even works at all). These guys are all way too nice, but this was a brutal takedown of Eigenlayer. The takeaway… https://t.co/kY8gKmzxrF — the_fett (@themandalore9) June 30, 2023\n\nRestaking could make or break new projects\n\nCosmos’ Aggarwal believes restaking will only benefit those blockchains with existing network effects for those with existing economic alliances or overlapping communities.\n\nHe also sees restaking protocols akin to a venture capital arm for layer 1s that might discourage solo stakers and further centralize networks.\n\nIn the end, competing layer-1 blockchains probably won’t engage in restaking across chains. For that reason, he feels that EigenLayer’s design could be improved.\n\nWhile EigenLayer is designed as a security system importing trust from Ethereum, builders will create their own tokens and revenue models. This has pluses and minuses.\n\nIn some cases, dodgy new tokens may benefit from Ethereum’s trust layer. Choi thinks “this trust layer benefit could potentially be moot due to the tokenomics that these alt layer 1s would want to try and attain (i.e., the use of their own token — their own agendas) could be problematic and so any supposed trust exported from Ethereum is lost anyway.”\n\nOn the other hand, experimental, well-meaning projects may now have a chance at success thanks to EigenLayer. That’s why Choi thinks the ultimate potential benefit EigenLayer is proposing is that other blockchains that do not want to spin up their own validator and staker sets have a chance at scaling to success.\n\nAggarwal also notes that with appropriate checks, restaking should be set within parameters to control risk. Restaking primitives need cleverly programmed governance, such as discounted voting power to restaked tokens on another chain. For example, one restaker can’t have more than 20% of the vote for another chain.\n\nSo, is restaking a good thing for Ethereum?\n\n“The purists would say Ethereum should only be securing the Ethereum Beacon Chain and nothing else. [They] shouldn’t be exporting Ethereum security to anything else. But I don’t think that is necessarily a bad thing to get node operators to do other work,” says Sassano.\n\n\n\n“If it can happen on the Ethereum network, it will happen. If the network can’t resist it and Ethreuem’s chain becomes insecure because of it, and there are adverse effects because of it, then Ethereum as a protocol was not designed correctly and needs to be improved.”\n\nWe’ll find out soon enough."}]