[{"id": 7, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vdS50b2RheS92aXRhbGlrLWJ1dGVyaW4tYWRkcmVzc2VzLWV0aGVyZXVtcy1tYWpvci1pc3N1ZXMtcmlnaHQtbm930gFOaHR0cHM6Ly91LnRvZGF5L3ZpdGFsaWstYnV0ZXJpbi1hZGRyZXNzZXMtZXRoZXJldW1zLW1ham9yLWlzc3Vlcy1yaWdodC1ub3c_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 08 Sep 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Addresses Ethereum's Major Issues Right Now - U.Today", "content": "In a recent keynote address at Nanyang Technological University in Singapore, Ethereum cofounder <PERSON><PERSON> offered a candid assessment of blockchain's key challenges and the strides made in addressing them.\n\nAdvertisement\n\n<PERSON><PERSON><PERSON> centered his speech on Ethereum's persistent challenges: privacy, consensus, smart contract security and scalability. These issues, he emphasized, have remained constant over the past six years.\n\nBack in 2017, Ethereum took a significant step toward enhancing privacy by introducing zero-knowledge proofs like zk-SNARKs. Fast forward to 2023, and Ethereum has made substantial progress in this domain. Advanced technologies like STARK, Zero-Knowledge rollups and Cairo are now part of the Ethereum landscape. Nevertheless, privacy remains a concern, mainly due to legal and acceptance issues surrounding tokens generated by privacy systems, as exemplified by the Tornado Cash case, concluded the developer.\n\n<PERSON><PERSON><PERSON> introduced the concept of proof-of-innocence, a means to validate tokens within privacy systems without revealing transaction origins. This concept underscores the importance of privacy and ongoing efforts to optimize zk-SNARKs for seamless integration into mainstream finance.\n\nCentralization and hacks\n\nShifting to consensus security, Ethereum has fully embraced a consensus mechanism, but challenges endure. These include simplifying the protocol, addressing centralization concerns and streamlining participation, notably through proof of stake.\n\nIn the realm of smart contract security, notable efforts have been made since 2017. Fast forward to 2023, and Ethereum has made significant progress in this arena. <PERSON><PERSON><PERSON> noted that the frequency of hacking incidents has dramatically decreased since the notorious DAO hack. Improved security measures and heightened awareness within the Ethereum community have played a pivotal role in mitigating risk.\n\nIn summary, <PERSON>lik But<PERSON>n's keynote address shed valuable light on Ethereum's journey over the years. While recognizing ongoing challenges, his speech was aimed at demonstrating <PERSON>thereum's focus on practical solutions, mainstream adoption and enhancing user benefits."}, {"id": 46, "url": "https://news.google.com/rss/articles/CBMivgFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtOC1zZXB0ZW1iZXItMjAyMy9hcnRpY2xlc2hvdy8xMDM0OTY2MTAuY21z0gHCAWh0dHBzOi8vbS5lY29ub21pY3RpbWVzLmNvbS9tYXJrZXRzL2NyeXB0b2N1cnJlbmN5L2NyeXB0by1wcmljZXMtdG9kYXktbGl2ZS1uZXdzLWJpdGNvaW4tZG9nZWNvaW4tZXRoZXJldW0tc2hpYmhhLWludS1jcnlwdG9jdXJyZW5jeS1sYXRlc3QtdXBkYXRlcy04LXNlcHRlbWJlci0yMDIzL2FtcF9hcnRpY2xlc2hvdy8xMDM0OTY2MTAuY21z?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 08 Sep 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin rises above $26,200; Ethereum holds above $1,600 level - The Economic Times", "content": "The crypto tokens were trading higher in Friday's trade. Bitcoin (BTC) rose 2% to $26,259, whereas Ethereum (ETH) was just below the 1,650 level.Meanwhile, the global cryptocurrency market cap was trading higher, around $1.06 trillion, rising 1.32% in the last 24 hours.\"Bitcoin's value is currently trading around the $26,200 level. This positive price movement might be because of a paper that came out yesterday from the IMF and the G20’s Financial Stability Board. The paper said that banning all cryptocurrencies wouldn't work well in the long term and suggested using targeted restrictions and a comprehensive monetary strategy instead,\" <PERSON><PERSON>, CEO & Co-Founder of Mudrex, said.Other top crypto tokens were also trading higher. Solana, Polkadot, and Litecoin surged over 1%.The total volume in DeFi is currently $2.37 billion, 9.22% of the total crypto market 24-hour volume. The volume of all stablecoins is now $24.11 billion, which is 93.92% of the total crypto market 24-hour volume.The market cap of Bitcoin, the world's largest cryptocurrency, was around $512 billion. Bitcoin's dominance is currently 48.57%, according to CoinMarketCap. Meanwhile, BTC volume stood at approximately $12.18 billion, falling 0.76% in the last 24 hours.\"Bitcoin currently grapples with the challenge of surmounting the pivotal $26,200 threshold, having failed to breach it despite rallying from $25,350, hinting at a bearish sentiment,\" said Sathvik Vishwanath, Co-Founder & CEO of Unocoin.\"As BTC's price hovers beneath $26,000, it encounters resistance from a significant bearish trend line near $25,650. Key barriers stand at $26,000 and $26,200, with a successful breakthrough potentially setting sights on $26,500 and $27,000,\" Sathvik added.Crypto Cart: Quick Glance (Source: coinmarketcap.com , data as of 11.22 hours, IST on September 8, 2023)Bitcoin $26,259 1.9%Ethereum $1,647 0.51%Tether $0.9997 0.03% BNB $217 0.74% XRP $0.5059 0.56%Cardano $0.2575 0.14% Dogecoin $0.06367 0.25%Solana $19.9 1.11%Polygon $0.5613 -2.27%Litecoin $63.45 0.63%Polkadot $4.3 0.87%Tron $0.07921 0.22%Shiba Inu $0.000007668 0.15%(Note: Price change in last 24 hours)(Disclaimer: Recommendations, suggestions, views and opinions given by the experts are their own. These do not represent the views of The Economic Times)"}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMivgFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vbWFya2V0cy9jcnlwdG9jdXJyZW5jeS9jcnlwdG8tcHJpY2VzLXRvZGF5LWxpdmUtbmV3cy1iaXRjb2luLWRvZ2Vjb2luLWV0aGVyZXVtLXNoaWJoYS1pbnUtY3J5cHRvY3VycmVuY3ktbGF0ZXN0LXVwZGF0ZXMtOC1zZXB0ZW1iZXItMjAyMy9hcnRpY2xlc2hvdy8xMDM0OTY2MTAuY21z0gHCAWh0dHBzOi8vbS5lY29ub21pY3RpbWVzLmNvbS9tYXJrZXRzL2NyeXB0b2N1cnJlbmN5L2NyeXB0by1wcmljZXMtdG9kYXktbGl2ZS1uZXdzLWJpdGNvaW4tZG9nZWNvaW4tZXRoZXJldW0tc2hpYmhhLWludS1jcnlwdG9jdXJyZW5jeS1sYXRlc3QtdXBkYXRlcy04LXNlcHRlbWJlci0yMDIzL2FtcF9hcnRpY2xlc2hvdy8xMDM0OTY2MTAuY21z?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 08 Sep 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin rises above $26,200; Ethereum holds above $1,600 level - The Economic Times", "content": "The crypto tokens were trading higher in Friday's trade. Bitcoin (BTC) rose 2% to $26,259, whereas Ethereum (ETH) was just below the 1,650 level.Meanwhile, the global cryptocurrency market cap was trading higher, around $1.06 trillion, rising 1.32% in the last 24 hours.\"Bitcoin's value is currently trading around the $26,200 level. This positive price movement might be because of a paper that came out yesterday from the IMF and the G20’s Financial Stability Board. The paper said that banning all cryptocurrencies wouldn't work well in the long term and suggested using targeted restrictions and a comprehensive monetary strategy instead,\" <PERSON><PERSON>, CEO & Co-Founder of Mudrex, said.Other top crypto tokens were also trading higher. Solana, Polkadot, and Litecoin surged over 1%.The total volume in DeFi is currently $2.37 billion, 9.22% of the total crypto market 24-hour volume. The volume of all stablecoins is now $24.11 billion, which is 93.92% of the total crypto market 24-hour volume.The market cap of Bitcoin, the world's largest cryptocurrency, was around $512 billion. Bitcoin's dominance is currently 48.57%, according to CoinMarketCap. Meanwhile, BTC volume stood at approximately $12.18 billion, falling 0.76% in the last 24 hours.\"Bitcoin currently grapples with the challenge of surmounting the pivotal $26,200 threshold, having failed to breach it despite rallying from $25,350, hinting at a bearish sentiment,\" said Sathvik Vishwanath, Co-Founder & CEO of Unocoin.\"As BTC's price hovers beneath $26,000, it encounters resistance from a significant bearish trend line near $25,650. Key barriers stand at $26,000 and $26,200, with a successful breakthrough potentially setting sights on $26,500 and $27,000,\" Sathvik added.Crypto Cart: Quick Glance (Source: coinmarketcap.com , data as of 11.22 hours, IST on September 8, 2023)Bitcoin $26,259 1.9%Ethereum $1,647 0.51%Tether $0.9997 0.03% BNB $217 0.74% XRP $0.5059 0.56%Cardano $0.2575 0.14% Dogecoin $0.06367 0.25%Solana $19.9 1.11%Polygon $0.5613 -2.27%Litecoin $63.45 0.63%Polkadot $4.3 0.87%Tron $0.07921 0.22%Shiba Inu $0.000007668 0.15%(Note: Price change in last 24 hours)(Disclaimer: Recommendations, suggestions, views and opinions given by the experts are their own. These do not represent the views of The Economic Times)"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiVmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS1wcm90by1kYW5rc2hhcmRpbmctdG8tbWFrZS1yb2xsdXBzLTEweC1jaGVhcGVy0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 08 Sep 2023 07:00:00 GMT", "title": "Ethereum's proto-danksharding to make rollups 10x cheaper — Consensys zkEVM Linea head - Cointelegraph", "content": "Zero-knowledge (ZK) proof solutions have proved critical in helping scale the Ethereum ecosystem, but proto-danksharding is expected to drastically reduce the cost of rollups, according to Consensys’ zkEVM Linea head <PERSON>.\n\nSpeaking exclusively to Cointelegraph Magazine editor <PERSON> during Korea Blockchain Week, Liochon estimated that proto-danksharding could further reduce rollup costs by 10 times.\n\nProto-danksharding, also known by its Ethereum Improvement Proposal (EIP) identifier EIP-4844, is aimed at reducing the cost of rollups, which typically batch transactions and data off-chain and submit computational proof to the Ethereum blockchain.\n\nThe Ethereum Foundation has yet to nail down an expected launch date for proto-danksharding, but development and testing are still ongoing.\n\nAs Liochon explained, Linea delivers 15 times cheaper transactions compared to those made on Ethereum’s layer 1, but rollups are still limited by the fact that transactions are posted in call data in Ethereum blocks.\n\nAccording to Ethereum’s documentation, rollups are still expensive in terms of their potential because call data is processed by all Ethereum nodes and the data is stored on-chain indefinitely despite the fact that the data only needs to be available for a short period of time.\n\nEIP-4844 will introduce data blocks that can be sent and attached to blocks. The data stored in blocks is not accessible to the Ethereum Virtual Machine and will be deleted after a certain time period, which is touted to drastically reduce transaction costs.\n\n“In reality, the cost of rollups is down to data availability. We are writing all the data to layer 1, which is why we have exactly the same security. But it’s expensive; it represents 95% of the cost.”\n\nLiochon said that Linea’s prover, which essentially handles the off-chain computation that verifies, bundles, and then creates a cryptographic proof of the combined transactions, only represents a fifth of the cost.\n\nThis highlights the major hurdle in making ZK-rollups the go-to scaling solution for the Ethereum ecosystem as opposed to other solutions like Optimistic Rollups.\n\nLiochon also said that Linea aims to be a general-purpose ZK-rollup that will be used for a variety of decentralized applications and solutions within the Ethereum ecosystem.\n\n“We are a generic rollup. We don’t want to have a specific use case or specific domain. It’s quite important to support all types of applications, including DeFi, gaming and social.”\n\nAs Cointelegraph previously reported, Consensys completed the launch of Linea in August 2023, having onboarding over 150 partners and bridging more than $26 million in Ether (ETH).\n\nMagazine: Here’s how Ethereum’s ZK-rollups can become interoperable"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vY3J5cHRvLm5ld3MvbGVhcm4vZXRoZXJldW0tbm9kZXMtYW5kLWNsaWVudHMtYS1jb21wbGV0ZS1ndWlkZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 08 Sep 2023 07:00:00 GMT", "title": "Ethereum Node and Client: A Complete Guide - crypto.news", "content": "Ethereum nodes and clients play a pivotal role in the ongoing transformation of the crypto landscape. Ethereum, a cryptocurrency that has captured the attention of enthusiasts for months, is poised to surpass Bitcoin as the industry’s leading digital currency. <PERSON><PERSON>, the visionary creator of Ethereum, and the dedicated team of developers have been diligently working on a series of upgrades leading up to the eagerly anticipated main merge.\n\nEthereum nodes and clients form the backbone of this groundbreaking network. They are responsible for validating transactions, executing smart contracts, and ensuring the overall security and decentralization of the Ethereum ecosystem. In this article, we will delve deeper into the world of Ethereum nodes and clients, exploring their significance, functionality, and integral role in shaping the future of blockchain technology.\n\nThe Origin Story of Ethereum\n\nIn 2014, Vitalik Buterin published a white paper introducing Ethereum. The following year, 2015, saw the launch of the Ethereum platform by <PERSON><PERSON><PERSON> and <PERSON>. <PERSON> is also the founder of ConsenSys – a blockchain software company.\n\nEthereum has changed and improved a lot since it was first created, and all of these changes have led to one goal: making Ethereum the best crypto blockchain on the internet. Its operations are insanely good, and investors can trust that they are investing in a good project.\n\nIts ecosystem is made up of a variety of different specifications. Let’s take a look at some of its finest features: Ethereum Nodes and Clients.\n\nThe Ethereum Ecosystem\n\nIn simple terms, Ethereum is a decentralized platform that runs smart contracts. Smart contracts are basically application code that is executed and verified by the network. Ethereum has become the go-to platform for DeFi (decentralized finance) and NFTs (non-fungible tokens).\n\nEthereum was created to be scalable, programmable, secure, and decentralized. It’s the blockchain of choice for those who are creating technology on top of it. This blockchain is essential for these DeFi applications to develop and bring about a financial revolution.\n\nEthereum provides a highly flexible platform on which developers can create dApps using the native Solidity programming language and Ethereum Virtual Machine. The founders of Ethereum saw blockchain technology’s potential for more than just creating a secure virtual payment method. Now, Ethereum is changing from proof-of-work (PoW) to proof-of-stake (PoS).\n\nWhat Is an Ethereum node?\n\nA node on the Ethereum network is any computer that runs the software required to interact with it. Nodes communicate with one another to validate transactions and store data about the blockchain’s state.\n\nDepending on your needs, whether a dApp or a wallet, any client can run three types of nodes: full, light, and archive. Each node will uniquely evaluate data and provide various synchronization methods. This refers to how quickly your node can get new information so that you can use it.\n\nFull Nodes: Full nodes are jam-packed with data. They keep and distribute all of the blockchain data from the Ethereum network. A full node will also participate in block validation.\n\nA full node can interact directly with any smart contract on a public blockchain. In addition, nodes can also deploy new smart contracts onto the same type of blockchain. However, running a full node may be costly in terms of hardware and bandwidth. Obtaining all of the data can be very time-consuming.\n\nLight Nodes: Light nodes are similar to full nodes. However, they handle significantly less information. The light node keeps track of the header chain. This is the most basic information in a block, such as a timestamp and the previous block’s hash.\n\nLight nodes are blockchain clients that only request data as needed. They can verify the validity of data, but they don’t participate in block validation. Remote clients often use light nodes.\n\nArchive Nodes: A archive node is a node that keeps all of the data that a full node does and creates an archive of past blockchain states. Even after a client has finished synchronizing, archive nodes will store historical data.\n\nOn the other hand, full and light nodes will “prune” the historical blockchain data. This means they can rebuild it but do not retain this information. Archive nodes may not be of much use to the average user. However, they’ve been instrumental in creating block explorers, wallet providers, and chain analytics applications.\n\nWhat Are Clients in Ethereum?\n\nIn computing, a “client” is software downloaded to your computer that helps you interact with another type of software or service provided by a server. An Ethereum client is a program that enables Ethereum nodes to read blocks on the Ethereum blockchain and smart contracts. You’ll need to download an Ethereum client software to run a node.\n\nWith Ethereum, many different clients can be created using various programming languages. However, they all follow the same protocol and rules. This allows them to interact with each other on the Ethereum network.\n\nThe most popular functions of Ethereum clients include transaction and mining interfaces, but their applications extend far beyond basic blockchain operations. The Ethereum Foundation maintains the following Ethereum clients:\n\n· Geth (Go)\n\n· OpenEthereum (Rust)\n\n· Nethermind (C#, .NET)\n\n· Besu (Java)\n\n· Erigon (Go/Multi)\n\nIf the Ethereum Foundation doesn’t support your go-to programming language, don’t worry–numerous third-party Ethereum clients can provide you with the language support you need. There are 3 types of Ethereum clients.\n\nFull Client: Full clients enable any node on the network to complete all tasks, such as mining, transactions, validating block headers, and running smart contracts.\n\nFull clients enable any node on the network to complete all tasks, such as mining, transactions, validating block headers, and running smart contracts. Light Client: Light clients are a subset of client functionality. Because they do not keep the entire Ethereum blockchain, light clients can offer quicker speeds and free up data storage space over full clients.\n\nLight clients are a subset of client functionality. Because they do not keep the entire Ethereum blockchain, light clients can offer quicker speeds and free up data storage space over full clients. Remote Client: A remote client is a third kind of client that resembles a light client. The primary distinction is that a remote client does not keep its copy of the blockchain and does not validate transactions or block headers.\n\nRather than running a full client, remote clients simply connect to the Ethereum blockchain network via a light or full client. These clients are most often used as wallets for sending and receiving transactions.\n\nThe Difference Between Ethereum Nodes and Clients?\n\nAlthough nodes and clients are often used interchangeably, they serve different purposes in accessing the Ethereum network. It may help to think of nodes and clients like a computer accessing the internet: the node would be an operating system, such as Windows or iOS, while the client is the physical computer.\n\nYou can tap into the node operating system using a client computer. From there, you gain access to the internet–regardless of which type of computer or operating system you use.\n\nBottom Line\n\nNodes and clients are frequently equated, although they are not the same thing. The Clients and Nodes play vital roles in keeping the Ethereum blockchain functioning correctly. In short, an Ethereum node and client depend on each other to keep the Ethereum network functioning."}]