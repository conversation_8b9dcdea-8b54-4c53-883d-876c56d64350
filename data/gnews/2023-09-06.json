[{"id": 15, "url": "https://news.google.com/rss/articles/CBMifWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDkvMDYvZXRoZXJldW1zLXZpdGFsaWstYnV0ZXJpbi1hcmd1ZXMtZm9yLWJsb2NrY2hhaW4tcHJpdmFjeS1wb29scy10by13ZWVkLW91dC1jcmltaW5hbHMv0gGBAWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDkvMDYvZXRoZXJldW1zLXZpdGFsaWstYnV0ZXJpbi1hcmd1ZXMtZm9yLWJsb2NrY2hhaW4tcHJpdmFjeS1wb29scy10by13ZWVkLW91dC1jcmltaW5hbHMvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 06 Sep 2023 07:00:00 GMT", "title": "Ethereum's <PERSON><PERSON> for Blockchain 'Privacy Pools' to Weed Out Criminals - CoinDesk", "content": "“In many cases, privacy and regulatory compliance are perceived as incompatible,” the authors wrote. “This paper suggests that this does not necessarily have to be the case, if the privacy-enhancing protocol enables its users to prove certain properties regarding the origin of their funds.”"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDkvMDYvZXZlcnlib2R5LWluLWJsb2NrY2hhaW5zLXRhbGtpbmctYWJvdXQtc2VxdWVuY2Vycy1oZXJlcy13aHktdGhleXJlLW1pc3VuZGVyc3Rvb2Qv0gF-aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL3RlY2gvMjAyMy8wOS8wNi9ldmVyeWJvZHktaW4tYmxvY2tjaGFpbnMtdGFsa2luZy1hYm91dC1zZXF1ZW5jZXJzLWhlcmVzLXdoeS10aGV5cmUtbWlzdW5kZXJzdG9vZC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 06 Sep 2023 07:00:00 GMT", "title": "‘Sequencers’ Are Blockchain’s Air Traffic Control. Here’s Why They’re Misunderstood - CoinDesk", "content": "Binance zeroed in on the problems in a recent research report: “As the sequencer controls the ordering of transactions, it has the power to censor user transactions (although complete censorship is unlikely as users can submit transactions directly to the L1),” the report stated. “The sequencer can also extract the maximal extractable value (“MEV”), which could be economically harmful to the user base. Furthermore, liveness can be a major issue, i.e., if the sole, centralized sequencer goes down, then the entire rollup gets affected.”"}, {"id": 14, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vY3J5cHRvbmV3cy5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tZXRoZXJldW1zLWZ1dHVyZS11cGdyYWRlcy1jb3VsZC1icmluZy1mdWxsLW5vZGVzLW1vYmlsZS1kZXZpY2VzLmh0bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 06 Sep 2023 07:00:00 GMT", "title": "<PERSON><PERSON>: Ethereum's Future Upgrades Could Bring Full Nodes to Mobile Devices - Cryptonews", "content": "Vitalik Buterin: Ethereum’s Future Upgrades Could Bring Full Nodes to Mobile Devices\n\nSource: Adobe / eakgrungenerd\n\nEthereum (ETH) full nodes could in the future become so light-weight that they can run on mobile devices, Ethereum co-founder <PERSON><PERSON> has predicted.\n\nIn a speech at Korea Blockchain Week that was cited by Cointelegraph, <PERSON><PERSON><PERSON> admitted that centralization of nodes is one of the Ethereum network’s biggest challenges, given the fact that the majority of the 5,901 active Ethereum full nodes are run on centralized platforms such as Amazon Web Services (AWS).\n\nIf <PERSON><PERSON><PERSON> gets his way, however, there will come a time in the future when fully verified Ethereum nodes can “literally” run on a phone.\n\nMaking Ethereum more decentralized\n\nAccording to <PERSON><PERSON><PERSON>, solving the issue with centralization of full nodes is a “big piece of the puzzle” to making Ethereum more decentralized.\n\nThe Ethereum co-founder said:\n\n“One of those six things is making it technically easier for people to run nodes, and statelessness is one of the really important technologies in doing that right.”\n\nHe further explained why most node operators today rely on centralized services to run their nodes, saying the task requires “hundreds of gigabytes” of storage space, while noting that a concept called “statelessness” can potentially solve this.\n\n“With stateless clients, you can run a node on basically zero,” <PERSON><PERSON><PERSON> said.\n\nAchieving statelessness, meaning no reliance on centralized service providers to run the network, has been on the Ethereum Foundation’s roadmap for some time already, but <PERSON><PERSON><PERSON> admitted in his speech that a solution to the issue lies many years into the future.\n\n“These technical problems will have to be addressed eventually — maybe a 10-year timescale, maybe a 20-year timescale,” Buterin said."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA5LzA2L29uZS1ldGhlcmV1bS1yaXZhbC1hdC1zY3JlYW1pbmctbG93LXByaWNlcy1hbWlkLWluc3RpdHV0aW9uYWwtaW50ZXJlc3QtaW52ZXN0YW5zd2Vycy_SAXpodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wOS8wNi9vbmUtZXRoZXJldW0tcml2YWwtYXQtc2NyZWFtaW5nLWxvdy1wcmljZXMtYW1pZC1pbnN0aXR1dGlvbmFsLWludGVyZXN0LWludmVzdGFuc3dlcnMvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 06 Sep 2023 07:00:00 GMT", "title": "One Ethereum Rival at ‘Screaming’ Low Prices Amid Institutional Interest: InvestAnswers - The Daily Hodl", "content": "A widely followed crypto strategist believes that one Ethereum (ETH) competitor with high institutional interest has massive upside potential.\n\nThe anonymous host of InvestAnswers tells his 447,000 YouTube subscribers that Solana (SOL) has key advantages over ETH that could send its price soaring.\n\n“But the real difference is how much better by what magnitude is Solana. And if you look here, red is Solana and the deltas, and then blue is ETH. You can see it smashes it. These things are on a log scale on the left. And that is the big difference. Looking at all of the key factors, they have 10 wins each, but in reality, where Solana wins, it wins by a lot more. Where Ethereum wins, it only wins by a fraction.”\n\nHis chart shows 19 different categories comparing Solana to ETH. Solana beats ETH in such categories as block time, average fee and the maximum number of transactions that a blockchain can carry out in a second, also known as transactions per second (TPS).\n\nThe trader says that ETH is considered more established than Solana and a safer bet for investors.\n\nHowever, he notes that more institutional money has flowed into Solana so far in 2023.\n\n“Ethereum is safe. It’s established. It has been around for a long time. The risk is very low. When big money comes in, they’ll go into ETH first, although we have seen so far this year in 2023 institutional money is going into Solana more so than Ethereum.”\n\nAccording to the trader, since Solana’s market cap is much smaller than Ethereum’s, SOL has a greater opportunity for a larger price increase than ETH.\n\nSolana is the 10th-ranked digital asset by market cap with a market cap of $8.2 billion. Ethereum is the second top digital asset by market cap with a market cap of $196 billion.\n\n“This is the magic question though. When you break it all down is Ethereum 20 times better than Solana? And the answer in my opinion is no. But there is risk with Solana, therefore you allocate accordingly. My Solana bag is about a third the size of my ETH bag but it will grow and I think it’ll grow faster.\n\nBut that little skinny green sliver on the right is the market cap of Solana versus the market cap of ETH. And the ETH market cap is 20 times higher, which is stunning. And I look as an investor for relative value. Solana is a screaming buy at this price.”\n\nSolana is trading for $20.28 at time of writing, up 4% in the last 24 hours.\n\nI\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nGenerated Image: Midjourney"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiQ2h0dHBzOi8vd3d3LmNjbi5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tZXRoZXJldW0tZGVjZW50cmFsaXphdGlvbi_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 06 Sep 2023 07:00:00 GMT", "title": "<PERSON>erin Admits Node Centralization is Ethereum's Challenge as <PERSON>s Loom About Chinese Control - CCN.com", "content": "Key Takeaways\n\n<PERSON><PERSON> has outlined his vision for a more decentralized Ethereum network in which it is much easier to run validators.\n\nAt present, over half of all Ethereum nodes are run through hosting services operated by companies like Amazon.\n\nMeanwhile, critics have speculated that Chinese firms secretly control a huge stash of ETH.\n\nThe Ethereum founder <PERSON><PERSON> has criticized the present state of Ethereum nodes. Pointing out that the majority of nodes are currently run through providers like Amazon Web Services, he identified node centralization as a major problem the Ethereum community needs to deal with.\n\nIn another potential threat to Ethereum’s decentralization, critics have speculated that Chinese firms secretly control some of the richest ETH wallets in existence.\n\nThe Problem of Ethereum Node Centralization\n\nThe Ethereum network is made up of computers that communicate with each other in order to validate transactions and record data about the status of the blockchain.\n\nEach computer in the network is known as an Ethereum node. From its inception, the Ethereum community has worked to establish nodes all around the world. In order to ensure the network’s decentralization, various computers belonging to individuals, businesses, universities and other organizations all operate nodes.\n\nHowever, not all computers are up to the task. In fact, to operate a full Ethereum node, Quicknode recommends a minimum of 16 GB of RAM, a fast SSD drive with at least 1 TB of space, at least 25 MBit/s bandwidth and a fast CPU with 4 or more cores.\n\nDue to the high technical bar node operators must meet, 57.5% of all nodes are run through cloud hosting services. As a consequence, although there are currently 5882 Ethereum nodes in operation, over half of these are controlled by just 21 cloud service providers.\n\nIn fact, 1805 nodes are operated via Amazon Web Services (AWS) alone, presenting a major challenge to Ethereum’s decentralization.\n\nVitalik Buterin on the Future of Ethereum Nodes\n\nSpeaking at Korea Blockchain Week, Vitalik Buterin discusses how to create a more decentralized Ethereum network by making it easier to run nodes.\n\nAddressing the problem of node centralization in Seoul on Monday, September 4, Buterin observed that reducing the amount of data each node has to store would lower technical barriers for would-be operators.\n\n“Today, it takes hundreds of gigabytes of data to run a node… In the longer term, there’s a plan to maintain fully verified Ethereum nodes where you could literally run it on your phone,” he said .\n\nPlans to change how Ethereum nodes handle data have already been formed. For example, the Ethereum Roadmap identifies planned upgrades that will allow computers operating Ethereum nodes to delete historical data they no longer need.\n\nIn the immediate future, the Ethereum Foundation is focused on data expiry as the most achievable goal for reducing the computational burden on network participants.\n\nLooking further ahead, however, a concept known as statelessness could make it much easier to run an Ethereum node.\n\nUnder a stateless system, not all nodes would have to access the network’s full state data. But the most ambitious vision for the future of Ethereum nodes, a concept known as “strong statelessness,” may not happen for a long time.\n\nAccording to Buterin, “These technical problems will have to be addressed eventually—maybe a 10-year timescale, maybe a 20-year timescale.”\n\nChinese Government Wallets May Hold a Huge Stash of ETH\n\nAs well as the concentration of nodes among a few operators, another threat to Ethereum’s decentralization could come from the concentration of ETH among a small number of wallets.\n\nIn the proof of stake (PoS) era, ETH accumulation of that kind poses a major threat to Ethereum decentralization.\n\nUnder Ethereum’s previous proof-of-work (PoW) consensus mechanism, what’s known as a 51% attack would require a single party to control over half of all miners, thus requiring a huge amount of resources to pull off. However, with the switch from miners to stakers, to seize control of the network, attackers would need to control more than half of the circulating supply of ETH instead.\n\nFor some researchers, this prospect is especially worrying given that many of the oldest and richest Ethereum wallets remain shrouded in secrecy. Moreover, concerns have been raised recently about the role of the Chinese business conglomerate Wanxiang in Ethereum’s early history.\n\nAs the Twitter sleuth TruthLabs has observed, Wanxiang has been associated with Ethereum since its inception and likely received a portion of the 72 million ETH that was distributed to early investors.\n\nA quick 🧵showing that Wanxiang, Chinese auto and early Ethereum Investor funded and setup Vitalik Buterins official wallets. 1/6https://t.co/4uMLLnzguq pic.twitter.com/Ec1cjLOLz2 — TruthLabs 🫡 (@BoringSleuth) January 29, 2023\n\nIn fact, the Chinese company has ties to Vitalik Buterin going back to 2015, when the then-CEO of Wanxiang Blockchain Labs, Feng Xia, purchased $500,000 worth of ETH from the Ethereum founder.\n\nFor TruthLabs, Wanxiang’s deep roots in the Chinese Communist Party (CCP) are a good reason to be suspicious of its ETH holdings. In fact, the crypto investigator speculates that the CCP is Ethereum’s biggest whale, secretly controlling as much as 66.6% Of the ETH supply.\n\nA complete guess, 66.6%. I found 12 Genesis Block Wallets today, alone where they ended up with the majority of the ETH from. Its so hard to know. They were invested in over 500 projects by EOY 2021, and in many cases got the pre-ICO tokens prior to both ETH/altcoin price waves. — TruthLabs 🫡 (@BoringSleuth) June 28, 2023"}]