[{"id": 6, "url": "https://news.google.com/rss/articles/CBMifWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzAxLzA2L3RyYWRlci13aG8tc29sZC1iaXRjb2luLWF0LXRoZS10b3Atc2F5cy1ldGhlcmV1bS1pcy1zaG93aW5nLXJlbWFya2FibGUtc3RyZW5ndGgtYWdhaW5zdC1idGMv0gGBAWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzAxLzA2L3RyYWRlci13aG8tc29sZC1iaXRjb2luLWF0LXRoZS10b3Atc2F5cy1ldGhlcmV1bS1pcy1zaG93aW5nLXJlbWFya2FibGUtc3RyZW5ndGgtYWdhaW5zdC1idGMvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 06 Jan 2023 08:00:00 GMT", "title": "Trader Who Sold Bitcoin at the Top Says Ethereum Is Showing Remarkable Strength Against BTC - The Daily Hodl", "content": "A popular crypto strategist known for selling Bitcoin (BTC) at its peak says Ethereum (ETH) is showing unprecedented strength against the king crypto.\n\nPseudonymous analyst <PERSON><PERSON><PERSON> tells his 449,100 Twitter followers that the smart contract platform is dominating other altcoins in its price ratio with BTC.\n\n“There has never been a coin in crypto history that has been so dominant against BTC over this kind of timeframe. I’m honestly impressed. This is the kind of dominance you want to see to separate yourself from the other altcoins.”\n\nLooking at DonAlt’s chart, he shows ETH/BTC since June 2021 trading in the range of a low of $0.569 to a high of $0.0850. At time of writing, Ethereum is changing hands at $1,263, or ETH/BTC $0.0751.\n\n<PERSON><PERSON><PERSON> also says he remains bullish on Litecoin (LTC), setting a price target of $100 if it breaks through the $80 resistance level.\n\n“Above $80 there is nothing besides $100. Still long, been a pretty chill ride. Nothing has changed, my LTC trade is a high timeframe swing trade, please stop asking me for an update every 10 minutes, please stop asking me where to short.”\n\n<PERSON><PERSON><PERSON> said in November he purchased an undisclosed amount of Litecoin at $60, sold a lesser undisclosed amount at $77, and later rebought what he sold at $70.\n\n<PERSON><PERSON><PERSON><PERSON> is valued at $75.51 at time of writing.\n\nWhile <PERSON><PERSON><PERSON> sold his Bitcoin holdings at peak price, he recently bought back Bitcoin in the low-$16,000 range “for the long term.”\n\nCurrently, Bitcoin is changing hands at $16,819.\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on Twitter Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any loses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/monkographic\n\nGenerated Image: Midjourney"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiY2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1jbGFzc2ljLWFjaGlldmVzLXN0cm9uZy1ib3VuY2UtYmFjay10aGFua3MtdG8taGFzaC1yYXRlLXN0aW11bGF0aW9uL9IBZ2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS1jbGFzc2ljLWFjaGlldmVzLXN0cm9uZy1ib3VuY2UtYmFjay10aGFua3MtdG8taGFzaC1yYXRlLXN0aW11bGF0aW9uL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 06 Jan 2023 08:00:00 GMT", "title": "Ethereum Classic achieves strong bounce back, thanks to hash rate stimulation - AMBCrypto News", "content": "Ethereum Classic’s hash rate bounced back slightly, potentially indicating increased network activity.\n\nETC’s bullish momentum might be limited, according to its MA indicators.\n\nEvery once in a while, Ethereum Classic [ETC] registers a large price pump that is usually associated with whale activity. The latest such event occurred on 4 January, when ETC bulls pulled off a surprise attack and pushed the token up by over 20% in one day.\n\nRead Ethereum Classic’s [ETC] Price Prediction 2023-2024\n\nThis ETC surge meant that the token had outperformed some of the top cryptocurrencies, including Ethereum [ETH] and Bitcoin [BTC]. A potential reason for this was the pivot in Ethereum Classic’s hash rate. The latter had an undeniable impact on ETC’s price action.\n\nThe Ethereum Classic hash rate declined for most of December and pivoted towards the end. It may not have achieved a major recovery, but the minor upside does demonstrate a shift in miner activity. This could be because of higher miner profitability, which would indicate that there is a significant surge in transactions.\n\nThe idea that ETC transactions have increased in the last few days is supported by a surge in volume. This has been the case in the last two days, during which Ethereum Classic’s volume soared to a new monthly high.\n\nWhy the upside of ETC might be limited\n\nThis strong volume surge resulted in a sizable uptick in ETC’s price action, which traded at $18.63 at the time of writing. In contrast, it traded as low as $15.83 during the 4 January trading session. However, this rally might be restricted, especially now that ETC has come into contact with its 50-day moving average.\n\nETC’s price has also crossed above the 50% RSI level. So far, the price has experienced some selling pressure, indicating that profit-taking has intensified after the rally. In addition, the MFI is now overbought, hence the chances that the rally will be capped are notably higher.\n\nThere are other observations that suggest a higher chance that the bears will regain control. For example, the weighted sentiment indicates that many analysts are still leaning toward the bearish side.\n\nHow many ETCs can you get for $1?\n\nThe Binance funding rate confirmed a surge in derivatives demand for ETC. However, this upside was limited, suggesting that the demand wave might be weak. It also does not help that Ethereum Classic has seen relatively low development activity in the last few weeks.\n\nThe lack of strong development activity may fail to facilitate a strong favorable sentiment. We can thus conclude that ETC’s short-term prospects may still favor the bears. Nevertheless, this might not necessarily be the case if the market sees an unexpected surge in demand."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMicmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9ldGhlcmV1bS1zLXNoYW5naGFpLXVwZ3JhZGUtY291bGQtc3VwZXJjaGFyZ2UtbGlxdWlkLXN0YWtpbmctZGVyaXZhdGl2ZXMtaGVyZS1zLWhvd9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 06 Jan 2023 08:00:00 GMT", "title": "Ethereum's Shanghai upgrade could supercharge liquid staking derivatives — Here's how - Cointelegraph", "content": "The crypto market witnessed the DeFi summer of 2020, where decentralized finance applications like Compound and Uniswap turned Ether (ETH) and Bitcoin (BTC) into yield-bearing assets via yield farming and liquidity mining rewards. The price of Ether nearly doubled to $490 as the total liquidity across decentralized finance (DeFi) protocols quickly surged to $10 billion.\n\nToward the end of 2020 and early 2021, the COVID-19-induced quantitative easing across global markets was in full effect, causing a mega-bull run that lasted almost a year. During this time, Ether’s price increased nearly ten times to a peak above $4,800.\n\nAfter the euphoric bullish phase ended, a painful cool-down journey was exacerbated by the UST-LUNA crash which began in early 2022. This took Ether’s price down to $800. A ray of hope eventually arrived in the third quarter as the market experienced a positive rally led by the Ethereum Merge narrative.\n\nThe shift to an environmentally-friendly proof-of-stake (PoS) consensus mechanism was a big step forward. The event also reduced Ether inflation post-merge. During a lead-up to the Merge on Sept. 15, 2021, ETH peaked at over $2,000. However, the bullish momentum faded quickly, turning the Merge into a buy-the-rumor and sell-the-news event.\n\nA similar bullish opportunity could be brewing in Ether as the upcoming Shanghai upgrade scheduled for March 2023 grabs the market spotlight. The upgrade will finally enable withdrawals from Ethereum staking contracts, which are locked presently. The upgrade will significantly reduce the risk of staking ETH.\n\nIt will provide an opportunity for liquidity staking protocols to grow. The governance tokens of some of these protocols have jumped since the start of the new year as hype builds around.\n\nThere’s a possibility that the upgrade can push these tokens toward last year’s Merge highs. Moreover, Ethereum’s staking space is still in its early stages, providing a market opportunity for the growth of these protocols.\n\nThe percentage of staked Ether is low\n\nCurrently, 13.18% of Ether’s total supply is staked on the Beacon Chain, which is low compared to other PoS chains like Cosmos Hub with a staking ratio of 62.5%, Cardano with 71.8%, and Solana at 71.4%. The reason for Ethereum’s low staking ratio is that the Staked Ether (stETH) is locked in its current state, but this will change in March.\n\nEthereum has the lowest staking ratio compared to other L1 blockchains. Source: Staking Rewards\n\nThe upcoming Shanghai upgrade will include a code known as EIP 4895 that will allow Beacon Chain staked Ether withdrawals, enabling a 1:1 exchange of staked Ether for Ether. Ethereum’s staking ratio should reach parity with other leading PoS networks after this update. A significant portion of which will likely move to liquid staking protocols.\n\nDe-risking of liquid staking derivatives\n\nLiquid staking protocols like Lido and Rocket Pool let Ether holders stake without running a validator node. Since Ether is pooled, a single user doesn’t have a minimum threshold of 32 ETH (worth around $40,000) for staking. People can stake fractions of Ether, reducing the entry barrier for staking.\n\nThe protocols also enable liquidity provision for staked assets, which would otherwise be locked in the staking contracts. The DeFi contracts give a derivative token (for instance, Lido’s stETH) in exchange for staked Ether on the proof-of-stake (PoS) network. A user can trade with stETH while earning yields from the staking contract.\n\nAs Ethereum’s staking ratio increases after March’s update, the use of liquidity staking protocols will likely increase with it. Currently, the liquid staking protocols account for 32.65% of the total staked Ether. Due to the benefits mentioned above, their market share should remain near or above current levels after the Shanghai upgrade.\n\nThe governance tokens of liquid staking protocols could also benefit from their increased locked value, similar to DeFi tokens, which benefited from a rise in total locked value (TVL) in the latest bull run.\n\nHow are LSD governance tokens performing ahead of Shanghai?\n\nLido DAO (LDO)\n\nLido DAO is the leader of the liquid staking space with higher annual yield and market share than other protocols. Lido commands 88.55% of the total Staked Ether in these protocols.\n\nLet's take the amount of staked Ether as a proxy for evaluating the protocol. We again find that Lido has the most competitive market capitalization to Staked Ether ratio.\n\nSource: Coingecko, Dune Analytics\n\nThe weak point of the project’s token economics is that LDO is a governance token. It doesn’t entitle holders to a share of the generated yield or fees. Moreover, the token has additional inflation from investor token unlocking until May this year.\n\nLDO 4-hour price chart. Source: TradingView\n\nTechnically, the LDO token broke above the short-term resistance of around $1.17 with significant buying volume. Bulls will likely target $1.80, capitalizing on the hype around the Shanghai upgrade.\n\nThe token is heavily shorted in the futures market after the recent 26% rise in its price since Jan. 1. The funding rate for LDO perpetual swap turned negative with a large magnitude, providing an opportunity for a further uptrend in a short-squeeze. The current support levels for LDO are $1.17 and $1.\n\nRocket Pool (RPL)\n\nRocket Pool is similar to Lido, albeit smaller in size. The market capitalization to the stETH ratio of the platform is five times larger than Lido, which likely makes it overpriced.\n\nNevertheless, the RPL token has additional utility besides governance as an insurance token for users. Node operators stake RPL as insurance, where users receive the staked RPL in case of losses due to the operator's fault.\n\nThe Ethereum Merge high of RPL in September 2021 was $34.30. Since the start of 2023, its price has increased by 10%, last trading at $22.40. If buyers are successful in building support above the $20 level, there’s a possibility that RPL can reach last year’s high of $30, which was attained around the Ethereum Merge.\n\nAnkr (ANKR)\n\nAnkr is a blockchain infrastructure provider which offers API endpoints and runs RPC nodes besides staking solutions. Similar to LDO, ANKR is only used for governance purposes.\n\nThe token’s price has stayed relatively flat over the last few days. The market capitalization to the staked Ether ratio of Ankr is on the higher side at par with Rocket Pool, which is a negative sign.\n\nStill, if the hype around Shanghai upgrade increases, ANKR can reach August 2021 highs of $0.05. The recent breakdown level of $0.03 will act as resistance for buyers. Currently, the token is trading around $0.015.\n\nStakewise (SWISE)\n\nStakewise offers the highest staking yield of 4.43%. Its governance token is comparatively less inflated than RPL and ANKR in the market capitalization to Staked Ether ratio, making it cheaper than RPL and ANKR.\n\nHowever, the token distribution is adversely skewed towards private investors and the founding team, which have 46.9% of SWISE’s total supply. According to data from Nansen, wallets identified as “smart money” have been slowly accumulating SWISE since April 2021.\n\nSmart wallet holdings of SWISE tokens. Source: Nansen\n\nThe Ethereum Merge high for SWISE was $0.23, which will be the likely target for buyers. The support lies near 2022-lows around $0.07.\n\nShared Stake is flagged red because the protocol was suspected of an insider exploit, which caused a 95% decline in the token’s price in June 2021. The high staking return of the Shared Stake compared to others is also an eyebrow-raising detail to take note of. On the other hand, Cream Finance has discontinued its Ether staking service.\n\nThe upcoming Ethereum Shanghai upgrade provides an opportunity for the liquid staking space to grow. Lido DAO is the clear leader in this space with an optimum market price. The de-risking of ETH staking and hype around the event could translate to a series of rallies that could push the price of LDO and other liquid staking protocols back to their Merge highs from last year.\n\nThe views, thoughts and opinions expressed here are the authors’ alone and do not necessarily reflect or represent the views and opinions of Cointelegraph.\n\nThis article does not contain investment advice or recommendations. Every investment and trading move involves risk, and readers should conduct their own research when making a decision."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtdnMtcG9seWdvbi1kZXZlbG9wZXItcGVyc3BlY3RpdmUtMTUxMDAwNTM4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 06 Jan 2023 08:00:00 GMT", "title": "Solana vs. Polygon: A Developer's Perspective - Yahoo Finance", "content": "I’ve been working on consumer products for about a decade, having contributed to teams that have built and scaled apps to hundreds of millions of users. For the last six years I’ve been 100% focused on crypto. A large part of that has been working on scaling solutions for large-scale, consumer-crypto products.\n\nI’ve been part of teams that have built and launched products on Ethereum, Stellar and Solana, and have evaluated most of the other major blockchains and layer 2 scaling options, which has included spinning up basic proof-of-concepts. In particular, I’ve looked deeply at the benefits and tradeoffs of Polygon – the dominant scaling option for Ethereum today.\n\n<PERSON> is a senior blockchain developer and currently the head of operations and go-to-market for Code, an upcoming self-custodial crypto wallet app built on Solana.\n\nWeb2 developers planning to jump into Web3 often wonder which blockchain they should start with. Most of these discussions end up focusing on Solana and Polygon. It’s a debate that’s only grown more heated in the weeks since <PERSON>’s crypto trading empire collapsed, given he was one of Solana’s most prominent supporters.\n\nAlthough many see the downfall of FTX as a potential death knell to Solana, the network remains a promising technological solution to blockchain’s scaling issues. Even Ethereum co-creater <PERSON><PERSON> has noted the strength and gumption of developers building on Solana. And, developer activity is one of the best leading indicators of value creation over time.\n\nGiven this I thought it would be helpful to share my perspective as someone experienced in different crypto networks on the pros and cons of Solana and Polygon. Note: This article primarily focuses on Polygon POS (proof-of-stake), the dominant product within the Polygon product suite, with which I have direct experience. This article also contemplates other Polygon products currently in development, based on the information I’m able to gather publicly, which will be noted when referenced.\n\nStory continues\n\nSee also: Why Solana Was Decimated by Bankman-Fried's Downfall | Opinion\n\nThere’s three criteria I think are both critically important and materially different between the two chains: performance, approach to scaling and security.\n\n1. Performance\n\nIt might be an unpopular opinion to say that transactions-per-second (TPS) is a bad metric. What really matters is seconds-per-transaction (SPT). These are not the same; let me explain.\n\nTransactions-per-second is calculated by taking the number of transactions in a block and dividing that by the time it takes for validators or miners to produce a block, a.k.a. block time. This method misses an important nuance in how block time feels for crypto users.\n\nLet’s say a blockchain produces a block once every hour, but that block contains a billion transactions. This blockchain would technically boast 277,000 TPS, but anyone using it wouldn’t think so. The median seconds-per-transaction (assuming normal distribution of transaction submission across the hour) would be 30 minutes. This is why block time matters.\n\nOn Solana, the block time (referred to as “slot time”) is ~0.4 seconds. On Polygon the block time is ~2 seconds. This might seem like splitting hairs but the difference between 2 seconds and a half second is a big deal. When a user experiences more than a second of latency in an app (the brain processes information it sees at <0.15 seconds), it can feel like an eternity. I’ve seen this phenomenon first hand while working on consumer apps. Moreover, every millisecond counts when price discovery and execution are so tied for financial applications.\n\nAnother technical consideration when comparing block times is that Polygon (like other EVM chains) uses a mempool where transactions are indexed before being added to a block. Solana takes a different route, where transactions are submitted directly to the leader in the validator set. So while block time on Polygon is ~2 seconds, there is no guarantee a transaction will make it into the next block since it could get stuck in the mempool – especially in times of high volatility.\n\nThis phenomenon of getting “stuck” or “delayed” is a result of the block size. Imagine Venmo ran an app on-chain and needed to fill dozens of orders per second, if it chose a blockchain with 0.01-second block times that could only fit one transaction in a block, the observed SPT would be sluggish.\n\nOn Solana, blocks can theoretically reach a maximum size of 128MB. The Solana Turbine protocol breaks a block into 1280 byte packets called shreds. Through Solana’s Tower BFT protocol, these can be verified concurrently by separate validators, enabling parallelized computation.\n\nOn Polygon, the block size for the POS chain is currently between 50-120KB. There is a product in development called Polygon Avail that should increase this capacity. Avail is a data availability protocol that sits on top of the Polygon POS chain to increase storage. It is currently spec’d at 2MB per block with a 20 second block time though may scale to 128MB (with a theoretical minimum block time for a block that size of 5 seconds).\n\nBlock capacity manifests in fee markets, which we can see today. The average transaction fee on Polygon is ~$0.02 whereas Solana is ~$0.0002. According to 0x, when block utilization approaches 80%, the fee markets start increasing substantially, and as block utilization approaches 95%, it starts increasing exponentially.\n\nPolygon is designed to scale through parallelized sidechains, which opens up the option to increase total block capacity through more chains, so that could bring the fee markets down. Although, this approach to scaling carries bigger systemic risks that, depending on what you’re building, may make building on Polygon challenging.\n\nThis is where I believe the biggest divergence in the two chains is, and is the subject of the next section.\n\n2. Approach to scaling\n\nA one sentence summary of how Solana and Polygon differ on their approach to scaling would be: Solana is built to keep everything on a single chain and Polygon is built to add more concurrent chains that merge state periodically.\n\nTo expand on this, a Solana cluster (set of validators contributing to consensus) has a leader schedule. This leader schedule enumerates which validator will be verifying each block (a.k.a. a slot on Solana). With a predefined leader schedule, transactions are forwarded to the scheduled leader, cutting down on unnecessary coordination.\n\nSee also: Solana Co-Founder Sees Upside From Ethereum's Merge\n\nIn the near future, Solana should also see multiple block leaders which will improve performance and mitigate downtime. All of this is uniquely enabled by Solana’s proof-of-history consensus protocol that aligns the network around a shared clock. Without a shared time-keeper, the network has to be reactive to emergent leaders in the validation schedule.\n\nProof-of-history combined with the Turbine protocol enables fast streaming of large amounts of data that all lives on one chain.\n\nPolygon has a similar system of defining a block leader through its Bor protocol, but the approach to scaling is different. Polygon is set up to scale by adding more sidechains running in parallel and merging state periodically through a commitment to Ethereum. That’s why you may see Polygon POS and Plasma referred to as a “commit chain.” Multiple, parallel sidechains may mean it’s possible for two users to be on separate sidechains using the same app, which would mean users are beholden to the latency of the state merge between chains and developers have to build for complexity.\n\nThis way Polygon scales may also introduce the risk a blockchain could see a “re-org” (where transactions are rolled back) since a user cannot reliably consider their transaction “final” until block height reaches a certain threshold. There’s no magic number for this, but as an example, stablecoin issuer Circle waits for 372 blocks to pass on Polygon (~20 minutes) before they consider a transaction “final” and 1 block (~0.4 seconds) on Solana.\n\nPolygon’s variant finality is a substantial drawback, and may impact what type of apps could be built on it. For instance, crypto apps might put a user’s funds on hold until a transaction is considered final or allow users to transact immediately and accept the risk of a double spend exploit (like accepting credit card chargeback risk). This is a step backwards from crypto’s supposed elimination of financial counterparty risks.\n\nA single chain will always be better than a collection of sidechains, assuming the single chain can scale. A single chain has less coordination complexity, less aggregation latency and less surface area for attack.\n\nThe question is, can Solana support the same scale that Polygon claims to? Based on a recent demo from Kevin Bowers at Jump Crypto, it seems it can. The team at Jump Crypto has built a new validator client for Solana called Firedancer that demonstrated 1.2 million transactions per second, while maintaining the sub-second slot time. To put that into perspective, if you counted every Twitter, WhatsApp and Instagram interaction as a transaction, they could all run concurrently on Solana without any performance degradation.\n\nPolygon also has a substantial set of technical improvements in development. Specifically, the investment in zero-knowledge (zk) rollups should result in substantial performance improvements. However, zk rollups must settle on the layer 1 (currently Ethereum) to be considered valid – meaning while they open up capacity they also add latency.\n\n3. Security\n\nIt’s one thing to be high performing and scalable, but we also need confidence in the network. Both Solana and Polygon have had significant soak time, so the chance of a critical bug lying in plain sight is diminishing (although not impossible).\n\nGiven this, potential blockchain developers should focus on a network’s resiliency to bad actors trying to influence state. This is most objectively measured by the Nakamoto Coefficient (NC), a metric that quantifies the number of validators that would need to collude to corrupt a network.\n\nAt the time of this writing the Solana NC is 32 and Polygon is 4. Both are proof-of-stake networks so distribution of stake matters. Solana has approximately 1,900 validators and Polygon has roughly 100. What the Nakamoto Coefficient says here is that the top 32 validators on Solana and the top four validators on Polygon could take down the network.\n\nBoth networks would do well to increase security on this vector, and I think they both will, and both Polygon and Solana have seen an increase in their NC over the last year. As more validators come online, Polygon’s NC will grow, and other technical improvements in development like zk rollups will improve security.\n\nIt’s argued that Polygon has higher security because it commits state periodically to Ethereum. This is a bit misguided though because the Polygon network is the one updating state to Ethereum, so if four validators colluded to corrupt the ledger, in theory a commitment could be made to Ethereum with the corrupted ledger. This is a solvable problem the Polygon team and broader ecosystem are working on.\n\nWhile I do think there is a divergence in security today, I think both blockchains will see big improvements here over time and we may see some convergence coming from a mix of technical and social advancements. This is probably the hardest one to project.\n\n4. Bonus: community\n\nWhile not a technical criteria, community is nonetheless an important variable in the success of a network. Polygon has an advantage in being connected to Ethereum where there is an objectively larger developer and consumer community than Solana. Further, the Polygon Foundation has done a good job cultivating brands and developers to build on its network – I would expect this to continue.\n\nAnd given Polygon plans to bridge to multiple blockchains, they’ll be able to leverage existing communities bringing even more developers and consumers into the fold.\n\nThis isn’t a moat, however. It’s early in crypto with most of the world still on the sidelines, I think performant applications will bring them in, and the developer, creator and consumer communities will congregate where the best apps and the most users are. That’s why I think the technical criteria above are a leading indicator of future community development.\n\nWhile Solana had to overcome a cold-start problem, in less than three years it has grown to one of the most active developer, creator and consumer communities. There is a diverse set of developers making the core more performant and robust, while others are adding services and tooling to make the developer experience better.\n\nSee also: The Fake Team That Made Solana DeFi Look Huge\n\nThat foundation has enabled talented teams to build strong consumer experiences across the major categories in crypto. I would expect this cycle to continue, bringing more talented people into the ecosystem as the talent density compounds.\n\nI do think both Polygon and Solana serve important needs in the broader crypto ecosystem, but I do think it is important for developers to understand the pros and cons. I hope this helps."}, {"id": 18, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtdnMtcG9seWdvbi1kZXZlbG9wZXItcGVyc3BlY3RpdmUtMTUxMDAwNTM4Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 06 Jan 2023 08:00:00 GMT", "title": "Solana vs. Polygon: A Developer's Perspective - Yahoo Finance", "content": "I’ve been working on consumer products for about a decade, having contributed to teams that have built and scaled apps to hundreds of millions of users. For the last six years I’ve been 100% focused on crypto. A large part of that has been working on scaling solutions for large-scale, consumer-crypto products.\n\nI’ve been part of teams that have built and launched products on Ethereum, Stellar and Solana, and have evaluated most of the other major blockchains and layer 2 scaling options, which has included spinning up basic proof-of-concepts. In particular, I’ve looked deeply at the benefits and tradeoffs of Polygon – the dominant scaling option for Ethereum today.\n\n<PERSON> is a senior blockchain developer and currently the head of operations and go-to-market for Code, an upcoming self-custodial crypto wallet app built on Solana.\n\nWeb2 developers planning to jump into Web3 often wonder which blockchain they should start with. Most of these discussions end up focusing on Solana and Polygon. It’s a debate that’s only grown more heated in the weeks since <PERSON>’s crypto trading empire collapsed, given he was one of Solana’s most prominent supporters.\n\nAlthough many see the downfall of FTX as a potential death knell to Solana, the network remains a promising technological solution to blockchain’s scaling issues. Even Ethereum co-creater <PERSON><PERSON> has noted the strength and gumption of developers building on Solana. And, developer activity is one of the best leading indicators of value creation over time.\n\nGiven this I thought it would be helpful to share my perspective as someone experienced in different crypto networks on the pros and cons of Solana and Polygon. Note: This article primarily focuses on Polygon POS (proof-of-stake), the dominant product within the Polygon product suite, with which I have direct experience. This article also contemplates other Polygon products currently in development, based on the information I’m able to gather publicly, which will be noted when referenced.\n\nStory continues\n\nSee also: Why Solana Was Decimated by Bankman-Fried's Downfall | Opinion\n\nThere’s three criteria I think are both critically important and materially different between the two chains: performance, approach to scaling and security.\n\n1. Performance\n\nIt might be an unpopular opinion to say that transactions-per-second (TPS) is a bad metric. What really matters is seconds-per-transaction (SPT). These are not the same; let me explain.\n\nTransactions-per-second is calculated by taking the number of transactions in a block and dividing that by the time it takes for validators or miners to produce a block, a.k.a. block time. This method misses an important nuance in how block time feels for crypto users.\n\nLet’s say a blockchain produces a block once every hour, but that block contains a billion transactions. This blockchain would technically boast 277,000 TPS, but anyone using it wouldn’t think so. The median seconds-per-transaction (assuming normal distribution of transaction submission across the hour) would be 30 minutes. This is why block time matters.\n\nOn Solana, the block time (referred to as “slot time”) is ~0.4 seconds. On Polygon the block time is ~2 seconds. This might seem like splitting hairs but the difference between 2 seconds and a half second is a big deal. When a user experiences more than a second of latency in an app (the brain processes information it sees at <0.15 seconds), it can feel like an eternity. I’ve seen this phenomenon first hand while working on consumer apps. Moreover, every millisecond counts when price discovery and execution are so tied for financial applications.\n\nAnother technical consideration when comparing block times is that Polygon (like other EVM chains) uses a mempool where transactions are indexed before being added to a block. Solana takes a different route, where transactions are submitted directly to the leader in the validator set. So while block time on Polygon is ~2 seconds, there is no guarantee a transaction will make it into the next block since it could get stuck in the mempool – especially in times of high volatility.\n\nThis phenomenon of getting “stuck” or “delayed” is a result of the block size. Imagine Venmo ran an app on-chain and needed to fill dozens of orders per second, if it chose a blockchain with 0.01-second block times that could only fit one transaction in a block, the observed SPT would be sluggish.\n\nOn Solana, blocks can theoretically reach a maximum size of 128MB. The Solana Turbine protocol breaks a block into 1280 byte packets called shreds. Through Solana’s Tower BFT protocol, these can be verified concurrently by separate validators, enabling parallelized computation.\n\nOn Polygon, the block size for the POS chain is currently between 50-120KB. There is a product in development called Polygon Avail that should increase this capacity. Avail is a data availability protocol that sits on top of the Polygon POS chain to increase storage. It is currently spec’d at 2MB per block with a 20 second block time though may scale to 128MB (with a theoretical minimum block time for a block that size of 5 seconds).\n\nBlock capacity manifests in fee markets, which we can see today. The average transaction fee on Polygon is ~$0.02 whereas Solana is ~$0.0002. According to 0x, when block utilization approaches 80%, the fee markets start increasing substantially, and as block utilization approaches 95%, it starts increasing exponentially.\n\nPolygon is designed to scale through parallelized sidechains, which opens up the option to increase total block capacity through more chains, so that could bring the fee markets down. Although, this approach to scaling carries bigger systemic risks that, depending on what you’re building, may make building on Polygon challenging.\n\nThis is where I believe the biggest divergence in the two chains is, and is the subject of the next section.\n\n2. Approach to scaling\n\nA one sentence summary of how Solana and Polygon differ on their approach to scaling would be: Solana is built to keep everything on a single chain and Polygon is built to add more concurrent chains that merge state periodically.\n\nTo expand on this, a Solana cluster (set of validators contributing to consensus) has a leader schedule. This leader schedule enumerates which validator will be verifying each block (a.k.a. a slot on Solana). With a predefined leader schedule, transactions are forwarded to the scheduled leader, cutting down on unnecessary coordination.\n\nSee also: Solana Co-Founder Sees Upside From Ethereum's Merge\n\nIn the near future, Solana should also see multiple block leaders which will improve performance and mitigate downtime. All of this is uniquely enabled by Solana’s proof-of-history consensus protocol that aligns the network around a shared clock. Without a shared time-keeper, the network has to be reactive to emergent leaders in the validation schedule.\n\nProof-of-history combined with the Turbine protocol enables fast streaming of large amounts of data that all lives on one chain.\n\nPolygon has a similar system of defining a block leader through its Bor protocol, but the approach to scaling is different. Polygon is set up to scale by adding more sidechains running in parallel and merging state periodically through a commitment to Ethereum. That’s why you may see Polygon POS and Plasma referred to as a “commit chain.” Multiple, parallel sidechains may mean it’s possible for two users to be on separate sidechains using the same app, which would mean users are beholden to the latency of the state merge between chains and developers have to build for complexity.\n\nThis way Polygon scales may also introduce the risk a blockchain could see a “re-org” (where transactions are rolled back) since a user cannot reliably consider their transaction “final” until block height reaches a certain threshold. There’s no magic number for this, but as an example, stablecoin issuer Circle waits for 372 blocks to pass on Polygon (~20 minutes) before they consider a transaction “final” and 1 block (~0.4 seconds) on Solana.\n\nPolygon’s variant finality is a substantial drawback, and may impact what type of apps could be built on it. For instance, crypto apps might put a user’s funds on hold until a transaction is considered final or allow users to transact immediately and accept the risk of a double spend exploit (like accepting credit card chargeback risk). This is a step backwards from crypto’s supposed elimination of financial counterparty risks.\n\nA single chain will always be better than a collection of sidechains, assuming the single chain can scale. A single chain has less coordination complexity, less aggregation latency and less surface area for attack.\n\nThe question is, can Solana support the same scale that Polygon claims to? Based on a recent demo from Kevin Bowers at Jump Crypto, it seems it can. The team at Jump Crypto has built a new validator client for Solana called Firedancer that demonstrated 1.2 million transactions per second, while maintaining the sub-second slot time. To put that into perspective, if you counted every Twitter, WhatsApp and Instagram interaction as a transaction, they could all run concurrently on Solana without any performance degradation.\n\nPolygon also has a substantial set of technical improvements in development. Specifically, the investment in zero-knowledge (zk) rollups should result in substantial performance improvements. However, zk rollups must settle on the layer 1 (currently Ethereum) to be considered valid – meaning while they open up capacity they also add latency.\n\n3. Security\n\nIt’s one thing to be high performing and scalable, but we also need confidence in the network. Both Solana and Polygon have had significant soak time, so the chance of a critical bug lying in plain sight is diminishing (although not impossible).\n\nGiven this, potential blockchain developers should focus on a network’s resiliency to bad actors trying to influence state. This is most objectively measured by the Nakamoto Coefficient (NC), a metric that quantifies the number of validators that would need to collude to corrupt a network.\n\nAt the time of this writing the Solana NC is 32 and Polygon is 4. Both are proof-of-stake networks so distribution of stake matters. Solana has approximately 1,900 validators and Polygon has roughly 100. What the Nakamoto Coefficient says here is that the top 32 validators on Solana and the top four validators on Polygon could take down the network.\n\nBoth networks would do well to increase security on this vector, and I think they both will, and both Polygon and Solana have seen an increase in their NC over the last year. As more validators come online, Polygon’s NC will grow, and other technical improvements in development like zk rollups will improve security.\n\nIt’s argued that Polygon has higher security because it commits state periodically to Ethereum. This is a bit misguided though because the Polygon network is the one updating state to Ethereum, so if four validators colluded to corrupt the ledger, in theory a commitment could be made to Ethereum with the corrupted ledger. This is a solvable problem the Polygon team and broader ecosystem are working on.\n\nWhile I do think there is a divergence in security today, I think both blockchains will see big improvements here over time and we may see some convergence coming from a mix of technical and social advancements. This is probably the hardest one to project.\n\n4. Bonus: community\n\nWhile not a technical criteria, community is nonetheless an important variable in the success of a network. Polygon has an advantage in being connected to Ethereum where there is an objectively larger developer and consumer community than Solana. Further, the Polygon Foundation has done a good job cultivating brands and developers to build on its network – I would expect this to continue.\n\nAnd given Polygon plans to bridge to multiple blockchains, they’ll be able to leverage existing communities bringing even more developers and consumers into the fold.\n\nThis isn’t a moat, however. It’s early in crypto with most of the world still on the sidelines, I think performant applications will bring them in, and the developer, creator and consumer communities will congregate where the best apps and the most users are. That’s why I think the technical criteria above are a leading indicator of future community development.\n\nWhile Solana had to overcome a cold-start problem, in less than three years it has grown to one of the most active developer, creator and consumer communities. There is a diverse set of developers making the core more performant and robust, while others are adding services and tooling to make the developer experience better.\n\nSee also: The Fake Team That Made Solana DeFi Look Huge\n\nThat foundation has enabled talented teams to build strong consumer experiences across the major categories in crypto. I would expect this cycle to continue, bringing more talented people into the ecosystem as the talent density compounds.\n\nI do think both Polygon and Solana serve important needs in the broader crypto ecosystem, but I do think it is important for developers to understand the pros and cons. I hope this helps."}]