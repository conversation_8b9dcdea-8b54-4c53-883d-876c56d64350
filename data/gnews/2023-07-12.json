[{"id": 0, "url": "https://news.google.com/rss/articles/CBMibGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDcvMTIvd2hhdC1pcy1ldGhlcmV1bXMtZGF0YS1hdmFpbGFiaWxpdHktcHJvYmxlbS1hbmQtd2h5LWRvZXMtaXQtbWF0dGVyL9IBcGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDcvMTIvd2hhdC1pcy1ldGhlcmV1bXMtZGF0YS1hdmFpbGFiaWxpdHktcHJvYmxlbS1hbmQtd2h5LWRvZXMtaXQtbWF0dGVyL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 12 Jul 2023 07:00:00 GMT", "title": "What Is Ethereum’s ‘Data Availability' Problem, and Why Does It Matter? - CoinDesk", "content": "What Is Ethereum’s ‘Data Availability' Problem, and Why Does It Matter?\n\nSeparate “data availability” layers could reduce congestion on the Ethereum network by making it easier for ancillary “rollup” networks to verify that transactional details exist and are available to download if needed — without actually downloading them. The concept might offer an alternative to Ethereum’s own proposed solution, seen as years away.\n\nBy Margaux Nijkerk Jul 12, 2023 at 3:34 p.m. UTC"}, {"id": 40, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9laWdlbmxheWVyLWF0dHJhY3RzLW92ZXItMzAtMDAwLTAyNDM1NzQ1MS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 12 Jul 2023 07:00:00 GMT", "title": "EigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps - Yahoo Finance", "content": "EigenLayer, a protocol designed to allow staked Ether to secure additional applications outside of the Ethereum blockchain, is unquestionably on crypto investors’ radars.\n\nEarlier today, the project raised the cap on liquid staking tokens (LSTs) users could deposit in the protocol from 9,600 to 30,000 staked ETH. After investors quickly piled in, EigenLayer bumped the limit up to 45,000 tokens less than an hour later.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nDeposits of Lido’s stETH and Rocket Pool’s rETH, the two most widely held LSTs, are now full. Deposits of cbETH, issued by Coinbase, stand at just above 12,600 at the time of writing. In total, EigenLayer has roughly $75M worth of LSTs deposited in its smart contracts.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nLSTs are yield-bearing tokens which represent ETH staked to secure the Ethereum blockchain and have fostered a burgeoning sector dubbed LSTfi.\n\nEigenLayer is one of the more recognizable projects in LSTfi, and the speed at which investors filled up its deposits demonstrates the demand for the project’s restaking capabilities.\n\nEigenLayer is designed to allow other projects, like bridges or oracle networks, to rely on restaked ETH for security, rather than needing to bootstrap their own validator sets and native token economies. These projects would pay EigenLayer for its services, resulting in additional yield for restakers.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nPotential Airdrop\n\nEigenLayer doesn’t have a token yet, so some early users are undoubtedly hoping for a future airdrop.\n\nEigenLayer’s rollout is planned in three phases, the first of which was completed with the launch of the restaking mainnet last month. Next up, which EigenLayer has said will happen later this year, is the mainnet for “Operators” who facilitate the usage of the restaking protocol. The final phase is “actively validated services” or AVS, which will be the projects reusing Ethereum’s security to bootstrap their own ventures.\n\nEigenLayer has been the source of some controversy because of concerns that it may threaten the neutrality of Ethereum’s consensus mechanism as more applications come to rely on LSTs.\n\nimg,[object Object]\n\nEigenLayer launched last month with an initial cap of 9,600 LSTs, which was filled in minutes."}, {"id": 44, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9laWdlbmxheWVyLWF0dHJhY3RzLW92ZXItMzAtMDAwLTAyNDM1NzQ1MS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 12 Jul 2023 07:00:00 GMT", "title": "EigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps - Yahoo Finance", "content": "EigenLayer, a protocol designed to allow staked Ether to secure additional applications outside of the Ethereum blockchain, is unquestionably on crypto investors’ radars.\n\nEarlier today, the project raised the cap on liquid staking tokens (LSTs) users could deposit in the protocol from 9,600 to 30,000 staked ETH. After investors quickly piled in, EigenLayer bumped the limit up to 45,000 tokens less than an hour later.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nDeposits of Lido’s stETH and Rocket Pool’s rETH, the two most widely held LSTs, are now full. Deposits of cbETH, issued by Coinbase, stand at just above 12,600 at the time of writing. In total, EigenLayer has roughly $75M worth of LSTs deposited in its smart contracts.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nLSTs are yield-bearing tokens which represent ETH staked to secure the Ethereum blockchain and have fostered a burgeoning sector dubbed LSTfi.\n\nEigenLayer is one of the more recognizable projects in LSTfi, and the speed at which investors filled up its deposits demonstrates the demand for the project’s restaking capabilities.\n\nEigenLayer is designed to allow other projects, like bridges or oracle networks, to rely on restaked ETH for security, rather than needing to bootstrap their own validator sets and native token economies. These projects would pay EigenLayer for its services, resulting in additional yield for restakers.\n\nEigenLayer Attracts Over 30,000 ETH Just Minutes After Raising Deposit Caps\n\nPotential Airdrop\n\nEigenLayer doesn’t have a token yet, so some early users are undoubtedly hoping for a future airdrop.\n\nEigenLayer’s rollout is planned in three phases, the first of which was completed with the launch of the restaking mainnet last month. Next up, which EigenLayer has said will happen later this year, is the mainnet for “Operators” who facilitate the usage of the restaking protocol. The final phase is “actively validated services” or AVS, which will be the projects reusing Ethereum’s security to bootstrap their own ventures.\n\nEigenLayer has been the source of some controversy because of concerns that it may threaten the neutrality of Ethereum’s consensus mechanism as more applications come to rely on LSTs.\n\nimg,[object Object]\n\nEigenLayer launched last month with an initial cap of 9,600 LSTs, which was filled in minutes."}, {"id": 24, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9pbnRyb2R1Y2luZy1zb2xhcngtcmV2b2x1dGlvbml6aW5nLWNyeXB0b2N1cnJlbmN5LW1pbmluZy0yMDAwMDA2NDQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 12 Jul 2023 07:00:00 GMT", "title": "Introducing SolarX: Revolutionizing Cryptocurrency Mining with Renewable Energy on the Ethereum Chain - Yahoo Finance", "content": "SolarX\n\nCALIFORNIA CITY, CALIFORNIA, July 12, 2023 (GLOBE NEWSWIRE) -- SolarX, a ground-breaking crypto project, is set to revolutionize the cryptocurrency mining industry by harnessing the power of renewable energy sources. With its innovative and eco-friendly approach, SolarX aims to lead the way towards sustainable and responsible blockchain technology. The project will launch on the Ethereum Chain, providing users with a secure and transparent platform for mining cryptocurrencies.\n\n\n\n\n\nTraditional cryptocurrency mining has long been associated with high energy consumption and its negative impact on the environment. SolarX recognizes this challenge and seeks to address it head-on by leveraging renewable energy sources, such as solar power, to fuel its mining operations on the Ethereum Chain. This innovative approach not only reduces the carbon footprint typically associated with mining, but it also promotes the adoption of clean energy solutions, driving the industry towards a more sustainable future.\n\n\n\n\n\nSolarX's mining operations will be strategically located in regions abundant in solar energy resources. By tapping into these renewable sources, the project aims to minimize its reliance on non-renewable energy and substantially decrease its carbon emissions. Through the integration of cutting-edge technology and sustainability practices, SolarX envisions a future where crypto mining can coexist harmoniously with the planet's natural resources.\n\n\n\n\n\nKey Features of the SolarX Project:\n\n\n\n\n\nRenewable Energy-Powered Mining: SolarX will utilize solar power and other renewable energy sources to power its mining operations, significantly reducing carbon emissions and promoting sustainability.\n\nDecentralized and Transparent: Built on the Ethereum Chain, SolarX operates within a decentralized framework, ensuring transparency, security, and immutability of transactions.\n\nCommunity-Driven Governance: SolarX will empower its community by allowing token holders to actively participate in decision-making processes through decentralized governance mechanisms, ensuring a fair and inclusive ecosystem.\n\nStory continues\n\nEco-Friendly Staking: Users will have the opportunity to stake their SolarX tokens, further supporting the project's commitment to sustainability and earning rewards in return.\n\n\n\n\n\nSolarX is driven by a team of experienced professionals who are passionate about both cryptocurrency and environmental sustainability. By combining their expertise, they aim to redefine the mining industry, fostering a more sustainable future for generations to come.\n\n\n\n\n\nThe SolarX token (SOLX) will be the native cryptocurrency of the SolarX ecosystem. It will serve as a utility token, enabling users to participate in the project's governance, access exclusive features, and benefit from various incentives.\n\n\n\n\n\nThe SolarX project will launch its token through a public sale, providing an opportunity for individuals to support the initiative and become part of the growing community committed to sustainable crypto mining.\n\n\n\n\n\nFor more information about SolarX and to stay updated on the project's latest developments, please visit the official resources:\n\n\n\n\n\nWebsite: https://solarx.ai/\n\nTwitter: https://twitter.com/solarxcoin\n\nTelegram: http://t.me/solarxgroup\n\n\n\n\n\n\n\nJoin us in our mission to reshape the future of cryptocurrency mining, one sustainable block at a time.\n\n\n\n\n\nAbout SolarX:\n\nSolarX is an innovative and eco-friendly crypto project that aims to revolutionize the landscape of cryptocurrency mining. By harnessing the power of renewable energy sources, SolarX seeks to reduce the carbon footprint associated with mining operations and pave the way for a more sustainable future. Built on the Ethereum Chain, SolarX is driven by a team of passionate professionals committed to promoting the adoption of clean energy solutions within the crypto industry.\n\nCONTACT: Alex Parker SolarX info at solarx.ai\n\n\n\n"}, {"id": 28, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9pbnRyb2R1Y2luZy1zb2xhcngtcmV2b2x1dGlvbml6aW5nLWNyeXB0b2N1cnJlbmN5LW1pbmluZy0yMDAwMDA2NDQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 12 Jul 2023 07:00:00 GMT", "title": "Introducing SolarX: Revolutionizing Cryptocurrency Mining with Renewable Energy on the Ethereum Chain - Yahoo Finance", "content": "SolarX\n\nCALIFORNIA CITY, CALIFORNIA, July 12, 2023 (GLOBE NEWSWIRE) -- SolarX, a ground-breaking crypto project, is set to revolutionize the cryptocurrency mining industry by harnessing the power of renewable energy sources. With its innovative and eco-friendly approach, SolarX aims to lead the way towards sustainable and responsible blockchain technology. The project will launch on the Ethereum Chain, providing users with a secure and transparent platform for mining cryptocurrencies.\n\n\n\n\n\nTraditional cryptocurrency mining has long been associated with high energy consumption and its negative impact on the environment. SolarX recognizes this challenge and seeks to address it head-on by leveraging renewable energy sources, such as solar power, to fuel its mining operations on the Ethereum Chain. This innovative approach not only reduces the carbon footprint typically associated with mining, but it also promotes the adoption of clean energy solutions, driving the industry towards a more sustainable future.\n\n\n\n\n\nSolarX's mining operations will be strategically located in regions abundant in solar energy resources. By tapping into these renewable sources, the project aims to minimize its reliance on non-renewable energy and substantially decrease its carbon emissions. Through the integration of cutting-edge technology and sustainability practices, SolarX envisions a future where crypto mining can coexist harmoniously with the planet's natural resources.\n\n\n\n\n\nKey Features of the SolarX Project:\n\n\n\n\n\nRenewable Energy-Powered Mining: SolarX will utilize solar power and other renewable energy sources to power its mining operations, significantly reducing carbon emissions and promoting sustainability.\n\nDecentralized and Transparent: Built on the Ethereum Chain, SolarX operates within a decentralized framework, ensuring transparency, security, and immutability of transactions.\n\nCommunity-Driven Governance: SolarX will empower its community by allowing token holders to actively participate in decision-making processes through decentralized governance mechanisms, ensuring a fair and inclusive ecosystem.\n\nStory continues\n\nEco-Friendly Staking: Users will have the opportunity to stake their SolarX tokens, further supporting the project's commitment to sustainability and earning rewards in return.\n\n\n\n\n\nSolarX is driven by a team of experienced professionals who are passionate about both cryptocurrency and environmental sustainability. By combining their expertise, they aim to redefine the mining industry, fostering a more sustainable future for generations to come.\n\n\n\n\n\nThe SolarX token (SOLX) will be the native cryptocurrency of the SolarX ecosystem. It will serve as a utility token, enabling users to participate in the project's governance, access exclusive features, and benefit from various incentives.\n\n\n\n\n\nThe SolarX project will launch its token through a public sale, providing an opportunity for individuals to support the initiative and become part of the growing community committed to sustainable crypto mining.\n\n\n\n\n\nFor more information about SolarX and to stay updated on the project's latest developments, please visit the official resources:\n\n\n\n\n\nWebsite: https://solarx.ai/\n\nTwitter: https://twitter.com/solarxcoin\n\nTelegram: http://t.me/solarxgroup\n\n\n\n\n\n\n\nJoin us in our mission to reshape the future of cryptocurrency mining, one sustainable block at a time.\n\n\n\n\n\nAbout SolarX:\n\nSolarX is an innovative and eco-friendly crypto project that aims to revolutionize the landscape of cryptocurrency mining. By harnessing the power of renewable energy sources, SolarX seeks to reduce the carbon footprint associated with mining operations and pave the way for a more sustainable future. Built on the Ethereum Chain, SolarX is driven by a team of passionate professionals committed to promoting the adoption of clean energy solutions within the crypto industry.\n\nCONTACT: Alex Parker SolarX info at solarx.ai\n\n\n\n"}]