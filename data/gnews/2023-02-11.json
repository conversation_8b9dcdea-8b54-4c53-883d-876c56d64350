[{"id": 10, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ibGFja3JvY2stZXRmLXRoZXJlcy10b2tlbi0xNDQwMTQ2NTcuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 11 Feb 2023 08:00:00 GMT", "title": "BlackRock ETF? There's a Token For That - Yahoo Finance", "content": "Decrypting DeFi is Decrypt's DeFi email newsletter. (art: <PERSON>)\n\nThe integration of real-world assets, or RWAs, into the world of DeFi has been one of the hottest narratives so far in 2023. And emerging just in time to meet that red-hot trend head on is a crypto startup looking to soak up the interest.\n\nThis week, Switzerland-based Backed Finance launched a tokenized version of a BlackRock ETF (CSPX) that includes tons of big names like Apple, Microsoft, Amazon, Alphabet, and 496 other large American companies.\n\nThe token is called bCSPX, and runs on Ethereum as an ERC-20 token. The token is backed 1:1 by shares of those stocks, and the shares are held by a licensed custodian, according to Backed.\n\nAnd insofar as it runs on Ethereum, that means it can hypothetically interact with a whole host of DeFi applications, leading to some pretty innovative ideas like, for example, minting MakerDAO’s decentralized stablecoin DAI using a stock index as collateral.\n\nAnd it’s not just the S&P 500, either. Based on the project’s marketing materials, the expectation is to onboard additional public securities under a regulatory framework that takes a page from Switzerland’s Distributed Ledger Technology act.\n\nIn terms of who can get a hold of these tokens, it looks like there’s three kinds of users. The first is professional investors and exchange platforms that may want to offer the token to clients; the second is KYC’d token owners who are also interested in redeeming the underlying asset (a key feature should the token depeg from the price of the underlying asset like Grayscale’s GBTC product); and the third is everyone else on the market.\n\nWell, almost everyone. You can't get the token in the United States—likely a decision by Backed Finance to avoid having to navigate the SEC's choppy regulatory waters right now.\n\nThe United States has put itself in strange company. (Source: Backed Finance)\n\nThere’s another hurdle to overcome, too.\n\nWhen the product was initially launched, a Uniswap pool was seeded with enough liquidity for folks to get started trading bCSPX. But now, Uniswap’s interface indicates that the Backed’s new token is “unsupported” because it “may not work well with the smart contracts or we [Uniswap] are unable to allow trading for legal reasons.” Tokens that fall under this list, and dont’t have “caution” or “warning” next to them, are “manually curated by Uniswap Labs.”\n\nStory continues\n\nOf course, this may not be the biggest issue. If you’re an American citizen living in the United States, you likely won’t need a product like this. You can just head over to Fidelity or another broker and scoop up your fill there.\n\nUnfortunately, it means that American traders won’t get to enjoy speedy swaps between, say, Apple stock and Circle’s USDC. They may also get left behind should assets like bCSPX become deeply integrated in other DeFi apps.\n\nStill, BlackRock CEO Larry Fink doesn't sound concerned about that. In December, the finance exec declared that “the next generation for markets, the next generation for securities, will be tokenization of securities.”\n\nDecrypting DeFi is our DeFi newsletter, led by this essay. Subscribers to our emails get to read the essay before it goes on the site. Subscribe here."}, {"id": 22, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ibGFja3JvY2stZXRmLXRoZXJlcy10b2tlbi0xNDQwMTQ2NTcuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 11 Feb 2023 08:00:00 GMT", "title": "BlackRock ETF? There's a Token For That - Yahoo Finance", "content": "Decrypting DeFi is Decrypt's DeFi email newsletter. (art: <PERSON>)\n\nThe integration of real-world assets, or RWAs, into the world of DeFi has been one of the hottest narratives so far in 2023. And emerging just in time to meet that red-hot trend head on is a crypto startup looking to soak up the interest.\n\nThis week, Switzerland-based Backed Finance launched a tokenized version of a BlackRock ETF (CSPX) that includes tons of big names like Apple, Microsoft, Amazon, Alphabet, and 496 other large American companies.\n\nThe token is called bCSPX, and runs on Ethereum as an ERC-20 token. The token is backed 1:1 by shares of those stocks, and the shares are held by a licensed custodian, according to Backed.\n\nAnd insofar as it runs on Ethereum, that means it can hypothetically interact with a whole host of DeFi applications, leading to some pretty innovative ideas like, for example, minting MakerDAO’s decentralized stablecoin DAI using a stock index as collateral.\n\nAnd it’s not just the S&P 500, either. Based on the project’s marketing materials, the expectation is to onboard additional public securities under a regulatory framework that takes a page from Switzerland’s Distributed Ledger Technology act.\n\nIn terms of who can get a hold of these tokens, it looks like there’s three kinds of users. The first is professional investors and exchange platforms that may want to offer the token to clients; the second is KYC’d token owners who are also interested in redeeming the underlying asset (a key feature should the token depeg from the price of the underlying asset like Grayscale’s GBTC product); and the third is everyone else on the market.\n\nWell, almost everyone. You can't get the token in the United States—likely a decision by Backed Finance to avoid having to navigate the SEC's choppy regulatory waters right now.\n\nThe United States has put itself in strange company. (Source: Backed Finance)\n\nThere’s another hurdle to overcome, too.\n\nWhen the product was initially launched, a Uniswap pool was seeded with enough liquidity for folks to get started trading bCSPX. But now, Uniswap’s interface indicates that the Backed’s new token is “unsupported” because it “may not work well with the smart contracts or we [Uniswap] are unable to allow trading for legal reasons.” Tokens that fall under this list, and dont’t have “caution” or “warning” next to them, are “manually curated by Uniswap Labs.”\n\nStory continues\n\nOf course, this may not be the biggest issue. If you’re an American citizen living in the United States, you likely won’t need a product like this. You can just head over to Fidelity or another broker and scoop up your fill there.\n\nUnfortunately, it means that American traders won’t get to enjoy speedy swaps between, say, Apple stock and Circle’s USDC. They may also get left behind should assets like bCSPX become deeply integrated in other DeFi apps.\n\nStill, BlackRock CEO Larry Fink doesn't sound concerned about that. In December, the finance exec declared that “the next generation for markets, the next generation for securities, will be tokenization of securities.”\n\nDecrypting DeFi is our DeFi newsletter, led by this essay. Subscribers to our emails get to read the essay before it goes on the site. Subscribe here."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjMvMDIvMTEvd2hhdC1pbnZlc3RvcnMtbmVlZC10by1rbm93LWFib3V0LWNyeXB0by1zdGFraW5nLmh0bWzSAVlodHRwczovL3d3dy5jbmJjLmNvbS9hbXAvMjAyMy8wMi8xMS93aGF0LWludmVzdG9ycy1uZWVkLXRvLWtub3ctYWJvdXQtY3J5cHRvLXN0YWtpbmcuaHRtbA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 11 Feb 2023 08:00:00 GMT", "title": "What investors need to know about ‘staking,’ the passive income opportunity at the center of crypto’s latest regulation scare - CNBC", "content": "Not six months ago, ether led a recovery in cryptocurrency prices ahead of a big tech upgrade that would make something called \"staking\" available to crypto investors.\n\nMost people have hardly wrapped their heads around the concept, but now, the price of ether is falling amid mounting fears that the Securities and Exchange Commission could crack down on it.\n\nOn Thursday, Kraken, one of the largest crypto exchanges in the world, closed its staking program in a $30 million settlement with the SEC, which said the company failed to register the offer and sale of its crypto staking-as-a-service program.\n\nThe night before, Coinbase CEO <PERSON> warned his Twitter followers that the securities regulator may want more broadly to end staking for U.S. retail customers.\n\n\"This should put everyone on notice in this marketplace,\" SEC Chair <PERSON> told CNBC's \"Squawk Box\" Friday morning. \"Whether you call it lend, earn, yield, whether you offer an annual percentage yield – that doesn't matter. If someone is taking [customer] tokens and transferring to their platform, the platform controls it.\"\n\nStaking has widely been seen as a catalyst for mainstream adoption of crypto and a big revenue opportunity for exchanges like Coinbase. A clampdown on staking, and staking services, could have damaging consequences not just for those exchanges, but also Ethereum and other proof-of-stake blockchain networks. To understand why, it helps to have a basic understanding of the activity in question.\n\nHere's what you need to know:\n\nWhat is staking?\n\nStaking is a way for investors to earn passive yield on their cryptocurrency holdings by locking tokens up on the network for a period of time. For example, if you decide you want to stake your ether holdings, you would do so on the Ethereum network. The bottom line is it allows investors to put their crypto to work if they're not planning to sell it anytime soon.\n\nHow does staking work?\n\nStaking is sometimes referred to as the crypto version of a high-interest savings account, but there's a major flaw in that comparison: crypto networks are decentralized, and banking institutions are not.\n\nEarning interest through staking is not the same thing as earning interest from a high annual percentage yield offered by a centralized platform like those that ran into trouble last year, like BlockFi and Celsius, or Gemini just last month. Those offerings really were more akin to a savings account: people would deposit their crypto with centralized entities that lent those funds out and promised rewards to the depositors in interest (of up to 20% in some cases). Rewards vary by network but generally, the more you stake, the more you earn.\n\nBy contrast, when you stake your crypto, you are contributing to the proof-of-stake system that keeps decentralized networks like Ethereum running and secure; you become a \"validator\" on the blockchain, meaning you verify and process the transactions as they come through, if chosen by the algorithm. The selection is semi-random – the more crypto you stake, the more likely you'll be chosen as a validator.\n\nThe lock-up of your funds serves as a sort of collateral that can be destroyed if you as a validator act dishonestly or insincerely.\n\nThis is true only for proof-of-stake networks like Ethereum, Solana, Polkadot and Cardano. A proof-of-work network like Bitcoin uses a different process to confirm transactions.\n\nStaking as a service\n\nIn most cases, investors won't be staking themselves – the process of validating network transactions is just impractical on both the retail and institutional levels.\n\nThat's where crypto service providers like Coinbase, and formerly Kraken, come in. Investors can give their crypto to the staking service and the service does the staking on the investors' behalf. When using a staking service, the lock-up period is determined by the networks (like Ethereum or Solana), and not the third party (like Coinbase or Kraken).\n\nIt's also where it gets a little murky with the SEC, which said Thursday that Kraken should have registered the offer and sale of the crypto asset staking-as-a-service program with the securities regulator.\n\nWhile the SEC hasn't given formal guidance on what crypto assets it deems securities, it generally sees a red flag if someone makes an investment with a reasonable expectation of profits that would be derived from the work or effort of others.\n\nCoinbase has about 15% of the market share of Ethereum assets, according to Oppenheimer. The industry's current retail staking participation rate is 13.7% and growing.\n\nProof-of-stake vs. proof-of-work\n\nStaking works only for proof-of-stake networks like Ethereum, Solana, Polkadot and Cardano. A proof-of-work network, like Bitcoin, uses a different process to confirm transactions.\n\nThe two are simply the protocols used to secure cryptocurrency networks.\n\nProof-of-work requires specialized computing equipment, like high-end graphics cards to validate transactions by solving highly complex math problems. Validators gets rewards for each transaction they confirm. This process requires a ton of energy to complete.\n\nEthereum's big migration to proof-of-stake from proof-of-work improved its energy efficiency almost 100%.\n\nRisks involved\n\nThe source of return in staking is different from traditional markets. There aren't humans on the other side promising returns, but rather the protocol itself paying investors to run the computational network.\n\nDespite how far crypto has come, it's still a young industry filled with technological risks, and potential bugs in the code is a big one. If the system doesn't work as expected, it's possible investors could lose some of their staked coins.\n\nVolatility is and has always been a somewhat attractive feature in crypto but it comes with risks, too. One of the biggest risks investors face in staking is simply a drop in the price. Sometimes a big decline can lead smaller projects to hike their rates to make a potential opportunity more attractive."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vd3d3LmNuYmMuY29tLzIwMjMvMDIvMTEvd2hhdC1pbnZlc3RvcnMtbmVlZC10by1rbm93LWFib3V0LWNyeXB0by1zdGFraW5nLmh0bWzSAVlodHRwczovL3d3dy5jbmJjLmNvbS9hbXAvMjAyMy8wMi8xMS93aGF0LWludmVzdG9ycy1uZWVkLXRvLWtub3ctYWJvdXQtY3J5cHRvLXN0YWtpbmcuaHRtbA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 11 Feb 2023 08:00:00 GMT", "title": "What investors need to know about ‘staking,’ the passive income opportunity at the center of crypto’s latest regulation scare - CNBC", "content": "Not six months ago, ether led a recovery in cryptocurrency prices ahead of a big tech upgrade that would make something called \"staking\" available to crypto investors.\n\nMost people have hardly wrapped their heads around the concept, but now, the price of ether is falling amid mounting fears that the Securities and Exchange Commission could crack down on it.\n\nOn Thursday, Kraken, one of the largest crypto exchanges in the world, closed its staking program in a $30 million settlement with the SEC, which said the company failed to register the offer and sale of its crypto staking-as-a-service program.\n\nThe night before, Coinbase CEO <PERSON> warned his Twitter followers that the securities regulator may want more broadly to end staking for U.S. retail customers.\n\n\"This should put everyone on notice in this marketplace,\" SEC Chair <PERSON> told CNBC's \"Squawk Box\" Friday morning. \"Whether you call it lend, earn, yield, whether you offer an annual percentage yield – that doesn't matter. If someone is taking [customer] tokens and transferring to their platform, the platform controls it.\"\n\nStaking has widely been seen as a catalyst for mainstream adoption of crypto and a big revenue opportunity for exchanges like Coinbase. A clampdown on staking, and staking services, could have damaging consequences not just for those exchanges, but also Ethereum and other proof-of-stake blockchain networks. To understand why, it helps to have a basic understanding of the activity in question.\n\nHere's what you need to know:\n\nWhat is staking?\n\nStaking is a way for investors to earn passive yield on their cryptocurrency holdings by locking tokens up on the network for a period of time. For example, if you decide you want to stake your ether holdings, you would do so on the Ethereum network. The bottom line is it allows investors to put their crypto to work if they're not planning to sell it anytime soon.\n\nHow does staking work?\n\nStaking is sometimes referred to as the crypto version of a high-interest savings account, but there's a major flaw in that comparison: crypto networks are decentralized, and banking institutions are not.\n\nEarning interest through staking is not the same thing as earning interest from a high annual percentage yield offered by a centralized platform like those that ran into trouble last year, like BlockFi and Celsius, or Gemini just last month. Those offerings really were more akin to a savings account: people would deposit their crypto with centralized entities that lent those funds out and promised rewards to the depositors in interest (of up to 20% in some cases). Rewards vary by network but generally, the more you stake, the more you earn.\n\nBy contrast, when you stake your crypto, you are contributing to the proof-of-stake system that keeps decentralized networks like Ethereum running and secure; you become a \"validator\" on the blockchain, meaning you verify and process the transactions as they come through, if chosen by the algorithm. The selection is semi-random – the more crypto you stake, the more likely you'll be chosen as a validator.\n\nThe lock-up of your funds serves as a sort of collateral that can be destroyed if you as a validator act dishonestly or insincerely.\n\nThis is true only for proof-of-stake networks like Ethereum, Solana, Polkadot and Cardano. A proof-of-work network like Bitcoin uses a different process to confirm transactions.\n\nStaking as a service\n\nIn most cases, investors won't be staking themselves – the process of validating network transactions is just impractical on both the retail and institutional levels.\n\nThat's where crypto service providers like Coinbase, and formerly Kraken, come in. Investors can give their crypto to the staking service and the service does the staking on the investors' behalf. When using a staking service, the lock-up period is determined by the networks (like Ethereum or Solana), and not the third party (like Coinbase or Kraken).\n\nIt's also where it gets a little murky with the SEC, which said Thursday that Kraken should have registered the offer and sale of the crypto asset staking-as-a-service program with the securities regulator.\n\nWhile the SEC hasn't given formal guidance on what crypto assets it deems securities, it generally sees a red flag if someone makes an investment with a reasonable expectation of profits that would be derived from the work or effort of others.\n\nCoinbase has about 15% of the market share of Ethereum assets, according to Oppenheimer. The industry's current retail staking participation rate is 13.7% and growing.\n\nProof-of-stake vs. proof-of-work\n\nStaking works only for proof-of-stake networks like Ethereum, Solana, Polkadot and Cardano. A proof-of-work network, like Bitcoin, uses a different process to confirm transactions.\n\nThe two are simply the protocols used to secure cryptocurrency networks.\n\nProof-of-work requires specialized computing equipment, like high-end graphics cards to validate transactions by solving highly complex math problems. Validators gets rewards for each transaction they confirm. This process requires a ton of energy to complete.\n\nEthereum's big migration to proof-of-stake from proof-of-work improved its energy efficiency almost 100%.\n\nRisks involved\n\nThe source of return in staking is different from traditional markets. There aren't humans on the other side promising returns, but rather the protocol itself paying investors to run the computational network.\n\nDespite how far crypto has come, it's still a young industry filled with technological risks, and potential bugs in the code is a big one. If the system doesn't work as expected, it's possible investors could lose some of their staked coins.\n\nVolatility is and has always been a somewhat attractive feature in crypto but it comes with risks, too. One of the biggest risks investors face in staking is simply a drop in the price. Sometimes a big decline can lead smaller projects to hike their rates to make a potential opportunity more attractive."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiMGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2V0aGVyZXVtLXdhbGxldHMtMjAyM9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 11 Feb 2023 08:00:00 GMT", "title": "The Best Ethereum Wallets 2023 – An Investor's Guide - Blockworks", "content": "The Ethereum wallet landscape is changing at breakneck speed as it has become more than a simple payment and storage device. There is an all out war to win what futurists see as the browser to Web3 and maybe even the metaverse.\n\nThis new ecosystem can be intimidating for someone who just learned the difference between a seed phrase and a private key. How do you compare Ethereum wallets when their scope of use keeps changing? It is important to know that your wallet of choice will give you the functionality you need to traverse worlds and decentralized applications with ease.\n\nWhat you need to know:\n\nEthereum wallets are now more than just a banking app without the bank. They have become a place to store digital art, community membership, voting privileges and even online gaming personas.\n\nEthereum wallets are different from wallets only used for non-smart contract blockchains like Bitcoin. They have a signing function that enables users to execute complex smart functions such as depositing liquidity on Uniswap.\n\nThe need to sign multiple transactions can make it difficult to use various wallets for more complex applications such as DEXes and games.\n\nWallets that try to offer a better user experience through browser extensions of mobile apps tend to have more security vulnerabilities.\n\nThis guide will walk you through the basics of evaluating and comparing the best Ethereum wallets on the market. But first, the highlights:\n\nTop Ethereum wallets:\n\nMost Compatible: MetaMask\n\nMost Secure: Ellipal\n\nMost Secure and Compatible: Ledger\n\nMost User-Friendly: Trust Wallet\n\nMost Versatile: Argent\n\nMost Affordable: Coinbase, Meta Mask and Trust Wallet\n\nWhat’s an Ethereum wallet?\n\nAn Ethereum wallet is a hardware device or software enabling users to interact with the Ethereum blockchain and its decentralized applications (dapps) ecosystem. An Ethereum wallet supports sending and receiving ETH and Ethereum-based tokens, including crypto tokens (ERC-20) and non-fungible tokens (NFTs).\n\nThere are three main functions of an Ethereum wallet, with one being more used than the others. These functions include:\n\nAn app: The primary use case for an Ethereum wallet is to help you manage your funds. It provides an interface to transfer assets and use the Ethereum network without much technical knowledge. Your Ethereum account: An Ethereum wallet holds access to a user’s unique addresses on the blockchain and provides an immutable account of their activities on the network. With an Ethereum account, users can build a social identity across different applications such as Twitter, Reddit, and Web3 applications. A login for Ethereum apps: The Ethereum ecosystem includes a variety of dapps built around finance, gaming, predictions, NFTs, and more. An Ethereum wallet provides login access to these applications and allows users to interact with them seamlessly.\n\nAn Ethereum wallet is one of the many types of cryptocurrency wallets. You can learn more about them in our in-depth guide on crypto wallets. Meanwhile, the next section analyzes what to consider when choosing an Ethereum wallet.\n\nWhat to look for in an Ethereum wallet?\n\nWhen choosing an Ethereum wallet, you need to first identify your priorities. Do you plan to use it for investing or storage or as your main interface for Ethereum-based applications? You may need multiple wallets for different purposes. Comparing these features will help you determine the best Ethereum wallet.\n\nSecurity and reputable history: Choose a wallet that integrates advanced security features and boasts a strong reputation within the crypto industry. The wallet seed phrase generation process, PIN options, and recovery process should be highly-secure for even less experienced users.\n\nIt is advisable to choose wallets with no record of a security breach. That way, users can rest assured that the company implements the best security practices and thus maintains a good reputation within the crypto industry.\n\nUser-friendliness : Investors find it helpful to look for wallets with an easy-to-navigate interface. Basic functionalities such as sending, receiving, and swapping crypto tokens should work seamlessly. Additional features such as NFT display, multiple accounts, portfolio tracking, and others should work in an intuitive manner.\n\n: Investors find it helpful to look for wallets with an easy-to-navigate interface. Basic functionalities such as sending, receiving, and swapping crypto tokens should work seamlessly. Additional features such as NFT display, multiple accounts, portfolio tracking, and others should work in an intuitive manner. Broad ecosystem support : If you intend to use the wallet to access Web3 applications such as decentralized finance (DeFi) protocols and NFT marketplaces, then it is advisable to choose a broadly supported wallet. This way, you can easily connect to these platforms without transferring funds to another compatible wallet.\n\n: If you intend to use the wallet to access Web3 applications such as decentralized finance (DeFi) protocols and NFT marketplaces, then it is advisable to choose a broadly supported wallet. This way, you can easily connect to these platforms without transferring funds to another compatible wallet. EVM compatibility : The Ethereum Virtual Machine (EVM) allows developers to build Ethereum-like applications across other blockchain networks. The best Ethereum wallets implement support for other EVM-compatible networks such as Polygon, BNBChain, Avalanche, Arbitrum, Optimism, Fantom, Gnosis, and many others. Thus, users can use a single wallet to access dapps built on these networks.\n\n: The Ethereum Virtual Machine (EVM) allows developers to build Ethereum-like applications across other blockchain networks. The best Ethereum wallets implement support for other EVM-compatible networks such as Polygon, BNBChain, Avalanche, Arbitrum, Optimism, Fantom, Gnosis, and many others. Thus, users can use a single wallet to access dapps built on these networks. Regular upgrades The blockchain industry evolves at neck-breaking speed. The best Ethereum wallet providers monitor the latest trends and adapt their solutions to meet the needs of contemporary users. Regular updates to fix security issues and improve user experience also count as one of the things to consider when choosing an Ethereum wallet.\n\nThe blockchain industry evolves at neck-breaking speed. The best Ethereum wallet providers monitor the latest trends and adapt their solutions to meet the needs of contemporary users. Regular updates to fix security issues and improve user experience also count as one of the things to consider when choosing an Ethereum wallet. Hot or cold wallet: A hot wallet is connected to the internet and intended for regular usage. On the other hand, a cold wallet is designated for the long-term storage of your ETH and other assets. Many investors combine hot and cold wallet setups to balance their portfolio risk.\n\n10 Best Ethereum Wallets for All Users\n\nType of wallet: Browser and mobile wallet\n\nBrowser and mobile wallet USP: Connect to dapps and Web3\n\nConnect to dapps and Web3 User-friendliness: Relatively easy\n\nPros Cons Free-to-use The wallet experience could be significantly improved MetaMask is widely supported across the dapp ecosystem MetaMask does not support new-age networks such as Solana, Cosmos, Polkadot, etc The wallet is EVM compatible (with support for BNBChain, Polygon, Avalanche, etc.) Being a hot-wallet solution makes users susceptible to hacks and other online attack vectors MetaMask offers a built-in swap feature and portfolio tracker\n\nReleased in September 2016 by Ethereum development studio Consensys, MetaMask is arguably the most popular Ethereum wallet. It boasts over 10 million active monthly users and has grown significantly since launching on mobile devices. Initially, the wallet was only available as a browser extension.\n\nThe MetaMask wallet is a self-custodial solution. Users create a new seed phrase upon installation or import an existing one. There is also the option to import wallets via a cryptographic private key. The MetaMask wallet features a relatively straightforward experience on both mobile and browsers. MetaMask users can send and receive tokens and connect with decentralized applications across Ethereum and almost all other EVM networks.\n\nType of wallet: Hardware wallet\n\nUSP: Advanced security\n\nUser-friendliness: Relatively easy\n\nPros Cons The Ledger Nano X provides secure offline storage for crypto assets Expensive for the average crypto investor The wallet is durable and portable The Ledger Live application might confuse users with more than one wallet — because the app sums up user balances, users must label wallets correctly The device supports over 5,000 crypto tokens, making it an option for investors with a diverse portfolio The device isn’t completely air gapped, so it is still vulnerable to certain security threats Directly exchange and swap cryptocurrencies through supported partners\n\nThe Ledger Nano X is a USB-shaped hardware device for storing cryptocurrencies. The device stores your cryptographic keys offline, eliminating the risk of losing funds to online attackers.\n\nUsers can connect to their mobile devices via Bluetooth to manage their portfolios using the Ledger Live application available on Android and iOS. A wired connection is required to use Ledger Nano X with a Windows, macOS or Linux device.\n\nEven though this wallet is a hardware device, it is not considered cold storage or air gapped. This makes it vulnerable to certain attacks that can exploit Bluetooth and USB connections. Air-gapped wallets use QR codes to sign transactions instead of direct connections. The process can make for a clunky user experience, but it is more secure.\n\nUpon installing Ledger Live, users can set up their Ethereum app to manage ether (ETH) and other ETH-based tokens. The Ledger Nano X wallet supports Ethereum staking and is among the best-supported options for decentralized applications.\n\nType of wallet: Hardware wallet\n\nUSP: Affordable security\n\nUser-friendliness: Simple\n\nPros Cons Secure offline storage Has fewer functionalities than the Nano Optimized to offer a seamless NFT and DeFi experience Not compatible with iOS devices It weighs less than the Nano X and is thus more portable A known issue of summing portfolio balances presents a challenge to users who manage more than one wallet with Ledger Live The device isn’t completely air gapped, so it is still vulnerable to certain security threats\n\nThe Ledger Nano S Plus is a pocket-friendly alternative to the Nano X device. For around 60% of the price of the older counterpart, the Nano S device also features secure offline private key storage. More importantly, the device enables users to manage ETH and ETH-based tokens and interact with dapps.\n\nThe Ledger Nano S Plus is a predecessor to the Ledger Nano S device. An immediate improvement to the newer hardware wallet is an increased focus on optimizing the wallet for NFT and DeFi transactions. Meanwhile, the Nano S Plus device is not Bluetooth-enabled, one of the core differences between the most expensive Nano X.\n\nType of wallet: Hardware wallet\n\nHardware wallet USP: Advanced offline security\n\nAdvanced offline security User friendliness: Simple\n\nPros Cons Trezor hardware wallets are user-friendly and offer seamless management with the Trezor Suite web and desktop apps Trezor hardware wallets do not offer native support for displaying and managing NFTs, although users can sign transactions Trezor supports the use of an advanced seed phrase for additional security Trezor devices support fewer coins and tokens compared to competitors Built-in exchange to buy, sell, and swap cryptocurrencies The device isn’t completely air gapped, so it is still vulnerable to certain security threats\n\nProduced by Satoshi Labs, Trezor hardware wallets are another popular physical storage device for managing ETH and ERC-20 tokens. Trezor offers two products, the Trezor Model T and Trezor One devices.\n\nThe Trezor Model T is the more expensive option, offering advanced security features such as a touchscreen display and support for more cryptocurrencies. The Trezor One device is more affordable, enables managing fewer cryptocurrencies, and is sold at approximately 30% of the price of the Model T.\n\nHowever, both wallets are easy to set up and rank highly on the list of best Ethereum wallets. Users can connect their Trezor device to MetaMask via a simple integration to enjoy top-notch DeFi experiences.\n\nType of wallet: Hardware, cold air-gapped wallet\n\nUSP: Most secure\n\nUser-friendliness: Complex\n\nPros Cons Most secure storage Cannot connect to smart contract applications User-friendly touch screen Sending crypto requires multiple steps on different devices Wide selection of coins and staking options Requires USB updates\n\nEllipal Titan Wallet is a cold storage wallet that utilizes air-gap technology. Unlike traditional cold storage wallets, the Ellipal Titan does not connect to the internet, Bluetooth or USB. Instead, transactions are conducted using QR codes, which are generated on the Ellipal app and scanned using the Titan’s camera.\n\nThis air gap technology ensures a higher level of security as it prevents the wallet from being hacked through internet connections. The Ellipal app allows users to receive coins but not to send coins, as the private keys are stored inside the Titan wallet and transactions must be signed and approved using the QR code on the wallet.\n\nThe Ellipal Titan is a highly secure wallet, but it does have the drawback of requiring manual updates using a mini-USB, as it does not connect to the internet. Unlike Trezor and Ledger, it cannot connect to Metamask to approve transactions and interact with Ethereum dapps. Overall, the Ellipal Titan is a unique and secure option for those seeking to store their cryptocurrencies in cold storage.\n\nType of wallet: Web and mobile wallet\n\nUSP: Open source\n\nUser-friendliness: Relatively easy\n\nPros Cons Open-source wallet with multi-device support A hot wallet solution, making users susceptible to online attacks Native dapp store and NFT management application Users report frequent crashes on MyEtherWallet mobile and instability while interacting with the Polygon network Built-in token cross-chain swap functionality powered by decentralized exchanges MyEtherWallet is developer friendly, including a smart contract interaction, gas unit conversion, and verification message add-ons\n\nLaunched in 2015, MyEtherWallet is the oldest Ethereum wallet, becoming the first open-source solution for users to create and manage assets on the network. The wallet has since evolved to provide new functionalities, including a web, mobile application, and browser extension (Enkrypt).\n\nIn addition to pioneering self-custodial ownership, MyEtherWallet is compatible with most EVM-compatible networks and tokens. Users also enjoy the luxury of a built-in dapp store that features the most popular protocols and an NFT manager app.\n\nLike MetaMask, users can connect MyEtherWallet with many hardware wallets, including Ledger, Trezor and Keep Key. This integration provides another gateway for accessing dapps with funds stored in a secure offline location.\n\nType of wallet: Mobile and browser extension\n\nMobile and browser extension USP: Multichain\n\nMultichain User-friendliness: Extremely easy\n\nPros Cons Trust Wallet users can directly stake ETH and several Ethereum-based tokens Trust Wallet popularity makes users susceptible to phishing attacks Trust Wallet includes an option to track charts and prices Prone to mobile wallet malware attacks Users can swap assets across different networks using the built-in multichain swap widget\n\nTrust Wallet is one of the best Ethereum wallets and boasts remarkable adoption with over 25 million users. The self-custodial wallet launched in 2017 and is notably backed by popular cryptocurrency exchange Binance.\n\nTrust Wallet enables storing ETH and managing ERC-20 tokens. Users can simply toggle support for several assets and manage Ethereum gas fee settings based on network usage.\n\nThe Trust Wallet application also includes an NFT display feature and a native dapp store for accessing protocols across Ethereum and other EVM-compatible networks. Trust Wallet launched its browser extension in 2022 to broaden its user appeal and ward off competition.\n\nType of wallet: Desktop, web, and mobile wallet\n\nDesktop, web, and mobile wallet USP: Beginner friendly and multi-signature features\n\nBeginner friendly and multi-signature features User-friendliness: Relatively easy\n\nPros Cons Multi-signature features available for ether and bitcoin At the time of writing, users pay a relatively high fee of 5.7% when using the in-app purchase option to buy cryptocurrencies Guarda provides advanced security options, including biometrics unlock and Face ID The wallet is not purpose-built for accessing dapps and NFTs The wallet supports native Ethereum staking Guarda Wallet is developer-friendly with several add-ons for token creation\n\nLaunched in 2017, Guarda is another top wallet for Ethereum users. Guarda Wallet supports over 50 blockchain networks and 400,000 tokens, making it an excellent option for investors with a diversified portfolio.\n\nThe Guarda wallet is available on multiple devices and offers a user-friendly interface for managing assets. Guarda provides developer-friendly add-ons such as a token generator, mnemonic converter, Etherscan integration, and smart contract deployer.\n\nOther prominent features include a built-in multichain swap widget, fiat purchases, and a Visa debit card for users to spend cryptoassets. Unlike a handful of self-custodial wallets, Guarda also prides itself in providing prompt customer service through LiveChat and email.\n\nType of wallet: Smart contract mobile wallet\n\nSmart contract mobile wallet USP: Ethereum layer-2 wallet\n\nEthereum layer-2 wallet User-friendliness: Relatively easy\n\nPros Cons Cheap and fast transactions via Ethereum layer-2 Argent Wallet is only available on mobile Argent offers a user-friendly interface ideal for onboarding new investors to Ethereum Only supports tokens built on the Ethereum blockchain Native Ethereum-staking solution and DeFi integration No seed phrase recovery required\n\nArgent is a smart contract wallet specially designed for accessing the Ethereum layer-2 network zkSync. It replaces the traditional Externally Owned Account (EOA) model of Ethereum wallets with a smart contract model.\n\nThis enables account abstraction — where the user can interact with the Ethereum ecosystem with more functionality. For example, instead of using a seed phrase recovery system, you can set up a multi-signature solution that utilizes social recovery. It also enables transaction bundling so that engaging with smart contracts does require multiple transaction approvals.\n\nArgent offers direct Ethereum staking and the option for users to earn interest across DeFi protocols on the layer-2 solution. There is also an inbuilt swap widget and a novel recovery process for users to retrieve lost wallets without a paper backup. The only trade-off is that the wallet is focused on optimizing user experience and thus does not support the layer-1 Ethereum network.\n\nRead more: The Seed Phrase Debate: Do Wallets Really Need Them?\n\nType of wallet: Desktop, mobile, and browser wallet\n\nDesktop, mobile, and browser wallet USP: Multichain and beginner friendly\n\nMultichain and beginner friendly User-friendliness: Relatively easy\n\nPros Cons Exodus wallet offers full multichain functionalities, enabling access to Ethereum, EVM-compatible networks, and new-age networks such as Solana Supports fewer cryptocurrencies than some competitors Multi-device support, including desktop and mobile synchronization Prone to mobile wallet malware attacks Native support for Ethereum staking and earning interest via DeFi protocols Exodus provides customer support through LiveChat and email\n\nLaunched in 2015, Exodus is one of the oldest cryptocurrency wallets and among the best Ethereum wallets. Exodus offers a user-friendly experience and native integrations with the Ethereum dapp ecosystem. One of these integrations is an app store that allows users to access DeFi protocols such as Compound Finance and Aave with a single click.\n\nExodus is accessible across multiple devices with Android, iOS, desktop, and browser options available to users. The wallet features an intuitive portfolio tracker and support for the Trezor Model T hardware wallet. Users can also seamlessly buy and sell crypto using Exodus mobile’s numerous fiat on-ramp solutions.\n\nType of wallet: Browser and mobile wallet\n\nBrowser and mobile wallet USP: Retail friendly\n\nRetail friendly User-friendliness: Straightforward\n\nPros Cons The Coinbase wallet is more user-friendly Integrates few EVM-compatible networks compared to competitors The wallet integrates native support for Ethereum and Polygon NFTs Close ties with the Coinbase exchange mean limited privacy options for connected users Users can store ETH, all Ethereum-based tokens, and several other cryptocurrencies Prone to mobile wallet malware attacks Coinbase Wallet lets users buy and sell cryptocurrencies with fiat\n\nDeveloped by the famous cryptocurrency exchange company Coinbase, the Coinbase Wallet is a self-custodial solution for Ethereum investors. Users can browse decentralized applications from the built-in dapp store and manage NFTs from the Ethereum and Polygon networks.\n\nThe wallet initially only existed as a mobile app for Android and iOS devices. However, the company released the Coinbase Wallet browser extension in 2021, expanding its grip on market share. Coinbase’s wallet is synonymous with a seamless user experience powered by a minimalistic design that even new investors can navigate.\n\nAmong other features, the Coinbase Wallet allows users to set up usernames. Thus, users can execute transactions without using hard-to-read cryptocurrency addresses. The wallet also supports Ethereum staking and the option to earn interest on crypto using DeFi protocols.\n\nEthereum wallet FAQs\n\nWhich wallet is best for Ethereum?\n\nThe best wallet for Ethereum depends on a user’s needs. For instance, MetaMask is considered the best Ethereum wallet by many to easily interact with dapps across the DeFi and Web3 gaming ecosystem. However, a hardware wallet provides the best security for the long-term storage of ETH and Ethereum-based assets. Mobile wallets are ideal for day-to-day transactions involving smaller amounts, while connecting your hardware wallet to browser extensions for transaction signing provides greater security while using dapps.\n\nWhich wallet is best for Bitcoin and Ethereum?\n\nMost Ethereum-focused wallets, such as MetaMask and MyEtherWallet, do not support Bitcoin. Therefore, the best choice for Bitcoin and Ethereum is any of the hardware wallet devices listed in this article. Other options, including Trust Wallet, Guarda Wallet, and Coinbase Wallet, enable storage and transfers for ETH and BTC.\n\nWhich is the safest Ethereum wallet?\n\nThe safest Ethereum wallet is a hardware solution such as Ledger Nano X or Trezor Model T device. These devices store a user’s private key offline and provide advanced security options such as extra passphrases and customized backup solutions. Users can further ensure safety by setting up a cold-storage wallet, one not used for regular transactions.\n\nWhich is the most popular crypto wallet for Ethereum?\n\nWith more than 30 million users, MetaMask is the most popular crypto wallet for Ethereum. It is the most supported wallet on dapps and boasts significant usage within and outside the Ethereum ecosystem. Other popular options include MyEtherWallet and Coinbase Wallet.\n\nWhat is the crypto wallet with the lowest fees?\n\nMost crypto wallets allow users to set the lowest transaction fees based on network usage. Because users pay transaction fees to validators/miners on the underlying networks, crypto wallets technically do not offer low fees.\n\nChoosing rightly to secure your Ethereum assets\n\nThe Ethereum ecosystem has significantly evolved from its early days. Nowadays, a user can choose the best Ethereum wallet for them based on their portfolio size, dapp usage, and other specific needs. Combining one or more wallet types may help users achieve the best security practices and benefit from the numerous opportunities available on Ethereum and the broader cryptocurrency industry.\n\nDon’t miss the next big story – join our free daily newsletter."}]