[{"id": 11, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9jb25zZW5zdXMtbWFnYXppbmUvMjAyMy8xMC8wNC9hLXNwb3QtZXRoLWV0Zi1yZXByZXNlbnRzLXRoZS1kYXduLW9mLWluc3RpdHV0aW9uYWwtbGlxdWlkLXN0YWtpbmcv0gF-aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2NvbnNlbnN1cy1tYWdhemluZS8yMDIzLzEwLzA0L2Etc3BvdC1ldGgtZXRmLXJlcHJlc2VudHMtdGhlLWRhd24tb2YtaW5zdGl0dXRpb25hbC1saXF1aWQtc3Rha2luZy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 04 Oct 2023 07:00:00 GMT", "title": "A Spot ETH ETF Represents the Dawn of Institutional Liquid Staking - CoinDesk", "content": "Why is there excitement over an ETH ETF? The answer lies in the resounding demand from investors seeking to integrate digital assets into their portfolios. While investors are hungry for these opportunities, current offerings fall short. U.S. investors eyeing a spot bitcoin ETF have to look beyond American shores."}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vY3J5cHRvLm5ld3MvZXRoZXJldW0tZnV0dXJlcy1ldGZzLW9uLWNtZS1iZWdpbi13aXRoLWxpZ2h0LXRyYWRpbmctdm9sdW1lcy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 04 Oct 2023 07:00:00 GMT", "title": "Ethereum futures ETFs on CME begin with light trading volumes - crypto.news", "content": "In a stark contrast of trading volumes, the debut of Ethereum futures ETFs on CME witnessed a modest start, with trading activity in the first 15 minutes hovering below $2 million. This pales in comparison to their Bitcoin-based counterparts, which boasted volumes a hundred times greater.\n\nBloomberg analyst <PERSON> suggests that these numbers are common for new ETFs but relatively low for Ethereum (ETH), given its market size.\n\nAccording to <PERSON><PERSON><PERSON><PERSON>, the top performer was Valkyrie’s ETF, based on a combination of the first and second-largest cryptocurrencies by market capitalization, with a trading volume of $787,000.\n\nVanEck’s Ethereum futures-only ETF had a modest volume of just over $300,000.\n\nPretty meh volume for the Ether Futures ETFs as a group, a little under $2m, about normal for a new ETF but vs $BITO (which did $200m in first 15min) it is low. Tight race bt VanEck and ProShares in the single eth lane. pic.twitter.com/F9AHtrVcVf — <PERSON> (@EricBalchunas) October 2, 2023\n\nIn total, the turnover for six exchange-traded funds based only on Ethereum futures at the end of the day reached to $1.92 million, including $879,000 for the ProShares Ether Strategy ETF.\n\nThe six new funds by ProShares, VanEck and Bitwise Asset Management allow investors to take positions in exchange-traded products based on ether for the first time. The six funds saw trading volumes total only $1.92 million in their first day of trading, according to data from… https://t.co/ASebNWJHO6 — Wu Blockchain (@WuBlockchain) October 3, 2023\n\nBloomberg pointed out that compared to two years ago when Bitcoin ETF trading began, the current bear market has made the competition tougher.\n\nRoxanne Islam, deputy research director at VettaFi, suggests that the subdued interest in Ethereum-based futures ETFs is because investors are anticipating spot versions, primarily Bitcoin (BTC), possibly in early 2024.\n\nHowever, the US Securities and Exchange Commission (SEC) has been postponing the consideration of applications for a spot Bitcoin ETF.\n\nIn early September, the regulator extended its evaluation timeline for several spot Bitcoin ETF applications. It has also postponed its decision on ARK 21Shares’ Bitcoin ETF application to next year.\n\nFollow Us on Google News"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiO2h0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvZXRoZXJldW0tY2FuY3VuLXVwZ3JhZGUtZXhwbGFpbmVk0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 04 Oct 2023 07:00:00 GMT", "title": "Ethereum Cancun Upgrade Explained - Watcher Guru", "content": "The Ethereum network has made the switch from proof-of-work mining to proof-of-stake mining. The transition was completed successfully on September 15, 2022. Additionally, the blockchain went through some major changes as validators replaced miners.\n\nRecently, the much-awaited Shapella upgrade went live, allowing these speakers to withdraw their staked Ethereum 2.0. Additionally, with numerous developments on the Ethereum blockchain, let us learn about ETH a bit more.\n\nAlso read: How to Get a New Bitcoin Address on Cash App?\n\nEthereum in a nutshell\n\nVitalik Buterin built the decentralized blockchain platform Ethereum in 2015, along with the Ethereum Foundation, to support the development and execution of smart contracts and decentralized applications (dApps). Additionally, it has developed to become one of the biggest and most popular cryptocurrencies in the world. Ethereum is distinguished from Bitcoin by its programmability.\n\nThe blockchain has grown to accommodate a wide range of applications. Developers on the ETH network can design decentralized applications by using smart contracts, which are self-executing contracts with predefined rules. Additionally, over time, ETH has grown to be the second-largest cryptocurrency in the world.\n\nAlso read: How to Add MidJourney to my Discord Server?\n\nSource: CNN\n\nEthereum Cancun upgrade explained\n\nThe upcoming Cancun upgrade for the Ethereum blockchain represents a major milestone focused on boosting scalability and security.\n\nAlso dubbed the “Dencun” upgrade, Cancun will introduce 5 Ethereum Improvement Proposals (EIPs) designed to increase network throughput and reduce transaction costs.\n\nAt its core, Cancun looks to improve the network’s transactions per second (TPS) to support more usage while retaining decentralization. This aligns with the “Surge” phase of Ethereum’s phased roadmap.\n\nKey changes include EIP-4844 which enhances data availability across Ethereum shards to scale the network. EIP-1153 aims to lower data storage costs to optimize block space usage.\n\nAdditionally, EIP-4788 exposes Beacon chain data to execution layers to strengthen bridges, while EIP-5656 adds a new memory copying instruction to enable more efficient data structures.\n\nAlso read: Will ETH Go Up?\n\nRounding out the upgrades, EIP-6780 alters the SELFDESTRUCT function to limit potential smart contract disruptions.\n\nIn total, the Cancun EIPs constitute foundational improvements to Ethereum’s technical capabilities and security primitives. Researchers tentatively target full implementation in October 2023.\n\nBy boosting throughput and reducing the viability of attacks, the upgrade promises to make the network an even more attractive ecosystem for developers and users over the long term."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1wbHVuZ2VzLXRvLTE2MDAv0gFIaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vYW5hbHlzaXMvZXRoL2V0aGVyZXVtLXByaWNlLXBsdW5nZXMtdG8tMTYwMC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 04 Oct 2023 07:00:00 GMT", "title": "Ethereum Price Plunges to $1,600: Can Bulls Save the Day? - NewsBTC", "content": "Ethereum price trimmed all gains and revisited $1,580 against the US dollar. ETH could continue to move down if it settles below the $1,600 support.\n\nEthereum started a fresh decline below the $1,650 support.\n\nThe price is trading below $1,665 and the 100-hourly Simple Moving Average.\n\nThere is a key bearish trend line forming with resistance near $1,645 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could start a fresh increase if it clears the $1,650 and $1,665 resistance levels.\n\nEthereum Price Revisits Support\n\nEthereum’s price failed to stay above the $1,665 level and moved into a bearish zone. ETH extended its decline below the $1,650 level, like Bitcoin.\n\nThe decline was such that the price even spiked below the $1,600 on Kraken. The price retested the key support at $1,585. A low was formed near $1,585 and the price is now attempting a fresh increase. There was a move above the $1,620 level.\n\nEther climbed above the 23.6% Fib retracement level of the downward move from the $1,743 swing high to the $1,585 low. Ethereum is now trading below $1,665 and the 100-hourly Simple Moving Average.\n\nOn the upside, the price might face resistance near the $1,650 level. There is also a key bearish trend line forming with resistance near $1,645 on the hourly chart of ETH/USD. The next major resistance is $1,665 and the 100-hourly Simple Moving Average.\n\nSource: ETHUSD on TradingView.com\n\nThe 50% Fib retracement level of the downward move from the $1,743 swing high to the $1,585 low is also near the $1,665 level. A clear move above the $1,665 resistance zone could set the pace for a fresh increase. In the stated case, the price could visit the $1,700 resistance. The next key resistance might be $1,750. Any more gains might open the doors for a move toward $1,820.\n\nMore Losses in ETH?\n\nIf Ethereum fails to clear the $1,665 resistance, it could continue to move down. Initial support on the downside is near the $1,620 level. The next key support is $1,600.\n\nA downside break below the $1,600 support might start another strong bearish wave. In the stated case, the price could even trade below the $1,585 level. In the stated case, Ether could visit the $1,540 level.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is losing momentum in the bearish zone.\n\nHourly RSI – The RSI for ETH/USD is now below the 50 level.\n\nMajor Support Level – $1,600\n\nMajor Resistance Level – $1,665\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3ZpdGFsaWstY29uc2lkZXJzLWltcGxpY2F0aW9ucy1vZi1hZGRpbmctemstZXZtLW90aGVyLWZlYXR1cmVzLXRvLWV0aGVyZXVtLW1haW5uZXQv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 04 Oct 2023 07:00:00 GMT", "title": "<PERSON><PERSON> considers implications of adding ZK-EVM, other features to Ethereum mainnet - CryptoSlate", "content": "Ethereum founder <PERSON><PERSON> recently wrote an in-depth blog post exploring the question of which features should become official parts of the Ethereum protocol versus being built on top of it. This has been an ongoing debate as the network evolves.\n\nIn the early days, <PERSON><PERSON><PERSON> explains, Ethereum strove to keep its base layer as simple and minimalist as possible. This aligned with the Unix philosophy of creating uncomplicated, flexible software. The goal was for Ethereum to provide a solid foundation for decentralized applications, with most functionality implemented through smart contracts built on top.\n\nOver time, however, some have questioned whether more features should be directly enshrined in the core protocol. But what does “enshrining” mean? <PERSON><PERSON><PERSON> defines it as making something intrinsic to the official Ethereum specification that client developers must implement. The alternative, “de-enshrining,” means removing a feature from the base layer and pushing it out to be handled by smart contracts instead.\n\nPros and Cons of Enshrining Features\n\n<PERSON><PERSON>n analyzes the pros and cons of enshrining several potential features. Enshrining can provide efficiency gains, more robust security, and censorship resistance. But it also risks making transactions more expensive, over-complicating governance, and reducing flexibility to meet unanticipated user needs down the road.\n\n<PERSON><PERSON><PERSON> uses account abstraction as a case study to analyze this debate. Earlier proposals like EIP-86 tried to make transactions just simple VM calls, minimizing protocol complexity but increasing miner responsibilities. More recent proposals like ERC-4337 still start outside the protocol but may later enshrine components for efficiency and security.\n\n<PERSON><PERSON><PERSON> explores enshrining several other potential features:\n\nZK-EVMs: Could improve efficiency and allow leveraging Ethereum’s governance to manage bugs, but challenges around supporting diverse ZK technologies remain.\n\nProposer-builder separation: Could reduce trust assumptions, but extra-protocol approaches already exist.\n\nPrivate mempools: No current encryption technology seems robust enough to enshrine, but valuable to build at the application layer.\n\nLiquid staking: Could reduce centralization risks and open more staking options, but challenges around governance remain.\n\nMore precompiles: This could improve efficiency, but risks over-complicating the protocol and low usage of past precompiles.\n\nEnshrining features can provide efficiency, security, and censorship resistance. But it can also over-extend the protocol’s governance and make it too rigid for unanticipated user needs.\n\nHow the community may be fractured on enshrining.\n\nWithin the Ethereum community, differing perspectives emerge on this question. Pragmatists may prioritize enshrining features that offer clear benefits to users today, even if complex to govern. In contrast, purists argue that radically minimizing the base layer preserves Ethereum’s vision as a decentralized application platform.\n\nBusinesses and institutions want features that support their use cases quickly enshrined, while decentralization advocates worry that risks unaccountable control by privileged groups. Developers desire expanded base layer functionality to ease app building, but security researchers warn enshrinement may lock in suboptimal technical choices.\n\nAs Buterin thoughtfully lays out, navigating these tradeoffs will only grow more complex as expectations of Ethereum diversify and scale. However, discussing core principles helps anchor the conversation as progress compels reassessment. The full blog post “Should Ethereum be okay with enshrining more things in the protocol?” is well worth the read.\n\nUltimately, Ethereum’s open “soft forking” process allows continued evolution based on emerging community priorities. Buterin’s post thus provides a valuable framework to weigh options and build alignment as Ethereum marches toward its ambitious vision."}]