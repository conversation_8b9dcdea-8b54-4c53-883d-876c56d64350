[{"id": 1, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9jb25zZW5zdXMtbWFnYXppbmUvMjAyMy8wNy8yOC93aHktZXRoZXJldW0tZGl0Y2hpbmctbWluaW5nLWlzbnQtYmV0dGVyLWZvci10aGUtZW52aXJvbm1lbnQv0gF4aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2NvbnNlbnN1cy1tYWdhemluZS8yMDIzLzA3LzI4L3doeS1ldGhlcmV1bS1kaXRjaGluZy1taW5pbmctaXNudC1iZXR0ZXItZm9yLXRoZS1lbnZpcm9ubWVudC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "Why Ethereum Ditching Mining Isn't as Green as Advertised - CoinDesk", "content": "The second, also alluded to above, is MEV, which represents money captured from unsophisticated, everyday network users. When the network spends more on security, miners consolidate, MEV opportunities diminish and everyday users pay more in the form of inflation. By contrast, with lower spending under PoS, everyday users pay instead in the form of hidden transaction fees due to MEV. Over $1 billion worth of MEV is estimated to have been extracted on the Ethereum network alone over the past two to three years, to say nothing of the various side chains and layer 2s, and cross-domain MEV between and among them."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vY29pbmdhcGUuY29tL2V0aGVyZXVtLXByaWNlLXJhbGx5LWF3YWl0cy10aGlzLWJ1bGxpc2gtcGF0dGVybi1jb25maXJtYXRpb24v0gFXaHR0cHM6Ly9jb2luZ2FwZS5jb20vZXRoZXJldW0tcHJpY2UtcmFsbHktYXdhaXRzLXRoaXMtYnVsbGlzaC1wYXR0ZXJuLWNvbmZpcm1hdGlvbi9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "Ethereum Price Rally Awaits This Bullish Pattern Confirmation - CoinGape", "content": "Ethereum price has steadied above $1,800 for nearly two months and traded at $1,864 on Friday. On the upside, price action has been capped under $2,000, amid a general slump in the trading volume.\n\nFollowing the Ripple ruling in early July, investors focused less on Bitcoin (BTC) and Ethereum, favoring other leading altcoins like XRP, Cardano (ADA), Polygon (MATIC), Solana (SOL), and most recently Dogecoin (DOGE).\n\nadvertisement\n\nThe biggest task among Ethereum is holding the price above $1,800 – a move likely to prevent possible declines to $1,700 and $1,600.\n\nIf a recovery ensues from the current market value, the token powering the largest smart contracts token might trigger a surge in investor interest, who are still waiting on the sideline for an uptrend confirmation above $2,000.\n\nHere’s What It Will Take Ethereum to Rally?\n\nEthereum price is in the middle of nurturing a potentially massive breakout that could propel it not only above $2,000 but allow bulls to close the gap to $2,400 for the first time since May 2022.\n\nThe daily chart shows the formation of an inverse head-and-shoulders (H&S) pattern, promising a 19% bullish move to $2,385.\n\nThe inverse H&S pattern shows a downtrend ending and an uptrend starting. It has three lows: the middle one is the lowest (head) and the outer ones are higher (shoulders).\n\nThis pattern is validated when the price breaks above a line joining the highs of the shoulders (neckline), located around $2,000 for Ethereum price.\n\nTraders are always advised to buy when this happens or wait for a pullback to the neckline to be certain that the breakout is sustainable and not a bull trap. Note that the price target is the distance from the head to the neckline (19%) added to the breakout point.\n\nOther indicators must be consulted when trading the H&S, especially the Money Flow Index (MFI), which monitors the inflow and outflow of funds in Ethereum markets. That said, the MFI as presented on the daily chart, has a bullish outlook, which implies that a breakout is in the offing.\n\nEvaluating the Bullish Case in Ethereum Price\n\nAdding credence to the bullish outlook on the daily chart is the Moving Average Convergence Divergence (MACD) indicator – likely to send a buy signal over the weekend.\n\nTraders seeking fresh exposure to ETH long positions should be on the lookout for bullish crosses in the momentum indicator, marked by the MACD line in blue crossing below the signal line in red.\n\nTo be on the safe side, Ethereum price must break and hold above the immediate hurdle, as highlighted by the 50-day Moving Average (EMA) at $1,870. Profit booking can start at $2,000 but extremely bullish traders may want to hold on for the H&S pattern breakout to $2,385.\n\nRelated Articles"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vZGVjcnlwdC5jby8xNTA0OTUvc2hpYi1kZXZzLXNoaWJhcml1bS1ldGhlcmV1bS1icmlkZ2XSAUNodHRwczovL2RlY3J5cHQuY28vMTUwNDk1L3NoaWItZGV2cy1zaGliYXJpdW0tZXRoZXJldW0tYnJpZGdlP2FtcD0x?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "SHIB Devs Begin Testing Shibarium to Ethereum Bridge - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nShibarium, an upcoming SHIB-based, layer-2 network that's being built on top of the Ethereum blockchain, now has a testnet bridge that allows tokens to be transferred between the two networks. But for now, at least, only dummy assets are being supported.\n\nPseudonymous \"marketing specialist\" <PERSON><PERSON>, who represents the Shiba Inu ecosystem, tweeted today that the public testing phase will allow enthusiasts to \"be among the first to try out this revolutionary cross-chain solution.\"\n\n🔥 Shibarium Beta Bridge has gone live for public testing! 🎉 Be among the first to try out this revolutionary cross-chain solution. Remember, always exercise caution and verify authenticity before connecting your wallet to any website. PLEASE DO NOT USE YOUR REAL ASSETS!… pic.twitter.com/7yfhX8gQc6 — 𝐋𝐔𝐂𝐈𝐄 (@LucieSHIB) July 26, 2023\n\nAD\n\nAD\n\nShiba Inu (SHIB), the 15th-biggest crypto asset by market cap, is a meme coin based on the same internet meme as <PERSON>ecoi<PERSON>. And the launch of the Shibarium-to-Ethereum testnet bridge appears to giving doge-themed tokens a boost.\n\nSHIB, Dogecoin, and even ShibaSwap (BONE) have seen big gains.\n\nData from CoinGecko shows BONE has rallied by 9% over the past 24 hours. Meanwhile, SHIB, with a market cap of nearly $5 billion, has surged by nearly 6% over the same timeframe—making it the fifth-best performer among the 100 biggest cryptocurrencies by market capitalization.\n\nDespite the enthusiasm, bridges have become a common attack vector for cybercriminals. Perhaps the worst example relates to the $622 million hack of the Ronin Network in 2022, an Ethereum sidechain used by play-to-earn game Axie Infinity. North Korean hackers were linked to that exploit, along with several others.\n\nNonetheless, this could help the Shiba Inu ecosystem unlock far greater utility—and help the project move away from being castigated as a \"meme coin\" where price rises are driven by speculation and greed rather than anything tangible.\n\nAD\n\nAD\n\nBut even if Shibarium takes off, affiliated tokens are unlikely to match the heady days of the last bull market. Both BONE and SHIB remain 90% below all-time highs set in 2021, per CoinGecko.\n\nThere's been renewed interest in dog-themed tokens of late, with Dogecoin also performing fairly well at certain points this week.\n\nThat likely has something to do with the never-ending speculation that Elon Musk may make the altcoin an official payment method on X, formerly known as Twitter. The billionaire began pumping the coin on the social network back in 2020, and had an instrumental role in the 14,700% surge that Dogecoin enjoyed in the first four months of 2021.\n\nAfter unsuccessful campaigns to drive DOGE to one dollar and SHIB to one cent, potential use as widespread payment methods could herald the start of a new chapter—and mean there's life in the old dogs yet."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3MvZXRoZXJldW0vZXRoZXJldW0tcHJpY2Utc2t5cm9ja2V0LWFpLWRhby1yZXZvbHV0aW9uL9IBVWh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3MvZXRoZXJldW0vZXRoZXJldW0tcHJpY2Utc2t5cm9ja2V0LWFpLWRhby1yZXZvbHV0aW9uL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "Ethereum Price Will Skyrocket Due To AI-Driven DAO Revolution: <PERSON> - NewsBTC", "content": "In a thought-provoking blog post titled “<PERSON><PERSON>,<PERSON> <PERSON>, co-founder of the renowned crypto exchange BitMEX, delves into the potential impact of Artificial Intelligence (AI) on the future of economic organization and the role of Ethereum. <PERSON> argues that the rise of AI-driven Decentralized Autonomous Organizations (DAOs) will revolutionize the global economy and propel Ethereum to new heights.\n\nThe Critical Role Of DAOs In The AI Era\n\nHayes contends that the current economic progress and per capita wealth of global civilization can be attributed to the efficient self-organization of human societies. He emphasizes that traditional company structures, empowered and regulated by the state, have been the primary vehicles for economic development. However, he highlights the limitations of these structures when it comes to AI-driven entities.\n\nHe states, “An AI has no reason to follow any laws. It cannot be coerced by the state, and therefore, exchanges that trade tokens issued by AI-powered DAOs will likely become natural monopolies.<PERSON>\n\n<PERSON> lays out a compelling argument for why DAOs, relying on smart contracts executed on public blockchains like Ethereum, are the ideal organizational structure for AI-driven entities. These smart contracts provide transparency, immutability, and cryptographic verification of transactions and agreements. Consequently, DAOs can operate efficiently and securely without the need for third-party intermediaries or costly auditing processes.\n\nHe envisions a future where AI-powered DAOs will raise capital and trade their tokens on decentralized exchanges (DEXs) on Ethereum rather than traditional centralized exchanges. This will create truly global capital markets accessible to anyone with an internet connection. <PERSON> predicts that DEXs will become natural monopolies due to the advantages they offer in terms of trust, security, and ease of use.\n\nThe BitMEX founder presents a hypothetical example of “PoetAI,” an AI-powered DAO that aims to fundraise and produce original poetry for profit. He envisions how PoetAI could issue its tokens, called “POET,” through a smart contract with specific attributes, such as revenue sharing and voting mechanisms. Investors can confidently invest in PoetAI DAO knowing that its financial statements are continuously available on the public blockchain, eliminating the need for traditional auditors.\n\nMoreover, Hayes explains that DAOs can raise capital by issuing debt, enabling economic time travel by borrowing from the future to stimulate present economic activity. The enforceability of contracts in DAOs can be facilitated through smart contracts on public blockchains, ensuring that investors are protected.\n\nHayes’ Bull Case For Ethereum\n\nHayes concludes, “Ethereum transactions will grow exponentially as DAOs proliferate. As a result, the price of ETH should skyrocket in anticipation if this AI DAO hypothesis is widely believed.”\n\nHe also suggests that identifying and investing in Ethereum based governance tokens of DEXs facilitating AI-driven DAO trading will lead to significant profits. Furthermore, Ethereum middleware layers that enable visualization of AI DAO accounts will become essential for the smooth functioning of these capital markets.\n\nWhile these ideas represent bold predictions about the future of AI and the role of Ethereum, Hayes presents a compelling case for the potential disruptive power of AI-driven DAOs. Hayes is one of the great thinkers of crypto space and his thesis a narrative to watch.\n\nAt press time, the Ether (ETH) price was at $1,863, just below the mid-range resistance.\n\nFeatured image from rc.xyz NFT gallery / Unsplash, chart from TradingView.com\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiQmh0dHBzOi8vY3J5cHRvLm5ld3MvbGVhcm4vZXRoZXJldW1zLWFjY291bnQtYWJzdHJhY3Rpb24tZXhwbGFpbmVkL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "Ethereum's account abstraction explained - crypto.news", "content": "Ethereum’s (ETH) account abstraction uses smart contract wallets to manage users’ account activity and blockchain transactions. It hides the complex processes of blockchain interaction and could finally make web3 apps easier for users.\n\nBlockchain technology has long promised a decentralized future, but its notoriously complex inner workings have severely hampered widespread mainstream adoption. Now, Ethereum developers are leveraging a feature called “account abstraction” to completely simplify and streamline blockchain interactions for everyday users. But how does this new abstraction model actually work under the hood?\n\nYou might also like: What Is Ether (ETH)?\n\nEOAs vs. smart contract wallets\n\nTo understand what account abstraction is, we first need to examine the two primary account types that exist on Ethereum today:\n\nExternally owned accounts (EOAs) consist of public-private key pairs that users must securely manage themselves. The public key is the account address you share, while the private key proves ownership and allows you to sign your transactions.\n\nTo perform any action on the blockchain, users need to install browser extensions like MetaMask, copy long cryptographic addresses, approve every transaction prompt, and manually sign each transaction with their private key. This cumbersome process can frustrate new users. And if you ever lose your private key, you lose access to your account and funds forever with no recovery options.\n\nYou might also like: <PERSON>lik Buterin shares insights on Ethereum’s account abstraction journey\n\nSmart contracts are advanced programmable accounts where developers can program customized logic, rules, and capabilities right into the contract code itself. For example, a smart contract wallet could enable social recovery features, daily transaction limits, access controls, account freezing, batch sending, and many other capabilities not possible with a basic private key-based account. Smart contracts are accounts controlled by code rather than private keys.\n\nThe core innovation of Ethereum’s new account abstraction protocol is utilizing smart contract wallets to completely manage users’ account activity and blockchain transactions behind the scenes. This abstracts away many of the complexities of blockchain interaction from the user experience.\n\nHow does account abstraction work?\n\nThe technical design is outlined in an Ethereum Improvement Proposal called EIP-4337:\n\nWhen a user wants to perform any action through a web3 application, they simply submit something called a “User Operation” into a pool known as the alt mempool. This User Operation contains instructions like “send 0.05 ETH to Address1″ or “purchase NFT #2456” without requiring direct blockchain signatures.\n\nValidators on the network called “bundlers” periodically pick up User Operations from the mempool, bundle groups of them together into packaged transactions called “Bundle Transactions”, and submit these bundles to a central global smart contract called the EntryPoint.\n\nThe EntryPoint contract acts as a router, forwarding each User Operation to the relevant smart contract wallet it pertains to. It calls validateUserOp(), which verifies the user’s signature, pays gas fees, and approves/rejects the operation. Next, execute() runs to perform the operation on-chain.\n\nThis sequence allows users to simply submit User Operations, while their smart contract wallet handles validating, bundling, paying gas, and executing Ethereum transactions behind the scenes. All blockchain complexity is abstracted away from users.\n\nBenefits of Ethereum account abstraction\n\nThe implications are profound in terms of unlocking web3 for the mainstream. Regular users could participate in DeFi, NFTs, and blockchain applications without crypto wallets or understanding public-key cryptography, addresses, gas fees, and signatures.\n\nAccount abstraction also enables user-friendly features not possible otherwise, like social recovery, spending limits, batched transactions, and approvals. This brings smart contract wallets significantly closer to the usability standards people expect from traditional finance.\n\nWhile highly complex under the hood, account abstraction finally provides the simplified user experience necessary for decentralized apps to achieve mass adoption. By abstracting away blockchain complexity, Ethereum’s new protocol brings web3’s user-owned vision closer to reality.\n\nWhat are the disadvantages of account abstraction?\n\nStill, this approach has one major drawback. While the smart contracts involved in Ethereum’s account abstraction implementation have been reviewed extensively, they still introduce more complexity to a critical system compared to a simple private-public key pair-based account.\n\nComplexity results in more potential security vulnerabilities and a greater attack surface. For this reason, it is only recommended to use this kind of account when intending to actually use its exclusive feature and the most recommended wallet for most users is still a hardware wallet."}]