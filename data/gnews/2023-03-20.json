[{"id": 0, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL21pY3Jvc29mdC1lZGdlLXRlc3RpbmctY3J5cHRvLXdhbGxldNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 20 Mar 2023 07:00:00 GMT", "title": "Microsoft Edge Testing Built-In Ethereum Crypto Wallet, Screenshots Show - Blockworks", "content": "Microsoft is reportedly working on an Ethereum-based Web3 wallet for its flagship browser, allowing users to send and receive digital assets and NFTs.\n\nThe wallet is currently in a limited testing phase and is not yet available to the public, IT media outlet Bleeping Computer reported Friday.\n\nFirst reported by Microsoft sleuth Albacore, who shared screenshots of the wallet on Twitter, Microsoft is asking “testers” via its Edge browser to put up their own assets.\n\n“We encourage you to test our first Web3 wallet and provide candid feedback along the journey,” Microsoft said in one of the screenshots. “As the first testers, you have the unique opportunity to shape our foray into cryptocurrencies and NFTs.”\n\nThe wallet is non-custodial, meaning users are in control of their funds and the tech giant will not have access to their passwords or recovery keys. The wallet generates an Ethereum address that allows users to receive funds through the Ethereum network.\n\nUsers can also manage multiple Ethereum accounts and choose between the built-in Edge wallet or an extension.\n\nMicrosoft has partnered with Consensys to make available a swap feature supporting ETH, DAI, UNI, USDC, and USDT, according to Bleeping Computer.\n\nMicrosoft and Consensys did not immediately respond to requests for comment.\n\nIt remains to be seen if Microsoft will add support for other digital assets in the future.\n\nAccording to Albacore, the wallet may launch soon.\n\n“Enabling the crypto wallet ended up being less complex than I anticipated, which is also why I think that a launch might not be far away,” he told Blockworks.\n\nAccording to the screenshots, the beta version of the wallet also features a news section to keep track of industry developments, which includes the ability to connect to decentralized apps.\n\nSimon Kertonegoro, who previously helped usher in the implementation of a collaborative program between Microsoft and Enjin known as Azure Heroes, told Blockworks the tech giant’s Web3 wallet was a “savvy play” as it sought to “gain an upper hand” against rival Google Chrome.\n\n“This would be amazing for us as users too, we’ll no longer need to input our credit card information every time we make a payment,” the founder of MyMetaverse said.\n\nWhile the Edge crypto wallet is still in the testing phase, it could be a significant development for Microsoft, as it allows users to easily send and receive crypto without installing additional extensions or apps.\n\nThe move signals a pivot from its previous Web3 endeavors; Microsoft axed its metaverse unit in February, just four months after it had created a team tasked with building industrial networks.\n\nJane Ma, co-founder of zkLend, a layer-2 money-market protocol built on StarkNet, told Blockworks the emergence of Web3 technologies has spurred a growing trend of Web2 infrastructure companies looking to explore ways to integrate these decentralized solutions into their platforms.\n\n“Microsoft’s prototype crypto wallet for its Edge browser is a prime example of this trend, as it aims to simplify the onboarding process for new users,” Ma said. “With a massive user base of over 200 million, Microsoft Edge’s approach of providing a ready-made, packaged solution within its browser would break down technical barriers,” she said.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9hZnRlci1zY2FsYWJpbGl0eS1wcml2YWN5LWlzLXRoZS1uZXh0LWJpZy10aGluZy1pbi10aGUtYmxvY2tjaGFpbi1zcGFjZdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 20 Mar 2023 07:00:00 GMT", "title": "After scalability, privacy is the next big thing in the blockchain space - Cointelegraph", "content": "Public blockchains provide decentralization and transparency, but they lack on the privacy side. The anonymity of transactions on blockchains like Bitcoin (BTC) and Ethereum (ETH) is steadily losing ground, as transactions and addresses are easily tracked. With KYC imposed on most crypto exchanges, the majority of blockchain transactions can be traced to their initiators, exposing user activity, holdings, and financial data.\n\nEven decentralized finance (DeFi) interactions can be easily monitored by advanced on-chain analysis systems. This is why privacy is just as important as speed and scalability for crypto to reach mainstream acceptance. Average consumers expect at least bank account level privacy to freely transact and RAILGUN is a ZK (Zero-Knowledge) based solution for existing blockchains that provides such wallet-level privacy.\n\nThe utility of digital assets comes with major downsides amid a lack of privacy. What happens when a crypto user pays for a coffee in crypto? They risk revealing their holdings, income and shopping preferences to merchants, peers and anyone who wants to extract value from their data. And taking it a step further, how many people would be comfortable receiving their salaries in crypto if it meant broadcasting all their financial information to the world? The necessity for increased privacy in the crypto space is essential to mainstream adoption.\n\nPrivacy gains more attention across the blockchain community\n\nPrivacy is an important goal for public blockchains, especially Ethereum, which accounts for about two-thirds of all DeFi activity. The need for privacy on Ethereum has increased after the adoption of the widespread adoption of layer-2 solutions like Arbitrum.\n\nIn January 2023, Ethereum co-founder <PERSON><PERSON> admitted the need for improved privacy on the blockchain. He proposed a “stealth address system” to increase the privacy degree of Ethereum transactions. Stealth addresses would be generated by wallets and would represent obfuscated public key addresses to receive funds in a private environment. Access to stealth addresses would require a special code referred to as a “spending key.” This would enable two parties to transact without being visible to the public. However, stealth addresses are an incomplete solution as they don’t account for full DeFi functionality.\n\nIt’s still too early to know when Ethereum will implement privacy features and to what extent, but the good news is that there are solutions that can already achieve a high degree of privacy. RAILGUN is a smart contract system that provides crypto and DeFi users with privacy through zero-knowledge proof (zk-SNARK) technology.\n\nRAILGUN is currently among the leading complete privacy solutions for the DeFi space, as it generates zk-SNARK encryption entirely within a smart contract. That enables users to store their funds anonymously and interact with decentralized applications (DApps) in an anonymous, noncustodial manner.\n\nSource: Railgun\n\nRAILGUN, which is governed by a decentralized autonomous organization (DAO), employs zk-SNARKs to encrypt transaction details, wallet balances and transaction history. Unlike other third-party, Layer-2 privacy solutions, RAILGUN works directly on-chain, enabling users to transfer, swap, lend, borrow and transact with all kinds of DApps anonymously.\n\nOn top of that, the RAILGUN smart contracts can be plugged into any Ethereum Virtual Machine DApp for shielded transactions using the RAILGUN Connect dev toolkit.\n\nRecently, RAILGUN was deployed to Arbitrum — a layer-2 rollup technology for Ethereum, bringing privacy combined with speed and high throughput to the second-largest decentralized network. RAILGUN DAO'S co-founder Alan Scott stated:\n\n“The Arbitrum deployment of RAILGUN is a massive accomplishment for privacy in DeFi. Arbitrum’s scaling and RAILGUN’s zk-SNARK-based privacy together are an exciting match. I’m looking forward to seeing how DeFi builders will use RAILGUN’s infrastructure to create interesting and new privacy-preserving DeFi solutions on Arbitrum.”\n\nBesides working on Ethereum and Arbitrum, RAILGUN enables on-chain privacy for BSC and Polygon. What’s more, contributors are working on adding support for Solana, Near and Metis as well.\n\nDevelopers and users are starting to pay more attention to privacy as blockchain adoption expands across multiple use cases. Therefore, if developers can crack the puzzle, privacy is poised to become one of the most important trends in blockchain in the coming years.\n\nMaterial is provided in partnership with RAILGUN DAO"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9hcmJpdHJ1bS1zcGVlZGluZy1ldGhlcmV1bS11c2luZy1vcHRpbWlzdGljLTEzMzEwMjEzOS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 20 Mar 2023 07:00:00 GMT", "title": "What Is Arbitrum? Speeding Up Ethereum Using Optimistic Rollups - Yahoo Finance", "content": "The Ethereum network has a lot going for it—it’s decentralized, reliable, supports smart contracts written in a programming language familiar to many crypto developers, and is home to a thriving decentralized finance (DeFi) industry.\n\nHowever, Ethereum is also slow and expensive to use, and will remain so unless users opt to move to another blockchain (like Solana, Fantom or Avalanche), or until planned Ethereum upgrades speed things up within the next couple of years.\n\nWhile the world waits, a third fix has arisen: scaling solutions. These are pieces of software that sit atop the base layer of a blockchain, in this case Ethereum, to speed things up. Arbitrum is one such scaling solution, and it’s become a popular venue for Ethereum users to complete their transactions.\n\nOn March 23, 2023, Arbitrum plans to airdrop its new ARB token, allowing holders to vote in decisions related to the protocol. In so doing, Arbitrum is enacting its long-awaited transition to a DAO (decentralized autonomous organization).\n\nWhat is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain\n\nArbitrum is cheap and quick to use, and relays all transaction information back to the main Ethereum blockchain. While Ethereum manages a mere 14 transactions per second, Arbitrum races ahead at 40,000 TPS. Transactions cost several dollars to complete on Ethereum, while they cost about two cents on Arbitrum.\n\nAnd Arbitrum also supports the Ethereum Virtual Machine (EVM), meaning that Ethereum DeFi developers can integrate their decentralized applications (dapps) with Arbitrum without having to make any modifications.\n\nArbitrum was created by Offchain Labs. The company raised $120 million in a Series B funding round in September 2021.\n\nHow does Arbitrum work?\n\nArbitrum’s development documentation says that this is the most important graph to understand:\n\nArbitrum workflow. Image: Arbitrum\n\nPut simply, people and smart contracts ask Arbitrum’s blockchain to do something by placing transactions into the chain's ‘inbox’. Then Arbitrum processes it and outputs a transaction receipt. How Arbitrum processes that transaction—what determines its ‘chain state’—is decided by the transactions in its inbox.\n\nStory continues\n\nRight now, Arbitrum processes Ethereum transactions through a method called an optimistic rollup, and settles these on a sidechain before reporting back to Ethereum. Let’s break that down.\n\nWhat is an optimistic rollup?\n\nA rollup is a type of data compression technique for blockchain transactions. It involves ‘rolling up’ batches of transactions into a single transaction.\n\nThe benefit of this is that a blockchain need only process a single transaction—the rolled-up transaction—instead of confirming each individual transaction contained within the rollup. This saves time—multiple transactions are confirmed at once, so you do not have to wait until the blockchain gets around to your transaction—and money, since the blockchain only has to confirm one transaction.\n\nAn ‘optimistic’ rollup is a specific technique for rolling up transactions. To speed things up, optimistic rollups assume that the transactions contained within the rollup are valid. It is possible to contest transactions through a dispute resolution mechanism if a validator suspects fraudulent behavior. (Optimistic rollups are separate from ‘zero knowledge’ rollups, which bypasses a dispute resolution mechanism by validating transactions before they are added to the rollup).\n\nOptimistic rollups are the present *and future* of Ethereum scaling. We explain why here:https://t.co/FG0OGC8TJB — Arbitrum (💙,🧡) (@arbitrum) December 17, 2021\n\nArbitrum’s optimistic rollups are settled on a proprietary sidechain. A sidechain is a blockchain that is connected to a main chain; in this case, Ethereum. Arbitrum collects batches of transactions, settles them on its sidechain, and then feeds the transaction data back to the Ethereum blockchain ledger.\n\nArbitrum says that any transactions confirmed through this process are rubber stamped with the “AnyTrust Guarantee”—when all the validators agree with the validity of transactions contained within a block. Validators stake ETH before they can confirm transactions; by putting money on the line, they are incentivized to act honestly.\n\nUnlike other rollup networks, like Boba or Loopring, Arbitrum does not have a token. Offchain Labs claims the network does not need one, since all transactions on the sidechain are settled with ETH, the native cryptocurrency of the Ethereum blockchain.\n\nArbitrum has been integrated into several decentralized finance protocols, such as SushiSwap, Curve and Abracadabra. Data from DeFi Llama show that $2 billion worth of cryptocurrency is locked up within Arbitrum’s smart contracts. Approximately 30% is from decentralized exchange (DEX) SushiSwap.\n\nThe project’s blockchain, Arbitrum One, is in mainnet “beta”, which allows Arbitrum’s developers “various levels of control over the system”, including “the ability to pause the system”. Offchain Labs plans to eliminate these controls once it is certain the project is robust.\n\nHow to use Arbitrum\n\nYou can use Arbitrum through a decentralized application, like Aave, 1inch or Gnosis Safe, or directly on Arbitrum’s token bridge. For the token bridge, you can deposit funds to the Arbitrum network after you’ve connected your Web3 wallet. It takes about 10 minutes for deposits to clear.\n\nYou’ll have to pay an Ethereum gas fee—at Ethereum’s rates. To deposit under a cent of ETH to Arbitrum, MetaMask wallet quoted us $5.41 in gas fees."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9hcmJpdHJ1bS1zcGVlZGluZy1ldGhlcmV1bS11c2luZy1vcHRpbWlzdGljLTEzMzEwMjEzOS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 20 Mar 2023 07:00:00 GMT", "title": "What Is Arbitrum? Speeding Up Ethereum Using Optimistic Rollups - Yahoo Finance", "content": "The Ethereum network has a lot going for it—it’s decentralized, reliable, supports smart contracts written in a programming language familiar to many crypto developers, and is home to a thriving decentralized finance (DeFi) industry.\n\nHowever, Ethereum is also slow and expensive to use, and will remain so unless users opt to move to another blockchain (like Solana, Fantom or Avalanche), or until planned Ethereum upgrades speed things up within the next couple of years.\n\nWhile the world waits, a third fix has arisen: scaling solutions. These are pieces of software that sit atop the base layer of a blockchain, in this case Ethereum, to speed things up. Arbitrum is one such scaling solution, and it’s become a popular venue for Ethereum users to complete their transactions.\n\nOn March 23, 2023, Arbitrum plans to airdrop its new ARB token, allowing holders to vote in decisions related to the protocol. In so doing, Arbitrum is enacting its long-awaited transition to a DAO (decentralized autonomous organization).\n\nWhat is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain\n\nArbitrum is cheap and quick to use, and relays all transaction information back to the main Ethereum blockchain. While Ethereum manages a mere 14 transactions per second, Arbitrum races ahead at 40,000 TPS. Transactions cost several dollars to complete on Ethereum, while they cost about two cents on Arbitrum.\n\nAnd Arbitrum also supports the Ethereum Virtual Machine (EVM), meaning that Ethereum DeFi developers can integrate their decentralized applications (dapps) with Arbitrum without having to make any modifications.\n\nArbitrum was created by Offchain Labs. The company raised $120 million in a Series B funding round in September 2021.\n\nHow does Arbitrum work?\n\nArbitrum’s development documentation says that this is the most important graph to understand:\n\nArbitrum workflow. Image: Arbitrum\n\nPut simply, people and smart contracts ask Arbitrum’s blockchain to do something by placing transactions into the chain's ‘inbox’. Then Arbitrum processes it and outputs a transaction receipt. How Arbitrum processes that transaction—what determines its ‘chain state’—is decided by the transactions in its inbox.\n\nStory continues\n\nRight now, Arbitrum processes Ethereum transactions through a method called an optimistic rollup, and settles these on a sidechain before reporting back to Ethereum. Let’s break that down.\n\nWhat is an optimistic rollup?\n\nA rollup is a type of data compression technique for blockchain transactions. It involves ‘rolling up’ batches of transactions into a single transaction.\n\nThe benefit of this is that a blockchain need only process a single transaction—the rolled-up transaction—instead of confirming each individual transaction contained within the rollup. This saves time—multiple transactions are confirmed at once, so you do not have to wait until the blockchain gets around to your transaction—and money, since the blockchain only has to confirm one transaction.\n\nAn ‘optimistic’ rollup is a specific technique for rolling up transactions. To speed things up, optimistic rollups assume that the transactions contained within the rollup are valid. It is possible to contest transactions through a dispute resolution mechanism if a validator suspects fraudulent behavior. (Optimistic rollups are separate from ‘zero knowledge’ rollups, which bypasses a dispute resolution mechanism by validating transactions before they are added to the rollup).\n\nOptimistic rollups are the present *and future* of Ethereum scaling. We explain why here:https://t.co/FG0OGC8TJB — Arbitrum (💙,🧡) (@arbitrum) December 17, 2021\n\nArbitrum’s optimistic rollups are settled on a proprietary sidechain. A sidechain is a blockchain that is connected to a main chain; in this case, Ethereum. Arbitrum collects batches of transactions, settles them on its sidechain, and then feeds the transaction data back to the Ethereum blockchain ledger.\n\nArbitrum says that any transactions confirmed through this process are rubber stamped with the “AnyTrust Guarantee”—when all the validators agree with the validity of transactions contained within a block. Validators stake ETH before they can confirm transactions; by putting money on the line, they are incentivized to act honestly.\n\nUnlike other rollup networks, like Boba or Loopring, Arbitrum does not have a token. Offchain Labs claims the network does not need one, since all transactions on the sidechain are settled with ETH, the native cryptocurrency of the Ethereum blockchain.\n\nArbitrum has been integrated into several decentralized finance protocols, such as SushiSwap, Curve and Abracadabra. Data from DeFi Llama show that $2 billion worth of cryptocurrency is locked up within Arbitrum’s smart contracts. Approximately 30% is from decentralized exchange (DEX) SushiSwap.\n\nThe project’s blockchain, Arbitrum One, is in mainnet “beta”, which allows Arbitrum’s developers “various levels of control over the system”, including “the ability to pause the system”. Offchain Labs plans to eliminate these controls once it is certain the project is robust.\n\nHow to use Arbitrum\n\nYou can use Arbitrum through a decentralized application, like Aave, 1inch or Gnosis Safe, or directly on Arbitrum’s token bridge. For the token bridge, you can deposit funds to the Arbitrum network after you’ve connected your Web3 wallet. It takes about 10 minutes for deposits to clear.\n\nYou’ll have to pay an Ethereum gas fee—at Ethereum’s rates. To deposit under a cent of ETH to Arbitrum, MetaMask wallet quoted us $5.41 in gas fees."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9hcmJpdHJ1bS1zcGVlZGluZy1ldGhlcmV1bS11c2luZy1vcHRpbWlzdGljLTEzMzEwMjEzOS5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 20 Mar 2023 07:00:00 GMT", "title": "What Is Arbitrum? Speeding Up Ethereum Using Optimistic Rollups - Yahoo Finance", "content": "The Ethereum network has a lot going for it—it’s decentralized, reliable, supports smart contracts written in a programming language familiar to many crypto developers, and is home to a thriving decentralized finance (DeFi) industry.\n\nHowever, Ethereum is also slow and expensive to use, and will remain so unless users opt to move to another blockchain (like Solana, Fantom or Avalanche), or until planned Ethereum upgrades speed things up within the next couple of years.\n\nWhile the world waits, a third fix has arisen: scaling solutions. These are pieces of software that sit atop the base layer of a blockchain, in this case Ethereum, to speed things up. Arbitrum is one such scaling solution, and it’s become a popular venue for Ethereum users to complete their transactions.\n\nOn March 23, 2023, Arbitrum plans to airdrop its new ARB token, allowing holders to vote in decisions related to the protocol. In so doing, Arbitrum is enacting its long-awaited transition to a DAO (decentralized autonomous organization).\n\nWhat is Ethereum (ETH)? A Beginner's Guide to the Smart Contract Blockchain\n\nArbitrum is cheap and quick to use, and relays all transaction information back to the main Ethereum blockchain. While Ethereum manages a mere 14 transactions per second, Arbitrum races ahead at 40,000 TPS. Transactions cost several dollars to complete on Ethereum, while they cost about two cents on Arbitrum.\n\nAnd Arbitrum also supports the Ethereum Virtual Machine (EVM), meaning that Ethereum DeFi developers can integrate their decentralized applications (dapps) with Arbitrum without having to make any modifications.\n\nArbitrum was created by Offchain Labs. The company raised $120 million in a Series B funding round in September 2021.\n\nHow does Arbitrum work?\n\nArbitrum’s development documentation says that this is the most important graph to understand:\n\nArbitrum workflow. Image: Arbitrum\n\nPut simply, people and smart contracts ask Arbitrum’s blockchain to do something by placing transactions into the chain's ‘inbox’. Then Arbitrum processes it and outputs a transaction receipt. How Arbitrum processes that transaction—what determines its ‘chain state’—is decided by the transactions in its inbox.\n\nStory continues\n\nRight now, Arbitrum processes Ethereum transactions through a method called an optimistic rollup, and settles these on a sidechain before reporting back to Ethereum. Let’s break that down.\n\nWhat is an optimistic rollup?\n\nA rollup is a type of data compression technique for blockchain transactions. It involves ‘rolling up’ batches of transactions into a single transaction.\n\nThe benefit of this is that a blockchain need only process a single transaction—the rolled-up transaction—instead of confirming each individual transaction contained within the rollup. This saves time—multiple transactions are confirmed at once, so you do not have to wait until the blockchain gets around to your transaction—and money, since the blockchain only has to confirm one transaction.\n\nAn ‘optimistic’ rollup is a specific technique for rolling up transactions. To speed things up, optimistic rollups assume that the transactions contained within the rollup are valid. It is possible to contest transactions through a dispute resolution mechanism if a validator suspects fraudulent behavior. (Optimistic rollups are separate from ‘zero knowledge’ rollups, which bypasses a dispute resolution mechanism by validating transactions before they are added to the rollup).\n\nOptimistic rollups are the present *and future* of Ethereum scaling. We explain why here:https://t.co/FG0OGC8TJB — Arbitrum (💙,🧡) (@arbitrum) December 17, 2021\n\nArbitrum’s optimistic rollups are settled on a proprietary sidechain. A sidechain is a blockchain that is connected to a main chain; in this case, Ethereum. Arbitrum collects batches of transactions, settles them on its sidechain, and then feeds the transaction data back to the Ethereum blockchain ledger.\n\nArbitrum says that any transactions confirmed through this process are rubber stamped with the “AnyTrust Guarantee”—when all the validators agree with the validity of transactions contained within a block. Validators stake ETH before they can confirm transactions; by putting money on the line, they are incentivized to act honestly.\n\nUnlike other rollup networks, like Boba or Loopring, Arbitrum does not have a token. Offchain Labs claims the network does not need one, since all transactions on the sidechain are settled with ETH, the native cryptocurrency of the Ethereum blockchain.\n\nArbitrum has been integrated into several decentralized finance protocols, such as SushiSwap, Curve and Abracadabra. Data from DeFi Llama show that $2 billion worth of cryptocurrency is locked up within Arbitrum’s smart contracts. Approximately 30% is from decentralized exchange (DEX) SushiSwap.\n\nThe project’s blockchain, Arbitrum One, is in mainnet “beta”, which allows Arbitrum’s developers “various levels of control over the system”, including “the ability to pause the system”. Offchain Labs plans to eliminate these controls once it is certain the project is robust.\n\nHow to use Arbitrum\n\nYou can use Arbitrum through a decentralized application, like Aave, 1inch or Gnosis Safe, or directly on Arbitrum’s token bridge. For the token bridge, you can deposit funds to the Arbitrum network after you’ve connected your Web3 wallet. It takes about 10 minutes for deposits to clear.\n\nYou’ll have to pay an Ethereum gas fee—at Ethereum’s rates. To deposit under a cent of ETH to Arbitrum, MetaMask wallet quoted us $5.41 in gas fees."}]