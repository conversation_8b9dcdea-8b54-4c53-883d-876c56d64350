[{"id": 7, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS92aXRhbGlrLWJ1dGVyaW4tcHJvbW90ZXMtcHJlc2VydmF0aW9uLW9mLWV0aGVyZXVtLWJsb2NrY2hhaW5zLW1pbmltYWxpc20v0gFmaHR0cHM6Ly9jcnlwdG9wb3RhdG8uY29tL3ZpdGFsaWstYnV0ZXJpbi1wcm9tb3Rlcy1wcmVzZXJ2YXRpb24tb2YtZXRoZXJldW0tYmxvY2tjaGFpbnMtbWluaW1hbGlzbS8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 22 May 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Buterin Promotes Preservation of Ethereum Blockchain’s ‘Minimalism’ - CryptoPotato", "content": "Ethereum co-founder <PERSON><PERSON> has warned against ‘”overloading” Ethereum’s social consensus – which, if discouraged or resisted – could bring “high systemic risks to the ecosystem.”\n\nWhile admitting the “natural urge” to try to extend the Ethereum blockchain’s core functionality, <PERSON><PERSON><PERSON> added that such actions could end up making the consensus layer more fragile.\n\nRisks of Overloading Consensus Layer\n\nOver the years, several proposals have emerged suggesting the use of Ethereum social consensus for other purposes, including price and data oracles, re-staking initiatives, and using layer-1 soft forks to recover layer-2 projects. The Ethereum co-founder, however, warned against such a notion and explained,\n\n“It is natural for application-layer projects to attempt such a strategy, and indeed such ideas are often simply conceived without an appreciation of the risks, but its result can easily become very misaligned with the goals of the community as a whole.”\n\nIn the latest blog post titled, ‘Don’t overload Ethereum’s consensus,’ <PERSON><PERSON><PERSON> offered some potential solutions to address risks such as price oracles, either “not-quite-cryptoeconomic decentralized oracles” or validator-voting-based oracles that explicitly commit to their emergency recovery strategies being something other than appealing to L1 consensus.\n\n<PERSON><PERSON><PERSON> also pointed out that more complex truth oracles could be counted as a solution as the focus would be on reporting facts more subjective than price. He added that it would be “some kind of decentralized court system” built on a not-quite-cryptoeconomic DAO.\n\nMinimizing reliance on cross-chain bridges is also important since these protocols have become an attractive target for malicious entities.\n\n<PERSON>malism\n\nButerin said that any expansion of the “duties” of Ethereum’s consensus triggers an increase in costs, complexities as well as risks of running a validator. He argued that preserving the chain’s minimalism should be the focus which can be achieved by supporting uses of re-staking that do not look like slippery slopes to extending the role of Ethereum consensus.\n\nHe also highlighted the importance of helping developers find alternate strategies to achieve their security goals."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4td2FybnMtb3ZlcmxvYWRpbmctZXRoZXJldW0tY29uc2Vuc3Vz0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 22 May 2023 07:00:00 GMT", "title": "<PERSON><PERSON> warns against overloading Ethereum consensus layer - Cointelegraph", "content": "Ethereum co-founder <PERSON><PERSON> has published a lengthy blog post warning of the dangers of “stretching” Ethereum’s consensus past its core functions of validating blocks and securing the network.\n\nEthereum consensus is the process whereby blocks are validated by the proof-of-stake mechanism implemented in September 2022 with “the Merge.”\n\nIn a May 21 blog post titled “Don’t overload Ethereum’s consensus,” <PERSON><PERSON><PERSON> warned that using Ethereum’s network consensus for other things could bring “high systemic risks to the ecosystem and should be discouraged and resisted.”\n\nThe Ethereum co-founder was essentially promoting the preservation of the blockchain’s minimalism.\n\n<PERSON><PERSON><PERSON> noted that over the years, a number of proposals or ideas had floated around that suggested using the Ethereum social consensus for other purposes, such as price and data oracles, re-staking initiatives and using layer-1 soft forks to recover layer-2 projects should they have issues.\n\n“There is a natural urge to try to extend the blockchain’s core with more and more functionality because the blockchain’s core has the largest economic weight and the largest community watching it, but each such extension makes the core itself more fragile.”\n\n<PERSON><PERSON><PERSON> said that a certain subset of these techniques could bring “high systemic risks” to the ecosystem, such as bugs or an intentional 51% attack.\n\nSome high-risk examples include creating ETH/USD price oracles in which ETH (ETH) holders or validators can be bribed to vote on, which may result in a “fork out the bad participants' money” if there is disagreement.\n\nHowever, he acknowledged a need for better oracles, proposing a case-by-case approach because various problems are “inherently so different” from each other.\n\nOverall, Buterin said that any expansion of the “duties” of Ethereum's consensus increases the costs, complexities, and risks of running a validator.\n\nRelated: Buterin weighs in on zk-EVMs’ impact on decentralization and security\n\nApplication-layer projects “taking actions that risk increasing the ‘scope’ of blockchain consensus to anything other than verifying the core Ethereum protocol rules,” should be treated with caution, he said, summarizing:\n\n“We should instead preserve the chain’s minimalism, support uses of re-staking that do not look like slippery slopes to extending the role of Ethereum consensus, and help developers find alternate strategies to achieve their security goals.”\n\nThe Ethereum consensus mechanism switched from proof-of-work to proof-of-stake in September last year. Additionally, staked Ethereum has only just been released for withdrawal with the Shapella upgrade on April 12. This explains the increased scrutiny of validator roles and security risks on the world’s largest smart contract network.\n\nMagazine: ‘Account abstraction’ supercharges Ethereum wallets: Dummies guide"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vZmluYm9sZC5jb20vbWFjaGluZS1sZWFybmluZy1hbGdvcml0aG0tc2V0cy1ldGhlcmV1bS1wcmljZS1mb3ItanVuZS0xLTIwMjMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 22 May 2023 07:00:00 GMT", "title": "Machine learning algorithm sets Ethereum price for June 1, 2023 - Finbold - Finance in Bold", "content": "Over the past week, Ethereum (ETH) has demonstrated remarkable stability in its price movements, in contrast to Bitcoin (BTC), which has experienced a decline and fell below the $27,000 mark. However, it is crucial to note that Ethereum, as the second-largest market capitalization asset, has displayed resilience and exhibited signs of strength during this period.\n\nThis stability in Ethereum’s price suggests a certain level of confidence and investor trust in the asset. It signifies that despite the overall market fluctuations, Ethereum has managed to hold its ground, attracting potential investors and maintaining a relatively steady value.\n\nWith this in mind, Finbold has consulted the machine learning algorithms over at the cryptocurrency analytics and prediction platform Price Predictions, which set the price of ETH at $1,792 on June 1, 2023, according to the latest data accessed on May 22.\n\nETH 30-day price forecast. Source: PricePredictions\n\nThe algorithms, which rely on technical analysis (TA) indicators, such as moving average (MA), relative strength index (RSI), moving average convergence divergence (MACD), average true range (ATR), and Bollinger Bands (BB), predict a decline of -1.3% to Ethereum price at press time, which is price expected to continue gradually decline over the end of the month.\n\nEthereum price analysis\n\nCurrently, Ethereum is trading at $1,816, reflecting a slight increase of 0.06% for the day. However, over the course of the week, ETH has experienced a decline of 0.56%. These price movements indicate a relatively stable performance with some downward pressure in recent days.\n\nETH 1-day price chart. Source: Finbold\n\nAnalyzing the support and resistance levels, we find that $1,720 acts as a crucial support level for Ethereum, indicating a price point where buying interest is expected to be strong and potential downward movements may be limited. On the other hand, $1,894 represents the resistance level, serving as a price point where selling pressure may intensify, potentially limiting upward movements unless surpassed.\n\nIn terms of market capitalization, Ethereum boasts a total market cap of $218.5 billion, highlighting its significant position within the cryptocurrency market.\n\nMeanwhile, the technical analysis over at TradingView suggests a largely bearish sentiment around ETH. Indeed, according to the analysis, the one-day gauges indicate a ‘sell’ at 10, which is the summary of oscillators in the ‘sell’ zone at 2, and moving averages with a ‘sell’ at 8.\n\nETH technical analysis. Source: TradingView\n\nWhether the machine algorithm-based projections come true or ETH manages to turn the tide will depend on its further developments in the space and how long Ethereum can continue to show strength as Bitcoin struggles to break resistance at $27,600.\n\nDisclaimer: The content on this site should not be considered investment advice. Investing is speculative. When investing, your capital is at risk."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiaGh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3ZpdGFsaWstYnV0ZXJpbi1zYXlzLWV0aGVyZXVtcy1jb25zZW5zdXMtaXMtZnJhZ2lsZS1hbmQtc2hvdWxkLW5vdC1iZS1zdHJldGNoZWQv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 22 May 2023 07:00:00 GMT", "title": "<PERSON><PERSON> says Ethereum's consensus is 'fragile' and should not be stretched - CryptoSlate", "content": "<PERSON><PERSON>n warned in a blog post today that Ethereum’s consensus is fragile and should be used “sparingly” because of the high risk of forks in the chain.\n\n<PERSON><PERSON><PERSON> wrote:\n\n“There is a natural urge to try to extend the blockchain’s core with more and more functionality, because the blockchain’s core has the largest economic weight and the largest community watching it, but each such extention makes the core itself more fragile.”\n\n<PERSON><PERSON><PERSON> added that we should be wary of projects that seek to increase the “scope” of blockchain consensus to anything other than verifying the core Ethereum protocol rules as this could lead to more “mandates” over time and an increased risk of forking the chain.\n\nEthereum (ETH) has over half a million validators securing the network that have collectively staked 18.5 million ETH, worth more than $34 billion. These validators finalize blocks every 6.4 minutes on the Ethereum network. The process is secured and sophisticated so that the chain recovers to the correct state even if a bug hits or a 51% attack occurs.\n\nStretching the consensus system for other purposes can introduce “high systemic risks to the ecosystem and should be discouraged and resisted,” <PERSON><PERSON><PERSON> wrote. He added:\n\n“Dual-use of validator staked ETH, while it has some risks, is fundamentally fine, but attempting to “recruit” Ethereum social consensus for your application’s own purposes is not.”\n\n<PERSON><PERSON><PERSON> further explained that so long as a protocol kept its losses contained to the validators and users in case of a complete collapse, it is “low-risk.” But, if the protocol is designed in a way that the original Ethereum chain has to fork or reorganize to solve its problems, then it is “high-risk, and I argue that we should strongly resist all attempts to create such expectations,” he wrote.\n\nThere could be a middle ground, Buterin suggested, if protocols in the low-risk category incentivize participants to slide into the higher-risk category. He also suggested using SchellingCoin-style techniques, a consensus mechanism where participants are asked to guess the average value of a certain parameter, like price, and those whose guesses are closest to the average are rewarded.\n\nWhat are the risks of extending Ethereum’s consensus?\n\nAccording to Buterin:\n\n“As soon as a blockchain tries to “hook in” to the outside world, the outside world’s conflicts start to impact on the blockchain too.”\n\nIn other words, if Ethereum validators start voting on things like price oracles that include the currency of a country in the middle of a political crisis, it could lead to a split of the Ethereum chain.\n\nButerin added:\n\n“…once a blockchain starts incorporating real-world price indices as a layer-1 protocol feature, it could easily succumb to interpreting more and more real-world information.”\n\nFurthermore, introducing Layer 1 price indices could change blockchains from neutral technical platforms to explicitly financial tools. This, in turn, could attract legal trouble for blockchains, Buterin said.\n\nFurthermore, it is not just price indices that pose a risk. Buterin wrote:"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiaWh0dHBzOi8vd3d3LnRoZWJsb2NrLmNvL3Bvc3QvMjMxNjc2L3ZpdGFsaWstYnV0ZXJpbi11cmdlcy1jYXV0aW9uLXdoZW4taXQtY29tZXMtdG8tcmUtc3Rha2luZy1vbi1ldGhlcmV1bdIBbWh0dHBzOi8vd3d3LnRoZWJsb2NrLmNvL2FtcC9wb3N0LzIzMTY3Ni92aXRhbGlrLWJ1dGVyaW4tdXJnZXMtY2F1dGlvbi13aGVuLWl0LWNvbWVzLXRvLXJlLXN0YWtpbmctb24tZXRoZXJldW0?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 22 May 2023 07:00:00 GMT", "title": "<PERSON><PERSON> urges caution when it comes to re-staking on Ethereum - The Block", "content": "Ethereum co-founder <PERSON><PERSON> expressed concerns about overcomplicating the Ethereum consensus mechanism beyond its original design, specifically in terms of re-staking.\n\nIn a new blog post, <PERSON><PERSON><PERSON> voiced reservations about initiatives that could unnecessarily introduce risks into the ecosystem and complicate the roles of Ethereum validators beyond their primary duty of verifying the core protocol rules. <PERSON><PERSON><PERSON> was concerned about re-staking, a mechanism being developed by <PERSON><PERSON><PERSON> Layer, among others, which broadens the responsibilities of Ethereum validators to include securing external chains. He was worried that re-staking might introduce risks that could affect the safety of the network.\n\n“We should tread lightly when application-layer projects aim to extend the ‘scope’ of blockchain consensus beyond the validation of essential Ethereum protocol rules,” <PERSON><PERSON><PERSON> stated in the post.\n\nIn Ethereum’s proof-of-stake model, validators are selected based on the number of ether they hold and are willing to stake. The network has the largest validator set among all proof-of-stake chains, both in terms of the number of validator entities and the total value of staked ether, some 18 million ETH (~$34 billion). This considerable size has prompted the development of systems to leverage this network security to secure third-party chains. However, <PERSON><PERSON><PERSON> advocated for a cautious approach.\n\nThe risk of re-staking\n\n<PERSON><PERSON><PERSON> noted that while re-staking can be used for low-risk purposes, there are situations where it could compromise the mainnet’s security, such as when Ethereum validators face slashing on third-party chains. Slashing is a punitive measure for validators who engage in undesirable activities, like improperly maintaining their stake or incorrectly processing transactions.\n\nTHE SCOOP Keep up with the latest news, trends, charts and views on crypto and DeFi with a new biweekly newsletter from The Block's Frank Chaparro By signing-up you agree to our Terms of Service and Privacy Policy EMAIL Also receive The Daily and our weekly Data & Insights newsletters - both are FREE By signing-up you agree to our Terms of Service and Privacy Policy\n\n“We should instead preserve the chain’s minimalism and support uses of re-staking that do not seem like slippery slopes towards extending the role of Ethereum consensus,” Buterin suggested.\n\nButerin’s comments elicited responses from Sreeram Kannan, co-founder of Eigen Layer. Kannan agreed with Buterin’s analysis and acknowledged that Eigen Layer should avoid building complex financial primitives with the help of re-staking, as they “can spiral out of control.” Nevertheless, he stated that re-staking can be used for “low-risk” scenarios.\n\nKannan agreed that Eigen Layer can extend the functionality of validators beyond Ethereum but said these should be developed without the need for “slashing,” which would introduce unnecessary complexity. He further emphasized that Eigen Layer is cautious about not impacting Ethereum’s security in any way, aligning with Buterin’s blog post.\n\nButerin discussed a 2015 proposal from Martin Köppelmann, the co-founder of Gnosis, for an “ultimate oracle,” deriving security from ETH stake. In the context of smart contracts, oracles are vital as they supply off-chain data. However, Buterin said that if the security of these data feeds were intertwined with Ethereum’s stake, it might contribute to increased complexity.\n\nButerin suggested that such expanded roles or “duties” added to Ethereum’s consensus mechanism could magnify the challenges and risks involved in operating as a validator. He emphasized, “Validators are required to exert significant human effort in terms of monitoring, running, and updating additional software to ensure their adherence to any newly implemented protocols.”"}]