[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1sYWNrcy1tb21lbnR1bS0xOTUwL9IBTGh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1sYWNrcy1tb21lbnR1bS0xOTUwL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 05 Jul 2023 07:00:00 GMT", "title": "Ethereum Price Lacks Momentum Above $1,950 But Dips Could Be Limited - NewsBTC", "content": "Ethereum price failed to test $2,000 and corrected lower against the US Dollar. ETH is testing the $1,930 support and might start a fresh increase.\n\nEthereum is correcting gains from the $1,975 zone.\n\nThe price is trading above $1,930 and the 100-hourly Simple Moving Average.\n\nThere is a short-term declining channel forming with resistance near $1,950 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could start another increase if it remains stable above $1,930 in the near term.\n\nEthereum Price Holds Support\n\nEthereum’s price attempted an upside break above the $1,975 zone but failed. ETH struggled to gain pace for a move toward $2,000 and corrected gains, similar to Bitcoin.\n\nThere was a drop below the $1,950 support. The price declined below the 23.6% Fib retracement level of the upward move from the $1,890 swing low to the $1,975 high. However, the bulls were active near the $1,930 support zone.\n\nIt also tested the 50% Fib retracement level of the upward move from the $1,890 swing low to the $1,975 high. Ether price is now trading above $1,930 and the 100-hourly Simple Moving Average.\n\nImmediate resistance is near the $1,950 level. There is also a short-term declining channel forming with resistance near $1,950 on the hourly chart of ETH/USD. The next major resistance is near the $1,975 level. A clear move above the $1,975 resistance could push the price toward $2,000.\n\nSource: ETHUSD on TradingView.com\n\nThe next resistance sits near $2,050, above which the price could rise toward the $2,120 level. Any more gains could send Ether toward the $2,200 resistance.\n\nMore Losses in ETH?\n\nIf Ethereum fails to clear the $1,950 resistance or $1,950, it could continue to move down. Initial support on the downside is near the $1,930 level and the 100-hourly Simple Moving Average.\n\nThe first major support is near the $1,910 level. The next major support is near the $1,900 level. If there is a move below the $1,900 support, the price could drop toward the $1,870 support level. Any more losses may perhaps send the price toward the $1,820 support in the near term.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is losing momentum in the bullish zone.\n\nHourly RSI – The RSI for ETH/USD is now below the 50 level.\n\nMajor Support Level – $1,930\n\nMajor Resistance Level – $1,975\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vd3d3LnRoZWJsb2NrLmNvL3Bvc3QvMjM3ODQxL2V0aGVyZXVtLWxheWVyLTItc3RhcmtuZXTSAUFodHRwczovL3d3dy50aGVibG9jay5jby9hbXAvcG9zdC8yMzc4NDEvZXRoZXJldW0tbGF5ZXItMi1zdGFya25ldA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 05 Jul 2023 07:00:00 GMT", "title": "Ethereum Layer 2 network Starknet plans 'Quantum Leap' upgrade on July 12 - The Block", "content": "Starknet, an Ethereum Layer 2 network, is preparing to launch an upgrade this month to boost its capacity for transactions per second.\n\nScheduled for launch around July 12, the \"Quantum Leap\" upgrade aims to elevate the network's throughput to over hundred transactions per second. This would mark a significant upswing from current levels, according to the team at StarkWare, a key network contributor.\n\nCurrently undergoing testing, Starknet version 12.0 awaits approval from the Starknet community through an ongoing vote. If it passes the community validation, the upgrade will be deployed to the mainnet. After a dedicated period of engineering by StarkWare, LambdaClass, and other participants within the Starknet ecosystem, the upgrade was first released on the Goerli testnet on July 3.\n\n“Starknet’s new version, Quantum Leap, is exactly what the name suggests – a leap in TPS, the likes of which nobody has yet achieved in the Ethereum ecosystem,” said <PERSON>, co-founder of StarkWare.\n\nStarknet, a decentralized Layer 2 network, utilizes a solution classified as a Zero Knowledge roll-up, which condenses multiple transactions on an off-chain layer and publishes them together on the Ethereum network. StarkWare was the original architect of Starknet, employing the Stark cryptographic proofs mechanism invented by the team. Today, StarkWare is among a collaborative group of contributors shaping Starknet’s evolution, with the network’s operations and roadmap now under the purview of the Starknet Foundation.\n\nTHE SCOOP Keep up with the latest news, trends, charts and views on crypto and DeFi with a new biweekly newsletter from The Block's Frank <PERSON>ro By signing-up you agree to our Terms of Service and Privacy Policy EMAIL Also receive The Daily and our weekly Data & Insights newsletters - both are FREE By signing-up you agree to our Terms of Service and Privacy Policy\n\nEnhancing Ethereum’s scalability with Starknet\n\nWhile the upgrade is expected to position Starknet at the top of the TPS leaderboards for Ethereum Layer 2 scaling solutions, it will also improve latency, with an expected time-to-inclusion of under 15 seconds under normal conditions. According to StarkWare, this is expected to help scale DeFi applications and the gaming niche by facilitating faster transactions and greater throughput for users.\n\nThe Starknet roadmap includes plans for version 13.0 later this year that aims to cut down transaction costs. Version 14, meanwhile, will introduce a system where developers can pay extra to prioritize transactions, thereby creating a marketplace for block space on the Layer 2 network.\n\nBen-Sasson added that a major focus for the Starknet network in the future will also be leveraging the account abstraction feature. The mechanism could enable innovative integrations within crypto apps, such as the use of two-factor authentication for signing crypto transactions and wallet recovery."}, {"id": 27, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9nZW5lcmF0aXZlLXRlem9zLWFydC1wbGF0Zm9ybS1meGhhc2gtMTM0NTEyNTQ3Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 05 Jul 2023 07:00:00 GMT", "title": "Tezos Generative Art Platform fxhash Adds Ethereum in Multichain Push - Yahoo Finance", "content": "Often coined \"the art blockchain,\" <PERSON><PERSON><PERSON> has earned this reputation thanks to at least one key platform.\n\nNow, though, fxhash is set to also join the Ethereum ecosystem\n\nFxhash lets users create new iterations of generative artwork, with artists limiting the number of mints for their work. Collectors discover the specific iteration they've acquired at the reveal, adding a unique aspect to the process of collecting generative art.\n\nThe decision to expand fxhash to the Ethereum blockchain marks a significant step in the platform's evolution. Initially, fxhash chose the Tezos blockchain for its appealing proof-of-stake consensus, especially when Ethereum was still using proof-of-work.\n\nThe work of digital artist Zancan. Image: fxhash.\n\nHowever, with the upcoming launch of fxhash 2.0, the platform is set to become multichain, a move influenced by requests from its partners for expansion beyond Tezos.\n\nThe implementation of fxhash 2.0 aims to break down the barriers that currently exist between chains. At present, artists can often sell their artworks at significantly higher prices on Ethereum, which also offers greater visibility for artists.\n\n\"By bringing communities together and showing artworks side by side, regardless of the chain they were minted on, we aim to elevate generative art,\" <PERSON>, the creative director at fxhash, told Decrypt. “The chain doesn't matter. It's the art that matters.\"\n\n<PERSON>'s IN/Visible NFT Exhibition Highlights Black Artists, AI's ‘Skewed’ Lens\n\nThe platform was initially started by the artist <PERSON><PERSON><PERSON><PERSON>. As a generative artist, he felt difficult to find a platform that could properly showcase his work. This struggle led him to create fxhash.\n\nSince then, it has been highly community-focused.\n\nThis plays a significant role in decision-making processes, too, particularly with the upcoming fxhash 2.0.\n\n\"fxhash is the largest live repository of Generative Art, as such we can never stand still, but need to provide Artists with a wide variety of tools that we are continuously adding to in close communication with our community,” said <PERSON>. “fxhash 2.0 is a natural consequence of that, and something our team, our partners, the community, and most importantly artists are incredibly excited about.\"\n\nStory continues\n\n2 million pieces of art on fxhash\n\nThe platform boasts a diverse array of talented artists, including Zancan, William Mapan, Iskra Velitchkova, Melissa Wiederrecht, Andreas Rau, and Kim Asendorf.\n\nSome have gained significant popularity, with Zancan's \"Garden, Monoliths\" collection, for instance, racking up over 1.2 million XTZ in secondary market sales.\n\nAs of now, fxhash hosts around 8,000 artists or users who have minted at least one token, and it showcases two million pieces of generative art. The platform has facilitated the movement of approximately $40 million to $45 million, with the lion's share of this amount going to the artists.\n\nThe team at fxhash is currently hard at work on the Ethereum integration, with the launch of fxhash 2.0 slated for after the summer.\n\nThis will likely come with a UI and UX overhaul, too, and there are plans for events, both online and in-person, to celebrate the launch.\n\n\"Right now we've got our sights focused on an event happening in Berlin, which is related to the hackathon we've just started,” said Middleton. “As we get closer to the launch, we'll have a clearer idea of what we can plan.\"\n\nIn a world where the chain often matters more than the art, fxhash is striving to shift the focus back to where it belongs: on the artists and their creations.\n\n\"Our goal was always to build tools allowing artists and collectors to live out their passion for generative art,” fxhash COO Paul Viktor Schmidt told Decrypt. “We have always prioritized pushing the boundaries of what is possible, and will continue to do so.”"}, {"id": 25, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9nZW5lcmF0aXZlLXRlem9zLWFydC1wbGF0Zm9ybS1meGhhc2gtMTM0NTEyNTQ3Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 05 Jul 2023 07:00:00 GMT", "title": "Tezos Generative Art Platform fxhash Adds Ethereum in Multichain Push - Yahoo Finance", "content": "Often coined \"the art blockchain,\" <PERSON><PERSON><PERSON> has earned this reputation thanks to at least one key platform.\n\nNow, though, fxhash is set to also join the Ethereum ecosystem\n\nFxhash lets users create new iterations of generative artwork, with artists limiting the number of mints for their work. Collectors discover the specific iteration they've acquired at the reveal, adding a unique aspect to the process of collecting generative art.\n\nThe decision to expand fxhash to the Ethereum blockchain marks a significant step in the platform's evolution. Initially, fxhash chose the Tezos blockchain for its appealing proof-of-stake consensus, especially when Ethereum was still using proof-of-work.\n\nThe work of digital artist Zancan. Image: fxhash.\n\nHowever, with the upcoming launch of fxhash 2.0, the platform is set to become multichain, a move influenced by requests from its partners for expansion beyond Tezos.\n\nThe implementation of fxhash 2.0 aims to break down the barriers that currently exist between chains. At present, artists can often sell their artworks at significantly higher prices on Ethereum, which also offers greater visibility for artists.\n\n\"By bringing communities together and showing artworks side by side, regardless of the chain they were minted on, we aim to elevate generative art,\" <PERSON>, the creative director at fxhash, told Decrypt. “The chain doesn't matter. It's the art that matters.\"\n\n<PERSON>'s IN/Visible NFT Exhibition Highlights Black Artists, AI's ‘Skewed’ Lens\n\nThe platform was initially started by the artist <PERSON><PERSON><PERSON><PERSON>. As a generative artist, he felt difficult to find a platform that could properly showcase his work. This struggle led him to create fxhash.\n\nSince then, it has been highly community-focused.\n\nThis plays a significant role in decision-making processes, too, particularly with the upcoming fxhash 2.0.\n\n\"fxhash is the largest live repository of Generative Art, as such we can never stand still, but need to provide Artists with a wide variety of tools that we are continuously adding to in close communication with our community,” said <PERSON>. “fxhash 2.0 is a natural consequence of that, and something our team, our partners, the community, and most importantly artists are incredibly excited about.\"\n\nStory continues\n\n2 million pieces of art on fxhash\n\nThe platform boasts a diverse array of talented artists, including Zancan, William Mapan, Iskra Velitchkova, Melissa Wiederrecht, Andreas Rau, and Kim Asendorf.\n\nSome have gained significant popularity, with Zancan's \"Garden, Monoliths\" collection, for instance, racking up over 1.2 million XTZ in secondary market sales.\n\nAs of now, fxhash hosts around 8,000 artists or users who have minted at least one token, and it showcases two million pieces of generative art. The platform has facilitated the movement of approximately $40 million to $45 million, with the lion's share of this amount going to the artists.\n\nThe team at fxhash is currently hard at work on the Ethereum integration, with the launch of fxhash 2.0 slated for after the summer.\n\nThis will likely come with a UI and UX overhaul, too, and there are plans for events, both online and in-person, to celebrate the launch.\n\n\"Right now we've got our sights focused on an event happening in Berlin, which is related to the hackathon we've just started,” said Middleton. “As we get closer to the launch, we'll have a clearer idea of what we can plan.\"\n\nIn a world where the chain often matters more than the art, fxhash is striving to shift the focus back to where it belongs: on the artists and their creations.\n\n\"Our goal was always to build tools allowing artists and collectors to live out their passion for generative art,” fxhash COO Paul Viktor Schmidt told Decrypt. “We have always prioritized pushing the boundaries of what is possible, and will continue to do so.”"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3dpbGwtZXJjLTY1NTEtdHJhbnNmb3JtLWV0aGVyZXVtLW1ldGF2ZXJzZXMtd2l0aC1uZnQtb3duZWQtd2FsbGV0cy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 05 Jul 2023 07:00:00 GMT", "title": "Will ERC 6551 transform Ethereum metaverses with NFT-owned wallets? - CryptoSlate", "content": "The proposed ERC 6551 token standard for NFTs is generating interest among crypto enthusiasts, as it would enable digital assets to own other tokens, a development that could significantly transform the NFT and metaverse landscape.\n\nSpeaking to <PERSON> on a recent episode of the Unchained Podcast, <PERSON> and <PERSON><PERSON>, two of the authors of the Ethereum Improvement Proposal (EIP) for ERC 6551, stated this new standard allows NFTs to own other tokens, essentially equipping them with their own ‘wallets.’\n\nWhat is ERC 6551?\n\nThe token standard proposal, introduced in February, focuses on creating a system that assigns every ERC-721 (non-fungible token) a smart contract account. This will allow these tokens to own assets and interact with applications without changing existing ERC-721 smart contracts or infrastructure.\n\nThis system comprises two main components: a permissionless registry for deploying token-bound accounts and a standard implementation interface.\n\nThe registry will deploy a unique smart contract account for each ERC-721 token, allowing the token to interact with the blockchain, record transaction history, and own on-chain assets. Control of each token-bound account is delegated to the owner of the ERC-721 token, allowing the owner to initiate on-chain actions on behalf of their token.\n\nThe proposal seeks to be maximally backward compatible with existing non-fungible token contracts. It also uses EIP-155 chain IDs to uniquely identify ERC-721 tokens, allowing for optional support of multi-chain token-bound accounts.\n\nDeveloper reaction\n\nThe initial developer discussion for the proposal centered on potential security implications, such as the risk of duplicating registries and the need for trust verification. Suggestions included creating a registry for broad registration of account Implementations and making the proposal’s registry canonical due to its permissionless nature.\n\nThe proposal’s potential implications for reducing airdrop costs were also highlighted, along with a discussion that also raised security worries about adding metadata to the NFT registry.\n\nMore recently, with over 160 comments from Ethereum developers, concerns were raised about the proposal still being in the ‘Draft’ status, potentially subjecting it to significant changes. Finally, there were discussions about finalizing the naming of functions and the potential impacts on those implementing the EIP.\n\nBenefits of ERC 6551\n\nAs Giang explained on the Unchained Podcast, ERC 6551 was sparked by a simple query –\n\n“What if we had a project that was an NFT project that had a character, and sometimes we call this a PFP (Profile Picture) project, right? Our main question was like, why can’t you change the clothing or the aesthetic of this PFP character?”\n\nThis question led to the inception of the ERC 6551 standard, allowing NFTs to own other tokens, essentially giving them their own ‘wallets.’\n\nGiang further added,\n\n“Do you pursue the off-chain way where you build a database, and you have all this kind of centralization where you could render the NFT? Or you go the on-chain way, which is like every item an NFT can be applicable on-chain as a transaction. So through this kind of journey, as we were feeling in the dark in the last eight or nine months, we realized that there was a potential solution.”\n\nGiang and Windle also discussed the pros and cons of pursuing the on-chain or off-chain method for applying every item an NFT could own as a transaction.\n\nThey concluded that the ERC 6551 standard was a potential solution to the limitations presented by previous attempts to standardize NFTs owning assets, such as the necessity for custom logic in their smart contract. The ERC 6551 standard overcomes these restrictions, granting NFTs the same rights as Ethereum users, allowing them to own assets and take action.\n\nThe podcast guests also highlighted that while ERC 721, ERC 1155, and soul-bound tokens exist as ways to own items on Ethereum, ERC 6551 is not a token standard in the traditional sense because it gives every existing ERC 721 its own wallet, thus unlocking a new layer of compatibility for NFTs.\n\nUse cases of ERC 6551\n\nThe implementation of ERC 6551 has created exciting prospects across various industries, according to the EIP authors. It allows NFTs to own assets and perform actions autonomously, potentially beneficial in gaming, DAOs, infrastructure and tooling, and social networks.\n\nGiang mentioned several projects like ‘Sapiens,’ ‘Fuel Worlds,’ and ‘Parallel Trading Card Game,’ leveraging the standard for gamification. Giang commented, “It makes a lot of sense for a decentralized game inventory.” Such games, thus, allow characters to have their own wallets and act independently in the game.\n\nAccording to Giang, Decentralized Autonomous Organizations, or DAOs, such as ‘Station’ and ‘Dow House,’ are also exploring this standard to monitor engagement within their communities. Further, the infrastructure and tooling industry, represented by ‘Manifold,’ ‘Nosis Guild,’ and ‘Rabbit Hole,’ are reportedly working on modules around the standard.\n\nGiang suggested a future in which NFTs could become Network Playable Characters (NPCs), digital entities that can perform on-chain actions controlled by either a human or an AI model. This could potentially resolve the “empty world problem” observed in many digital worlds.\n\nThe ERC-6551 proposal, as explained by Jayden Windle, is simple.\n\n“A token-bound account, under this standard, is its own type of wallet. Thus, an NFT can have a unique wallet address that can own any assets — a concept that Windle describes ‘gets pretty fractal pretty quickly.”\n\nUnder this standard, a token-bound account is its own type of wallet. Therefore, an NFT can have a unique wallet address to own assets. However, it is important to note that while any Ethereum wallet can control an NFT’s wallet, the ownership of the token-bound account will always be owned by the NFT.\n\nIf approved, the advent and application of the ERC 6551 standard could mark a significant development, expanding the functionality of NFTs and enhancing their utility.\n\nMentioned in this article"}]