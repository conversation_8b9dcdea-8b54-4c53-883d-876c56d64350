[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy92aXRhbGlrLWJ1dGVyaW4tZGVjbGFyZXMtbm90LXN0YWtpbmctYWxsLWV0aC1hLXNtYWxsLXBvcnRpb27SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 02 Jul 2023 07:00:00 GMT", "title": "<PERSON><PERSON> declares he is not staking all of his ETH, merely a 'small portion' - Cointelegraph", "content": "Ethereum co-founder <PERSON><PERSON> stated that he does not stake all of his Ether (ETH) due to multisignature wallets being “complicated in a bunch of ways.”\n\nOn the June 29 episode of the Bankless podcast, <PERSON><PERSON><PERSON> revealed the “biggest reason” why he is only staking a small fraction of his ETH. He explained:\n\n“Because if you stake your ETH, the keys that access it have to be public on a subsystem that is online. For safety, it has to be a Multisig. Multisig for staking is still fairly difficult to set up; it gets complicated in a bunch of ways.”\n\n<PERSON><PERSON><PERSON> speaking on the Bankless podcast on June 29. Source: Bank<PERSON>, the co-founder of Ethereum and founder of Cardano, took to Twitter on June 30, stating he is “at a loss for words” after hearing <PERSON><PERSON><PERSON> only stakes a small portion of his Ether.\n\n<PERSON><PERSON><PERSON> added that “all” of his Cardano (ADA) is staked.\n\nI had to listen to this a few times. I'm just at a loss for words. All of our Ada is staked. Guess what that's how it's supposed to be for a properly designed Proof of Stake protocol. https://t.co/ye6va1DH06 — <PERSON> (@IOHK_Charles) June 29, 2023\n\n<PERSON><PERSON><PERSON> also discussed the EigenLayer protocol, which allows Ethereum validators and stakers to “re-stake” their assets onto other emerging networks.\n\nWhile it is only in its testnet phase and not expected to launch until the third quarter of 2023, <PERSON><PERSON><PERSON> said the main challenge is that it creates “centralization risks,” stating:\n\n“Trustworthy stakers would be valued more by the system than untrustworthy stakers. Trustworthy stakers are much less likely to actually get slashed.”\n\nSreeram Kaanan, the founder of EigenLayer, explained there are “complex risks” with restaking, and it is important to take a “constrained approach in building restaking.“\n\n“Constraints being what is really good for the ecosystem, and having constraints on building what new innovation can be unleashed based on this concept,” he stated.\n\nPanelists on the June 29 Bankless podcast episode. Source: Bankless\n\nRelated: Vitalik Buterin and Polygon co-founder to help send $100M toward COVID-19 research\n\nThis comes after Buterin stated in a June 9 blog that the Ethereum blockchain outright “fails” without sufficient scaling infrastructure to make transactions cheap.\n\nHe also noted another point of failure related to smart contract wallets.\n\nHe explained that a move to smart contract wallets has resulted in certain challenges arising due to the complexities associated with user experience when users take control of multiple addresses at once.\n\nMagazine: SEC calls ETF filings inadequate, Binance loses euro partner and other news: Hodler’s Digest, June 25–July 1"}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vY3J5cHRvZGFpbHkuY28udWsvMjAyMy8wNy9ldGhlcmV1bS1uYW1lLXNlcnZpY2UtaW50cm9kdWNlcy1sYXllci0yLWludGVyb3BlcmFiaWxpdHktZXRo0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 02 Jul 2023 07:00:00 GMT", "title": "Ethereum Name Service Introduces Layer 2 Interoperability $ETH - CryptoDaily", "content": "Ethereum Name Service (ENS) Labs has announced plans to introduce Layer 2 (L2) interoperability for its ENS domains during a community call on June 28.\n\nThe implementation aims to improve performance, scalability, and cost efficiency for users managing their ENS domains.\n\nThe decentralized domain name system is set to implement off-chain resolvers, leveraging an ENS off-chain registrar contract and the services of Coinbase’ cb.id, Lens Protocol, and OptiNames on the Optimism network. This step comes as the L2 ecosystem continues to witness an influx of projects and protocols.\n\nAccording to L2Beat data, the total value of assets locked on L2 service providers has surged by over 190% in the past year, standing at $9.78 billion at the time of writing. Arbitrum leads the market with a 60% share and a total value locked (TVL) of $5.87 billion.\n\nENS domain name registrations experienced robust growth in Q2 2021. Dune Analytics data shows monthly ENS domain name registrations totalling 79,463 last quarter. June, marked by a significant drop in gas fees on the Ethereum Network, recorded the highest number of registrations during the quarter with 35,932 ENS name registrations.\n\nWhile .eth name registrations ended Q2 with a surge, primary ENS name registrations saw a notable decline. A primary ENS name is a unique domain name registered and associated with an Ethereum address on the ENS platform. Moreover, ENS registered a cumulative profit of $4.8 million between April 1 and June 30, despite a 21% dip in revenue during this period. ENS’ annualized revenue has fallen by 5.11%, as per Token Terminal.\n\nThe daily price performance chart of ENS showed an increase in ENS token accumulation since mid-June, along with a corresponding 22% value growth. The Relative Strength Index (RSI) and Money Flow Index (MFI) indicators were on an upward trajectory, indicating a bullish run.\n\nThe Ethereum Name Service (ENS) plays a pivotal role in enhancing the user-friendliness of the Ethereum network by replacing complex blockchain addresses with easy-to-remember names. With the latest move of introducing Layer 2 interoperability, ENS Labs takes a significant stride towards improving the operational efficiency and cost-effectiveness of managing ENS domains.\n\nThis development arrives amidst a growing trend of Layer 2 adoption across the blockchain sphere, an effort to counteract the scalability and performance bottlenecks often associated with the Ethereum mainnet. By enabling Layer 2 solutions, ENS is not only keeping pace with industry trends but also reaffirming its commitment to user experience and network efficiency.\n\nDisclaimer: This article is provided for informational purposes only. It is not offered or intended to be used as legal, tax, investment, financial, or other advice."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiV2h0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3MvZXRoZXJldW0vZXRoZXJldW0tY28tZm91bmRlci1zdGFrZWQtZXRoLXByaWNlcy1iZWxvdy0yMDAwL9IBW2h0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3MvZXRoZXJldW0vZXRoZXJldW0tY28tZm91bmRlci1zdGFrZWQtZXRoLXByaWNlcy1iZWxvdy0yMDAwL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 02 Jul 2023 07:00:00 GMT", "title": "Ethereum Co-Founder Thinks The Over $40 Billion Staked ETH Can Be Stolen - NewsBTC", "content": "It appears that the co-founder of Ethereum, <PERSON><PERSON>, doesn’t trust the security of infrastructure allowing ETH staking. Consequently, in a recent interview, <PERSON><PERSON><PERSON> stated that he would only stake a limited amount of coins to ensure the network is distributed and remain robust against malicious agents who might try to take over the platform, reversing transactions.\n\n<PERSON><PERSON> Buterin Has Doubts On Ethereum Staking\n\nBut<PERSON><PERSON> has raised concerns about the potential risks of ETH staking through third-party infrastructure, specifically regarding the exposure of private keys and the danger it poses to his entire stake. He believes that implementing a multi-signature system could provide better protection. However, the current process is more difficult to set up, leading to his increased caution.\n\nIn a multi-sig system, users have their private key to sign transactions. A specific number of signatures must be provided to approve a transaction, which varies based on the Ethereum wallet’s configuration. This setup boosts security and reduces the risk of unauthorized access to funds.\n\nDuring the Bankless Podcast, the co-founder explains:\n\nProbably the biggest reason why I personally am not just staking all of my ETH, that I’m instead staking a fairly small portion, is because if you stake your ETH, it has to be all out, like the keys that access it have to be public on some system that’s online, and for safety, it has to be a multi-sig, and multi-sigs for staking are still fairly difficult to set up, and it gets complicated in a bunch of ways.\n\nETH Prices Stable Below $2,000\n\nHis remarks have generated a lot of discussion. Most critics are concerned about the entire security framework of Ethereum. After shifting from a proof-of-work to a proof-of-stake system, Ethereum relies on a network of validators who have to stake at least 32 ETH for a chance to approve a block of transactions and earn block rewards and transaction fees. These validators are also needed to secure the network; without them, the blockchain will be susceptible to attacks.\n\nAccording to on-chain data, there are over 643,000 validators spread across the globe who have staked over 20.5 million ETH. On average, each validator has staked 32.17 ETH. Notably, the validator count has steadily risen over the years, and the number of ETH staked has sharply increased despite the recent upgrade permitting stakers to unlock their coins.\n\nCharles Hoskinson, the founder of Cardano and one of the original co-founders of Ethereum, said he was “lost for words,” clarifying that all their ADA is staked as expected in a “properly designed proof-of-stake system.”\n\nAt the time of writing, ETH prices remain firm and weren’t affected by Buterin’s comments. However, the coin is yet to breach $2,000 and trends below April 2023 highs in early July 2023.\n\nFeatured image from Canva, chart from TradingView\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiggFodHRwczovL3d3dy5hbmFseXRpY3NpbnNpZ2h0Lm5ldC8yLWNyeXB0b3MtdGhhdC13aWxsLW9mZmVyLWhpZ2gtcmV0dXJucy10by10aGVpci1pbnZlc3RvcnMtaW4tMjAyMy1ldGhlcmV1bS1ldGgtYW5kLWlucXViZXRhLXF1YmUv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 02 Jul 2023 07:00:00 GMT", "title": "2 Cryptos That Will Offer High Returns to Their Investors in 2023: Ethereum (ETH) and InQubeta (QUBE) - Analytics Insight", "content": "There are strata within the crypto market, with the peak being “blue chip tokens.” Bitcoin (BTC) and Ethereum (ETH) fit into this category, with the latter predicted to offer even more returns in 2023. Alongside Ethereum (ETH), another emerging crypto to watch out for is InQubeta (QUBE). InQubeta leverages blockchain technology and intersects with AI, the two most disruptive technologies and fastest-growing industries in the world. Consequently, it has been touted as an investment opportunity to offer high returns in 2023, alongside Ethereum (ETH).\n\nInQubeta (QUBE): The utility token with immense growth potential\n\nInQubeta has something for everyone, whether they are adopters or investors. As the world’s first decentralized crowdfunding platform for AI startups, adopters in the form of AI developers can raise funds through its platform, while investors can invest in promising projects by purchasing stakes in them. On the other hand, its utility coin, QUBE, has significant growth potential and has been predicted to soar after launch. This makes its deflationary token a great investment opportunity for investors.\n\nWith its novel marketplace, AI startups will be able to mint investment opportunities and offer them on the marketplace to collect funding for their projects. Meanwhile, investors can buy equity-based NFTs, which provides them with a stake in startups and the profits that come from their success. InQubeta’s fractional investment approach allows for partial investments in companies and eliminates buy limits, allowing investors to pick their level of commitment.\n\nInQubeta’s native token, QUBE, will be vital to the ecosystem and will soar 30x after its launch, according to analysts and experts. Aside from its enormous potential growth, QUBE also provides holders with other incentives such as staking rewards and voting rights.\n\nWith the presale still ongoing and currently in stage 1, early adopters have ample opportunity to purchase the token at a cheap price. A QUBE token currently costs $0.00875, which is a great entry point as it is poised to skyrocket. To participate in the presale, click on the link below.\n\nEthereum (ETH) is in an uptrend to reclaim $2,000 as a support\n\nEthereum (ETH) is a decentralized open-source blockchain created to enable the development and execution of smart contracts and also serve as a platform for other cryptocurrencies. It is the second-largest cryptocurrency in the world and a blue-chip token. Through Ethereum (ETH), decentralized apps (dApps) and smart contracts can be deployed, making it crucial to the crypto and blockchain ecosystems.\n\nEthereum (ETH) has experienced a steady increase this year since its opening price of $1,196 on January 1. Although a long way from its all-time high of $4,891 recorded on November 16, 2021, Ethereum (ETH) has been in an uptrend in 2023 to reclaim old prices. With its current bullish momentum, Ethereum (ETH) is poised to break the $2,000 resistance. This makes Ethereum (ETH) a token to look out for in 2023, as its bullish indicators are strong and its return on investment promises to be high.\n\nConclusion\n\nEthereum (ETH) is integral to the crypto and blockchain ecosystems, meaning it will soar over time. Riding this bullish wave will be InQubeta, an innovative idea that cuts across crypto, blockchain, and AI. Its utility coin, QUBE, is poised to skyrocket, making it one to look out for in 2023 alongside Ethereum (ETH). To enjoy even greater returns and ride the bullish wave to its fullest, invest in the presale now at its early stage by clicking on the link below.\n\nVisit InQubeta Presale\n\nJoin The InQubeta Communities\n\nJoin our WhatsApp and Telegram Community to Get Regular Top Tech Updates"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiNWh0dHBzOi8vY29pbmdhcGUuY29tL2VkdWNhdGlvbi9idXktZXRoZXJldW0tb24tZXRvcm8v0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 02 Jul 2023 07:00:00 GMT", "title": "How to Buy Ethereum on eToro: A Guide for Beginners - CoinGape", "content": "Key Takeaways eToro is one of the best crypto exchange platforms to buy Ethereum. It offers user-friendly interface couple with features like industry-best security features.\n\nYou can buy Ethereum on eToro in 5 simple steps - creating an account, verifying identity, adding funds, searching for ETH, and place an order to finally buy Ethereum.\n\nWith its recent transition from a Proof-of-Work consensus mechanism to Proof-of-Stake, Ethereum aims to improve the efficiency of the network. It is often considered a blue-chip crypto investment with potential gains in the future. Here, we will walk you through the process to buy Ethereum on eToro. eToro is an exchange platform that makes the process of registering and buying assets simple within a few steps. Instead of getting stuck on a hard-to-navigate platform, you can easily buy Ethereum on eToro. Let us dive into the process of buying Ethereum on the eToro platform.\n\nHow to Buy Ethereum on eToro?\n\nStep 1 - Register and Create an account To buy Ethereum on eToro, it is necessary to register and create an account first. eToro makes the whole process of registration and creating an account easy and convenient for its users. First, you need to go to the eToro official website and then click on the “Join Now” button to create an account. 1 ETH to USD = $4044.366 0.41% (24h) Buy / Sell eToro Buy Crypto on an easy to use platform Invest in leading cryptocurrencies such as Bitcoin, Ethereum, Cardano and more… ETH btc\n\nbtc eth\n\neth xrp\n\nxrp bnb\n\nbnb sol\n\nsol ltc\n\nltc polygon\n\npolygon cardano\n\ncardano tron\n\ntron doge USD USD\n\nUSD INR\n\nINR JPY\n\nJPY CNY\n\nCNY GBP\n\nGBP EUR You need to enter some basic details like your name, email address and create a strong password. Once you ensure that all your details are correct, confirm and create the account.\n\nStep 2 - Login and Verify your Identity After registering, you can now use the same username and password to log in to your eToro account. Now, you need to verify your account by verifying your email address or by using text messaging. It is also important to verify your identity using your identity card and address proofs like utility bills and bank statements. Verifying your identity is a non-negotiable process to comply with KYC and AML guidelines. Moreover, you cannot trade assets without verifying your identity on eToro.\n\nStep 3 - Add Money to Your eToro Account As part of the next step, you need to deposit money into your eToro account. The eToro platform sports different kinds of payment methods. They include bank transfers, online payments, and card payments. You need to select the “Deposit funds” option and then select the payment method through which you would like to transfer money. After that, you need to enter payment details like bank account number, name, etc along with the deposit amount. Enter all the details and confirm the transaction to add money to your account.\n\nStep 4 - Search the Watch list for Ethereum Next, you need to go to the “Watchlist” section on the dashboard of your eToro account. In that, search for Ethereum in the search bar. You can find it easily as it is one of the most popular and second-largest cryptocurrencies by market capitalization. Now, when you select Ethereum, you can find a separate page. It shows all the information like market performance, historical details, and other information related to Ethereum. Select the “Buy” option and enter the details to buy Ethereum on eToro.\n\nStep 5 - Enter Payment Details and Buy Ethereum on eToro In the next step, you need to enter purchase details like how much money you wish to spend on Ethereum. Or, you can also select how many Ether coins you would like to buy. After entering these details, choose the “Open trade” option to continue buying Ethereum. Before that make sure all the details are correct and you have verified your identity on the platform. Confirm the trade to buy Ethereum on eToro and you will receive it into your account. You can go to the “portfolio” section to see the Ethereum you’ve bought on the platform. Buy Ethereum"}]