[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDcvMTcvY2Vsby1wcm9wb3Nlcy10by1kaXRjaC1vd24tc3RhbmRhbG9uZS1ibG9ja2NoYWluLWZvci1sYXllci0yLW5ldHdvcmstb24tZXRoZXJldW0v0gF-aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL3RlY2gvMjAyMy8wNy8xNy9jZWxvLXByb3Bvc2VzLXRvLWRpdGNoLW93bi1zdGFuZGFsb25lLWJsb2NrY2hhaW4tZm9yLWxheWVyLTItbmV0d29yay1vbi1ldGhlcmV1bS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Jul 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Proposes to Ditch Own Standalone Blockchain for Layer-2 Network on Ethereum - CoinDesk", "content": "The existential change could simplify liquidity sharing between Celo and Ethereum while boosting security and facilitating a seamless developer experience, according to the post. Celo is already compatible with the Ethereum Virtual Machine or EVM, meaning Ethereum developers can easily port over their existing apps or can develop new ones using many of the same tools."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vdS50b2RheS9ldGhlcmV1bS1zdGFibGVjb2luLWRldmVsb3Blci1ldGhlbmEtc3VycGFzc2VzLWVwaWMtbWlsZXN0b25l0gFRaHR0cHM6Ly91LnRvZGF5L2V0aGVyZXVtLXN0YWJsZWNvaW4tZGV2ZWxvcGVyLWV0aGVuYS1zdXJwYXNzZXMtZXBpYy1taWxlc3RvbmU_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Jul 2023 07:00:00 GMT", "title": "Ethereum Stablecoin Developer Ethena Surpasses Epic Milestone - U.Today", "content": "Ethena, a crypto start-up, has achieved a significant milestone by securing an impressive $6 million in seed funding, according to a report from Axios. The funding round, led by prominent investment firm Dragonfly, demonstrates growing interest in Ethena's cutting-edge stablecoin project.\n\nAdvertisement\n\nThus, in response to the challenges faced by previous stablecoin ventures, the start-up has developed a unique approach to ensuring its stablecoin, known as USDe, maintains a stable value relative to the U.S. dollar. By employing Ethereum derivatives and a hedging mechanism, Ethena aims to ensure that the value of USDe remains pegged to the dollar.\n\nThe difference\n\nThe key to this solution lies in the innovative use of collateral from users, allowing the platform to effectively hedge price exposure through shorting Ethereum via perpetual swaps. Through a balanced combination of staked ETH and derivatives, Ethena aims to neutralize any potential gains or losses, safeguarding the stability of USDe.\n\nThe start-up wants to distinguish itself from previous attempts at algorithmic stablecoins, such as the infamous Terra Luna. Moreover, Ethena's approach sets it apart from current stablecoin leaders USDC and USDT, which predominantly rely on traditional fiat-based assets like bills and bonds.\n\nThe success of Ethena's funding round is further amplified by the support it has garnered from prominent players in the cryptocurrency industry. Notable investors include big crypto exchanges and, additionally, <PERSON>. The founder of BitMEX and his family office have provided crucial backing to the project. This is especially noteworthy since <PERSON> himself has advocated for the development of an algorithmic stablecoin backed by Bitcoin derivatives, known as NakaDollar."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiNGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2NlbG8tcmV0dXJuLWhvbWUtZXRoZXJldW3SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Jul 2023 07:00:00 GMT", "title": "'Carbon-negative' <PERSON><PERSON> to 'return home' to Ethereum as layer-2 - Blockworks", "content": "The primary architect of the Celo blockchain, c<PERSON>abs, plans to “return home” as an Ethereum layer-2 network using the Optimism stack.\n\nc<PERSON><PERSON><PERSON> said the decision had come after months of research and initial discussions with stakeholders, alongside technical development in rollups. The team announced it Saturday during the Ethereum Community Conference in Paris.\n\n“It has become a viable path by which to allow Celo to align even more closely with Ethereum — by connecting trustlessly with it and leveraging its economic security,” cLabs said.\n\nIn an optimistic rollup, transactions are first processed off-chain and considered valid even before they’re settled on Ethereum mainnet, a process which happens in batches — hence the term “optimistic.”\n\ncLabs is also planning to incorporate EigenDA, a data availability layer sourced from re-staking project EigenLayer. The move would aim to reduce network storage expenses.\n\nThe team intends to design a decentralized sequencer for use by Celo’s existing validators. The network’s native token would still be used for governance. CELO, which started out on Ethereum as an ERC-20, has jumped 11% today to a $300 million market cap.\n\nCelo has lost about half of its market cap over the past year\n\nDetailed in the project’s governance forum, Berlin-headquartered cLabs said adopting a rollup model for <PERSON>lo could help the network grow.\n\nCelo mainnet, which is EVM-compatible, launched on Earth Day in 2020 — around two and a half years before Ethereum ditched proof of work for the less energy-intensive proof of stake. <PERSON><PERSON> says it’s carbon negative via an offset program with Wren, a subscription startup for reducing carbon footprints.\n\nThe upgrade will eventually come as a hard fork, the team said. End users shouldn’t notice if everything goes well. Transaction fees should remain consistent with no big changes for existing dApps, mobile-first features or its regenerative finance stack.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiPmh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1tb3ZlLTE5MDAv0gFCaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vYW5hbHlzaXMvZXRoL2V0aGVyZXVtLXByaWNlLW1vdmUtMTkwMC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Jul 2023 07:00:00 GMT", "title": "Ethereum Price Prepares For The Next Move As Risk of Bounce Grows - NewsBTC", "content": "Ethereum price failed to stay above $2,000 and corrected lower against the US Dollar. ETH could start a fresh rally if there is a move above $1,950.\n\nEthereum started a downside correction and tested the $1,900 level.\n\nThe price is trading below $1,950 and the 100-hourly Simple Moving Average.\n\nThere is a key bearish trend line forming with resistance near $1,930 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could start a fresh increase if it clears the $1,930 and $1,950 resistance levels.\n\nEthereum Price Could Restart Rally\n\nEthereum’s price rallied above the $1,970 resistance. ETH even climbed above $2,000 but failed to extend gains. A high was formed near $2,027 before there was a bearish reaction.\n\nThere was a move below the $1,950 support zone and the 100-hourly Simple Moving Average. The price even tested the $1,900 level. A low is formed near $1,900 and the price is now consolidating losses. It is trading near the 23.6% Fib retracement level downward move from the $2,027 swing high to the $1,900 low.\n\nEther is also trading below $1,950 and the 100-hourly Simple Moving Average. Besides, there is a key bearish trend line forming with resistance near $1,930 on the hourly chart of ETH/USD.\n\nOn the upside, immediate resistance is near the $1,930 level. The first major resistance is near the $1,950 zone, above which the price could rise toward the $1,975 resistance zone. It is close to the 61.8% Fib retracement level downward move from the $2,027 swing high to the $1,900 low.\n\nSource: ETHUSD on TradingView.com\n\nThe next major resistance is near the $2,000 level. Any more gains could send Ether toward the $2,050 resistance or even $2,120.\n\nMore Losses in ETH?\n\nIf Ethereum fails to clear the $1,930 resistance, it could a fresh decline. Initial support on the downside is near the $1,920 level.\n\nThe first major support is near the $1,900 level, below which the price might gain bearish momentum. The next major support is near the $1,850 support level. Any more losses could send Ether toward the $1,825 support level in the near term.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is losing momentum in the bearish zone.\n\nHourly RSI – The RSI for ETH/USD is now below the 50 level.\n\nMajor Support Level – $1,900\n\nMajor Resistance Level – $1,950\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vZGVjcnlwdC5jby8xNDg3NjAvZXRoZXJldW0tc2NhbGluZy1zb2x1dGlvbi16a3N5bmMtbGF1bmNoZXMtbGF0ZXN0LXByb3Zlci10ZWNoLWJvb2p1bdIBY2h0dHBzOi8vZGVjcnlwdC5jby8xNDg3NjAvZXRoZXJldW0tc2NhbGluZy1zb2x1dGlvbi16a3N5bmMtbGF1bmNoZXMtbGF0ZXN0LXByb3Zlci10ZWNoLWJvb2p1bT9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 17 Jul 2023 07:00:00 GMT", "title": "Ethereum Scaling Solution zkSync Unveils Latest Prover Tech ‘Boojum’ - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nzkSync is taking yet another step to decentralize.\n\nNamed after a mythical creature found in <PERSON>’s poem ‘The Hunting of the Snark,’ the team behind zkSync has launched the latest upgrade to the speedy layer-2 network.\n\nThe launch is in so-called mainnet shadow mode, per the team, as part of the launch's phased rollout. This mode is a testing zone that runs in parallel to the mainnet.\n\nzkSync is a novel scaling solution for Ethereum and one of the few that use zero-knowledge (zk) rollups to do so. Rollups come in two varieties: zk and optimistic. Both batch transactions off of the mainnet, roll them up into even smaller bundles, then those bundles are compressed into a proof, and settled on Ethereum.\n\nPart of that process involves a prover. This piece of technology is what does the compressing and packing of all those transactions. It's the cryptographic equivalent of a trash compactor, except it's not trash but potentially thousands of dollars in crypto activity.\n\nGiven the high computing power needed to generate those proofs, however, the barrier for users to participate is quite high.\n\nThe latest upgrade will address precisely this.\n\nCalled Boojum, which, per <PERSON>, is the most dangerous variety of Snark (itself a fictionalized creature), the tech will lower the hardware barrier to help secure zkSync.\n\nAD\n\nAD\n\nThe new prover can be operated with as little as 8 gigabytes of hardware, whereas the average prover demands roughly 500 gigabytes on average, said zkSync CEO <PERSON> <PERSON>lucho<PERSON>.\n\n“So far, we've seen the benchmark competitors indicating something like north of 500 gigabytes of RAM for a prover,” he told Decrypt. “And as you can only run it on the cloud, it's not ready for this [wide adoption]. Our prover requires only eight gigabytes of GPU RAM and it can run on GPUs that are compatible with gaming computers.”\n\nAlong with lowering the hardware demands, Boojum is also doing all its transaction compressing for much cheaper, too. Cutting costs here is crucial, even if it's only pennies.\n\n“Just because a single transaction costs $1 to produce, or even 10 cents, or even less than that, getting through hundreds of thousands of transactions per second would mean massive expanding, and you will probably not have enough of the hardware in the vulnerable clusters to produce and sustain this load,” said Gluchowski.\n\nInstead of several large data centers supporting a blockchain network, especially one executing such taxing computations as generating cryptographic proofs, the upgraded prover will make it accessible to anyone.\n\nJust like miners on Bitcoin and validators on Ethereum are paid for securing their networks, so too would provers on zkSync, he said.\n\nThe zkSync CEO even argued that it could be a viable way to repurpose the mining industry.\n\n“I personally don't think the proof of work will be a sustainable source of business,” he told Decrypt. “They will have to shift to something that actually provides normally redundant value, not like waste kind of work. Actually like doing some useful work.”\n\nAD\n\nAD\n\nWith this step in the decentralization step executed, Gluchowski said that the next step is to decentralize zkSync’s sequencer. This piece of a blockchain is responsible for ordering transactions in each block.\n\nExecuting this step also suggests a token launch in the future, he told Decrypt.\n\n“When you decentralized the sequencer, you will need some way to permissionlessly verify transactions,” said Gluchowski."}]