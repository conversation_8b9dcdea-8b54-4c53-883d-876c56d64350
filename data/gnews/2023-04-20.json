[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiR2h0dHBzOi8vd3d3Lm5ld3NidGMuY29tL2FuYWx5c2lzL2V0aC9ldGhlcmV1bS1wcmljZS1ub3NlZGl2ZXMtYmVsb3ctMmsv0gFLaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vYW5hbHlzaXMvZXRoL2V0aGVyZXVtLXByaWNlLW5vc2VkaXZlcy1iZWxvdy0yay9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Ethereum Price Nosedives Below $2K, Here’s The Next Bearish Target - NewsBTC", "content": "Ethereum price started a major decline below $2,000 against the US Dollar. ETH might extend its decline and revisit the $1,860 support.\n\nEthereum is gaining bearish momentum below the $2,000 support.\n\nThe price is trading below $2,000 and the 100-hourly Simple Moving Average.\n\nThere is a connecting bearish trend line forming with resistance near $1,955 on the hourly chart of ETH/USD (data feed via Kraken).\n\nThe pair could decline further if it breaks the $1,930 support zone.\n\nEthereum Price Takes Hit\n\nEthereum’s price struggled to climb further above the $2,125 resistance zone. ETH started a fresh decline below the $2,050 and $2,000 support levels, similar to Bitcoin.\n\nEther gained bearish momentum below the $2,000 support. It tested the $1,930 support. A low is formed near $1,933 and the price is now consolidating losses. It is now trading below $2,000 and the 100-hourly Simple Moving Average.\n\nImmediate resistance is near the $1,960 zone. There is also a connecting bearish trend line forming with resistance near $1,955 on the hourly chart of ETH/USD. The 23.6% Fib retracement level of the downward move from the $2,125 swing high to the $1,933 low is also just above the trend line.\n\nAn upside break above the trend line resistance might send Ethereum toward $2,000. The next major resistance is near the $2,025 zone or the 50% Fib retracement level of the downward move from the $2,125 swing high to the $1,933 low. A close above the $2,025 resistance zone could start a fresh increase.\n\nSource: ETHUSD on TradingView.com\n\nIn the stated case, the price could rise toward the $2,125 resistance. Any more gains could send Ether toward the $2,200 resistance in the near term.\n\nMore Losses in ETH?\n\nIf Ethereum fails to clear the $1,960 resistance, it could continue to move down. Initial support on the downside is near the $1,930 level. A downside break below the $1,930 support could spark bearish moves.\n\nThe next major support is near the $1,900 zone, below which ether price might decline toward $1,860. In the stated case, the price could revisit $1,820. Any more losses may perhaps send the price toward $1,780 in the coming days.\n\nTechnical Indicators\n\nHourly MACD – The MACD for ETH/USD is now gaining momentum in the bearish zone.\n\nHourly RSI – The RSI for ETH/USD is well below the 50 level.\n\nMajor Support Level – $1,930\n\nMajor Resistance Level – $1,960\n\nDisclaimer: The article is provided for educational purposes only. It does not represent the opinions of NewsBTC on whether to buy, sell or hold any investments and naturally investing carries risks. You are advised to conduct your own research before making any investment decisions. Use information provided on this website entirely at your own risk."}, {"id": 30, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhlcmV1bS1zdGFraW5nLXByb3ZpZGVyLXAycC1vcmctMTMwMTAyMDQ2Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Ethereum Staking Provider P2P.org Raises $23M Series A - Yahoo Finance", "content": "Non-custodial staking provider P2P.org has raised $23 million in Series A funding from investors, including Jump Crypto, Bybit, and Sygnum.\n\nFounded in 2018, P2P.org provides staking infrastructure for Ethereum and 49 other blockchain programs, including Cardano and Cosmos. The company's institutional staking solutions, including white label nodes and slashing insurance, have been used to stake more than $1.3 billion worth of assets, according to a press release. It plans to use the newly raised funds to expand its portfolio to include more chains in the Ethereum space, including Celestia, Sui, and Aleo.\n\nP2P.org also plans to expand its business model, focusing on building a stronger community, enhancing consumer support, improving customer experience, and elevating current infrastructure, according to a press release.\n\nEthereum Soars to 11-Month High Following Shanghai Upgrade\n\nP2P.org credits the successful round of funding to the recent Shanghai Upgrade, which it hopes will increase institutional interest in staking Ethereum. Institutional interest in staking on Ethereum has historically been lower compared to other blockchains, according to <PERSON>, founder of P2P.org. But the upgrade has already resulted in increased staking of Ethereum across the board.\n\nThough Ethereum is seeing more inflows, digital assets firm CoinShares reported institutional investors are still prioritizing investments in Bitcoin. More than $104 million was reported in Bitcoin inflows as of Monday. As of Thursday morning, Bitcoin is trading for $28,650.40, down 2% in the past 24 hours, according to CoinGecko.\n\nBitcoin Fund Inflows Top $100M in One Week as Investors 'Flee to Safety'\n\n“We are pleased to announce this fundraising round with our strategic investors,” Lomashuk said. “Now that the upgrade is complete and as the market continues to expand, we may expect ETH staking to grow significantly in market size.”\n\nSaurabh Sharma, Head of Investments at Jump Crypto, is optimistic about the opportunity. “We are excited to participate in this funding round and continue to leverage their cross-chain expertise,” Sharma said.\n\nStory continues\n\nBill Xing, Head of Financial Product of Bybit, said in the release that he thinks the P2P.org will enhance the mission of Web3, with the ultimate goal of putting control of the economy into people’s hands.\n\n“The third iteration of the internet was born with the promise of decentralization and democratization of finance,” he said. “We are proud to associate the Bybit name with P2P.org, the most trusted validator and infrastructure provider in the space. We look forward to elevating the space for believers in the digital economy together.”\n\nEthereum, at the time of writing, is trading at $1,841.33, down 2% in the past 24 hours, according to CoinGecko. This after Ethereum recently broke past $2,000 for the first time in months."}, {"id": 32, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9ldGhlcmV1bS1zdGFraW5nLXByb3ZpZGVyLXAycC1vcmctMTMwMTAyMDQ2Lmh0bWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Ethereum Staking Provider P2P.org Raises $23M Series A - Yahoo Finance", "content": "Non-custodial staking provider P2P.org has raised $23 million in Series A funding from investors, including Jump Crypto, Bybit, and Sygnum.\n\nFounded in 2018, P2P.org provides staking infrastructure for Ethereum and 49 other blockchain programs, including Cardano and Cosmos. The company's institutional staking solutions, including white label nodes and slashing insurance, have been used to stake more than $1.3 billion worth of assets, according to a press release. It plans to use the newly raised funds to expand its portfolio to include more chains in the Ethereum space, including Celestia, Sui, and Aleo.\n\nP2P.org also plans to expand its business model, focusing on building a stronger community, enhancing consumer support, improving customer experience, and elevating current infrastructure, according to a press release.\n\nEthereum Soars to 11-Month High Following Shanghai Upgrade\n\nP2P.org credits the successful round of funding to the recent Shanghai Upgrade, which it hopes will increase institutional interest in staking Ethereum. Institutional interest in staking on Ethereum has historically been lower compared to other blockchains, according to <PERSON>, founder of P2P.org. But the upgrade has already resulted in increased staking of Ethereum across the board.\n\nThough Ethereum is seeing more inflows, digital assets firm CoinShares reported institutional investors are still prioritizing investments in Bitcoin. More than $104 million was reported in Bitcoin inflows as of Monday. As of Thursday morning, Bitcoin is trading for $28,650.40, down 2% in the past 24 hours, according to CoinGecko.\n\nBitcoin Fund Inflows Top $100M in One Week as Investors 'Flee to Safety'\n\n“We are pleased to announce this fundraising round with our strategic investors,” Lomashuk said. “Now that the upgrade is complete and as the market continues to expand, we may expect ETH staking to grow significantly in market size.”\n\nSaurabh Sharma, Head of Investments at Jump Crypto, is optimistic about the opportunity. “We are excited to participate in this funding round and continue to leverage their cross-chain expertise,” Sharma said.\n\nStory continues\n\nBill Xing, Head of Financial Product of Bybit, said in the release that he thinks the P2P.org will enhance the mission of Web3, with the ultimate goal of putting control of the economy into people’s hands.\n\n“The third iteration of the internet was born with the promise of decentralization and democratization of finance,” he said. “We are proud to associate the Bybit name with P2P.org, the most trusted validator and infrastructure provider in the space. We look forward to elevating the space for believers in the digital economy together.”\n\nEthereum, at the time of writing, is trading at $1,841.33, down 2% in the past 24 hours, according to CoinGecko. This after Ethereum recently broke past $2,000 for the first time in months."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiN2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2xheWVyLTItZXRoZXJldW0tc2NhbGFiaWxpdHnSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Ethereum L2s Are a Bug Masquerading as a Feature - Blockworks", "content": "The Ethereum community has been raving about “layer-2” or “L2” solutions for quite some time now. These solutions are marketed as a way to make Ethereum faster, cheaper and more scalable.\n\nBut if you look at what is actually being built and why, you will see that the generally accepted L2 narrative isn’t telling the whole story. What layer-2 technology is actually doing is putting a Band-Aid over some specific issues on Ethereum — but not actually creating solutions that will solve any of Ethereum’s real problems.\n\nIf there’s one belief at the core of crypto, it’s that incentives matter; the motivations, often financial or reputational, that drive individuals or entities to promote or criticize a certain narrative.\n\nWhat are the incentives motivating the people behind L2s?\n\nThey stand to benefit from L2s being perceived as a feature rather than a bug.\n\nIf layer-2s were widely understood to be a Band-Aid, rather than a fix, this would negatively impact anyone even tangentially involved in the Ethereum space — and certainly anyone behind these solutions, or anyone who intends to leverage these solutions.\n\nA shift in perception that identifies L2s as an insufficient stopgap measure would likely encourage the community to prioritize finding and developing long-term solutions to Ethereum’s challenges, potentially leading to the emergence of more innovative and sustainable technologies.\n\nWhat Ethereum is doing wrong\n\nThe main problem with Ethereum is that it can be simply too expensive for people to use. <PERSON><PERSON> Buterin has freely admitted this.\n\nWe can actually keep this discussion fairly non-technical. L2s are a solution to a PROBLEM. Something is wrong with Ethereum, and that needs to be fixed. That’s just reality and it is FINE.\n\nThe reason why it is too expensive to use Ethereum is because the network charges gas (essentially, ETH) to use its resources. The more people use the network’s resources, the scarcer those resources become and the higher the cost of those resources in ETH. Of course, the price of ETH can go up as this happens, which obviously creates certain financial incentives. Simply put, high usage and high ETH fees benefit people who already hold ETH.\n\nThe problem then with Ethereum is less about the fact that gas fees exist, but instead that gas fees are TOO high. They’re so high that they are limiting the upside for the people who control the chain.\n\nIf gas fees are high because of limited network resources, then the solution is obvious; Increase network resources. L2s accomplish this by giving users a place to send their transactions instead of the main network (main net) where they can be processed. The results of those transactions can then be broadcast down into the main network, Ethereum.\n\nSimply put — L2s are a way of NOT using Ethereum. Why, then, is the L2 narrative one of the successful solving of Ethereum’s problems, rather than a critical look at this solution’s temporary nature?\n\nThe dominant approach in the crypto space has been to avoid speaking honestly about both the benefits and the limitations of the technologies we are developing.\n\nInstead, we act as though our existing solutions are perfect. Even though we can acknowledge when they need small improvements, it’s normal to see anyone who says anything more intensely critical as an enemy, someone uninformed who doesn’t know what they are talking about, someone just trying to pump their own bag.\n\nIncentives matter\n\nWhenever you see a very one-sided narrative being put forward, especially when there is a lot of money on the line, this is an indication that something isn’t right. There’s almost always two sides to every story, especially when that story is about a solution to a problem. It’s crucial to approach such narratives with a discerning eye and critically evaluate the motivations behind each side of any argument to develop a well-rounded understanding of the issue at hand.\n\nIn the context of Ethereum and layer-2 solutions, it’s important to keep in mind that different parties have their own incentives to either promote or criticize these technologies.\n\nOn the one hand, there are those who have a vested interest in promoting layer-2s as the ultimate solution to Ethereum’s scalability issues; these individuals are likely to be Ethereum proponents or developers involved in creating and maintaining L2 projects. On the other hand, there are those who stand to gain from criticizing Ethereum and its scaling solutions, such as competitors in the blockchain space or individuals who have invested in rival platforms.\n\nIf L2 solutions were seen as temporary workarounds rather than real solutions, users, developers, and investors might become less inclined to participate in or support layer-2 projects.\n\nAnd while this would obviously hinder the growth and development of L2s, this lack of confidence in the real L2 narrative could actually prompt individuals to seek out alternative solutions, including new blockchain platforms, that offer genuine fixes to scalability and other issues faced by the Ethereum network. In other words, acknowledging the truth would be more beneficial long term to Ethereum than denying it.\n\nThe point here is not that L2s and going off of Ethereum to do your transactions are bad ideas. But if no one talks about what L2s do accurately (and that’s obviously because there are no incentives to do so), then real solutions to Ethereum’s issues will not be found — because no one will even be looking for them.\n\nAndrew Levine is the CEO of Koinos Group, a team of industry veterans accelerating decentralization through accessible blockchain technology. Their foundational product is Koinos, a fee-less and infinitely upgradeable blockchain with universal language support.\n\nDon’t miss the next big story – join our free daily newsletter."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbWFnYXppbmUvZXRoZXJldW1zLWxheWVyLTItemstcm9sbHVwcy1jYW4tYmVjb21lLWludGVyb3BlcmFibGUv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Here's how Ethereum's ZK-rollups can become interoperable - Cointelegraph", "content": "The past few weeks have seen a wave of zero-knowledge proof project launches, including Polygon’s zkEVM and Matter Lab’s zkSync Era on mainnet, and the Linea zkEVM from ConsenSys on testnet.\n\nThey join StarkWare’s long-running StarkEx solution and its decentralized cousin StarkNet along with a variety of other projects in development from Polygon (Miden, Zero, etc.) and Scroll.\n\nThey all promise faster and cheaper transactions to scale Ethereum using zero-knowledge proofs.\n\nBut is the brutal competition between ZK-rollups a zero-sum game where there can be only one winner? Or are we looking at a future in which lots of different rollups are able to work in harmony and interoperably?\n\n<PERSON>, head of engineering for zkSync, thinks the latter future is much more likely and predicts that one day, no one will think about which ZK-rollup they are on because it’ll all just be infrastructure.\n\n“I think that if we don’t get to that world, then we’ve probably failed,” he says. “It’s the same way as somebody using Snapchat or Facebook doesn’t really have to know about TCP/IP or HTTP — it’s just the plumbing of the way the internet works.”\n\nBut how do we move from a bunch of competing sovereign rollups to an ecosystem of ZK solutions that are interoperable and composable?\n\nPeople are already starting to think about this question, and all of the ZK projects Magazine spoke to have plans to make their projects interoperable with at least some other rollups — although the extent to which that can happen likely depends on the development of standards and protocols.\n\nAlso read: Attack of the zkEVMs! Crypto’s 10x moment\n\nZero knowledge about ZK-rollups?\n\nIf you’re unfamiliar with the term “zero-knowledge proofs” — which StarkWare insists should be called “validity proofs” — they’re a way to scale Ethereum using cryptography. Rollups take the computation for tens of thousands of transactions off the main blockchain and write a tiny cryptographic proof back to Ethereum that proves the computation was carried out correctly.\n\n“Every proof we generate covers roughly 20,000 transactions and fits inside a single block of Ethereum,” explains StarkWare co-founder Eli Ben-Sasson.\n\nDespite this increase in transactions per block, zkSync’s Rose doesn’t think Ethereum can come close to scaling up to become the base layer for everything via a single rollup.\n\n“A ZK-rollup on its own will not scale to the world that we’re talking about,” Rose says. “If we think that applications with some interactions on the blockchain are providing value to hundreds of millions of people, the scalability problem is still there to be solved.”\n\nScaling is a little like internet bandwidth, in that the more you get, the more you realize you need. Back in 2017, Ethereum planned to scale using “Eth2” sharding. This roadmap was then ripped up after ZK-rollups emerged in 2018 and promised vastly greater scaling, but only if Ethereum upgraded the blockchain with a different form of sharding (proto danksharding and then danksharding) to enable the ZK-rollups to achieve higher throughput.\n\nEven then, Rose says it’s likely rollups will need to work in collaboration. “This is a big active area of research for us,” Rose says of interoperability. “As the systems mature as well… I think, naturally, this is kind of the pattern that these systems suggest.”\n\nEthereum scaling is some way off\n\nIt’s the early days yet for scaling, however. Although various solutions claim they can theoretically hit tens of thousands of transactions per second (or even talk about “unlimited” scaling), in practice, they’re hamstrung by data availability on Ethereum.\n\nAt present, between them, the various Ethereum scaling solutions and Ethereum are running at about 25 transactions per second (TPS). Ethereum itself has performed an average of about 12 TPS over the past month, Arbitrum One was at 7.2 TPS, Optimism at 2.65 TPS and zkSync at 1.6 TPS, according to ETHTPS.info.\n\nThese numbers move around a bit and are low mostly due to demand rather than capacity. StarkEx is not covered, but StarkWare tells Magazine it averaged 5 TPS over the past month.\n\nDespite supply outweighing demand so far, interoperability between rollups would already be helpful to ensure that users don’t get stuck in walled gardens. Optimistic Rollup users, for example, have to wait a week to withdraw funds, which rather limits interoperability.\n\nZK-rollups don’t have that limitation and can allow instant withdrawals (but don’t).\n\nAlso read: ZK-rollups are ‘the endgame’ for scaling blockchains: Polygon Miden founder\n\nInteroperable ZK-rollups are possible, but is it probable?\n\nBobbin Threadbare, founder of Polygon Miden, says interoperability between ZK-rollups is certainly technically possible, but “whether it will happen in practice is a different question.”\n\nHe explains that withdrawals aren’t instant yet because it’s not financially viable to put proofs on Ethereum that frequently, so transactions are fired off roughly every 10 or 20 minutes. As demand and throughput go up, this delay will become quicker and quicker.\n\n“And in that case, you get closer, closer and closer to this instant kind of movement between different places,” he says.\n\n“The second thing is that different rollups will have to have some kind of incentives to say, ‘Okay, let’s figure out how we can seamlessly move things from this to that.’”\n\nThreadbare adds, “Very fast interoperability between ZK-rollups is technically possible, but a) People need to agree on standards, and b) They need to actually implement these standards in their systems.”\n\n“And I think that’s a much, much more complicated thing to do.”\n\nInteroperability is not composability\n\nThere’s a difference between “interoperability” and “composability” — although people often use them interchangeably.\n\nInteroperability is easier and basically involves being able to move funds from one layer-2 (L2) solution to another. “By this definition, at least all of the rollups which share an L1 today already are interoperable!” notes Optimism co-founder Ben Jones.\n\nArbitrum’s Patrick McCorry also says that for basic interoperability, you can already send an asset from one rollup to another via Ethereum — it’s just slow.\n\n“Or you could have some off-chain solution, maybe like Hop protocol, where there’s someone in the middle who you give them the assets from StarkWare and then you take the assets to Scroll, and they provide some way to synchronize. So, there’s ways to do that,” he says.\n\nHop Protocol currently allows users to send funds between Ethereum, Polygon, Gnosis, Optimism and Arbitrum, though ZK-rollups aren’t currently supported. Connext offers a similar service, including BNB. A cross-chain DEX and bridge aggregator called Rango already connects StarkNet to other L2s.\n\n\n\nAlso read: Ethereum is eating the world — ‘You only need one internet’\n\nDeclan Fox, product lead for the ConsenSys Linea zkEVM, expects support will be added soon. “Many third-party bridge providers will continue to offer interoperability solutions for ZK-rollups,” he says, adding that bridges have drawbacks around trust and fees.\n\n“At Linea, we value open systems and interoperability highly. The Linea testnet has already integrated many of the leading bridging solutions for this reason. In the future, Linea will be able to trustlessly interoperate with any of the layer 3 off-chain systems deployed on top of the layer 2 through their validating bridges.”\n\n6/16) The case of two users exchanging value within an L1 is simple\n\n\n\nSimply scan the other user's QR code & press send, as long as they are also using ETH\n\n\n\nIn the case of L2s, this is not so simple, as the user now needs to know what L2 their friend is on & how to bridge between — Justin Bons (@Justin_Bons) April 10, 2023\n\nMetaMask Snaps might help\n\nAnother possibility for interoperability is via the browser wallet MetaMask. ConsenSys is in the midst of developing new crowdsourced wallet extensions called Snaps that projects can develop that extend the capabilities of MetaMask.\n\nMetaMask senior product manager Alex Jupiter says Snaps are still in the testing phase, “but if we imagine a future where you know Snaps is stable, developers can extend it in all manner of ways. Of course, the next step is to get these different Snaps talking to each other. So, one ZK-rollup can talk to another ZK-rollup, right? And that’s part of the vision of Snaps, and yeah, we want to make that world possible.”\n\nOne Snap that has been demoed already enables MetaMask users to control Bitcoin via their Ethereum wallet, so getting ZK-rollups talking to each other certainly seems achievable.\n\n“Who knows where bridging is gonna go in the future as well. I’m not an expert on ZK-rollups, but I don’t think there’s a core technical limitation of that being a problem in the future.”\n\nMessari slide highlighting “composable rollup ecosystems with shared infrastructure.”\n\nZK-rollups and composability\n\nComposability is the ability to initiate a transaction that involves operations on more than one different rollup. Jones calls it “a stronger form” of interoperability “where chains can do more than just communicate asynchronously with each other but actually have transactions, which are aware of the state of each chain in some more ‘real-time’ manner (think cross-chain flash loans).”\n\nThis is likely to require the development of new standards and protocols, and Rose says that the sooner this happens the better.\n\n“It is a strictly better user experience if teams can build through an interface, and we can attempt to have more standardization. I think there is appetite for some of this standardization as well, and I do think we will see more of it as these systems mature.”\n\nFox says that “to get to a point where we have synchronous composability, there will need to be a globally sequenced and ordered set of transactions across the different off-chain systems. This is theoretically possible with ZK-rollups thanks to SNARKs [a type of ZK proof] where, for example, a common sequencer could offer a UX of unified execution and pooled liquidity,” he says.\n\n“Imagine making a DeFi trade where parts of the trade are executed on different chains for optimal liquidity all within the same transaction.”\n\nBase layer advertisement from Coinbase. (Coinbase)\n\nOptimistic about the Superchain\n\nOne potential coordination method might be Optimism’s Superchain concept, which it announced at the same time Coinbase unveiled its base layer-2 fork of Optimism.\n\nOptimism is an Optimistic Rollup, which is another way to scale Ethereum, though more limited in potential throughput. According to the announcement:\n\n“The Superchain seeks to integrate otherwise siloed L2s into a single interoperable and composable system.”\n\nJones tells Magazine, “There is no silver bullet,” but there are a couple of requirements for interoperability and composability the Superchain aims to address:\n\nShared Sequencing: “To have a system where you can do a cross-chain flash loan, at the very least, at the time when that transaction is being processed, it needs to be included in both of the chains reliably. This requires some notion of sequencers being able to communicate, merge or otherwise network together.”\n\nSeparation of Proving and Execution: “Different applications have different security requirements, and those security requirements impose different kinds of restrictions on what interoperability properties can be achieved. By de-coupling the computation of chain state from the proving of cross-chain messages, we can maximize the interoperability of applications without fragmenting them to other chains.”\n\nHe says the Superchain can connect optimistic and ZK-rollups as well as other chains, providing a shared, modular “standard for all these innovations to happen on.”\n\n“It is going to be far easier to make these chains interoperate when they are built on the same codebase, compared to interoperating chains, which were written separately from the ground up,” he says.\n\nHowever, underscoring Threadbare’s point about political issues being more complicated than technical issues, Arbitrum CEO Steven Goldfeder dismissed the concept out of hand.\n\n“The notion that we’re going to sort of coalesce on one particular technology stack — a technology stack that’s not even built out today, that doesn’t have the core features that make it a layer 2 or make it a rollup — the notion that we do that is, I think, a bit presumptuous,” he told The Defiant.\n\nWhy connect ZK-rollups with Optimism?\n\nAnd Arbitrum is built using Optimistic Rollups. It might be even harder to convince ZK-rollups with their higher potential throughput, to coordinate via Optimism. To some it might seem like connecting fiber optic cables together with copper wire.\n\nAll the L2s make this claim though (Coinbase)\n\nHowever, Optimism is laying the groundwork to incorporate ZK proofs (validity proofs) in its systems with the Bedrock upgrade, and the Superchain will take this idea even further. “Compatibility there is the goal,” says Jones.\n\nOther potential coordination methods are the Inter-Blockchain Communication Protocol from Cosmos or “modular blockchain” Celestia (though the latter seems to be trying to replace Ethereum as the data availability layer).\n\nBut ZK-rollups could also connect directly with each other.\n\nPolygon ZK-rollups will be interoperable\n\nPolygon has a variety of flavors of ZK-rollup possible in development. They include Polygon Miden (similar to StarkNet), the Polygon zkEVM (compatible with existing EVM projects), Zero (recursive scaling) and Nightfall (Optimistic Rollups meet zero-knowledge cryptography).\n\nThreadbare says that coordinating internally to hook up Polygon’s ZK solutions is easier than coordinating with outside projects, and he believes the technical challenges are doable. The team is working on the LX-LY bridge to enable this interoperability already.\n\n“Because we are all part of the same company, then the technical integration becomes much easier to solve,” he says. “Moving between these rollups will be super, super simple.”\n\n“The friction, it’s not two separate chains or three separate chains. It doesn’t appear like that. It’s just one Polygon that settles on Ethereum. And moving assets or funds or tokens between these different environments is super, super straightforward and easy. That’s the end game.”\n\nEthereum is eating the world. Metaphorically that is.\n\nStarkEx and StarkNet\n\nStarkWare’s Ben-Sasson says they are building similar interoperability between StarkEx and StarkNet.\n\n“Yeah, definitely. We’re gonna be porting the StarkEx systems to be layer 3s over at StarkNet, and, at some point, for them to be solutions on top of StarkNet. That’s definitely the plan,” he says.\n\nBack in 2020, StarkWare released a blog laying out its plans for interoperability, but Ben-Sasson says that has been superseded. StarkWare’s Cairo is a Turing-complete language and virtual machine, which makes it similar in capability to a general-purpose computer.\n\n“A good analogy is to think of a layer 2 or a layer 1 as some computer that is just a bit slower than your laptop, but it has a lot of integrity and safety,” he says. “So, you can start just connecting these computer programs in various ways. Just like today, computers talk to each other and inter-operate or compose.”\n\nTo get computers to talk to each other over the internet, a set of standards like TCP/IP and HTTP were developed. Ben-Sasson agrees that’s the likely path for connecting validity-proof rollups, too.\n\nCointelegraph explainer on STARKs v SNARKs\n\nPerhaps ZK-rollups can connect direct\n\nStarkNet isn’t working on standards like that at present, but Ben-Sasson suggests there may be other paths to interoperability. He says smart contracts can be written to interpret the different types of incompatible proofs used by different rollups. StarkNet uses STARKs as the name suggests; zkSync uses SNARKs, for example, while Polygon Zero uses recursive SNARKs called PLONKs.\n\n“Someone already wrote on StarkNet a smart contract that allows you to verify a Groth 16 SNARK,” he says.\n\nZKPs might disrupt the design of the blockchain execution layer. Why bother with specialized languages, when you can just submit a proof of any computation in any language? — Jake Brukhman (@jbrukh) April 15, 2023\n\nThis means the two rollups can communicate directly.\n\n“As long as you can, in chain one, verify the proofs of chain two, you can start having interoperability. StarkNet is already able to verify STARKs, and now also Groth 16 SNARKs, and I’m pretty sure that very soon, we’ll have things like, you know, PLONKs and Plonky and other kinds of systems.”\n\n“So, at least in StarkNet, it should be relatively straightforward to be able to prove things happened correctly in other chains, and you can start having interoperability.”\n\nFox tells me separately that Linea’s system “is already using the EVM to verify proofs (Groth16, PlonK, etc.) in a smart contract,” which he says can make it interoperable with L3s.\n\nBen-Sasson says it seems likely that StarkNet would be able to connect to different rollups directly.\n\n“You can do it directly. You can do it because it’s a general-purpose computer and because of the validity rollup nature, right, that you can just have these systems talking to each other.”\n\nSo, it sounds like the future is interoperable and composable.\n\n“Yes, it definitely is interoperable and composable. Yes. Definitely.”"}]