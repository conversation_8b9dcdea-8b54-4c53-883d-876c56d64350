[{"id": 10, "url": "https://news.google.com/rss/articles/CBMiVmh0dHBzOi8vdW5jaGFpbmVkY3J5cHRvLmNvbS9wcmUtbWluZS1ldGhlcmV1bS1hZGRyZXNzLW1vdmVzLTExNi1taWxsaW9uLWFmdGVyLTgteWVhcnMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 18 Jul 2023 07:00:00 GMT", "title": "Pre-Mine Ethereum Address Moves $116 Million After 8 Years - Unchained - Unchained", "content": "A wallet address with 61,216 ETH has transferred the entire balance to crypto exchange Kraken after eight years of inactivity.\n\nBlockchain data tracker <PERSON><PERSON> flagged a transaction on Monday from a pre-mine address that had been inactive for close to a decade. The wallet in question held 61,216 ETH worth around $116 million at the time of the transfer.\n\n💤 💤 💤 💤 💤 💤 💤 💤 💤 💤 A dormant pre-mine address containing 61,216 #ETH (116,396,127 USD) has just been activated after 8.0 years!https://t.co/f79T0fYa7b — <PERSON><PERSON> (@whale_alert) July 18, 2023\n\nThe wallet first made a small transfer of 0.05 ETH to crypto exchange Kraken, which was likely a test transaction before sending a larger amount. Shortly after, the user transferred the wallet’s entire balance to the exchange, prompting some market participants to speculate that millions of dollars’ worth of sell pressure was due to hit the market for ETH.\n\nPre-mined ETH was issued as a reward to Ethereum’s early supporters, including those who funded the project during its Initial Coin Offering (ICO). The network started off with a supply of 72 million ETH, of which 83% was distributed to those who purchased the cryptocurrency in a crowd sale conducted in July and August 2014.\n\nThe crowd sale participants sent a total of 31,000 BTC to a designated Bitcoin address in exchange for an Ethereum wallet address to which the ETH they purchased would be distributed to after the network’s official launch.\n\nAccording to blockchain data analysed by CoinDesk, 6,600 transactions were sent to the Bitcoin address, although the consensus among industry watchers is that the true number of crowd sale participants is far smaller, with big players likely splitting their purchases into multiple wallets. The average purchase price of ETH amounted to $0.30 per coin for those that took part.\n\nWhile most early buyers sold off their coins as the price of ETH rose significantly over the years, the remaining pre-mine addresses were thought to be staunch supporters of the cryptocurrency or those that lost their private keys and could no longer access their wallets.\n\nIf the wallet’s owner does intend to sell, he or she will be cashing out on what is likely one of the largest returns on an initial investment in history."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9ldGhlcmV1bS13aGF0LTEwYi13b3J0aC1ldGgtYnVybmVkLW1lYW5zLWZvci10aGUtbmV0d29yay_SAVNodHRwczovL2FtYmNyeXB0by5jb20vZXRoZXJldW0td2hhdC0xMGItd29ydGgtZXRoLWJ1cm5lZC1tZWFucy1mb3ItdGhlLW5ldHdvcmsvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 18 Jul 2023 07:00:00 GMT", "title": "Ethereum: What $10B worth ETH burned means for the network - AMBCrypto News", "content": "The value of ETH burned since 2021 approached $10 billion.\n\nGas usage on the Ethereum network dropped due to low activity.\n\nSince the implementation of Ethereum Improvement Proposal 1559 (EIP-1559), an upgrade aimed at improving the network’s transaction fee mechanism, Ethereum [ETH] has witnessed a significant amount of ETH being burned.\n\nRead Ethereum’s [ETH] Price Prediction 2023-2024\n\nThe mechanism, which reduces the supply of ETH, has resulted in the burning of nearly $10 billion worth of ETH tokens, Dune Analytics revealed.\n\nCoping with the hope\n\nImplemented in August 2021, the Ethereum team developed EIP-1559 as one of the London Hark Fork Improvement Proposals. This happened alongside EIP-3554, 3198, 3529, and EIP-3541.\n\nAll these developments happened in preparation for its transition to Proof-of-Stake (PoS). For EIP-1559, the objective was to get rid of the previous fee market mechanism relating to its main gas fee calculation.\n\nWhile many users had hoped that the development would reduce gas fees on the network, it didn’t. Instead, it brought about a discrete base fee, aimed at prioritizing transactions when validating blocks.\n\nFurther information from Dune, the analytics platforms showed that projects like Uniswap [UNI], Circle [USDC], and NFT marketplace OpenSea, played vital roles in the increase.\n\nAt press time, nearly 300,00 ETH each had been burned via Uniswap and OpenSea. And the reason for this is clear. Uniswap maintained its position as the leading Decentralized Exchange (DEX).\n\nSo, a lot of ETH swaps with other tokens influenced its rise. For OpenSea, its place as the number on the Ethereum-based marketplace puts it in the aforementioned positions. As for USDC, its position as the favored stablecoin in DEXes helped up its rank.\n\nGas usage falls\n\nHowever, Ethereum gas used had decreased as of this writing. According to Santiment, the ETH gas used was 16.05 billion. Used gas usage spikes when there’s a lot of activity on the network.\n\nAnd this demand for ETH causes a rise in gas prices. So, the fall in usage reflects a relatively less busy period for the Ethereum network.\n\nOn looking at the network growth, the on-chain data provider revealed that the metric had decreased sharply. Typically, network growth measures the rate of adoption and influx of new users into a network.\n\nSo, when the network growth increases, it means that a project has impressive traction. However, when the metric decreases, it implies that utilization is low. And this is usually accompanied by low liquidity.\n\nIn conclusion, Ethereum’s burn mechanism has relatively addressed the network’s concerns around transaction fees.\n\nAlso, the substantial amount of ETH burned also demonstrates the demand and usage of the Ethereum network, as well as the effectiveness of creating a more deflationary ecosystem. Whether it will improve or not, time will tell."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vZGVjcnlwdC5jby8xNDkwNjMvdml0YWxpay1idXRlcmluLWV4cGxhaW5zLWhvdy1ldGhlcmV1bS1wbGFucy10by1tYWtlLWNyeXB0by13YWxsZXRzLWFzLXNpbXBsZS1hcy1lbWFpbNIBdWh0dHBzOi8vZGVjcnlwdC5jby8xNDkwNjMvdml0YWxpay1idXRlcmluLWV4cGxhaW5zLWhvdy1ldGhlcmV1bS1wbGFucy10by1tYWtlLWNyeXB0by13YWxsZXRzLWFzLXNpbXBsZS1hcy1lbWFpbD9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 18 Jul 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Explains How Ethereum Plans to Make Crypto Wallets as Simple as Email - Decrypt", "content": "Your Web3 Gaming Power-Up Enjoy exclusive benefits with the GG Membership Pass\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nModern account abstraction is “really elegant” because it doesn’t require changes to the underlying protocol like other upgrades before it, said Ethereum co-founder <PERSON><PERSON> at the Ethereum Community Conference (ETHCC) in Paris.\n\nDevelopers have been working on account abstraction, or different iterations of it, since 2015, even before Ethereum was launched. The idea is to switch from Externally Owned Wallets, or EOAs, to smart contract-based wallets. If they pull it off, managing a crypto wallet would become as easy as managing an email account.\n\nAD\n\nAD\n\nThat would mean users could potentially recover their seed phrase—the private key used to sign trasnactions—as easily as they can reset the password on an email account.\n\nThe latest version is EIP-4337 (Ethereum Improvement Proposal 4337), also known as Account Abstraction Using Alt Mempool. The Ethereum (ETH) upgrade would allow users to create non-custodial wallets as programmable smart contracts. This would unlock a number of features, such as easy wallet recovery, signless transactions–which translates into lower transaction fees–and team wallets (also known as multisignature wallets).\n\nAccording to <PERSON><PERSON><PERSON>, the upgrade could be one of the driving catalysts for Web3 adoption around the world. “One of the key properties we want blockchains to have is that they give you money before you register,” he said.\n\nThe idea, he said, is for users to be able to receive any token, like a stablecoin, that isn’t Ethereum with their smart contract wallet and be able to pay the gas fees without having to converthold ETH.\n\nIn order to allow for these types of wallets and transactions to be broadcasted, the latest account abstraction upgrade would enable the use of “paymasters,” which allow users to pay for gas fees with whatever token they are transacting with.\n\nEIP-4337 also incorporates signature aggregators—which allow for multiple signers to join together, and only one gets used in a transaction.\n\nAD\n\nAD\n\nButerin claimed today that “this is a pretty big deal,” especially in rollups, due to the outsized footprint of a signature on these types of layer 2 solutions. Ethereum layer 2 scaling solutions, like Arbitrum or Optimism, batch transactions together and verify them off the Ethereum mainnet. Account abstraction would allow for signature aggregation. In simple terms, that would allow for more data compression, which translates into cheaper computation, and according to the Ethereum co-founder, “will reduce costs by 86 times.”\n\nThis is not the only Ethereum upgrade currently in the works. Proto-danksharding, or EIP-4884 is also underway. It’s quickly become one of the main focuses of development on the network because it sets the foundation for a new data type which will drastically reduce costs and make data usage more efficient."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vY29pbnRlbGVncmFwaC5jb20vbmV3cy9jcnlwdG8tc3VydmV5LWZpbmRzLTQ3LW9mLWludmVzdG9ycy1leHBlY3QtZXRoZXJldW0tdG8tc3VycGFzcy1iaXRjb2lu0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 18 Jul 2023 07:00:00 GMT", "title": "Crypto survey finds 47% of investors expect Ether to 'surpass' Bitcoin - Cointelegraph", "content": "Fidelity Digital Assets released a “Q2 2023 Signals Report” on July 18, which claimed that Ether’s outlook for the next 12 months and the long term is positive. Year-to-date, Ether (ETH) has gained 62%, but while the investment firm might be short-term bullish on Ether, that does not mean it believes that the month-long bullish channel will be sustained.\n\nWhile institutional investors like Fidelity Digital Assets may have a bullish longer-term vision for ETH's price, let’s compare their analysis against network and market data to see if they’re on the money.\n\nEther/USD 1-day price index. Source: TradingView\n\nBeyond the technical indicators, the rationale behind Fidelity’s bullish outlook for Ether is the network’s higher burn rate versus coin issuance, the “new address momentum” and a growth in the number of network validators.\n\nFidelity “Q2 2023 Signals Report,” July 18. Source: Fidelity Digital Assets\n\nAccording to the Fidelity report, the net issuance since the Merge in September 2022 resulted in a net supply decrease of more than 700,000 Ether. Additionally, the analysts claim that Glassnode data showing an increasing number of Ethereum addresses that transacted for the first time ever proves healthy network adoption.\n\nThe report also points to a 15% increase in the number of active Ethereum validators in the second quarter.\n\nThe expectation around EIP-1153 is also building momentum for the Ethereum network, as the “transient storage opcode” improves smart contract efficiency, reduces costs and amplifies the Ethereum Virtual Machine design. The change is especially meaningful for decentralized exchanges (DEXs), where Ethereum’s dominance declined to 46% from 60% six months prior, according to DefiLlama data.\n\nDencun upgrade expected to reduce transaction costs\n\nAnother potentially bullish factor for the Ethereum network is the anticipated upgrade on the leading DEX, Uniswap. According to a July 17 presentation at the Ethereum Community Conference, the upcoming Uniswap v4 will allow users to build unlimited types of pools using programmable buttons (hooks), native ETH support and a singleton contract that performs internal transactions before settling final balances.\n\nThe announcement fueled the likelihood that EIP-1153 will be included in the next “Dencun” upgrade, which triggered Slingshot and DeFi Pulse co-founder Scott Lewis.\n\ni had missed the news that uniswap labs got eip1153 into cancun.\n\n\n\nthey kept the details for wanting eip1153 secret from the community, only publishing v4 only after inclusion was finalized.\n\n\n\ninformal governance captured by an insider-aligned, for-profit monopoly. sad day. — scottlewis.canto➕ (@scott_lew_is) June 13, 2023\n\nIf approved, the implementation will be vital for the Ethereum network to recoup the market share lost due to high gas fees, as the seven-day average transaction cost has been above $4 since February. Consequently, Ethereum’s total value locked has dropped to its lowest level since April 2020, at 13.55 million ETH, according to DefiLlama.\n\nMoreover, decentralized application activity has dwindled, as shown by DappRadar’s unique active wallets’ 30-day data: Uniswap, minus 28%; 1inch Network, minus 14%; MetaMask Swap, minus 8%; and OpenSea, minus 5%. As a comparison, in the same period, BNB Smart Chain’s PancakeSwap gained 10%, and Polygon’s Uniswap users increased 8%.\n\nDerivatives metrics remain flat\n\nEther quarterly futures have been signaling unease among professional traders. Those fixed-month contracts typically trade at a 5% to 10% premium compared to spot markets to compensate for the delayed settlement, a situation known as contango.\n\nEther 3-month futures premium. Source: Laevitas\n\nAccording to data from Laevitas, the Ether three-month futures premium currently stands at 4%, which is below the neutral threshold and lower than the 5.5% level seen on July 14. This indicator is clear evidence that traders are less inclined to use leverage for bullish ETH positions.\n\nMore concerningly, Ether’s 59% gains year-to-date might have caused investors to become overly optimistic. A recent survey from CryptoVantage of 1,000 North Americans that invested in cryptocurrencies over the past five years found that 46% named Ether as the top contender to surpass Bitcoin (BTC).\n\nRelated: Bitcoin rally will lead to \"speculative blow-off top” in 2024, Mark Yusko predicts\n\nCoins with the best chances of surpassing Bitcoin. Source: 2023 CryptoVantage survey\n\nThis is a somewhat startling point of view, but it could be misleading since the survey did not ask whether any coin would eventually flip Bitcoin, so respondents don’t necessarily place strong odds on this outcome.\n\nFidelity’s analysis has given valid reasons for why the firm is bullish on Ether’s 12-month price performance, but in the shorter term, the recurrent high gas fees and lack of interest from leverage buyers signal increased odds of the Ether price breaking below the channel support.\n\nThis article is for general information purposes and is not intended to be and should not be taken as legal or investment advice. The views, thoughts, and opinions expressed here are the author’s alone and do not necessarily reflect or represent the views and opinions of Cointelegraph."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiaWh0dHBzOi8vdGhlY3J5cHRvYmFzaWMuY29tLzIwMjMvMDcvMTgvZXRoZXJldW0tZGV2ZWxvcGVycy1hcmUtZGlzY3Vzc2luZy10aGUtcGxhbi1mb3ItdGhlLWRlbmN1bi11cGdyYWRlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 18 Jul 2023 07:00:00 GMT", "title": "Ethereum Developers Are Discussing The Plan For The Dencun Upgrade - The Crypto Basic", "content": "Not that long ago, the Ethereum blockchain underwent the Shanghai and Capella upgrades; the former impacted ETH’s execution layer, while the latter targeted the Consensus Layer. The network upgrades seem to have pushed Ethereum’s price higher, attracting a lot of investors (and speculators) in the cryptocurrency market. If you haven’t considered this asset class in the past, it’s high time you take ETH seriously. As you learn more about how to buy Ethereum, you’ll find that it’s better to purchase tokens on an exchange; it’s your responsibility to pay the gas fees.\n\nEthereum’s developers have proven they’re committed to steadily improving the network. The much-awaited Shapella Upgrade is live, and ETH has many more upgrades in the pipeline to enhance scalability, security, and sustainability. Surprising or not, developers are preparing for the upcoming upgrade on the Ethereum Mainnet, the Dencun upgrade, that will reduce transaction fees, expected in late 2023. If all goes as hoped, it will enable new use cases. According to <PERSON>, Protocol Support for the Ethereum Foundation, the developers are in the final stages of planning for Dencun, having a provisional set of EIPs.\n\nWhat’s New in The Recent Ethereum Upgrade?\n\nThe Dencun upgrade combines two upgrades, namely Deneb (the Execution Layer upgrade) and Cancun (the Consensus Layer upgrade). Some of the main improvement proposals considered for inclusion are:\n\n- Advertisement -\n\nEIP-4844 . Also referred to as Proto-Danksharding, EIP-4844 introduces temporary “data blobs” to the Beacon Chain for a short period of time. The format will be fully compatible with that used in full sharding. This proposal will lower the gas cost of Layer 2 transactions considerably. The new transaction format, i.e., the blob, is contained within the Prismatic Client, but not the execution environment.\n\n. Also referred to as Proto-Danksharding, EIP-4844 introduces temporary “data blobs” to the Beacon Chain for a short period of time. The format will be fully compatible with that used in full sharding. This proposal will lower the gas cost of Layer 2 transactions considerably. The new transaction format, i.e., the blob, is contained within the Prismatic Client, but not the execution environment. EIP-6780 . It deactivates the SELF DESTRUCT opcode, which allows for creating and erasing contracts within a single transaction. The issue of not knowing what storage to delete doesn’t occur. The operation sends all the ETH in an account to the caller, excluding when SELF DESTRUCT is called within the same transaction a contract was created.\n\n. It deactivates the SELF DESTRUCT opcode, which allows for creating and erasing contracts within a single transaction. The issue of not knowing what storage to delete doesn’t occur. The operation sends all the ETH in an account to the caller, excluding when SELF DESTRUCT is called within the same transaction a contract was created. EIP-1153 . This proposal brings about two new opcodes, namely TSTORE and TLOAD, which enable storage to be used provisionally. Transient storage is more affordable since it doesn’t require disk access and allows better smart contract designs. Use cases include single-transaction ERC-20 approvals and re-entry locks.\n\n. This proposal brings about two new opcodes, namely TSTORE and TLOAD, which enable storage to be used provisionally. Transient storage is more affordable since it doesn’t require disk access and allows better smart contract designs. Use cases include single-transaction ERC-20 approvals and re-entry locks. EIP-6475. Simple Serialize types are introduced to represent Optional [T] values. SSZ structures with optional values can be represented with the idiomatic types of the underlying programming languages. Using simple serialize encoding, developers guarantee transaction formats are forward-compatible.\n\nBesides the EIPs that are formally part of the Dencun upgrade, client teams have proposals that might be included. Let’s take EIP-2537 as an example. It focuses on the BLS12-381 curve, used for signatures within Ethereum 2.0. There’s no denying that Dencun is an essential step in Ethereum’s development, but let’s not forget that it’s still a work in progress.\n\nEthereum Is Undergoing a Period of Tremendous Transformation\n\nPeople are becoming increasingly comfortable with blockchain technology, and the more they use it, the more they rely on it. As Ethereum advances, we continue to see more changes; it’s hard, if not impossible, to anticipate its next move. Over the years, ETH has managed to adapt as new ideas have emerged from research and development, positioning itself as a powerful contender in the cryptocurrency industry. It faces regular updates meant to improve its technical capabilities, mainly realized with hard forks. Once the Dencun upgrade is live, Ethereum will undergo three critical updates: the Verge, the Purge, and the Splurge.\n\nThe Verge upgrade attempts to reduce the amount of data that validators must store on their machines; it will introduce Verkle trees for vector commitments and stateless protocols to make the Ethereum network more lightweight. As highlighted by Vitalik Buterin, it will be possible to launch nodes without having hundreds of gigabytes on your computer. The Purge upgrade is expected to remove legacy transactions and data from the post-Merge era when ETH relied on the Proof of Work consensus mechanism. Nodes will require less storage and bandwidth to sync with the network. What is more, the Purge will introduce sharding, used to disperse data among the nodes for effective management of big data.\n\nFinally, yet importantly, the Splurge upgrade aims to boost scalability and efficiency via rollups, a type of Layer 2 solutions that perform transaction execution outside the blockchain. It can be defined as a series of smaller upgrades striving to ensure the network runs as smoothly as possible. In other words, the Splurge combines several improvements that weren’t included in the earlier upgrades. By the time the Verge, the Purge, and the Splurge are over, Ethereum will be different from what we know today.\n\n- Advertisement -\n\nWrapping It Up\n\nFor the time being, it’s impossible to determine how the Dencun upgrade will impact the Ethereum network. From experience, we know that upgrades are applauded by the cryptocurrency community, so a surge in ETH deposits isn’t out of the ordinary. It’s highly unlikely there will be a decline in daily activity, but maybe there won’t be as many NFTs traded on the Ethereum network. While not quite as big as the Merge, the Dencun upgrade constitutes a significant change to ETH; the first testnet fork should be announced soon. There’s still room for other changes to the network, and only time will tell what EIPs developers will include.\n\nAs far as the timing of the Dencun upgrade is concerned, it’s impossible to make a rough estimate until the scope is finalized. Ethereum is still below the $2000 level it reached in April this year, and median gas fees hit a 12-month high due to the resurgence of meme coins. The fast rise of the pepecoin (PEPE) has turned into profit, as those undeterred by the warnings transact on Ethereum, helping the network earn more money. As gas fees increase, more tokens are burnt, which translates into the fact that Ethereum is a deflationary asset. Analysts, nevertheless, are concerned about pepecoin ownership."}]