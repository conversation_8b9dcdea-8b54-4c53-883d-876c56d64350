"""Adapted from https://github.com/ysymyth/ReAct/blob/master/alfworld.ipynb"""

import os
# 禁用代理（仅对当前 Python 脚本）
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)

import sys
import json
import yaml
import openai
import numpy as np
import time
import importlib
import webbrowser
import re
from datetime import datetime
from utils import Model, get_chat
from eth_env import ETHTradingEnv
from env_history import EnvironmentHistory
from concept_learning import ConceptLearner
from performance_report import PerformanceReport
from llm_prompt_updater import LLMPromptUpdater
# 导入区块链相关模块
from Send_data_to_eth import Agent_events
from agent_receive_eth_data import AgentListener

from typing import List, Dict, Any, Tuple

def llm(prompt, model, seed):
    try:
        text = get_chat(prompt=prompt, model=model, seed=seed)  # stop_strs=['\n']
        return text
    except Exception as e:
        print(prompt)
        print(e)
        import sys
        sys.exit(1)


def extract_action_amount(response):
    """从响应中提取交易动作和交易数量"""
    action = "hold"  # 默认动作
    amount = 0.0     # 默认数量

    # 尝试匹配 "ACTION: [action] AMOUNT: [amount]" 格式
    action_pattern = r"ACTION:\s*(buy|sell|hold)\s*AMOUNT:\s*([0-9]*\.?[0-9]+)"
    match = re.search(action_pattern, response, re.IGNORECASE)

    if match:
        action = match.group(1).lower()
        amount = float(match.group(2))

        # 确保数量在有效范围内
        if amount < 0:
            amount = 0.0
        elif amount > 1:
            amount = 1.0

    return action, amount

def debug_print(s, response=None, title='', role=None, cur_date=None, actual_date=None, run_name=None, action=None, amount=None):
    # 确保run_name有值
    if run_name is None:
        run_name = 'eth_run'  # 默认值

    # 日志路径
    log_path = f'{run_name}/experiments.log'

    # 构造日志内容
    log_content = f'\n*** START {title} ***\n{s}\n'
    if response is not None:
        log_content += f'*** {title} RESPONSE ***\n{response}\n'
    log_content += f'*** END {title} ***\n'

    # 打印到终端
    print(log_content)

    # 写入主日志文件
    with open(log_path, 'a') as log_file:
        log_file.write(log_content)

    # 保存 response 为 JSON 文件（如果提供了必要参数）
    if response is not None and role is not None and cur_date is not None:
        # 保存目录
        json_base_dir = f'{run_name}/json_logs'
        date_dir = os.path.join(json_base_dir, cur_date)
        os.makedirs(date_dir, exist_ok=True)  # 自动创建日期目录

        # 构建文件名，例如：ONCHAIN_ANALYST.json
        filename = f'{role}.json'
        json_path = os.path.join(date_dir, filename)

        # 如果未提供action和amount，尝试从response中提取
        if action is None or amount is None:
            extracted_action, extracted_amount = extract_action_amount(response)
            if action is None:
                action = extracted_action
            if amount is None:
                amount = extracted_amount

        # 准备JSON数据（不包含prompt）
        json_data = {
            "response": response
        }

        # 添加日期信息（如果提供）
        if actual_date:
            json_data["date"] = actual_date

        # 添加交易动作和数量（如果提供或提取）
        if action:
            json_data["action"] = action
        if amount is not None:
            json_data["amount"] = amount

        # 写入 JSON 文件
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        # 创建每日汇总文件
        if actual_date:
            # 创建或更新每日汇总文件
            summary_filename = f'DAILY_SUMMARY_{actual_date}.json'
            summary_path = os.path.join(date_dir, summary_filename)

            # 读取现有汇总文件（如果存在）
            summary_data = {}
            if os.path.exists(summary_path):
                try:
                    with open(summary_path, 'r', encoding='utf-8') as f:
                        summary_data = json.load(f)
                except:
                    summary_data = {}

            # 更新汇总数据
            summary_data[role] = {
                "action": action,
                "amount": amount
            }

            # 写入汇总文件
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)

        # 上传到区块链（如果提供了角色且启用了区块链功能）
        # 从args中获取use_blockchain参数，默认为0（禁用）
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'args' in frame.f_locals and hasattr(frame.f_locals['args'], 'use_blockchain'):
                use_blockchain = frame.f_locals['args'].use_blockchain
                break
            frame = frame.f_back
        else:
            # 如果找不到args，默认禁用区块链功能
            use_blockchain = 0

        if role and use_blockchain:
            try:
                # 将角色名称转换为与decotrade一致的格式
                role_mapping = {
                    "ONCHAIN_ANALYST": "onchain_analyst_agent",
                    "FACTUAL_NEWS_ANALYST": "factual_news_analyst_agent",
                    "SUBJECTIVE_NEWS_ANALYST": "subjective_news_analyst_agent",
                    "REFLECTION_ANALYST": "reflection_analyst_agent",
                    "RISK_MANAGER": "risk_manager_agent",
                    "BULLISH_TRADER": "bullish_trader_agent",
                    "BEARISH_TRADER": "bearish_trader_agent",
                    "FINAL_TRADER": "final_trader_agent"
                }

                agent_name = role_mapping.get(role, role.lower() + "_agent")

                # 确保角色目录存在
                os.makedirs(f"{agent_name}", exist_ok=True)

                # 创建区块链交互实例
                agent = Agent_events(agent_name)

                # 上传JSON数据到IPFS
                ipfs_hash = agent.Upload_json_to_ipfs(json_data)
                print(f"已将{role}的数据上传到IPFS，哈希值: {ipfs_hash}")

                # 将IPFS哈希上报到区块链
                tx_receipt = agent.report_to_EAAC(ipfs_hash, agent_name)
                print(f"已将{role}的IPFS哈希上报到区块链，交易哈希: {tx_receipt.transactionHash.hex()}")

                # 返回交易收据
                return tx_receipt
            except Exception as e:
                print(f"上传到区块链时出错: {e}")
                return None
        elif role:
            # 区块链功能已禁用，打印提示信息
            print(f"区块链功能已禁用，跳过{role}数据的上传")

def eth_run(env, base_prompt, memory, starting_state, args):
    to_print = args.to_print
    model = args.model
    seed = args.seed
    run_name = args.run_name

    # Initialize cycle data collection for CVRF and LLM prompt update
    cycle_data = {
        'actions': [],
        'daily_returns': [],
        'risk_metrics': [],
        'agent_reasoning': []
    }

    # 获取LLM函数
    from utils import get_chat
    llm_function = lambda prompt: get_chat(prompt=prompt, model=args.model, seed=args.seed)

    if len(memory) > 3:
        env_history = EnvironmentHistory(base_prompt, starting_state, memory[-3:], [], args)
    else:
        env_history = EnvironmentHistory(base_prompt, starting_state, memory, [], args)
    if to_print:
        print_state = {k: v for k, v in starting_state.items() if k != 'news'}
        debug_print(print_state, None, 'STATE')
    cur_step = 0
    returns = []
    done = False

    # 打印交易模拟开始提示
    if to_print:
        print(f"\n{'*'*30}")
        print(f"开始交易模拟，从{env.get_current_date() if hasattr(env, 'get_current_date') else '未知日期'}开始")
        print(f"{'*'*30}\n")

    while not done:
        use_news = args.use_news
        use_reflection = args.use_reflection
        use_risk = getattr(args, 'use_risk', True)
        price_s, factual_news_s, subjective_news_s, reflection_s, risk_s, template_bullish, template_bearish, template_s = env_history.get_prompt()

        # 打印当前步骤信息
        current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
        if to_print:
            print(f"\n{'#'*20} 交易日 {current_date} - 步骤 {cur_step} {'#'*20}\n")

        # 链上分析师生成内容并上传到区块链
        onchain_analysis = llm(price_s, model, seed).strip()
        onchain_action, onchain_amount = extract_action_amount(onchain_analysis)
        if to_print:
                tx_onchain = debug_print(price_s, onchain_analysis, f'STEP {cur_step} - ONCHAIN ANALYST',
                role='ONCHAIN_ANALYST', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                action=onchain_action, amount=onchain_amount)

        if use_news:
            # MCP功能已移除，使用原有方法进行新闻分析
            current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None

            # 事实新闻分析并上传到区块链
            factual_news_analysis = llm(factual_news_s, model, seed).strip()
            factual_action, factual_amount = extract_action_amount(factual_news_analysis)
            if to_print:
                tx_factual = debug_print(factual_news_s, factual_news_analysis, f'STEP {cur_step} - FACTUAL NEWS ANALYST',
                    role='FACTUAL_NEWS_ANALYST', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                    action=factual_action, amount=factual_amount)

            # 主观新闻分析并上传到区块链
            subjective_news_analysis = llm(subjective_news_s, model, seed).strip()
            subjective_action, subjective_amount = extract_action_amount(subjective_news_analysis)
            if to_print:
                tx_subjective = debug_print(subjective_news_s, subjective_news_analysis, f'STEP {cur_step} - SUBJECTIVE NEWS ANALYST',
                    role='SUBJECTIVE_NEWS_ANALYST', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                    action=subjective_action, amount=subjective_amount)
        else:
            factual_news_analysis = 'N/A'
            subjective_news_analysis = 'N/A'

        if use_reflection:
            reflection = llm(reflection_s, model, seed).strip()
            reflection_action, reflection_amount = extract_action_amount(reflection)
            if to_print:
                current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
                tx_reflection = debug_print(reflection_s, reflection, f'STEP {cur_step} - REFLECTION ANALYST',
                    role='REFLECTION_ANALYST', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                    action=reflection_action, amount=reflection_amount)
        else:
            reflection = 'N/A'

        # Risk management analysis
        current_state = env_history.get_current_state()
        if use_risk and 'risk' in current_state and current_state['risk'].get('warning'):
            risk_analysis = llm(risk_s, model, seed).strip()
            risk_action, risk_amount = extract_action_amount(risk_analysis)
            if to_print:
                current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
                tx_risk = debug_print(risk_s, risk_analysis, f'STEP {cur_step} - RISK MANAGER',
                    role='RISK_MANAGER', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                    action=risk_action, amount=risk_amount)
        else:
            risk_analysis = 'No significant risk detected. Continue with normal trading strategy.'

        # 看涨交易员决策 - 接收链上分析、事实新闻分析和主观新闻分析
        bullish_trader_prompt = template_bullish.format(onchain_analysis, factual_news_analysis, subjective_news_analysis, reflection)
        bullish_trader_response = llm(bullish_trader_prompt, model, seed).strip()
        bullish_action, bullish_amount = extract_action_amount(bullish_trader_response)
        if to_print:
            current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
            tx_bullish = debug_print(bullish_trader_prompt, bullish_trader_response, f'STEP {cur_step} - BULLISH TRADER',
                role='BULLISH_TRADER', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                action=bullish_action, amount=bullish_amount)

        # 看跌交易员决策 - 接收链上分析、事实新闻分析和主观新闻分析
        bearish_trader_prompt = template_bearish.format(onchain_analysis, factual_news_analysis, subjective_news_analysis, reflection)
        bearish_trader_response = llm(bearish_trader_prompt, model, seed).strip()
        bearish_action, bearish_amount = extract_action_amount(bearish_trader_response)
        if to_print:
            current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
            tx_bearish = debug_print(bearish_trader_prompt, bearish_trader_response, f'STEP {cur_step} - BEARISH TRADER',
                role='BEARISH_TRADER', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                action=bearish_action, amount=bearish_amount)

        # 最终交易员决策 - 加入风险管理建议
        # 修改提示词，要求最终交易员说明决策理由和依据哪位代理的报告
        final_trader_prompt = template_s.format(bullish_trader_response, bearish_trader_response, risk_analysis)
        final_trader_prompt += "\n\nPlease clearly state in your answer the reasons for making this trading decision and which agent (bullish trader, bearish trader, or risk manager)'s report you primarily relied on."

        final_trader_response = llm(final_trader_prompt, model, seed).strip()
        final_action, final_amount = extract_action_amount(final_trader_response)

        # 提取决策理由
        decision_reason = final_trader_response

        if to_print:
            current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
            tx_final = debug_print(final_trader_prompt, final_trader_response, f'STEP {cur_step} - FINAL TRADER',
                role='FINAL_TRADER', cur_date=str(cur_step), actual_date=current_date, run_name=run_name,
                action=final_action, amount=final_amount)

        state, reward, done, info = env.step(final_trader_response)
        raw_action = info['raw_action']
        actual_action = f"{info['actual_action']:.1f}"
        # 添加分析师结果到环境历史
        if use_news:
            env_history.add("factual_news_analysis", factual_news_analysis)
            env_history.add("subjective_news_analysis", subjective_news_analysis)

        # 添加交易员结果到环境历史
        env_history.add("bullish_trader_response", bullish_trader_response)
        env_history.add("bearish_trader_response", bearish_trader_response)
        env_history.add("final_trader_response", final_trader_response)
        env_history.add("action", actual_action)
        env_history.add("state", state)
        returns.append(state['today_roi'])

        # Collect data for CVRF
        cycle_data['actions'].append(info['actual_action'])
        cycle_data['daily_returns'].append(state['today_roi'])
        if 'risk' in state:
            cycle_data['risk_metrics'].append(state['risk'])
        cycle_data['agent_reasoning'].append(final_trader_response)

        # 检查是否需要更新提示词（每个周期结束时）
        cycle_length = getattr(args, 'cycle_length', 7)
        if (cur_step + 1) % cycle_length == 0:
            print(f"\n{'='*50}")
            print(f"周期结束，检查是否需要更新提示词...")
            print(f"{'='*50}\n")

            # 应用CVRF更新
            if getattr(args, 'use_cvrf', 0):
                # 初始化概念学习器（如果尚未初始化）
                if not hasattr(args, 'concept_learner'):
                    args.concept_learner = ConceptLearner(
                        learning_rate_base=getattr(args, 'learning_rate', 0.2),
                        run_name=run_name
                    )

                # 记录周期数据
                args.concept_learner.record_cycle_data(
                    cycle_id=cur_step // cycle_length,
                    actions=cycle_data.get('actions', []),
                    daily_returns=cycle_data.get('daily_returns', []),
                    risk_metrics=cycle_data.get('risk_metrics', []),
                    agent_reasoning=cycle_data.get('agent_reasoning', [])
                )

                # 检查是否有足够的数据生成概念
                if cur_step >= cycle_length:
                    # 如果是第一个周期，使用同一个周期的数据
                    if not hasattr(args, 'previous_cycle_data'):
                        # 保存当前周期数据作为上一个周期
                        args.previous_cycle_data = {
                            'actions': cycle_data['actions'][-cycle_length:],
                            'daily_returns': cycle_data['daily_returns'][-cycle_length:],
                            'risk_metrics': cycle_data['risk_metrics'][-cycle_length:],
                            'agent_reasoning': cycle_data['agent_reasoning'][-cycle_length:]
                        }
                        print("保存当前周期数据作为基准")
                    else:
                        # 准备当前周期数据
                        current_cycle_data = {
                            'actions': cycle_data['actions'][-cycle_length:],
                            'daily_returns': cycle_data['daily_returns'][-cycle_length:],
                            'risk_metrics': cycle_data['risk_metrics'][-cycle_length:],
                            'agent_reasoning': cycle_data['agent_reasoning'][-cycle_length:]
                        }

                        # 生成概念，比较当前周期和上一个周期
                        print("生成概念，比较当前周期和上一个周期")
                        concepts = args.concept_learner.generate_concepts_from_data(
                            cycle_data1=args.previous_cycle_data,
                            cycle_data2=current_cycle_data,
                            llm_function=llm_function
                        )

                        # 保存概念到文件
                        concepts_path = os.path.join(run_name, f'concepts_day_{cur_step}.json')
                        with open(concepts_path, 'w') as f:
                            json.dump(concepts, f, indent=2, ensure_ascii=False)

                        # 应用概念到智能体提示词
                        if concepts:
                            print("应用生成的概念到智能体提示词...")

                            # 获取原始智能体提示词（概念替换策略：每次都从原始提示词开始）
                            # 这确保了不会累积历史概念，每次只应用最新的概念
                            current_prompts = {}

                            # 修复：使用env_history获取当前提示词，而不是从文件系统读取
                            print("获取当前智能体提示词...")
                            try:
                                # 创建临时的env_history实例来获取提示词
                                temp_env_history = EnvironmentHistory(
                                    base_query="",
                                    start_state=env_history.get_current_state(),
                                    memory=[],
                                    history=[],
                                    args=args
                                )

                                # 获取所有提示词
                                prompts = temp_env_history.get_prompt()
                                price_s, factual_news_s, subjective_news_s, reflection_s, risk_s, template_bullish, template_bearish, template_s = prompts

                                # 映射提示词到智能体名称
                                current_prompts = {
                                    "onchain_analyst_agent": price_s,
                                    "factual_news_analyst_agent": factual_news_s,
                                    "subjective_news_analyst_agent": subjective_news_s,
                                    "reflection_analyst_agent": reflection_s,
                                    "risk_manager_agent": risk_s,
                                    "bullish_trader_agent": template_bullish,
                                    "bearish_trader_agent": template_bearish,
                                    "final_trader_agent": template_s
                                }

                                print(f"成功获取 {len(current_prompts)} 个智能体的提示词")

                            except Exception as e:
                                print(f"获取智能体提示词时出错: {e}")
                                # 使用默认提示词作为备选方案
                                current_prompts = {
                                    "onchain_analyst_agent": "You are an onchain analyst responsible for analyzing cryptocurrency price data and technical indicators.",
                                    "factual_news_analyst_agent": "You are a factual news analyst responsible for analyzing cryptocurrency news objectively.",
                                    "subjective_news_analyst_agent": "You are a subjective news analyst responsible for analyzing market sentiment from news.",
                                    "reflection_analyst_agent": "You are a reflection analyst responsible for analyzing past trading decisions.",
                                    "risk_manager_agent": "You are a risk manager responsible for monitoring and managing trading risks.",
                                    "bullish_trader_agent": "You are a bullish trader responsible for analyzing the market and providing bullish trading advice.",
                                    "bearish_trader_agent": "You are a bearish trader responsible for analyzing the market and providing bearish trading advice.",
                                    "final_trader_agent": "You are the ultimate decision-maker, responsible for synthesizing opinions from all parties to make the final transaction decision."
                                }
                                print("使用默认提示词作为备选方案")

                            if current_prompts:
                                # 手动计算比较指标（因为generate_concepts_from_data内部已经包含了比较逻辑）
                                # 计算周期1的指标
                                cycle1 = args.previous_cycle_data
                                cycle1_total_return = sum(cycle1.get("daily_returns", []))
                                cycle1_risk_warnings = sum(1 for r in cycle1.get("risk_metrics", []) if r and r.get("warning", False))

                                # 计算周期2的指标
                                cycle2 = current_cycle_data
                                cycle2_total_return = sum(cycle2.get("daily_returns", []))
                                cycle2_risk_warnings = sum(1 for r in cycle2.get("risk_metrics", []) if r and r.get("warning", False))

                                # 计算动作重叠百分比
                                actions1 = cycle1.get("actions", [])
                                actions2 = cycle2.get("actions", [])
                                min_len = min(len(actions1), len(actions2))
                                if min_len > 0:
                                    overlap_count = sum(1 for i in range(min_len) if np.sign(actions1[i]) == np.sign(actions2[i]))
                                    overlap_percentage = overlap_count / min_len
                                else:
                                    overlap_percentage = 0

                                # 构建比较指标
                                comparison_metrics = {
                                    "overlap_percentage": overlap_percentage,
                                    "return_difference": abs(cycle2_total_return - cycle1_total_return),
                                    "risk_warning_difference": abs(cycle2_risk_warnings - cycle1_risk_warnings),
                                    "better_cycle": 0 if cycle1_total_return > cycle2_total_return else 1
                                }

                                # 应用概念到提示词
                                updated_prompts = args.concept_learner.update_agent_prompts(
                                    agent_prompts=current_prompts,
                                    comparison=comparison_metrics
                                )

                                # 保存更新后的提示词到临时文件（用于下一个交易日）
                                print("保存更新后的提示词到temp文件...")
                                saved_files = 0
                                for agent_name, updated_prompt in updated_prompts.items():
                                    temp_prompt_path = os.path.join(run_name, f'temp_{agent_name}_prompt.txt')
                                    try:
                                        with open(temp_prompt_path, 'w', encoding='utf-8') as f:
                                            f.write(updated_prompt)

                                        # 验证文件是否包含概念
                                        if "Important:" in updated_prompt:
                                            print(f"✅ {agent_name}: temp文件已保存，包含概念指令")
                                            saved_files += 1
                                        else:
                                            print(f"⚠️ {agent_name}: temp文件已保存，但未包含概念指令")

                                    except Exception as e:
                                        print(f"❌ 保存 {agent_name} 的temp文件时出错: {e}")

                                print(f"✅ 已应用概念到 {len(updated_prompts)} 个智能体的提示词")
                                print(f"✅ 成功保存 {saved_files} 个包含概念的temp文件")

                                # 记录概念应用成功的日志
                                concept_application_log = {
                                    "timestamp": datetime.now().strftime("%Y-%m-%d_%H-%M-%S"),
                                    "cycle_day": cur_step,
                                    "concepts_generated": len(concepts),
                                    "agents_updated": len(updated_prompts),
                                    "temp_files_saved": saved_files,
                                    "concepts": concepts
                                }

                                concept_log_path = os.path.join(run_name, f'concept_application_day_{cur_step}.json')
                                with open(concept_log_path, 'w', encoding='utf-8') as f:
                                    json.dump(concept_application_log, f, ensure_ascii=False, indent=2)
                                print(f"概念应用日志已保存到: {concept_log_path}")

                            else:
                                print("❌ 警告: 无法获取当前智能体提示词，跳过概念应用")
                                # 记录失败日志
                                failure_log = {
                                    "timestamp": datetime.now().strftime("%Y-%m-%d_%H-%M-%S"),
                                    "cycle_day": cur_step,
                                    "error": "无法获取当前智能体提示词",
                                    "concepts_generated": len(concepts) if concepts else 0
                                }
                                failure_log_path = os.path.join(run_name, f'concept_application_failure_day_{cur_step}.json')
                                with open(failure_log_path, 'w', encoding='utf-8') as f:
                                    json.dump(failure_log, f, ensure_ascii=False, indent=2)
                                print(f"失败日志已保存到: {failure_log_path}")

                        # 更新上一个周期数据
                        args.previous_cycle_data = current_cycle_data.copy()

            # 应用LLM提示词更新
            if getattr(args, 'use_llm_update', 0):
                # 初始化LLM提示词更新器（如果尚未初始化）
                if not hasattr(args, 'llm_prompt_updater'):
                    # 【关键修复】从env_history获取完整的结构化提示词
                    print("从env_history获取完整的结构化提示词...")

                    try:
                        # 创建临时的env_history实例来获取完整提示词
                        temp_env_history = EnvironmentHistory(
                            base_query="",
                            start_state=env_history.get_current_state(),
                            memory=[],
                            history=[],
                            args=args
                        )

                        # 获取所有完整的结构化提示词
                        prompts = temp_env_history.get_prompt()
                        price_s, factual_news_s, subjective_news_s, reflection_s, risk_s, template_bullish, template_bearish, template_s = prompts

                        # 构建完整的base_prompts字典
                        base_prompts = {
                            "onchain_analyst_agent": price_s,
                            "factual_news_analyst_agent": factual_news_s,
                            "subjective_news_analyst_agent": subjective_news_s,
                            "reflection_analyst_agent": reflection_s,
                            "risk_manager_agent": risk_s,
                            "bullish_trader_agent": template_bullish,
                            "bearish_trader_agent": template_bearish,
                            "final_trader_agent": template_s
                        }

                        print(f"✅ 成功获取 {len(base_prompts)} 个智能体的完整结构化提示词")

                        # 验证提示词是否包含instructions标签
                        for agent_name, prompt in base_prompts.items():
                            if "<instructions>" in prompt and "</instructions>" in prompt:
                                print(f"✅ {agent_name}: 包含完整的instructions标签")
                            else:
                                print(f"⚠️ {agent_name}: 缺少instructions标签")

                    except Exception as e:
                        print(f"❌ 从env_history获取提示词失败: {e}")
                        print("使用备用的默认提示词")

                        # 备用方案：使用包含instructions标签的默认提示词
                        base_prompts = {
                            "bullish_trader_agent": """<optimized_prompt>
<task>Analyze cryptocurrency market information and generate trading decisions</task>
<context>You are a bullish ETH cryptocurrency trader.</context>
<instructions>
1. Analyze market conditions for bullish signals
2. Recommend buy/sell/hold actions with specific amounts
3. Focus on favorable market conditions
</instructions>
<output_format>ACTION: [action] AMOUNT: [amount]</output_format>
</optimized_prompt>""",
                            "bearish_trader_agent": """<optimized_prompt>
<task>Generate ETH trading recommendations based on unfavorable market conditions</task>
<context>You are a bearish ETH cryptocurrency trader.</context>
<instructions>
1. Analyze market conditions for bearish signals
2. Recommend buy/sell/hold actions with specific amounts
3. Focus on unfavorable market conditions
</instructions>
<output_format>ACTION: [action] AMOUNT: [amount]</output_format>
</optimized_prompt>""",
                            "final_trader_agent": """<optimized_prompt>
<task>Comprehensively analyze ETH cryptocurrency trading signals and make final decisions</task>
<context>You are the final decision-maker for ETH cryptocurrency trading.</context>
<instructions>
1. Consider all inputs from bullish and bearish traders
2. Make final trading decisions
3. Provide specific action and amount recommendations
</instructions>
<output_format>ACTION: [action] AMOUNT: [amount]</output_format>
</optimized_prompt>"""
                        }

                    args.llm_prompt_updater = LLMPromptUpdater(
                        cycle_length=cycle_length,
                        performance_threshold=getattr(args, 'performance_threshold', 0.0),
                        sharpe_threshold=getattr(args, 'sharpe_threshold', 0.5),
                        consecutive_days_threshold=getattr(args, 'consecutive_days_threshold', 3),
                        base_prompts=base_prompts,
                        run_name=run_name
                    )

                # 记录每日数据
                args.llm_prompt_updater.record_daily_data(
                    daily_return=state['today_roi'],
                    action=info['actual_action'],
                    reasoning=final_trader_response
                )

                # 检查是否需要更新提示词
                if args.llm_prompt_updater.should_update_prompts():
                    print("需要更新提示词...")
                    updated_prompts = args.llm_prompt_updater.update_prompts(llm_function)

                    # 提示词已经在LLMPromptUpdater中保存，这里只需要记录一下
                    print(f"提示词已更新，但不会影响下一次实验的起始提示词")

        # 记录决策理由
        if 'decision_reasons' not in cycle_data:
            cycle_data['decision_reasons'] = []
        cycle_data['decision_reasons'].append({
            'date': current_date,
            'action': final_action,
            'amount': final_amount,
            'reason': final_trader_response
        })
        if to_print:
            print_state = {k: v for k, v in state.items() if k != 'news'}
            current_date = env.get_current_date() if hasattr(env, 'get_current_date') else None
            debug_print(actual_action, None, f'STEP {cur_step} - ACTUAL ACTION',
                        role='ACTION', cur_date=str(cur_step), actual_date=current_date, run_name=run_name)
            debug_print(print_state, None, f'STEP {cur_step} - STATE',
                        role='STATE', cur_date=str(cur_step), actual_date=current_date, run_name=run_name)

            total_return = state['roi']
            tmp_returns = np.array(returns) * 100
            return_mean = np.mean(tmp_returns)
            return_std = np.std(tmp_returns)
            risk_free_rate = 0  # same as sociodojo
            sharpe_ratio = (return_mean - risk_free_rate) / return_std
            daily_result = f'Total return: {total_return*100:.2f}, sharpe ratio: {sharpe_ratio:.2f}, daily return mean: {return_mean:.2f}, daily return std: {return_std:.2f}'
            debug_print(daily_result, None, f'STEP {cur_step} - CURRENT RESULT',
                        role='RESULT', cur_date=str(cur_step), actual_date=current_date, run_name=run_name)

        cur_step += 1
        time.sleep(1)

    # 打印交易模拟结束提示
    if to_print:
        print(f"\n{'*'*30}")
        print(f"交易模拟结束，共执行{cur_step}个交易日")
        print(f"{'*'*30}\n")

    is_success = total_return > 0.1 # modify sucess condition
    return env_history, is_success, cycle_data

def run_trial(
        trial_log_path,
        world_log_path,
        trial_idx,
        env_configs: List[Dict[str, Any]],
        args,
    ) -> List[Dict[str, Any]]:
    use_memory = args.use_memory

    env = ETHTradingEnv(args)

    num_successes: int = 0
    num_additional_successes: int = 0
    num_envs: int = len(env_configs)

    for z, env_config in enumerate(env_configs):
        starting_state, reward, done, info = env.reset()

        if env_config["is_success"]:
            num_successes += 1

            with open(world_log_path, 'a') as wf:
                wf.write(f'Environment #{z} Trial #{trial_idx}: SUCCESS\n')
            with open(trial_log_path, 'a') as wf:
                wf.write(f'\n#####\n\nEnvironment #{z}: Success\n\n#####\n')
            continue

        final_env_history, is_success, cycle_data = eth_run(env, '', env_config["memory"] if use_memory else [], starting_state, args=args)

        # update env config
        if is_success:
            status_str: str = f'Environment #{z} Trial #{trial_idx}: SUCCESS'
            env_configs[z]['is_success'] = True
            num_successes += 1
            num_additional_successes += 1
        else:
            status_str: str = f'Environment #{z} Trial #{trial_idx}: FAIL'

        # log to world log
        with open(world_log_path, 'a') as f:
            f.write(status_str + '\n')

        # log env results to trial log
        with open(trial_log_path, 'a') as wf:
            wf.write(f'\n#####\n\nEnvironment #{z}:\n{str(final_env_history)}\n\nSTATUS: {"OK" if is_success else "FAIL"}\n\n#####\n')

    # close environment object
    env.close()

    # log trial results to trial and world logs
    log_str: str = f"""
-----
SUCCESS: {num_successes}
ADDITIONAL SUCCESS: {num_additional_successes}
FAIL: {num_envs - num_successes}
TOTAL: {num_envs}
ACCURACY: {round(num_successes / num_envs, 2)}
-----"""
    with open(trial_log_path, 'a') as wf:
        wf.write(log_str)
    with open(world_log_path, 'a') as wf:
        wf.write(log_str + '\n')

    # 生成性能报告（如果是最后一个试验）
    if getattr(args, 'generate_report', True) and trial_idx == args.num_trials - 1:
        try:
            # 确保报告目录存在
            reports_dir = os.path.join(args.run_name, 'reports')
            os.makedirs(reports_dir, exist_ok=True)

            # 创建性能报告生成器
            report_generator = PerformanceReport(args.run_name)

            # 收集环境数据
            # 检查cycle_data是否有足够的数据
            if cycle_data and 'actions' in cycle_data and len(cycle_data['actions']) > 0:
                print(f"使用cycle_data生成报告，包含{len(cycle_data['actions'])}个交易日数据")
                report_generator.collect_data(
                    env_history=final_env_history if 'final_env_history' in locals() else None,
                    cycle_data=cycle_data
                )
            else:
                print("警告: cycle_data中没有足够的数据，尝试从日志文件中恢复数据...")
                # 尝试从日志文件中恢复数据
                try:
                    from performance_report import recover_data_from_logs
                    recovered_data = recover_data_from_logs(args.run_name)
                    if recovered_data:
                        print(f"成功从日志中恢复了{len(recovered_data.get('actions', []))}个交易日数据")
                        report_generator.collect_data(cycle_data=recovered_data)
                    else:
                        print("无法从日志中恢复数据，将生成一个空报告")
                        report_generator.collect_data(cycle_data={})
                except Exception as e:
                    print(f"从日志恢复数据时出错: {e}")
                    report_generator.collect_data(cycle_data={})

            # 生成报告
            report_path = report_generator.generate_report()

            # 在日志中记录报告生成信息
            report_info = f"\n\n***** 性能报告已生成 *****\n报告路径: {report_path}\n"
            with open(world_log_path, 'a') as wf:
                wf.write(report_info)

            print(f"\n{'='*50}")
            print(f"性能报告已生成: {report_path}")
            print(f"{'='*50}\n")

            # 尝试在浏览器中打开报告
            try:
                if getattr(args, 'open_report', True):
                    webbrowser.open('file://' + os.path.abspath(report_path))
            except Exception as e:
                print(f"无法在浏览器中打开报告: {e}")
        except Exception as e:
            print(f"生成性能报告时出错: {e}")
            import traceback
            traceback.print_exc()

    return env_configs, cycle_data
